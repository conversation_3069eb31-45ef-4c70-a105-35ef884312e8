.zoom-in-top-enter-active[data-v-49a44f90],
.zoom-in-top-leave-active[data-v-49a44f90] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-49a44f90],
.zoom-in-top-leave-active[data-v-49a44f90] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-49a44f90],
.zoom-in-bottom-leave-active[data-v-49a44f90] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-49a44f90],
.zoom-in-bottom-leave-active[data-v-49a44f90] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.vfc-time-picker-container[data-v-49a44f90] {
    min-width: 250px
}

.vfc-time-picker-container .vfc-modal-time-line>span>span[data-v-49a44f90]:not(:nth-child(2)):not(.vfc-active):hover {
    cursor: pointer
}

.vfc-time-picker-container .titles[data-v-49a44f90] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 10px 0
}

.vfc-time-picker-container .titles>div[data-v-49a44f90] {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    text-align: center;
    color: #66b3cc;
    word-break: break-all;
    font-size: 25px
}

.vfc-time-picker-container .vfc-time-picker[data-v-49a44f90] {
    padding-bottom: 20px
}

.zoom-in-top-enter-active[data-v-9be65202],
.zoom-in-top-leave-active[data-v-9be65202] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-9be65202],
.zoom-in-top-leave-active[data-v-9be65202] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-9be65202],
.zoom-in-bottom-leave-active[data-v-9be65202] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-9be65202],
.zoom-in-bottom-leave-active[data-v-9be65202] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.vfc-day[data-v-9be65202] {
    position: relative
}

.vfc-day .times[data-v-9be65202] {
    position: absolute;
    top: -5px;
    background-color: red;
    color: #fff;
    border-radius: 50%;
    width: 15px;
    z-index: 20;
    height: 15px;
    line-height: 15px
}

.vfc-day .times[data-v-9be65202]:hover {
    cursor: pointer;
    background-color: #c70000
}

.vfc-day .number[data-v-9be65202] {
    position: absolute;
    top: -5px;
    right: calc(50% + 7px);
    background-color: green;
    color: #fff;
    font-size: 10px;
    border-radius: 50%;
    width: 15px;
    z-index: 30;
    height: 15px;
    line-height: 15px
}

.vfc-day .number[data-v-9be65202]:hover {
    background-color: #005e00
}

.vfc-day .toolTip[data-v-9be65202] {
    position: absolute;
    top: -20px;
    left: 0;
    padding: 5px;
    max-width: 108px;
    word-wrap: break-word;
    border-radius: 5px;
    z-index: 200;
    background-color: #005e00
}

.zoom-in-top-enter-active[data-v-be1f7bf6],
.zoom-in-top-leave-active[data-v-be1f7bf6] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-be1f7bf6],
.zoom-in-top-leave-active[data-v-be1f7bf6] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-be1f7bf6],
.zoom-in-bottom-leave-active[data-v-be1f7bf6] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-be1f7bf6],
.zoom-in-bottom-leave-active[data-v-be1f7bf6] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.footerCon[data-v-be1f7bf6] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    margin: 0 20px 20px
}

.zoom-in-top-enter-active,
.zoom-in-top-leave-active {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter,
.zoom-in-top-leave-active {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active,
.zoom-in-bottom-leave-active {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter,
.zoom-in-bottom-leave-active {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.vfc-popover-container:focus {
    outline: none
}

.vfc-multiple-input input,
.vfc-single-input {
    font-size: inherit;
    -webkit-transition: width .2s;
    transition: width .2s;
    padding: 7px;
    width: 143px;
    color: #aaa;
    border: 1px solid #efefef;
    text-align: center;
    outline: none
}

.vfc-single-input {
    border-radius: 10px
}

.vfc-multiple-input input:first-child {
    border-radius: 10px 0 0 10px
}

.vfc-multiple-input input:last-child {
    border-radius: 0 10px 10px 0
}

.vfc-tags-input {
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.vfc-tags-input input {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    background: transparent;
    border: none
}

.vfc-tags-input input[type=text] {
    color: #495057
}

.vfc-tags-input input:focus {
    outline: none
}

.vfc-tags-input span {
    margin-right: .3em;
    margin-bottom: .3em;
    padding-right: .75em;
    padding-left: .6em;
    border-radius: 10em
}

.vfc-tags-input-wrapper-default {
    width: 295px;
    padding: .5em .25em;
    min-height: 15px;
    background: #fff;
    border: 1px solid #dbdbdb;
    border-radius: 10px
}

.vfc-tags-input-badge {
    width: 85px;
    background-color: #f0f1f2;
    position: relative;
    display: inline-block;
    padding: .25em .4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em;
    overflow: hidden;
    text-overflow: ellipsis
}

.vfc-tags-input-remove {
    cursor: pointer;
    position: absolute;
    display: inline-block;
    right: .3em;
    top: .3em;
    padding: .5em;
    overflow: hidden
}

.vfc-tags-input-remove:after,
.vfc-tags-input-remove:before {
    content: "";
    position: absolute;
    width: 75%;
    left: .15em;
    background: #ff8498;
    height: 2px;
    margin-top: -1px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.vfc-tags-input-remove:after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.vfc-dark.vfc-multiple-input input,
.vfc-dark .vfc-single-input,
.vfc-dark.vfc-tags-input-root .vfc-tags-input-wrapper-default {
    border-color: #28456c;
    background-color: #1a202c
}

.vfc-dark.vfc-tags-input-root .vfc-tags-input-wrapper-default.vfc-tags-input .vfc-tags-input-badge,
.vfc-main-container {
    background-color: #fff
}

.vfc-main-container {
    position: relative;
    border-radius: .28571429rem;
    -webkit-box-shadow: 0 2px 15px 0 rgba(0, 0, 0, .25);
    box-shadow: 0 2px 15px 0 rgba(0, 0, 0, .25);
    font-family: -apple-system, BlinkMacSystemFont, PingFang SC, serif;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.vfc-main-container.vfc-modal {
    position: absolute;
    width: inherit;
    z-index: 1000
}

.vfc-main-container>* {
    -webkit-box-sizing: border-box;
    box-sizing: border-box
}

.vfc-main-container.vfc-dark {
    background-color: #1a202c
}

.vfc-main-container.vfc-dark .vfc-navigation-buttons div .vfc-arrow-left,
.vfc-main-container.vfc-dark .vfc-navigation-buttons div .vfc-arrow-right,
.vfc-main-container.vfc-dark .vfc-separately-navigation-buttons div .vfc-arrow-left,
.vfc-main-container.vfc-dark .vfc-separately-navigation-buttons div .vfc-arrow-right {
    border-color: #fff
}

.vfc-main-container.vfc-dark .vfc-navigation-buttons div .vfc-arrow-left:active,
.vfc-main-container.vfc-dark .vfc-navigation-buttons div .vfc-arrow-right:active,
.vfc-main-container.vfc-dark .vfc-separately-navigation-buttons div .vfc-arrow-left:active,
.vfc-main-container.vfc-dark .vfc-separately-navigation-buttons div .vfc-arrow-right:active {
    border-color: #d9d9d9
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content {
    background-color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-navigation-buttons div .vfc-arrow-left,
.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-navigation-buttons div .vfc-arrow-right {
    border-color: #000
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date {
    color: #000
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date .vfc-popover-caret {
    background-color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date.vfc-underline {
    -webkit-text-decoration: underline dotted #66b3cc;
    text-decoration: underline dotted #66b3cc
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-months div.vfc-item {
    color: #000
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-months div.vfc-item:hover {
    background-color: hsla(0, 0%, 44.3%, .3)
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar .vfc-months-container .vfc-content .vfc-months div.vfc-item.vfc-selected {
    background-color: #4299e1;
    color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-top-date span {
    color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-top-date span.vfc-underline {
    -webkit-text-decoration: underline #4299e1;
    text-decoration: underline #4299e1
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-top-date span.vfc-underline.vfc-underline-active {
    -webkit-text-decoration-color: #fff;
    text-decoration-color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-dayNames span {
    color: #bfbfbf
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week .vfc-week-number {
    border-color: #38b2ac
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day .vfc-base-end,
.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day .vfc-base-start {
    background-color: #28456c
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day {
    color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-today {
    background-color: #38b2ac;
    color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked {
    background-color: #4299e1
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked.vfc-borderd,
.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked.vfc-end-marked,
.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked.vfc-start-marked {
    color: #fff
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked:not(.vfc-start-marked):not(.vfc-end-marked):before {
    background-color: #28456c
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked:after {
    color: #000
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-marked.vfc-hide {
    color: #bfbfbf
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-hide {
    color: #464646
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-disabled {
    color: hsla(0, 0%, 52.2%, .2)
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day:after {
    color: #000
}

.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-hover:hover,
.vfc-main-container.vfc-dark .vfc-calendars .vfc-calendar div.vfc-content .vfc-week div.vfc-day span.vfc-span-day.vfc-hovered {
    z-index: 1;
    background-color: #4682b4
}

.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-time-picker__list .vfc-time-picker__item {
    color: #fff
}

.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-time-picker__list .vfc-time-picker__item--selected {
    color: #4299e1
}

.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-time-picker__list::-webkit-scrollbar-track {
    background: #28456c
}

.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-time-picker__list::-webkit-scrollbar-thumb {
    background: #4299e1
}

.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-close:after,
.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-close:before {
    background-color: #fff
}

.vfc-main-container.vfc-dark .vfc-time-picker-container .vfc-modal-time-mechanic .vfc-modal-time-line {
    background-color: #4299e1;
    color: #fff
}

.vfc-time-picker:after {
    content: "";
    display: table;
    clear: both
}

.vfc-time-picker-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.vfc-time-picker__list {
    float: left;
    width: 50%;
    height: 200px;
    overflow-y: scroll
}

.vfc-time-picker__list::-webkit-scrollbar {
    width: 3px
}

.vfc-time-picker__list::-webkit-scrollbar-track {
    background: #efefef
}

.vfc-time-picker__list::-webkit-scrollbar-thumb {
    background: #ccc
}

.vfc-time-picker__with-suffix .vfc-time-picker__list {
    width: 33.333333%
}

.vfc-time-picker__item {
    padding: 10px 0;
    font-size: 20px;
    text-align: center;
    cursor: pointer;
    -webkit-transition: font-size .3s;
    transition: font-size .3s
}

.vfc-time-picker__item:hover {
    font-size: 32px
}

.vfc-time-picker__item--selected {
    color: #66b3cc;
    font-size: 32px
}

.vfc-time-picker__item--disabled {
    opacity: .4;
    cursor: default;
    font-size: 20px !important
}

.vfc-close {
    position: absolute;
    right: 12px;
    top: 16px;
    width: 32px;
    height: 32px;
    opacity: .3;
    z-index: 100
}

.vfc-close:hover {
    opacity: 1
}

.vfc-close:after,
.vfc-close:before {
    position: absolute;
    left: 15px;
    content: " ";
    height: 26px;
    width: 2px;
    background-color: #fff;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.vfc-close:after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.vfc-modal-time-mechanic {
    position: relative;
    margin: 0 auto;
    width: 100%
}

.vfc-modal-time-line {
    width: 100%;
    background-color: #66b3cc;
    text-align: left;
    color: #fff;
    font-size: 16px;
    padding-top: 15px;
    padding-bottom: 15px;
    border-radius: .28571429rem .28571429rem 0 0
}

.vfc-modal-time-line span {
    margin-left: 15px
}

.vfc-modal-time-line span span.vfc-active {
    text-decoration: underline
}

.vfc-modal-append {
    color: #7d7d7d;
    font-weight: 400;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.vfc-modal-midle {
    display: inline-block
}

.vfc-modal-midle-dig {
    display: inline-block;
    text-align: center
}

.vfc-modal-digits {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    font-size: 50px
}

.vfc-modal-digits select {
    margin: 5px 0;
    width: 100%;
    text-align: center;
    -moz-text-align-last: center;
    text-align-last: center
}

.vfc-arrow {
    opacity: .3;
    -webkit-transition: .2s;
    transition: .2s
}

.vfc-arrow:hover {
    opacity: 1
}

.vfc-arrow-up {
    border-bottom: 20px solid #333
}

.vfc-arrow-down,
.vfc-arrow-up {
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent
}

.vfc-arrow-down {
    border-top: 20px solid #333
}

.vfc-separately-navigation-buttons {
    margin-bottom: -80px
}

.vfc-navigation-buttons {
    width: 100%;
    position: absolute
}

.vfc-navigation-buttons,
.vfc-separately-navigation-buttons {
    -webkit-box-flex: 0;
    -ms-flex: 0 1 15%;
    flex: 0 1 15%;
    margin-top: -10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.vfc-navigation-buttons.vfc-left,
.vfc-separately-navigation-buttons.vfc-left {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.vfc-navigation-buttons.vfc-right,
.vfc-separately-navigation-buttons.vfc-right {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.vfc-navigation-buttons.vfc-space-between,
.vfc-separately-navigation-buttons.vfc-space-between {
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.vfc-navigation-buttons div,
.vfc-separately-navigation-buttons div {
    z-index: 200;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    color: #000;
    font-size: 18px;
    margin: 20px 10px
}

.vfc-navigation-buttons div.vfc-cursor-pointer,
.vfc-separately-navigation-buttons div.vfc-cursor-pointer {
    cursor: pointer
}

.vfc-navigation-buttons div .vfc-arrow-left,
.vfc-separately-navigation-buttons div .vfc-arrow-left {
    width: 12px;
    height: 12px;
    border-top: 2px solid;
    border-left: 2px solid;
    border-color: #0a0c19;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.vfc-navigation-buttons div .vfc-arrow-left.vfc-disabled,
.vfc-navigation-buttons div .vfc-arrow-left:active,
.vfc-navigation-buttons div .vfc-arrow-right.vfc-disabled,
.vfc-navigation-buttons div .vfc-arrow-right:active,
.vfc-separately-navigation-buttons div .vfc-arrow-left.vfc-disabled,
.vfc-separately-navigation-buttons div .vfc-arrow-left:active,
.vfc-separately-navigation-buttons div .vfc-arrow-right.vfc-disabled,
.vfc-separately-navigation-buttons div .vfc-arrow-right:active {
    border-color: #ddd
}

.vfc-navigation-buttons div .vfc-arrow-right,
.vfc-separately-navigation-buttons div .vfc-arrow-right {
    width: 12px;
    height: 12px;
    border-top: 2px solid;
    border-right: 2px solid;
    border-color: #0a0c19;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.vfc-calendar {
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: auto;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column nowrap;
    flex-flow: column nowrap;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch
}

.vfc-calendar .vfc-content {
    margin-bottom: 20px
}

.vfc-calendars {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 75%;
    flex: 1 1 75%;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.vfc-calendars,
.vfc-calendars-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.vfc-calendars-container {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100%;
    position: relative;
    overflow: hidden
}

.vfc-calendar-fade-enter-active,
.vfc-calendar-fade-leave-active,
.vfc-calendar-slide-down-enter-active,
.vfc-calendar-slide-down-leave-active,
.vfc-calendar-slide-left-enter-active,
.vfc-calendar-slide-left-leave-active,
.vfc-calendar-slide-right-enter-active,
.vfc-calendar-slide-right-leave-active,
.vfc-calendar-slide-up-enter-active,
.vfc-calendar-slide-up-leave-active {
    -webkit-transition: all .25s ease-in-out;
    transition: all .25s ease-in-out
}

.vfc-calendar-fade-leave-active,
.vfc-calendar-none-leave-active,
.vfc-calendar-slide-down-leave-active,
.vfc-calendar-slide-left-leave-active,
.vfc-calendar-slide-right-leave-active,
.vfc-calendar-slide-up-leave-active {
    position: absolute
}

.vfc-calendar-none-enter-active,
.vfc-calendar-none-leave-active {
    -webkit-transition-duration: 0s;
    transition-duration: 0s
}

.vfc-calendar-slide-left-enter,
.vfc-calendar-slide-right-leave-to {
    opacity: 0;
    -webkit-transform: translateX(25px);
    transform: translateX(25px)
}

.vfc-calendar-slide-left-leave-to,
.vfc-calendar-slide-right-enter {
    opacity: 0;
    -webkit-transform: translateX(-25px);
    transform: translateX(-25px)
}

.vfc-calendar-slide-down-leave-to,
.vfc-calendar-slide-up-enter {
    opacity: 0;
    -webkit-transform: translateY(20px);
    transform: translateY(20px)
}

.vfc-calendar-slide-down-enter,
.vfc-calendar-slide-up-leave-to {
    opacity: 0;
    -webkit-transform: translateY(-20px);
    transform: translateY(-20px)
}

.vfc-months {
    -ms-flex: 1 1 75%;
    flex: 1 1 75%;
    padding: 0;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.vfc-months,
.vfc-months .vfc-item {
    -webkit-box-flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.vfc-months .vfc-item {
    -ms-flex: 1;
    flex: 1;
    -ms-flex-preferred-size: 30%;
    flex-basis: 30%;
    margin: 3px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center;
    outline-style: none;
    border-radius: 5px
}

.vfc-months .vfc-item:hover {
    background-color: hsla(0, 0%, 44.3%, .3);
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out;
    cursor: pointer
}

.vfc-months .vfc-item.vfc-selected {
    background-color: #4299e1;
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25)
}

.vfc-months-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    margin-left: -20px
}

.vfc-months-container.vfc-left {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    margin-left: 0
}

.vfc-months-container.vfc-left .vfc-content .vfc-navigation-buttons .vfc-top-date .vfc-popover-caret {
    left: 45px
}

.vfc-months-container.vfc-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.vfc-months-container.vfc-right {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end
}

.vfc-months-container.vfc-right .vfc-content .vfc-navigation-buttons .vfc-top-date .vfc-popover-caret {
    left: calc(100% - 90px)
}

.vfc-months-container .vfc-content {
    width: 45%;
    min-width: 133px;
    position: absolute;
    z-index: 1000;
    background-color: #2d3748;
    border: 1px solid;
    border-radius: 5px;
    top: 55px;
    color: #fff;
    padding: 5px 0
}

.vfc-months-container .vfc-content .vfc-navigation-buttons {
    position: unset;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.vfc-months-container .vfc-content .vfc-navigation-buttons div {
    margin: 10px 10px
}

.vfc-months-container .vfc-content .vfc-navigation-buttons div:hover {
    cursor: pointer
}

.vfc-months-container .vfc-content .vfc-navigation-buttons div:hover .vfc-arrow-left,
.vfc-months-container .vfc-content .vfc-navigation-buttons div:hover .vfc-arrow-right {
    border-color: #4299e1
}

.vfc-months-container .vfc-content .vfc-navigation-buttons div .vfc-arrow-left,
.vfc-months-container .vfc-content .vfc-navigation-buttons div .vfc-arrow-right {
    border-color: #fff;
    width: 8px;
    height: 8px
}

.vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date-has-delta:hover {
    cursor: pointer
}

.vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date .vfc-popover-caret {
    content: "";
    position: absolute;
    display: block;
    width: 12px;
    height: 12px;
    border-top: inherit;
    border-left: inherit;
    background: inherit;
    z-index: -1;
    background-color: #2d3748;
    -webkit-transform: translateY(-40%) rotate(45deg);
    transform: translateY(-40%) rotate(45deg);
    top: 0;
    left: 50%
}

.vfc-months-container .vfc-content .vfc-navigation-buttons .vfc-top-date.vfc-underline {
    cursor: pointer;
    -webkit-text-decoration: underline dotted #66b3cc;
    text-decoration: underline dotted #66b3cc
}

.vfc-months-container .vfc-content .vfc-months {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 75%;
    flex: 1 1 75%;
    padding: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.vfc-months-container .vfc-content .vfc-months div.vfc-item {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    -ms-flex-preferred-size: 30%;
    flex-basis: 30%;
    margin: 3px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: center;
    outline-style: none;
    border-radius: 5px
}

.vfc-months-container .vfc-content .vfc-months div.vfc-item:hover {
    background-color: hsla(0, 0%, 44.3%, .3);
    -webkit-transition: background-color .2s ease-in-out;
    transition: background-color .2s ease-in-out;
    cursor: pointer
}

.vfc-months-container .vfc-content .vfc-months div.vfc-item.vfc-selected {
    background-color: #4299e1;
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, .25)
}

.vfc-content {
    margin: 0 20px;
    z-index: 100
}

.vfc-top-date {
    margin: 25px;
    font-size: 18px;
    font-weight: 400
}

.vfc-top-date.vfc-left {
    text-align: left
}

.vfc-top-date.vfc-right {
    text-align: right
}

.vfc-top-date.vfc-center {
    text-align: center
}

.vfc-top-date span {
    cursor: default;
    text-decoration: unset;
    margin: 0 2px;
    color: #000
}

.vfc-top-date span.vfc-cursor-pointer {
    cursor: pointer
}

.vfc-top-date span.vfc-underline {
    cursor: pointer;
    -webkit-text-decoration: underline #66b3cc;
    text-decoration: underline #66b3cc
}

.vfc-top-date span.vfc-underline.vfc-underline-active {
    -webkit-text-decoration-color: #000;
    text-decoration-color: #000
}

.vfc-dayNames,
.vfc-week {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.vfc-dayNames {
    -webkit-box-flex: 30px;
    -ms-flex: 30px 0 0px;
    flex: 30px 0 0;
    margin-bottom: 10px
}

.vfc-dayNames span {
    width: 100%;
    margin-right: 5px;
    color: #333;
    text-align: center
}

.vfc-dayNames span:last-child {
    margin-right: 0
}

.vfc-week-number {
    border-right: 1px solid #ff8498
}

.vfc-week .vfc-day {
    position: relative;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-top: 3px
}

.vfc-week .vfc-day .vfc-base-end,
.vfc-week .vfc-day .vfc-base-start {
    position: absolute;
    background: #8fd8ec;
    width: 50% !important;
    border-radius: 0 !important;
    border-right-width: 0 !important;
    height: 100%
}

.vfc-week .vfc-day .vfc-base-start {
    right: 0
}

.vfc-week .vfc-day .vfc-base-end {
    left: 0
}

.vfc-week .vfc-day span.vfc-span-day {
    display: inline-block;
    text-align: center;
    width: 30px;
    line-height: 30px;
    border-radius: 50%;
    margin: 0 auto;
    vertical-align: middle
}

.vfc-week .vfc-day span.vfc-span-day.vfc-today {
    background-color: #ff8498;
    color: #fff
}

.vfc-week .vfc-day span.vfc-span-day.vfc-cursor-not-allowed {
    cursor: not-allowed
}

.vfc-week .vfc-day span.vfc-span-day.vfc-marked {
    margin: auto;
    background-color: #66b3cc;
    border-radius: 50%;
    opacity: 1;
    z-index: 1
}

.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-borderd,
.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-end-marked,
.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-start-marked {
    color: #fff
}

.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-borderd:before,
.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-end-marked:before,
.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-start-marked:before {
    background: transparent
}

.vfc-week .vfc-day span.vfc-span-day.vfc-marked:before {
    top: 0;
    left: 0;
    content: "";
    position: absolute;
    background-color: #8fd8ec;
    width: 100%;
    height: 100%;
    z-index: -1
}

.vfc-week .vfc-day span.vfc-span-day.vfc-marked:after {
    color: #000
}

.vfc-week .vfc-day span.vfc-span-day.vfc-marked.vfc-hide {
    color: #d9d9d9
}

.vfc-week .vfc-day span.vfc-span-day.vfc-hide {
    color: #bfbfbf
}

.vfc-week .vfc-day span.vfc-span-day.vfc-disabled {
    margin: auto;
    color: rgba(0, 0, 0, .2);
    border-radius: 50%;
    opacity: 1;
    z-index: 2
}

.vfc-week .vfc-day span.vfc-span-day:after {
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    content: attr(data-date);
    color: #000;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.vfc-week .vfc-day span.vfc-span-day.vfc-hover:hover,
.vfc-week .vfc-day span.vfc-span-day.vfc-hovered {
    background-color: #dadada;
    z-index: 100
}

.vfc-week .vfc-day:last-child {
    color: #000
}

.rangeCleaner {
    padding: 5px 0 10px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.rangeCleaner span {
    color: #fff;
    border-radius: 5px;
    border: none;
    padding: 5px
}

.rangeCleaner span.active {
    background-color: #66b3cc
}

.rangeCleaner span.active:hover {
    background-color: #4f8a9e;
    cursor: pointer
}

.rangeCleaner span.disabled {
    background-color: #949494
}

.swiper-container {
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
    list-style: none;
    padding: 0;
    z-index: 1
}

.swiper-container-no-flexbox .swiper-slide {
    float: left
}

.swiper-container-vertical>.swiper-wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    -ms-flex-direction: column;
    flex-direction: column
}

.swiper-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 1;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.swiper-container-android .swiper-slide,
.swiper-wrapper {
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.swiper-container-multirow>.swiper-wrapper {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.swiper-container-free-mode>.swiper-wrapper {
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    margin: 0 auto
}

.swiper-slide {
    -webkit-flex-shrink: 0;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    width: 100%;
    height: 100%;
    position: relative;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform
}

.swiper-slide-invisible-blank {
    visibility: hidden
}

.swiper-container-autoheight,
.swiper-container-autoheight .swiper-slide {
    height: auto
}

.swiper-container-autoheight .swiper-wrapper {
    -webkit-box-align: start;
    -webkit-align-items: flex-start;
    -ms-flex-align: start;
    align-items: flex-start;
    -webkit-transition-property: height, -webkit-transform;
    transition-property: height, -webkit-transform;
    transition-property: transform, height;
    transition-property: transform, height, -webkit-transform
}

.swiper-container-3d {
    -webkit-perspective: 1200px;
    perspective: 1200px
}

.swiper-container-3d .swiper-cube-shadow,
.swiper-container-3d .swiper-slide,
.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top,
.swiper-container-3d .swiper-wrapper {
    -webkit-transform-style: preserve-3d;
    transform-style: preserve-3d
}

.swiper-container-3d .swiper-slide-shadow-bottom,
.swiper-container-3d .swiper-slide-shadow-left,
.swiper-container-3d .swiper-slide-shadow-right,
.swiper-container-3d .swiper-slide-shadow-top {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10
}

.swiper-container-3d .swiper-slide-shadow-left {
    background-image: -webkit-gradient(linear, right top, left top, from(rgba(0, 0, 0, .5)), to(transparent));
    background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, .5), transparent);
    background-image: linear-gradient(270deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-3d .swiper-slide-shadow-right {
    background-image: -webkit-gradient(linear, left top, right top, from(rgba(0, 0, 0, .5)), to(transparent));
    background-image: -webkit-linear-gradient(left, rgba(0, 0, 0, .5), transparent);
    background-image: linear-gradient(90deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-3d .swiper-slide-shadow-top {
    background-image: -webkit-gradient(linear, left bottom, left top, from(rgba(0, 0, 0, .5)), to(transparent));
    background-image: -webkit-linear-gradient(bottom, rgba(0, 0, 0, .5), transparent);
    background-image: linear-gradient(0deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-3d .swiper-slide-shadow-bottom {
    background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 0, 0, .5)), to(transparent));
    background-image: -webkit-linear-gradient(top, rgba(0, 0, 0, .5), transparent);
    background-image: linear-gradient(180deg, rgba(0, 0, 0, .5), transparent)
}

.swiper-container-wp8-horizontal,
.swiper-container-wp8-horizontal>.swiper-wrapper {
    -ms-touch-action: pan-y;
    touch-action: pan-y
}

.swiper-container-wp8-vertical,
.swiper-container-wp8-vertical>.swiper-wrapper {
    -ms-touch-action: pan-x;
    touch-action: pan-x
}

.swiper-button-next,
.swiper-button-prev {
    position: absolute;
    top: 50%;
    width: 27px;
    height: 44px;
    margin-top: -22px;
    z-index: 10;
    cursor: pointer;
    background-size: 27px 44px;
    background-position: 50%;
    background-repeat: no-repeat
}

.swiper-button-next.swiper-button-disabled,
.swiper-button-prev.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none
}

.swiper-button-prev,
.swiper-container-rtl .swiper-button-next {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 44'%3E%3Cpath d='M0 22L22 0l2.1 2.1L4.2 22l19.9 19.9L22 44 0 22z' fill='%23007aff'/%3E%3C/svg%3E");
    left: 10px;
    right: auto
}

.swiper-button-next,
.swiper-container-rtl .swiper-button-prev {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 44'%3E%3Cpath d='M27 22L5 44l-2.1-2.1L22.8 22 2.9 2.1 5 0l22 22z' fill='%23007aff'/%3E%3C/svg%3E");
    right: 10px;
    left: auto
}

.swiper-button-prev.swiper-button-white,
.swiper-container-rtl .swiper-button-next.swiper-button-white {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 44'%3E%3Cpath d='M0 22L22 0l2.1 2.1L4.2 22l19.9 19.9L22 44 0 22z' fill='%23fff'/%3E%3C/svg%3E")
}

.swiper-button-next.swiper-button-white,
.swiper-container-rtl .swiper-button-prev.swiper-button-white {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 44'%3E%3Cpath d='M27 22L5 44l-2.1-2.1L22.8 22 2.9 2.1 5 0l22 22z' fill='%23fff'/%3E%3C/svg%3E")
}

.swiper-button-prev.swiper-button-black,
.swiper-container-rtl .swiper-button-next.swiper-button-black {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 44'%3E%3Cpath d='M0 22L22 0l2.1 2.1L4.2 22l19.9 19.9L22 44 0 22z'/%3E%3C/svg%3E")
}

.swiper-button-next.swiper-button-black,
.swiper-container-rtl .swiper-button-prev.swiper-button-black {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 27 44'%3E%3Cpath d='M27 22L5 44l-2.1-2.1L22.8 22 2.9 2.1 5 0l22 22z'/%3E%3C/svg%3E")
}

.swiper-button-lock {
    display: none
}

.swiper-pagination {
    position: absolute;
    text-align: center;
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    z-index: 10
}

.swiper-pagination.swiper-pagination-hidden {
    opacity: 0
}

.swiper-container-horizontal>.swiper-pagination-bullets,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    bottom: 10px;
    left: 0;
    width: 100%
}

.swiper-pagination-bullets-dynamic {
    overflow: hidden;
    font-size: 0
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    -webkit-transform: scale(.33);
    -ms-transform: scale(.33);
    transform: scale(.33);
    position: relative
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active,
.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
    -webkit-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
    -webkit-transform: scale(.66);
    -ms-transform: scale(.66);
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
    -webkit-transform: scale(.33);
    -ms-transform: scale(.33);
    transform: scale(.33)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
    -webkit-transform: scale(.66);
    -ms-transform: scale(.66);
    transform: scale(.66)
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
    -webkit-transform: scale(.33);
    -ms-transform: scale(.33);
    transform: scale(.33)
}

.swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 100%;
    background: #000;
    opacity: .2
}

button.swiper-pagination-bullet {
    border: none;
    margin: 0;
    padding: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.swiper-pagination-clickable .swiper-pagination-bullet {
    cursor: pointer
}

.swiper-pagination-bullet-active {
    opacity: 1;
    background: #007aff
}

.swiper-container-vertical>.swiper-pagination-bullets {
    right: 10px;
    top: 50%;
    -webkit-transform: translate3d(0, -50%, 0);
    transform: translate3d(0, -50%, 0)
}

.swiper-container-vertical>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 6px 0;
    display: block
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    top: 50%;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 8px
}

.swiper-container-vertical>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    display: inline-block;
    -webkit-transition: top .2s, -webkit-transform .2s;
    transition: top .2s, -webkit-transform .2s;
    transition: transform .2s, top .2s;
    transition: transform .2s, top .2s, -webkit-transform .2s
}

.swiper-container-horizontal>.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 4px
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
    left: 50%;
    -webkit-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    transform: translateX(-50%);
    white-space: nowrap
}

.swiper-container-horizontal>.swiper-pagination-bullets.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    -webkit-transition: left .2s, -webkit-transform .2s;
    transition: left .2s, -webkit-transform .2s;
    transition: transform .2s, left .2s;
    transition: transform .2s, left .2s, -webkit-transform .2s
}

.swiper-container-horizontal.swiper-container-rtl>.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
    -webkit-transition: right .2s, -webkit-transform .2s;
    transition: right .2s, -webkit-transform .2s;
    transition: transform .2s, right .2s;
    transition: transform .2s, right .2s, -webkit-transform .2s
}

.swiper-pagination-progressbar {
    background: rgba(0, 0, 0, .25);
    position: absolute
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    background: #007aff;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0);
    -webkit-transform-origin: left top;
    -ms-transform-origin: left top;
    transform-origin: left top
}

.swiper-container-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
    -webkit-transform-origin: right top;
    -ms-transform-origin: right top;
    transform-origin: right top
}

.swiper-container-horizontal>.swiper-pagination-progressbar,
.swiper-container-vertical>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite {
    width: 100%;
    height: 4px;
    left: 0;
    top: 0
}

.swiper-container-horizontal>.swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-container-vertical>.swiper-pagination-progressbar {
    width: 4px;
    height: 100%;
    left: 0;
    top: 0
}

.swiper-pagination-white .swiper-pagination-bullet-active {
    background: #fff
}

.swiper-pagination-progressbar.swiper-pagination-white {
    background: hsla(0, 0%, 100%, .25)
}

.swiper-pagination-progressbar.swiper-pagination-white .swiper-pagination-progressbar-fill {
    background: #fff
}

.swiper-pagination-black .swiper-pagination-bullet-active {
    background: #000
}

.swiper-pagination-progressbar.swiper-pagination-black {
    background: rgba(0, 0, 0, .25)
}

.swiper-pagination-progressbar.swiper-pagination-black .swiper-pagination-progressbar-fill {
    background: #000
}

.swiper-pagination-lock {
    display: none
}

.swiper-scrollbar {
    border-radius: 10px;
    position: relative;
    -ms-touch-action: none;
    background: rgba(0, 0, 0, .1)
}

.swiper-container-horizontal>.swiper-scrollbar {
    position: absolute;
    left: 1%;
    bottom: 3px;
    z-index: 50;
    height: 5px;
    width: 98%
}

.swiper-container-vertical>.swiper-scrollbar {
    position: absolute;
    right: 3px;
    top: 1%;
    z-index: 50;
    width: 5px;
    height: 98%
}

.swiper-scrollbar-drag {
    height: 100%;
    width: 100%;
    position: relative;
    background: rgba(0, 0, 0, .5);
    border-radius: 10px;
    left: 0;
    top: 0
}

.swiper-scrollbar-cursor-drag {
    cursor: move
}

.swiper-scrollbar-lock {
    display: none
}

.swiper-zoom-container {
    width: 100%;
    height: 100%;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
    text-align: center
}

.swiper-zoom-container>canvas,
.swiper-zoom-container>img,
.swiper-zoom-container>svg {
    max-width: 100%;
    max-height: 100%;
    -o-object-fit: contain;
    object-fit: contain
}

.swiper-slide-zoomed {
    cursor: move
}

.swiper-lazy-preloader {
    width: 42px;
    height: 42px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -21px;
    margin-top: -21px;
    z-index: 10;
    -webkit-transform-origin: 50%;
    -ms-transform-origin: 50%;
    transform-origin: 50%;
    -webkit-animation: swiper-preloader-spin 1s steps(12) infinite;
    animation: swiper-preloader-spin 1s steps(12) infinite
}

.swiper-lazy-preloader:after {
    display: block;
    content: "";
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath id='a' stroke='%236c6c6c' stroke-width='11' stroke-linecap='round' d='M60 7v20'/%3E%3C/defs%3E%3Cuse xlink:href='%23a' opacity='.27'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(30 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(60 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(90 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(120 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(150 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.37' transform='rotate(180 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.46' transform='rotate(210 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.56' transform='rotate(240 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.66' transform='rotate(270 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.75' transform='rotate(300 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.85' transform='rotate(330 60 60)'/%3E%3C/svg%3E");
    background-position: 50%;
    background-size: 100%;
    background-repeat: no-repeat
}

.swiper-lazy-preloader-white:after {
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath id='a' stroke='%23fff' stroke-width='11' stroke-linecap='round' d='M60 7v20'/%3E%3C/defs%3E%3Cuse xlink:href='%23a' opacity='.27'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(30 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(60 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(90 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(120 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(150 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.37' transform='rotate(180 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.46' transform='rotate(210 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.56' transform='rotate(240 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.66' transform='rotate(270 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.75' transform='rotate(300 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.85' transform='rotate(330 60 60)'/%3E%3C/svg%3E")
}

@-webkit-keyframes swiper-preloader-spin {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes swiper-preloader-spin {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.swiper-container .swiper-notification {
    position: absolute;
    left: 0;
    top: 0;
    pointer-events: none;
    opacity: 0;
    z-index: -1000
}

.swiper-container-fade.swiper-container-free-mode .swiper-slide {
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out
}

.swiper-container-fade .swiper-slide {
    pointer-events: none;
    -webkit-transition-property: opacity;
    transition-property: opacity
}

.swiper-container-fade .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-fade .swiper-slide-active,
.swiper-container-fade .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-cube {
    overflow: visible
}

.swiper-container-cube .swiper-slide {
    pointer-events: none;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 1;
    visibility: hidden;
    -webkit-transform-origin: 0 0;
    -ms-transform-origin: 0 0;
    transform-origin: 0 0;
    width: 100%;
    height: 100%
}

.swiper-container-cube .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-cube.swiper-container-rtl .swiper-slide {
    -webkit-transform-origin: 100% 0;
    -ms-transform-origin: 100% 0;
    transform-origin: 100% 0
}

.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-cube .swiper-slide-active,
.swiper-container-cube .swiper-slide-next,
.swiper-container-cube .swiper-slide-next+.swiper-slide,
.swiper-container-cube .swiper-slide-prev {
    pointer-events: auto;
    visibility: visible
}

.swiper-container-cube .swiper-slide-shadow-bottom,
.swiper-container-cube .swiper-slide-shadow-left,
.swiper-container-cube .swiper-slide-shadow-right,
.swiper-container-cube .swiper-slide-shadow-top {
    z-index: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.swiper-container-cube .swiper-cube-shadow {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: .6;
    -webkit-filter: blur(50px);
    filter: blur(50px);
    z-index: 0
}

.swiper-container-flip {
    overflow: visible
}

.swiper-container-flip .swiper-slide {
    pointer-events: none;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    z-index: 1
}

.swiper-container-flip .swiper-slide .swiper-slide {
    pointer-events: none
}

.swiper-container-flip .swiper-slide-active,
.swiper-container-flip .swiper-slide-active .swiper-slide-active {
    pointer-events: auto
}

.swiper-container-flip .swiper-slide-shadow-bottom,
.swiper-container-flip .swiper-slide-shadow-left,
.swiper-container-flip .swiper-slide-shadow-right,
.swiper-container-flip .swiper-slide-shadow-top {
    z-index: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden
}

.swiper-container-coverflow .swiper-wrapper {
    -ms-perspective: 1200px
}