.zoom-in-top-enter-active[data-v-ef22beee],
.zoom-in-top-leave-active[data-v-ef22beee] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-ef22beee],
.zoom-in-top-leave-active[data-v-ef22beee] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-ef22beee],
.zoom-in-bottom-leave-active[data-v-ef22beee] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-ef22beee],
.zoom-in-bottom-leave-active[data-v-ef22beee] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.cs-menu-wrap[data-v-ef22beee] {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.cs-menu[data-v-ef22beee] {
    width: auto;
    background-color: #363737;
    color: #fffffe;
    padding: 10px 1em;
    border-radius: 5px;
    cursor: pointer;
    font-size: 20px;
    font-weight: 700;
    -webkit-box-shadow: 0 20px 60px -2px rgba(27, 33, 58, .4);
    box-shadow: 0 20px 60px -2px rgba(27, 33, 58, .4)
}

.cs-menu li[data-v-ef22beee] {
    text-align: center;
    padding-bottom: 4px
}

.cs-menu li[data-v-ef22beee]:hover {
    color: #0286eb
}

.cs-menu li[data-v-ef22beee]:last-of-type {
    padding-bottom: 0
}

.opacity-enter-active[data-v-ef22beee] {
    -webkit-animation: slideInDown-ef22beee .3s;
    animation: slideInDown-ef22beee .3s
}

@-webkit-keyframes slideInDown-ef22beee {
    0% {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        visibility: visible
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes slideInDown-ef22beee {
    0% {
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0);
        visibility: visible
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.zoom-in-top-enter-active[data-v-4e7b913d],
.zoom-in-top-leave-active[data-v-4e7b913d] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-4e7b913d],
.zoom-in-top-leave-active[data-v-4e7b913d] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-4e7b913d],
.zoom-in-bottom-leave-active[data-v-4e7b913d] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-4e7b913d],
.zoom-in-bottom-leave-active[data-v-4e7b913d] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

#pp_model_dialog[data-v-4e7b913d] {
    z-index: 99998
}

.pp_model_dbody[data-v-4e7b913d] {
    max-width: 360px
}

.pp_model_dbhtml[data-v-4e7b913d] {
    line-height: 1.2
}

span.contract_type_title {
    margin-left: 28px;
    font-size: 20px;
    font-weight: 700;
    color: #020508
}

.br_index_modal.contract {
    display: none;
    z-index: 1111;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.br_index_modal.contract.active {
    display: block
}

.br_index_modal.contract .br_imodal_main {
    position: relative;
    width: 100%;
    height: 100%
}

.br_index_modal.contract .br_imodal_main .br_imodal_mbg {
    background-color: rgba(0, 0, 0, .5);
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.br_index_modal.contract .br_imodal_main .br_imodal_content {
    border-radius: 9px;
    width: 900px;
    max-height: 720px;
    z-index: 9;
    background-color: #fff;
    position: absolute;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    top: 50%;
    left: 50%
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain {
    width: 100%;
    height: 100%;
    position: relative
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmtitle {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-top: 19px;
    padding-bottom: 19px;
    padding-left: 28px;
    color: #000;
    font-weight: 700;
    font-size: 34px;
    line-height: 1.24;
    border-bottom: 1px solid #ccc
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmclose {
    border: 2px solid #555;
    text-align: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    border-radius: 50%;
    width: 25px;
    height: 26px;
    cursor: pointer;
    position: absolute;
    right: 27px;
    top: 20px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmclose span {
    border: 1px solid #555;
    border-radius: 2px;
    width: 13px;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    position: absolute
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmclose span:last-child {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmclose:hover {
    border: 2px solid #c30d23
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmclose:hover span {
    border: 1px solid #c30d23
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody {
    max-height: 610px;
    margin-bottom: 32px;
    overflow: hidden auto
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody {
    margin-top: 32px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq {
    margin-top: 22px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq span.title_category {
    color: #000;
    font-size: 20px;
    font-weight: 700;
    margin: 28px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain {
    width: 846px;
    margin: 10px 4px 20px 27px;
    padding: 20px;
    border-radius: 7px;
    background-color: #e1e1e1;
    position: relative
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start;
    gap: 10px;
    vertical-align: middle;
    width: 100%;
    height: 50px;
    text-align: center;
    font-size: 18px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname .agreement {
    vertical-align: top;
    position: relative;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    height: 100%
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname .agreement .icon_agreement {
    display: block;
    cursor: pointer;
    width: 80px;
    height: 100%;
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAABkJJREFUeNrcW2tsVEUUHpZSNdIC0aIFMS6NDyhoxMZoY2WjTVRiqhYRhL+aJoBEbXjEoInRiEjRKEFC9acvfCBGUTHRllJBSQVig6CJoAVfWLCCj9qU4vlyz01mz57b3u7O3W73JF+yd+7dmfnunTnnzJkzI0pLS01EUkyoJFQQyglxwkTCWMJofuYvQhfhZ8Ihwn5CG2EX4c8oOjXCMeGLCHMJNUy2IM16ThO+JGwmvEXoyDXCNxKWEm4jjHT8UUD+I0IDYXumlcUy/H+C0MIduT0CsobrRN3N3FZiKL4w/rSGsCDEs0cJXxEOEo4QThL+5nvn8lyfTLiCcCVPi4HkVR5Rv2SD8BzCS4QxAffPEFoJb/BQPDzI+uM8NaALqtDHgOeg1O7nOR4J4ULCWsLigPv4ahsJLxK+dzSc8eUXEep4NGiynlBP+M8l4SLCu4SblXu9hHWEVYTfIzJxJYQVhCUBmn8H4U7CCRdKC401BZCFvZxBeDhCsobrrue2dir3q7iPJZkSxjz9mHCNMk9XszlqN9kTtDWT2z4j7kHhbetHtwxIuJAN/wxR3k24m4dYr8m+9HLbtZa29+Vq7nNhOoSfJdykaMZbudKhli2EWxQXFH1+brCE57B2tKWH3+p2kzvyOfepW5QvJNwTlvCFhEZlzqKCz0zuCfo0T5nTGzUnRiO8llc0tsCres/krqBvz4gycHhhIMLwU+eLsi8IKx11rJzNSjebkUsdkl7JZtKWu6Q5lY5HC9s0WyPCJH3toEOjCAcIZVYZrqcR+hyRnk7YI5yTVptTTBjvKlHBOkdkDS8OykTZFMIEx3b6eVF2A/sLKYSXiQexqnnSYWd+ZU1vCyIexx3P56cUU7VMEh7P9tWWxjC+6SDdw6WWNsV0eYjwr2PCJxQrA3t9gU14vhj3fbwKcS3QmlPZxF1OeDkirb1e6AVwu9cmPFv8AdGFHyLqzEFewx6K0Ez9yFbAltk+YTjb14ub75jhL5IDOI7Bp65UYlGfOmwYntssZR3bSfhAUWQuPTAZG6tEJ64SNxAj/tbhwn0Px8A02aYoS1cCDj8ZLxbuS0WMlYctbQ4bndUPWV97jo9wWO+Wnp5G+IDDBsPEts6OkLAcqXEQniQKOxw22MpLtb2slTtMduWIuJ4IwsWKg+BSNnDUpExxXaOWY+K6WCP8j8kfkV5ckbYeLsgjwpJLT4wXCUlvIY8Ij5aLFY3w2DwiPE5cn4ppmiyPCEsuv4Hwd6Jwah4Rlj7G4ZhinCvyiPC14no/CO8ThROUNzMc5TJlSLeBMKKIp8WN6jwgLDmA4y4QRvxHhjfn5gFhyQEcu2IBi2VE+iYPY7JxxY3d7Ec8IK+Z5J1ApBksdtT4WcYLk1bzizTKy63mQISrldMik5wq0csckwLx7xsvW8YXbEVewpGJdAVpCsi3Kg/5PBLTrjNe+DZdOd94eSW2l7XV52b70muUzj6S4ZuuGQRZw8/WZNjmCsWlbPB/2IRbeP1qywPG275IV9JJHzyZQXvYtlmirMmbNcKQx5TVRmMGK6gPjbc53R3i2VP87NYMVkbo6yhR/rh9oWXxIOlL7iCu5qGSy7JK6SMyj2oHIoyCb8SqCdsj2HrM1T3iO5icrZm7eDoetR/UAgBI56sTZahok0nN+cgFQZ9eN6kZe3WSbBBhyJvGy6iT9hTGe2YOka3kPp0jyjcwBxOWMAQ7e3J/xs/bqs0BsjBfn5jUvCxYmweD/tQfYWyBIJtHJp7BG3qb8LQZmvhXAbe9xaTmX7bzx+hJhzDkOLt9+5Q5vdx4KUzTs0h2Gre5XJmz7dzXfjfYw+RaHuN52xQwh7B3hMyfkgiJnsfe0l5u0yjORcKkxqFTZGRRUaggJVJzkf88Toki4KVhK3IhdwzbK384IooVG7JzXuGXrn0gKKgFYf3vqBLEd7AZSzdBHDuK80z/CeJwQe8zESaIS+ekQfHINIEtxI6kfwSg0yQfAShhklNYH0wKUSfsbr3J0hEAWzBvnghY50YhyK18NECfhJJMT7U087BLsNPfFwHJPq47wS+2KZPKXB/Uuth4GTq1rNzSPdaDgNtu9qLgMeXcQS1N/GSZCp6bcY5GaEfxOlm5wZbiyM9OE9FRvP8FGACc9j8nO/3fBQAAAABJRU5ErkJggg==);
    background-size: 20px 20px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname .agreement .icon_agreement:hover {
    background-size: 20px 20px;
    background-repeat: no-repeat;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA/CAMAAACLtlvuAAAAQlBMVEVHcEz/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABn/ABkhm8KxAAAAFXRSTlMAKNpg5zR0HgT1VBenDrJFvoSZzZHtC5dEAAACcElEQVRIx81X25aDIAyU+00UUP//V3cFbLUmSPdp89RDGTJOQhKGATY3yzSFZQlTkrMb+k3PK90uRtdZd0Fd5BtgPD77V8lsiJmk2oTlG8pDilLGFN5EjGyQJ8sBTPZE0tl0HEAJhmXVbbA3B9qG6pzB2LVCBfy3qPAEUNdjIWzxr7KF/HTnNZU/mvFwE4wufuNTJGPeNl4XZV5kzynE8kZ5EcN0YivanET19HYcbpkk9dcvGXvvTZZnfSXWx1kvcdOy3lcrT3I+ar7v2rMiAK7nE1GCkM7rm8OIk8+fV9YZ7Afk2OzOG0ytdBbm5tr4I3DwZbARyXRxpMWer3T40nbBp98bYfrz45opRhfdAdZazL+GVS1RopvPALC1Iq0IuvBNcCawo+ghF3zKIQrw8fMBVnjNClk3SK+jfitcMZrJgxdZe9IA75/Fh30DkgqqAbZZZ+RGPYGzJpn2nz1zvHipx29e8IrbAu+Va8m3a/oeXGARv1QtMM2ELb6jAXYlwg4v9w0wq2m/oDW7AR6zXkU3478D57oXj1IIXA1fcpt4j7QcctR2ruFeXzrq7U9eWZdMu7m25yHKQo7tO2bG9YOdOeUGA/qNDm9s0EC3YeeOdos1EdUIFOPl2nuM6CvZZYoQHwpw0oMl/KZvnpGoesaq3NmvtdrRPt/FL3XQqrFtrDWwj4LeksehPm0YP1KeBhydxhhvTM2u5gVl0FzL6rMjIM1Lx2NWX4W+ttv1mNYjPuvPr1eNmaIVRCkiWJxebwc6tx8ZfEONy6f3kcfgXPqeDJzH2/PIjJ2vsqyQHJd6gllGKbqR79gpIZQb/qP9ALgiSmWXUpBkAAAAAElFTkSuQmCC)
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname span:last-child {
    margin-top: 1px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname span.br_imodal_ticon:first-child {
    background-color: #fff;
    border-radius: 50%;
    width: 25px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 25px
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname span.br_imodal_ticon:first-child img {
    width: 60%
}

.br_index_modal.contract .br_imodal_main .br_imodal_content .br_imodal_cmain .br_imodal_cmbody .br_imodal_pbody .br_imodal_typereq .br_imodal_trmain .br_imodal_tname p.subtitle {
    font-size: 20px;
    font-weight: 700;
    color: #191919
}

span.icon_agreement:hover>div.br_imodal_tagreement {
    visibility: visible;
    opacity: 1
}

div.br_imodal_tagreement {
    cursor: text;
    z-index: 1;
    visibility: hidden;
    opacity: 0;
    -webkit-transition: visibility 0s, opacity .5s linear;
    transition: visibility 0s, opacity .5s linear;
    padding: 15px;
    background-color: #616161;
    -webkit-box-shadow: 3px 3px 9px rgba(32, 32, 32, .418);
    box-shadow: 3px 3px 9px rgba(32, 32, 32, .418);
    border-radius: 7px;
    max-width: 470px;
    position: absolute;
    top: -12px;
    left: 35px
}

div.br_imodal_tagreement:before {
    position: absolute;
    top: 11px;
    left: -10px;
    content: "";
    display: inline-block;
    border-left: 0 solid transparent;
    border-right: 11px solid #616161;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;
    width: 0;
    height: 0
}

div.br_imodal_tagreement p {
    font-weight: 700;
    font-size: 12px;
    color: #fff;
    line-height: 1.6em;
    text-align: start;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text
}

div.br_imodal_tagreement p::-moz-selection {
    background: #fff;
    color: #616161
}

div.br_imodal_tagreement p::selection {
    background: #fff;
    color: #616161
}

div.br_imodal_tagreement p.title {
    font-size: 14px;
    margin-bottom: 15px
}

.br_imodal_tbtn {
    gap: 40px;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    margin-top: 25px
}

.br_imodal_tbtn,
.br_imodal_tbtn span {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 32px
}

.br_imodal_tbtn span {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 250px;
    color: #fff;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    border: 2px solid #333
}

.br_imodal_tbtn span.res {
    color: #e1e1e1;
    background-color: #333
}

.br_imodal_tbtn span.rej {
    color: #333;
    background-color: #e1e1e1
}

.br_imodal_tbtn span:hover {
    opacity: .9
}

.br_imodal_tlist {
    margin: 0 35px;
    border-radius: 6px;
    background-color: #fff
}

.br_imodal_tlist li {
    height: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    overflow: hidden;
    border-top: 2px solid #e1e1e1
}

.br_imodal_tlist li:first-child {
    border-top: 0 none
}

.br_imodal_tlist li:first-child span {
    font-size: 16px;
    font-weight: 700;
    font-style: normal;
    line-height: 1.2;
    padding: 12px;
    color: #191919
}

.br_imodal_tlist li span {
    font-size: 13px;
    font-weight: 700;
    text-align: center;
    line-height: 1.08;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding: 10px;
    color: rgba(25, 25, 25, .85);
    border-left: 2px solid #e1e1e1;
    float: left
}

.br_imodal_tlist li span:first-child {
    border-left: 0 none
}

.br_imodal_tlist.three li span {
    width: 33.33%
}

.geetest_captcha_wrap .form-label {
    padding-bottom: .5em
}

.zoom-in-top-enter-active[data-v-69adcc39],
.zoom-in-top-leave-active[data-v-69adcc39] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-69adcc39],
.zoom-in-top-leave-active[data-v-69adcc39] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-69adcc39],
.zoom-in-bottom-leave-active[data-v-69adcc39] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-69adcc39],
.zoom-in-bottom-leave-active[data-v-69adcc39] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.geetest_captcha[data-v-69adcc39] {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden
}

.geetest_captcha.success[data-v-69adcc39] {
    background-color: #eefff5;
    color: #18a452
}

.geetest_captcha.success .success_area[data-v-69adcc39] {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0)
}

.geetest_captcha.success .error_area[data-v-69adcc39],
.geetest_captcha.success .waiting_area[data-v-69adcc39] {
    opacity: 0
}

.geetest_captcha.waiting[data-v-69adcc39] {
    cursor: pointer;
    background-color: #fff
}

.geetest_captcha.waiting .waiting_area[data-v-69adcc39] {
    cursor: pointer;
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0)
}

.geetest_captcha.waiting .error_area[data-v-69adcc39],
.geetest_captcha.waiting .success_area[data-v-69adcc39] {
    opacity: 0
}

.geetest_captcha.error[data-v-69adcc39] {
    background-color: #f8d7da;
    color: #721c24
}

.geetest_captcha.error .error_area[data-v-69adcc39] {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0)
}

.geetest_captcha.error .success_area[data-v-69adcc39],
.geetest_captcha.error .waiting_area[data-v-69adcc39] {
    opacity: 0
}

.loading[data-v-69adcc39] {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: 100%;
    height: 100%
}

.loading img[data-v-69adcc39] {
    width: 30px;
    height: 30px;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    -webkit-animation: rotation-69adcc39 2s linear infinite;
    animation: rotation-69adcc39 2s linear infinite
}

.geetest_status[data-v-69adcc39] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 100%
}

.geetest_status .error_area[data-v-69adcc39],
.geetest_status .success_area[data-v-69adcc39],
.geetest_status .waiting_area[data-v-69adcc39] {
    position: absolute;
    left: 0;
    width: 100%;
    -webkit-transition: all .75s ease;
    transition: all .75s ease;
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.geetest_status .success_area .icon[data-v-69adcc39] {
    background-image: url(data:image/png;base64,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)
}

.geetest_status .error_area .icon[data-v-69adcc39] {
    background-image: url(data:image/png;base64,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)
}

.geetest_status .icon[data-v-69adcc39] {
    display: block;
    width: 25px;
    height: 25px;
    background-size: cover;
    background-repeat: no-repeat;
    margin-right: 10px
}

.geetest_status .text[data-v-69adcc39] {
    -webkit-box-flex: .9;
    -ms-flex: .9;
    flex: .9;
    word-break: break-word;
    white-space: normal;
    margin-right: 8px
}

.geetest_status .retry[data-v-69adcc39] {
    cursor: pointer;
    color: #005aff;
    text-decoration: underline;
    word-break: break-word
}

.geetest_captcha:hover .geetest_sector[data-v-69adcc39] {
    opacity: 0 !important;
    -webkit-animation: unset;
    animation: unset
}

.geetest_captcha:hover .geetest_ring[data-v-69adcc39] {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-animation: geetest_wait_compute-69adcc39 .8s linear infinite both;
    animation: geetest_wait_compute-69adcc39 .8s linear infinite both
}

.geetest_captcha:hover .geetest_dot[data-v-69adcc39] {
    background: #3873ff
}

.icon-detail-wrap[data-v-69adcc39] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.icon-detail-wrap .icon-detail[data-v-69adcc39] {
    width: 20px;
    height: 20px;
    cursor: pointer;
    margin-right: 4px
}

.waiting_area .icon[data-v-69adcc39] {
    position: relative;
    width: 25px;
    height: 25px
}

.waiting_area .icon .geetest_ring[data-v-69adcc39] {
    -webkit-transform: scale(1);
    transform: scale(1);
    -webkit-transition: all .5s ease;
    transition: all .5s ease;
    background-color: #c6d5f8
}

.waiting_area .icon .geetest_ring[data-v-69adcc39],
.waiting_area .icon .geetest_sector[data-v-69adcc39] {
    position: absolute;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    -webkit-box-shadow: inset 0 0 0 1px #3873ff;
    box-shadow: inset 0 0 0 1px #3873ff
}

.waiting_area .icon .geetest_sector[data-v-69adcc39] {
    background-color: #80a6fc;
    background-image: linear-gradient(115deg, transparent 50%, #c6d5f8 0), linear-gradient(65deg, #c6d5f8 50%, transparent 0);
    opacity: 1;
    -webkit-transition: all ease;
    transition: all ease;
    -webkit-animation: rotation-69adcc39 3s linear infinite;
    animation: rotation-69adcc39 3s linear infinite
}

.waiting_area .icon .geetest_dot[data-v-69adcc39] {
    position: absolute;
    border-radius: 50%;
    width: 100%;
    height: 100%;
    background: #3873ff;
    -webkit-transform: scale(.5);
    transform: scale(.5);
    -webkit-transition: all .5s ease;
    transition: all .5s ease
}

@-webkit-keyframes geetest_wait_compute-69adcc39 {
    60% {
        -webkit-transform: scale(.75);
        transform: scale(.75)
    }
}

@keyframes geetest_wait_compute-69adcc39 {
    60% {
        -webkit-transform: scale(.75);
        transform: scale(.75)
    }
}

@-webkit-keyframes rotation-69adcc39 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes rotation-69adcc39 {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.svg-icon[data-v-4ae816df] {
    width: 100%;
    height: 100%;
    vertical-align: middle;
    overflow: hidden;
    fill: currentColor
}

.back_mask_gamepopup {
    background: rgba(0, 0, 0, .45);
    position: fixed;
    left: 0;
    z-index: 999;
    top: 0;
    height: 100%;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.back_mask_gamepopup .game_popup {
    position: relative;
    background-color: #fff;
    border-radius: 12px;
    text-align: center;
    padding: 15px 0
}

.back_mask_gamepopup .game_popup .title {
    font-family: Arial-BoldMT;
    font-size: 32px;
    font-weight: 700;
    color: #333;
    display: inline-block;
    margin-bottom: 15px
}

.back_mask_gamepopup .game_popup .close_gamepopup_button {
    width: 31px;
    height: 31px;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    color: #666;
    position: absolute;
    right: 14px;
    top: 14px;
    font-size: 20px;
    border: 2px solid #666;
    cursor: pointer;
    font-weight: 700
}

.back_mask_gamepopup .game_popup .close_gamepopup_button:hover {
    border-color: #eb262a;
    color: #eb262a
}

.back_mask_gamepopup .game_popup .game_popup_list {
    width: 970px;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding: 0 20px
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item {
    margin-right: 20px;
    width: 170px;
    text-align: center;
    position: relative;
    overflow: hidden;
    margin-bottom: 20px
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item:nth-child(5n) {
    margin-right: 0
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item:hover .br_gamePopup_mgulbutton {
    opacity: 1
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton {
    width: 170px;
    height: 170px;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .4);
    opacity: 0;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span {
    font-size: 13px;
    width: 80px;
    height: 32px;
    display: block;
    cursor: pointer;
    text-align: center;
    line-height: 32px;
    border-radius: 20px;
    color: #fff;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    margin-top: 10px
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span.free_demo {
    background: #ff8a00
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span.star_game {
    background: #ee2121
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item img {
    width: 170px;
    height: 170px;
    margin-bottom: 5px;
    display: inline-block
}

.back_mask_gamepopup .game_popup .game_popup_list .game_title {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    display: block;
    color: #666;
    text-overflow: ellipsis
}

.back_mask_gamepopup .VuePagination {
    text-align: center
}

.back_mask_gamepopup .pagination {
    display: inline-block;
    padding-left: 0;
    margin-top: 10px;
    border-radius: 4px
}

.back_mask_gamepopup .VuePagination__count {
    font-size: 15px;
    color: #333;
    letter-spacing: 5px
}

.back_mask_gamepopup .pagination>li {
    display: inline;
    -webkit-transition: all .1s linear;
    transition: all .1s linear
}

.back_mask_gamepopup .pagination>li:first-child>a,
.back_mask_gamepopup .pagination>li:first-child>span {
    margin-left: 0
}

.back_mask_gamepopup .pagination>li>a:focus,
.back_mask_gamepopup .pagination>li>a:hover {
    border: 1px solid #eb262a
}

.back_mask_gamepopup .pagination>.disabled>a,
.back_mask_gamepopup .pagination>.disabled>a:focus,
.back_mask_gamepopup .pagination>.disabled>a:hover,
.back_mask_gamepopup .pagination>.disabled>span,
.back_mask_gamepopup .pagination>.disabled>span:focus,
.back_mask_gamepopup .pagination>.disabled>span:hover {
    color: #dedede;
    cursor: not-allowed
}

.back_mask_gamepopup .pagination>li>a,
.back_mask_gamepopup .pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    margin-right: 5px;
    line-height: 1.42857143;
    color: #333;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid transparent;
    border-radius: 10px;
    -webkit-transition: all .2s ease-in;
    transition: all .2s ease-in
}

.back_mask_gamepopup .pagination>.active>a,
.back_mask_gamepopup .pagination>.active>a:focus,
.back_mask_gamepopup .pagination>.active>a:hover,
.back_mask_gamepopup .pagination>.active>span,
.back_mask_gamepopup .pagination>.active>span:focus,
.back_mask_gamepopup .pagination>.active>span:hover {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #eb262a;
    border-color: #eb262a
}

.back_mask_gamepopup .VuePagination__count,
.back_mask_gamepopup .VuePagination__pagination-item.VuePagination__pagination-item-next-chunk,
.back_mask_gamepopup .VuePagination__pagination-item.VuePagination__pagination-item-prev-chunk {
    display: none
}

.notification {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    width: 330px;
    padding: 14px 26px 14px 13px;
    border-radius: 8px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #ebeef5;
    position: fixed;
    z-index: 500;
    background-color: #fff;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    -webkit-transition: opacity .3s, left .3s, right .3s, top .4s, bottom .3s, -webkit-transform .3s;
    transition: opacity .3s, left .3s, right .3s, top .4s, bottom .3s, -webkit-transform .3s;
    transition: opacity .3s, transform .3s, left .3s, right .3s, top .4s, bottom .3s;
    transition: opacity .3s, transform .3s, left .3s, right .3s, top .4s, bottom .3s, -webkit-transform .3s;
    overflow: hidden
}

.notification.right {
    right: 16px
}

.notification.left {
    left: 16px
}

.notification_group {
    max-width: 100%;
    margin-left: 10px;
    margin-right: 8px
}

.notification_title {
    max-width: 100%;
    font-weight: 700;
    font-size: 16px;
    color: #303133;
    margin: 0
}

.notification_content {
    font-size: 14px;
    line-height: 21px;
    margin: 6px 0 0 0;
    color: #606266;
    text-align: justify
}

.notification_content>p {
    margin: 0;
    word-break: break-all
}

.notification_closeBtn {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 5px;
    right: 5px;
    cursor: pointer;
    color: #909399;
    font-size: 16px
}

.notification_closeBtn:before {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.notification_closeBtn:after,
.notification_closeBtn:before {
    position: absolute;
    content: "";
    width: 15px;
    height: 2px;
    background: #333;
    top: 10px;
    left: 2px
}

.notification_closeBtn:after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.notification_closeBtn:hover {
    color: #606266
}

.notification-fade-enter.right {
    right: 0;
    -webkit-transform: translateX(100%);
    transform: translateX(100%)
}

.notification-fade-enter.left {
    left: 0;
    -webkit-transform: translateX(-100%);
    transform: translateX(-100%)
}

.notification-fade-leave-active {
    opacity: 0
}

.eventTips_wrap {
    position: fixed;
    bottom: 60px;
    right: 16px;
    z-index: 1112
}

.eventTips_content {
    position: relative
}

@media (max-width:1400px) {
    .eventTips_wrap {
        right: 100px
    }
}

.eventTips_wrap img {
    display: block
}

.eventTips_wrap .reward_count_time {
    width: 130px;
    margin: auto;
    text-align: center;
    font-family: Arial-BoldMT;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 0;
    text-shadow: 0 .02rem .1rem rgba(61, 0, 0, .37);
    -webkit-text-fill-color: #a200ed;
    -webkit-text-stroke: .02rem #fffffe
}

.eventTips_wrap .get_reward_button {
    position: absolute;
    left: 0;
    bottom: 14px;
    width: 130px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 15px;
    font-weight: 600;
    color: #fff;
    cursor: pointer
}

.eventTips_wrap .get_reward_button.disabled {
    color: hsla(0, 0%, 100%, .3);
    cursor: not-allowed
}

.available_promotions {
    position: fixed;
    bottom: 50px;
    right: 0;
    z-index: 1112
}

.available_promotions .clost_btn {
    position: absolute;
    left: 10px;
    top: -5px;
    width: 24px;
    height: 24px;
    background: url(data:image/png;base64,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) no-repeat;
    cursor: pointer
}

.available_promotions .unread_promo {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: -webkit-gradient(linear, left top, left bottom, from(#ff5841), to(#fe0425));
    background: linear-gradient(180deg, #ff5841, #fe0425);
    -webkit-box-shadow: 0 0 3px #ff5841;
    box-shadow: 0 0 3px #ff5841;
    text-align: center;
    line-height: 20px;
    color: #fff;
    font-size: 12px;
    position: absolute;
    right: 25px;
    top: 23px
}

.unread_promo .circle {
    display: inline-block;
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: #fff;
    vertical-align: middle
}

.available_promotions .promo_btn {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 108px;
    height: 108px;
    cursor: pointer
}

.modal_mask {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .7);
    z-index: 3100
}

.modal_mask .modal_container {
    width: 235px;
    position: absolute;
    top: 48%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.modal_mask .modal_container .get_prize_title {
    margin-top: -40px
}

.modal_mask .modal_container p {
    text-align: center;
    letter-spacing: 0;
    color: #fff
}

.modal_mask .modal_container .get_prize_title p {
    font-size: 30px;
    font-weight: 700
}

.modal_mask .modal_container .get_prize_content p {
    font-size: 20px
}

.modal_mask .modal_container .get_prize_button {
    width: 164px;
    height: 46px;
    border-radius: 23px;
    margin: 10px auto 0;
    -webkit-box-shadow: 0 4px 19px 2px rgba(229, 183, 126, .19);
    box-shadow: 0 4px 19px 2px rgba(229, 183, 126, .19);
    background: url(../img/get_prize_button.c43808f7.png) no-repeat;
    background-color: #eccda6;
    background-size: cover;
    color: #e7a03d;
    font-size: 19px;
    line-height: 46px;
    text-align: center;
    cursor: pointer
}

.modal_container span.modal_close {
    display: inline-block;
    position: absolute;
    right: 0;
    top: 0;
    width: 24px;
    height: 24px;
    background-color: #fff;
    border-radius: 50%;
    cursor: pointer
}

.modal_container span.modal_close:before {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.modal_container span.modal_close:after,
.modal_container span.modal_close:before {
    position: absolute;
    content: "";
    width: 12px;
    height: 2px;
    background: #333;
    top: 11px;
    left: 6px
}

.modal_container span.modal_close:after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.inbox {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between
}

.inbox .inbox-info {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 4px
}

.inbox .btn-inbox {
    text-decoration: underline
}

.br_wzInfo_logout span[data-v-7e76ad28] {
    display: none
}

.br_advertise_footer .pagination {
    height: 50px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.br_advertise_footer .pagination a {
    display: block;
    padding: 6px 10px;
    border-radius: 8px;
    overflow: hidden;
    margin: 5px;
    font-weight: 600;
    color: #666;
    border: 1px solid #666;
    text-decoration: none;
    outline: none
}

.br_advertise_footer .VuePagination__count {
    display: none
}

.br_advertise_footer .VuePagination__pagination-item.page-item.active a {
    border: 1px solid #c50101;
    color: #fff;
    background-color: #c50101
}

.br_advertise_footer .VuePagination__pagination-item.page-item.VuePagination__pagination-item-prev-page.disabled {
    color: #666
}

.br_advertise_footer .VuePagination__pagination-item.page-item.VuePagination__pagination-item-next-chunk.disabled a,
.br_advertise_footer .VuePagination__pagination-item.page-item.VuePagination__pagination-item-next-page.disabled a,
.br_advertise_footer .VuePagination__pagination-item.page-item.VuePagination__pagination-item-prev-chunk.disabled a,
.br_advertise_footer .VuePagination__pagination-item.page-item.VuePagination__pagination-item-prev-page.disabled a {
    color: #cdcdcd;
    border: 1px solid #cdcdcd
}

.tcg-date-picker {
    width: 240px
}

.tcg-date-picker .tcg-calendar {
    width: 300px;
    padding: 0
}

.tcg-date-picker .date-range-input {
    width: 240px;
    position: relative
}

.tcg-date-picker .date-range-input input {
    width: 100%;
    padding-left: 30px;
    text-align: left;
    font-size: 14px;
    color: #ccc;
    border-radius: 4px
}

.tcg-date-picker .date-range-input .calendar-icon {
    position: absolute;
    left: 9px;
    top: 8px;
    width: 16px;
    height: 16px;
    color: #ccc
}

.tcg-date-picker .date-range-input .clear-calendar {
    display: none;
    position: absolute;
    right: 8px;
    top: 7px;
    width: 16px;
    height: 16px;
    color: #ccc;
    cursor: pointer
}

.tcg-date-picker .date-range-input .clear-calendar:hover {
    color: #999
}

.tcg-date-picker .date-range-input .clear-calendar.on {
    display: block
}

.br_integral_main {
    position: relative;
    color: #999
}

.mx-range-wrapper {
    width: 504px
}

.mx-panel-month .cell {
    font-size: 12px;
    min-height: 40px;
    line-height: 12px !important;
    word-wrap: break-word;
    word-break: break-all;
    padding: 5% 2px 0
}

.br_integral_nav {
    width: 300px;
    height: 180px;
    border-radius: 12px;
    position: absolute;
    top: -200px;
    left: 50%;
    margin-left: 280px;
    z-index: 111
}

.br_integral_nmain {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative
}

.br_integral_nref {
    display: block;
    width: 34px;
    height: 34px;
    background-color: rgba(0, 0, 0, .1);
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAABY0lEQVQ4y63UMWsVQRiF4bMxhblcFARtEgiChZ1iI1jYWtkpWiRgYaOFiCkCphD8B4JY6m+wEFEMFiKJlZV1ihQBQwRFCcbExyJzdV03Zos9zbDfnHm/M8PMJi3CGGbxHN/90Tc8xZWG/3D2Eq7iSw3yEUtYxqdG/RSu4TOm2mAPagvm20w4jrsFqNbkzF6wNziWfYTb/tbp+uTFUlzaD1T85/yrE0lSFcNmkoNJBlVVbXYATiS5kGQiiSTrVVUtjiYvlw4LXdJ1ib9YgMO+gLDRCyzJeBm/9hDsTpLzKa/hXg/AD9DXToNVqHqCjSf5keT9WE8Bb5bx0e+EOJlkMruXfTvJuy6XvKzdSnIgu48jwbDxm4JLHWEvi3+mXjzagN3vABriWfE/aU6OgKOUq5jDZAvoCG5hp3gft3Ub4Cce4izWa2nX8BqvsFKr7+D6/7YwjUO171m8wFbjON7iBgZtnF/PmOrGybYDygAAAABJRU5ErkJggg==");
    background-repeat: no-repeat;
    background-position: 50%;
    border-radius: 17px;
    cursor: pointer;
    position: absolute;
    right: 6px;
    top: 6px
}

.br_integral_ninfo {
    padding: 12px 0 0 12px
}

.br_integral_ninfo img {
    display: block;
    width: 46px;
    height: 46px;
    border-radius: 23px;
    float: left
}

.br_integral_ninfo span,
.br_integral_nnum,
.br_integral_ntitle {
    font-weight: 700
}

.br_integral_ninfo span {
    padding-left: 10px;
    line-height: 46px;
    font-size: 24px
}

.br_integral_ninfo .loadIcon {
    display: inline-block;
    width: 46px;
    height: 46px;
    border-radius: 23px;
    background-color: #fff;
    vertical-align: middle
}

.br_integral_ntitle {
    font-size: 14px;
    text-align: center;
    line-height: 1.9
}

.br_integral_nnum {
    font-size: 36px;
    text-align: center
}

.br_integral_ibtn {
    padding-top: 15px;
    text-align: center
}

.br_integral_ibtn span {
    margin: 0 12px;
    cursor: pointer
}

.br_integral_body {
    margin: auto;
    width: 1200px;
    border-radius: 5px
}

.br_integral_btap {
    height: 56px;
    border-radius: 2px
}

.br_integral_btap span {
    display: inline-block;
    margin: 0 8px;
    padding: 0 8px;
    height: 56px;
    font-size: 15px;
    line-height: 56px;
    cursor: pointer
}

.br_integral_bul {
    overflow: hidden;
    margin-top: 15px;
    padding: 0 15px 30px
}

.br_integral_bul:last-child {
    border-bottom: none
}

.point_title {
    font-size: 16px;
    height: 25px;
    line-height: 25px;
    margin: 15px 0 5px;
    padding-left: 15px;
    font-weight: 400
}

.br_integral_buli {
    width: 277px;
    height: 276px;
    margin: 0 20px 20px 0;
    border-radius: 5px;
    float: left;
    position: relative
}

.br_integral_buli:nth-child(4n) {
    margin-right: 0
}

.br_integral_bulimg {
    display: block;
    margin: 14px auto 0;
    width: 152px;
    height: 152px
}

.br_integral_bulititle {
    font-size: 14px;
    line-height: 3em;
    text-align: center;
    width: 100%;
    height: 42px;
    overflow: hidden
}

.br_integral_bulinfo {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 68px;
    vertical-align: middle;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 0 0 4px 4px
}

.br_integral_bulinfo div {
    padding: 0 20px;
    font-size: 14px;
    text-align: center;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.br_integral_bulinfo div:last-child {
    border-right: 0 none
}

.br_integral_bulibtn {
    display: none;
    border-radius: 5px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.br_integral_buli:hover .br_integral_bulibtn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.br_integral_bulibtn span {
    display: block;
    width: 100px;
    height: 36px;
    font-size: 14px;
    line-height: 36px;
    text-align: center;
    border-radius: 18px;
    cursor: pointer
}

.br_integral_bulibtn span:last-child {
    margin-top: 14px
}

.br_integral_more {
    margin: 20px 0;
    text-align: center
}

.br_integral_more span {
    display: inline-block;
    width: 180px;
    height: 50px;
    font-size: 14px;
    text-align: center;
    line-height: 50px;
    border-radius: 25px;
    cursor: pointer
}

.br_integral_modal {
    z-index: 9999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0
}

.br_integral_mdom {
    width: 1110px;
    height: 618px;
    border-radius: 5px;
    overflow: hidden;
    position: absolute;
    left: 50%;
    margin-left: -555px;
    top: 50%;
    margin-top: -309px
}

.br_integral_mbody {
    position: relative
}

.br_integral_mbclose {
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 14px;
    border-radius: 10px;
    cursor: pointer;
    position: absolute;
    top: 13px;
    right: 14px
}

.br_integral_mbtop {
    padding: 0 24px;
    height: 46px
}

.first-li i {
    display: inline-block;
    width: 5px;
    height: 14px;
    margin-right: 10px
}

li.first-li {
    font-weight: 600
}

li.last_child {
    position: absolute;
    bottom: 0
}

.br_integral_mbtlist {
    display: inline-block;
    margin-right: 20px;
    font-size: 14px;
    line-height: 44px;
    background-position: 0;
    background-repeat: no-repeat;
    position: relative
}

.br_integral_mbtlist.account {
    padding-left: 23px;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAUCAYAAAC9BQwsAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAABc0lEQVQ4y53Ru2tUQRTH8c99YGGhFlpYiPiMJkZ8BIzEB2ihIeAGsRGFgIUgQfwf0tr46mJhIVYqeEGIoo1m0UBiYTAhKbSwkhSCSGzunbXI3oUE4+7mBz+YmTPfc87MiaxQ5Wm4gcs4gBhzeIbbLy7FtfJeVC4uPM/X4Q1O+re+4Gx2Mf0BaXka4uL1fyDowjj2qLdiIFscrMXF6VpcaOLdA9nirQYYkuJaSAoteqjRakjyXVrX9nNjP5MSzNsAc9RK8DMOtgjOvj2zNaRQJPl9XG0RvLtsjqeq3x5guAmUvevbUVkGQt/H+Xu4uQr0pNq790q5iVZGj0/OHEYF++rxr3j5oadzvI0PXF2Nij3TUxtxHf3owIZ66Dfm8Qqjk91HFxrgkZmJCkaxpUmhXxj+1HnscXRornoC79vsdDANSf5wDU98lIak6FgDuCkOcTES4kKbvhPB/u9jXZZm14ud2Iz19ex/sGBpnhPIZredn/4LTTihzj66lbQAAAAASUVORK5CYII=")
}

.br_integral_mbtlist.integral {
    padding-left: 29px;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAQCAYAAAAWGF8bAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAACLUlEQVQ4y6WSXUiTURjHf6+eY1GWBDHCkFFDKKFWNhWNVhRmaQ0yGxJMA4kugt0EFd3VTZDddOGVVNBCVhdTpgg1+oQafWDupguXgVaDDfQmyeh9x7rwvHJ82yLoDwfO83+e5/98nGOgIepZKAeSwDZFrQMuK04Au4GbwHflTwNN3dOVlq0hdMGy8vwzoEGjHgMDmsB64CDQrux64AWwd1nDvsTq5u8Jkd8nRB513ganqtqEyCc07mlwqqpDiPwrjWuJ1c0P2ToGQNybu65Gs5ENpFyb4t7cAyDISgwHUq7OuDf3Fdis8f2BlOuiMe7LhIFbmuMHsAfodRTR0Q8MAh+AtRp/QUhpFoDjwE/V8URr0j2XaJ5JA4eBgkPMAGpak+50onmmBvCpmNXAVuP5/k8ngBvAN+WIqsqDwFyJDjcC54DtQEgVqQauiIqKXyPAaaBLBR9QI1cCO0sIzqqJbmvcCBAzbOvdsdRd4IwyLwETQKKEYBtLf9XefaRhzNuz/Mo2Up3vHwKnWPp3O4AngMch9gXwq4IbgJg35jupL3gFPna/HgM6gD5gERhyhITUru8Dj+qiLUecL/YH0qGXb4BGwA1Mqk4AFtRDzAKTtRF/vTO3rJhgbcTfJKRpCmn2CmleFdJEnWtCmj3q3lgst6gggJCWR0hrl5BWRkirIKSFkNZnxXm23DlkFcsz+Asy58c9wBpgGFgFHAUWqwfap/kfZMOjfdnw6Nl/if0NLdSkZ+1wS3sAAAAASUVORK5CYII=")
}

.br_integral_mbtlist.balance {
    padding-left: 29px;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAASCAYAAABb0P4QAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAABQklEQVQ4y63RwUrVQRTH8c+ZmVtYBEFJLnQTboogX0CiVZGgVAsjLr6BD9C2ZYvoGS4EUkJEELSoHqBtbVoEoiJkIkWtSqdFI1wiFLr/A4eB+f3Od36cCUdUHXiEeVS8jCXLh/njH4DchsfwGAt/WV6gj+/NJ5b+nBB1oIcnOIVvSM3Ywxw+4z0yLmIcr/Bz6JFj+IG7UQfeNMB9TGCvJQ9sNO0kfrVUgSkcHwJu4R7GCiYxg6e43AZqSzS8kr12f6DXIe0DbuJdEZ7hFm4c9UGH1CQW8byI9ACzI8AO6isepujv70gxK4UR+0r0979EXemdxm4HCeFMkdNiRzC4U+S41iHwepHSTIfAS0XO5zoEThQ55Q6BUaRYx3RHwM0kpxU56ahXo74+ewKfMOout3E+oL4dn8IqLvwn7CNux9Xttd+iNkVy4udsyAAAAABJRU5ErkJggg==")
}

.br_integral_mbtlist.refresh {
    padding-left: 18px;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAABcUlEQVQ4y6XTz4vNYRTH8dd3ugu/ipUiFpIisaAslGJKUoxZTWJh5Sk//gEr/gMLNeqTmg1KsqLZYaNZCGliY6fmbljMAuVXN5vny7fbzdTcszmd03ne53zO8zyNEVZKOYNLONFJDzCP20nma90OLCX51QwBduER9tXUS7xHg/04UPNPcBNPMZPkYa8DOYZnNbyMuSTfhxptwCnMVQ9roFcLtlTIF+xJ0h8lOcnXUson/GgBmPgLwvPqdyb57P92CH28w88qX1NKmaxarySZtUqbwI069qohLegIXo8D6e5oabWAUsppTPdwH4/HGOYszvWSnB9T1dZ2R+PaUSyOBSqltGquNzWxGbewHhvxIsm1FSDr8A39JNvaW9uLmU7d3RUgm7BYw8nujgadut9YLqWsHQFoSikXsIztmErygX/vqP3lF3ESD+rBhdp5gN1td3zEdJK3bZMW9AoHk7zBnVLKFK7iOA53hlrAbJJ7w9P+AfAScOoNCUBDAAAAAElFTkSuQmCC");
    cursor: pointer
}

.br_integral_mcbody {
    padding-top: 25px
}

.br_integral_mcbleft {
    width: 690px;
    height: 516px;
    float: left;
    position: relative
}

.br_integral_mcblbtn .swiper-wrapper,
.br_integral_mcblimg .swiper-wrapper {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform;
    -webkit-box-sizing: content-box;
    box-sizing: content-box
}

.br_integral_mcblimg .swiper-wrapper {
    height: 516px
}

.br_integral_mcblbtn .swiper-wrapper {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 324px
}

.br_integral_mcblbtn .swiper-slide,
.br_integral_mcblimg .swiper-slide {
    height: 100%;
    text-align: center;
    font-size: 38px;
    font-weight: 700;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform, -webkit-transform
}

.br_integral_mcblbtn .swiper-slide {
    height: 72px
}

.br_integral_img,
.br_integral_img img {
    width: 100%;
    height: 100%
}

.br_integral_mcblbtn .br_integral_img {
    height: 72px
}

.br_integral_mcblimg {
    width: 564px;
    float: right;
    overflow: hidden
}

.br_integral_mcblbtn {
    margin-left: 27px;
    width: 72px;
    height: 516px;
    float: left;
    position: relative
}

.br_integral_mcblbtn .swiper-container {
    margin-top: 96px;
    height: 324px;
    overflow: hidden;
    position: static
}

.br_integral_mcblnext,
.br_integral_mcblprev {
    width: 32px;
    font-size: 32px;
    text-align: center;
    position: absolute;
    left: 50%;
    margin-left: -16px;
    cursor: pointer
}

.br_integral_mcblprev {
    top: 56px;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.br_integral_mcblnext {
    top: 455px
}

.br_integral_mcbleft .swiper-button-next.swiper-button-white,
.br_integral_mcbleft .swiper-button-prev.swiper-button-white {
    background: none
}

.br_integral_mcbright {
    margin-right: 30px;
    width: 361px;
    float: right
}

.br_integral_mcbright ul li {
    list-style: disc inside
}

.br_integral_mcbright ol li {
    list-style: decimal inside
}

.br_integral_rul {
    height: 460px;
    overflow: hidden;
    overflow-y: auto
}

.br_integral_rul li {
    list-style: none !important
}

.br_integral_ruli {
    padding-bottom: 20px;
    overflow: hidden
}

.br_integral_ruline:last-child {
    border-bottom: none
}

.br_integral_ruptop {
    padding-top: 20px
}

.br_integral_rullh34 {
    line-height: 34px
}

.br_imcbruli_name,
.br_imcbruli_title {
    display: block;
    float: left
}

.br_imcbruli_title {
    min-width: 85px;
    white-space: nowrap
}

.br_imcbruli_name {
    width: auto !important
}

.br_imcbruli_select {
    float: left
}

.br_imcbruli_slist {
    padding-bottom: 15px;
    cursor: pointer;
    overflow: hidden
}

.br_imcbruli_slist:last-child {
    padding-bottom: 0
}

.br_imcbruli_option {
    display: block;
    margin-right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 100%;
    float: left
}

.br_imcbruli_option.active i {
    display: inline-block;
    margin: 3px;
    width: 10px;
    height: 10px;
    border-radius: 100%
}

.br_imcbruli_add,
.br_imcbruli_sub {
    width: 34px;
    height: 34px;
    font-size: 32px;
    text-align: center;
    line-height: 34px;
    cursor: pointer;
    float: left;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.br_imcbruli_sub {
    border-right: 0 none;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px
}

.br_imcbruli_add {
    border-left: 0 none;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px
}

.br_imcbruli_num {
    width: 60px;
    height: 34px;
    padding: 6px 12px;
    text-align: center;
    float: left
}

.br_imcbruli_remain {
    display: block;
    line-height: 24px;
    float: left;
    width: 45%;
    margin-right: 10%;
    margin-top: 10px;
    word-wrap: break-word;
    padding-right: 5px
}

.br_imcbruli_remain:last-child {
    margin-right: 0;
    border-right: none
}

.br_imcbruli_wallet {
    float: left
}

.br_imcbruli_pay {
    padding-right: 34px;
    float: right
}

.br_imcbruli_input,
.br_imcbruli_textarea {
    width: 300px !important;
    padding: 6px 12px;
    border: 0 none;
    border-radius: 6px
}

.br_imcbruli_input {
    height: 34px
}

.br_imcbruli_textarea {
    height: 68px;
    outline: none;
    resize: none
}

.br_integral_mcbsubmit {
    padding-top: 20px;
    height: 34px;
    font-size: 14px
}

.br_integral_mcbsubmit span {
    cursor: pointer
}

.br_idetail_cbtn,
.br_integral_mcbconfirm {
    display: block;
    width: 98px;
    line-height: 34px;
    text-align: center;
    border-radius: 17px;
    cursor: pointer
}

.br_integral_mcbconfirm {
    float: left
}

.br_integral_mcbdetail {
    display: block;
    line-height: 34px;
    position: relative;
    float: right
}

.br_integral_mcbdetail span {
    float: left;
    margin: 9px 6px 0 0;
    width: 16px;
    height: 16px;
    line-height: 16px;
    border-radius: 100%;
    font-weight: 700;
    text-align: center
}

.br_idetail_title {
    padding-bottom: 20px;
    font-size: 24px
}

.br_idetail_condition {
    padding: 20px 0;
    position: relative
}

.br_idetail_ctitle {
    font-size: 14px;
    padding-bottom: 16px
}

.br_idetail_ctitle:last-child {
    padding-bottom: 0
}

.br_idetail_cbtn {
    position: absolute;
    right: 0;
    bottom: 20px
}

.br_idetail_chtml {
    padding-top: 20px;
    height: 348px;
    overflow: hidden;
    overflow-y: auto
}

.br_integral_mbtnlist {
    display: inline-block;
    margin: 0 19px;
    padding: 0 10px;
    font-size: 14px;
    line-height: 43px;
    cursor: pointer
}

.br_insearch_condition {
    height: 76px;
    padding: 21px 24px
}

.br_insearch_cdate {
    float: left
}

.br_insearch_cstatus {
    margin-right: 10px;
    height: 34px;
    line-height: 34px;
    font-size: 14px;
    float: left;
    position: relative
}

.br_insearch_cstitle {
    padding: 0 10px 0 14px
}

.br_insearch_csvalue {
    display: inline-block;
    padding: 0 15px;
    border-radius: 6px;
    height: 34px;
    cursor: pointer;
    position: relative
}

.br_insearch_csvalue span {
    display: inline-block;
    padding-right: 25px;
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAICAYAAADJEc7MAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAABmJLR0QAAAAAAAD5Q7t/AAAACXBIWXMAAAsSAAALEgHS3X78AAAAzUlEQVQY043MMStFcRgG8N+5ymAx+BpSOndgkJJMhluKTab/2SzuF2D1Cf7vzqAM16KUmNVZTCalbhksN1JMLEddR+m+4/M+v6dIVRoiIseRCS5VaQt7HQxwmKp0MAHaxCneO9jHCY5Tlap/0BrOcI3dqbquv8puOcAC+mW3fKzr+r6FlnGJO/Qix0cx9pzGBdaxEznOm3wRN3jARuR4haK1PNMsL6GHJ9ziGauRY/TT/QUbPIsrzGOEN6xEjpfx3h/Y4LkGf2I7cgzbnW/dMkNdauegGgAAAABJRU5ErkJggg==");
    background-repeat: no-repeat;
    background-position: 100%
}

.br_insearch_csul {
    display: none;
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    top: 33px
}

.br_insearch_csvalue:hover .br_insearch_csul {
    display: block
}

.br_insearch_csul li {
    padding: 0 10px
}

.br_indetail_ul {
    height: 434px
}

.br_indetail_uli {
    padding: 0 24px
}

.br_indetail_uli,
.br_indetail_uli span {
    height: 31px;
    line-height: 31px;
    font-size: 12px;
    overflow: hidden
}

.br_indetail_uli span {
    display: block;
    margin: 0 20px;
    text-align: right;
    text-overflow: ellipsis;
    white-space: nowrap;
    float: left
}

.redeemlist_uli span {
    margin: 0 6px !important
}

.br_indetail_uli span:first-child {
    margin-left: 0
}

.br_indetail_uli span:last-child {
    margin-right: 0
}

.br_ulitext_right {
    text-align: center
}

.width_30 {
    width: 30px
}

.width_65 {
    width: 85px
}

.width_105 {
    width: 105px
}

.width_125 {
    width: 125px
}

.width_210 {
    width: 210px
}

.br_integral_mcleft {
    margin-left: 10px
}

.br_indetail_ulinodata {
    text-align: center;
    height: 100px;
    line-height: 100px
}

.br_integral_mgpage {
    text-align: center
}

.br_indetail_uldtotal {
    padding-left: 232px
}

.br_integral_mgpage .pagination {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px
}

.br_integral_mgpage .pagination>li {
    display: inline;
    -webkit-transition: all .1s linear;
    transition: all .1s linear
}

.br_integral_mgpage .pagination>li a {
    position: relative;
    padding: 6px 12px;
    margin-right: 5px;
    line-height: 1.4em;
    text-decoration: none;
    border-radius: 10px;
    -webkit-transition: all .2s ease-in;
    transition: all .2s ease-in
}

.br_integral_mgpage .pagination>li.active a {
    cursor: default
}

.br_integral_mgpage .pagination>li.disabled a {
    cursor: no-drop
}

.br_integral_mgpage .VuePagination__count {
    display: none
}

.br_integral_drule {
    padding: 15px 26px;
    height: 570px;
    overflow: hidden;
    overflow-y: auto;
    position: relative
}

.br_integral_drule .point-tips {
    width: 686px;
    font-size: 15px
}

.br_integral_drule .point-tips .tip-item:not(:last-child) {
    margin-bottom: 20px
}

.br_integral_drtable {
    width: 686px;
    text-align: center;
    border-radius: 5px;
    overflow: hidden
}

.br_integral_drtable tr td:last-child {
    border-right: none
}

.br_integral_drtable tr {
    font-size: 12px;
    height: 32px
}

.br_integral_drtable tr th {
    font-size: 14px;
    height: 42px;
    line-height: 42px;
    border: none;
    border-right: 1px solid #444
}

.br_integral_drtable tr th:last-child {
    border-right: none
}

.br_integral_drtable .points_table_title {
    height: 42px;
    line-height: 42px;
    font-weight: 700;
    font-size: 14px;
    color: #cbcccd;
    background: none
}

.br_integral_drchange {
    overflow: hidden;
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 370px;
    text-align: center
}

.br_integral_drcli {
    float: left;
    overflow: hidden;
    padding: 10px 0;
    width: 100%;
    text-align: left;
    padding-left: 20px
}

.br_integral_drcli .title {
    display: inline-block;
    line-height: 32px;
    text-align: right
}

.first-li .title:first-child {
    font-weight: 600
}

.br_integral_drcli .input {
    padding: 0 5px;
    border-radius: 5px;
    height: 32px
}

.br_integral_drsubmit {
    display: inline-block;
    margin-left: 20px;
    padding: 0 35px;
    height: 32px;
    line-height: 32px;
    border-radius: 16px;
    cursor: pointer
}

.br_integral_lastp {
    margin-bottom: 20px
}

.br_integral_drule>p {
    font-size: 15px;
    line-height: 25px;
    width: 686px;
    overflow: hidden
}

.br_integral_all .swiper-pagination {
    display: none
}

.mx-calendar-icon {
    cursor: pointer
}

.br_index_modal[data-v-f30df6e8] {
    display: none;
    z-index: 1111;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.br_index_modal.active[data-v-f30df6e8] {
    display: block
}

.br_imodal_main[data-v-f30df6e8] {
    position: relative;
    width: 100%;
    height: 100%
}

.br_imodal_mbg[data-v-f30df6e8] {
    background-color: rgba(0, 0, 0, .5);
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.br_imodal_content[data-v-f30df6e8] {
    width: 1200px;
    height: 586px;
    z-index: 9;
    background-color: #fff;
    border-radius: 5px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -600px;
    margin-top: -293px
}

.br_imodal_cmain[data-v-f30df6e8] {
    width: 100%;
    height: 100%;
    position: relative
}

.br_imodal_cmtitle[data-v-f30df6e8] {
    height: 80px;
    line-height: 80px;
    padding-left: 30px;
    font-size: 25px;
    border-bottom: 1px solid #ccc
}

.br_imodal_cmclose[data-v-f30df6e8] {
    width: 36px;
    height: 36px;
    color: #999;
    font-size: 36px;
    cursor: pointer;
    position: absolute;
    right: 11px;
    top: 22px
}

.br_imodal_cmclose[data-v-f30df6e8]:hover {
    color: #f5a100
}

.br_imodal_cmbody[data-v-f30df6e8] {
    height: 506px;
    overflow: hidden
}

.br_imodal_pbody[data-v-f30df6e8] {
    height: 100%;
    overflow: auto
}

.br_imodal_typereq[data-v-f30df6e8] {
    border-bottom: 1px solid #ccc;
    overflow: hidden
}

.br_imodal_trmain[data-v-f30df6e8] {
    border-bottom: 1px solid #ccc;
    padding: 0 150px;
    position: relative
}

.br_imodal_tname[data-v-f30df6e8] {
    width: 150px;
    text-align: center;
    line-height: 30px;
    font-size: 18px;
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -15px
}

.br_imodal_tbtn[data-v-f30df6e8] {
    width: 150px;
    height: 32px;
    text-align: center;
    position: absolute;
    right: 0;
    top: 50%;
    margin-top: -16px
}

.br_imodal_tbtn span[data-v-f30df6e8] {
    display: inline-block;
    margin: auto 5px;
    width: 52px;
    height: 32px;
    color: #fff;
    line-height: 32px;
    text-align: center;
    border-radius: 5px;
    cursor: pointer
}

.br_imodal_tbtn span.res[data-v-f30df6e8] {
    background-color: #9b1021
}

.br_imodal_tbtn span.rej[data-v-f30df6e8] {
    background-color: #7cd1f9
}

.br_imodal_tbtn span[data-v-f30df6e8]:hover {
    opacity: .9
}

.br_imodal_tlist[data-v-f30df6e8] {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc
}

.br_imodal_tlist li[data-v-f30df6e8] {
    display: block;
    overflow: hidden;
    border-top: 1px solid #ccc
}

.br_imodal_tlist li[data-v-f30df6e8]:first-child {
    border-top: 0 none
}

.br_imodal_tlist li span[data-v-f30df6e8] {
    display: block;
    text-align: center;
    line-height: 35px;
    border-left: 1px solid #ccc;
    float: left
}

.br_imodal_tlist li span[data-v-f30df6e8]:first-child {
    border-left: 0 none
}

.br_imodal_tlist.four li span[data-v-f30df6e8] {
    width: 25%
}

.br_imodal_tlist.three li span[data-v-f30df6e8] {
    width: 33.33%
}

.br_imodal_tagreement[data-v-f30df6e8] {
    padding: 10px 30px
}

.br_imodal_tagreement p[data-v-f30df6e8] {
    font-size: 13px;
    color: #999;
    margin-left: 26px;
    line-height: 1.5em
}

.br_imodal_tagreement p.title[data-v-f30df6e8] {
    font-size: 14px;
    margin-left: 0
}

@-webkit-keyframes fadeInRight-909959a6 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInRight-909959a6 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-20px, 0, 0);
        transform: translate3d(-20px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes bounceOutRight-909959a6 {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0)
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0)
    }
}

@keyframes bounceOutRight-909959a6 {
    20% {
        opacity: 1;
        -webkit-transform: translate3d(20px, 0, 0);
        transform: translate3d(20px, 0, 0)
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-200px, 0, 0);
        transform: translate3d(-200px, 0, 0)
    }
}

.fadeInRight[data-v-909959a6] {
    -webkit-animation-name: fadeInRight-909959a6;
    animation-name: fadeInRight-909959a6
}

.bounceOutRight[data-v-909959a6] {
    -webkit-animation-name: bounceOutRight-909959a6;
    animation-name: bounceOutRight-909959a6
}

.animated[data-v-909959a6] {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.qr_img_wrap img {
    display: block
}

.payment-control {
    width: 100%
}

.payment-control .file-control {
    width: 280px;
    margin: 25px auto 0
}

.payment-control .submit-file {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    min-width: 90px;
    height: 25px;
    line-height: 25px;
    font-size: 14px;
    font-weight: 600;
    text-align: center;
    color: #fff;
    border-radius: 30px;
    background-color: #797979
}

.payment-control .upload-tip {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 5px;
    font-size: 12px;
    color: #df2f2f
}

.payment-control .upload-tip .icon-warning {
    width: 20px;
    height: 17px;
    margin-right: 5px
}

.payment-control .file-input {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    width: 100%;
    position: relative;
    cursor: pointer;
    border: 1px solid #cdcdcd;
    border-radius: 30px
}

.payment-control .file-input .btn-upload {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    width: 100%;
    height: 25px;
    line-height: 25px;
    padding: 0 10px;
    border-radius: 30px;
    border: none;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 12px
}

.payment-control .file-input .btn-upload .upload-placeholder {
    color: #cdcdcd
}

.payment-control .file-input .btn-upload .upload-filename {
    color: #666
}

.payment-control .file-input .upload-img {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    width: 100%;
    height: 100%;
    opacity: 0
}

.show-modal {
    position: fixed
}

.transaction-modal .modal-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10001;
    background: rgba(0, 0, 0, .6)
}

.transaction-modal .modal-main {
    position: fixed;
    left: 50%;
    top: 45%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    z-index: 10002;
    width: 400px;
    padding: 25px 30px;
    vertical-align: middle;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ebeef5;
    font-size: 18px;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    text-align: left;
    overflow: hidden;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform-origin: center;
    transform-origin: center
}

.transaction-modal .modal-main .modal-title {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
    color: #666;
    text-align: center
}

.transaction-modal .modal-main .modal-content {
    width: 100%;
    max-height: 400px;
    overflow: auto
}

.transaction-modal .modal-main .transaction-tip {
    margin-bottom: 10px
}

.transaction-modal .modal-main .payment-info,
.transaction-modal .modal-main .transaction-tip {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    text-align: center
}

.transaction-modal .confirm-payment {
    width: 280px;
    margin: 25px auto 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    gap: 20px
}

.transaction-modal .btn-cancel,
.transaction-modal .btn-confirm {
    display: inline-block;
    min-width: 130px;
    height: 25px;
    line-height: 25px;
    padding: 0 5px;
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    text-align: center;
    cursor: pointer
}

.transaction-modal .btn-confirm {
    background-color: #ec2529
}

.transaction-modal .btn-cancel {
    background-color: #797979
}

.transaction-modal .opacity-enter-active {
    -webkit-animation: opacityIn .3s;
    animation: opacityIn .3s
}

.transaction-modal .opacity-leave-active {
    -webkit-animation: opacityOut .3s;
    animation: opacityOut .3s
}

@-webkit-keyframes opacityIn {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes opacityIn {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@-webkit-keyframes opacityOut {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

@keyframes opacityOut {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

.sm-wrapper[data-v-945f6a22] {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 1111;
    background: rgba(0, 0, 0, .8)
}

.sm-content[data-v-945f6a22] {
    width: 400px;
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.sm-content img[data-v-945f6a22] {
    display: block
}

.sm-content .next[data-v-945f6a22],
.sm-content .prev[data-v-945f6a22] {
    width: 40px;
    height: 66px;
    position: absolute;
    top: 40%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    cursor: pointer;
    border-radius: 3px
}

.sm-content .prev[data-v-945f6a22] {
    left: -80px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAkCAYAAACJ8xqgAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjFBQkE2N0MxRTE2ODExRTdBMjk4RjU0Q0RERDkxQzlBIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjFBQkE2N0MyRTE2ODExRTdBMjk4RjU0Q0RERDkxQzlBIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MUFCQTY3QkZFMTY4MTFFN0EyOThGNTRDREREOTFDOUEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MUFCQTY3QzBFMTY4MTFFN0EyOThGNTRDREREOTFDOUEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz7//LepAAAB4UlEQVR42qSWaytEQRjHd49NQimSfAMvaItCUZRLaCOXUBK5JEk+gM9ASpJbvJFyV8g1QiRbtpQvQbnVtpHLf+qZOp2eGWfOTv1e7Pz3/DqXeWaeQHTTF8/IBjOgAtyCPn8cwixwDnJsc1eWR1kmOHHIxCjyIkwHxyCXySKmwjRwBIJM9gR6LUPZAShgshdQDR7cClPALihmsjeSRcQPN8JkkpUy2TuoBWE58Z8wCWyDciaLghC4sU/qhIlArNIqjezSGVga2So9jnPEQCM44y7khAlgGTQw2SdopqXjcyOUshaFrA3s61665ZAt0EXO8Q066AP53Aj9tGt0KWSdYN3NgrVINiXKhsl/xJYEVtyWkxCOgkEm+wUDYMmk2IVwSCEbBvOmW5FFFzuHnz6Sz4twXJFN0CMbC8fApOIup0GPl0ceAXMK6SwtG9dC+xddVJSimG81EUppP5Weqr6bTISyKkS1rDH/DdACD5kI7XW7pdjWNkCNiVCML9AO9jTSShOh3K7EOztUnDM7oMxEaJeeag6vEhOhPD/qwQWTpdJrKTQR2g+la003kW8iFOMD1IE7jTTPtLeRXcI9k2WIMvXSfb3KPobJgl77w2fqWh8d82GvQtm+VdI6jVFL0v0nwAAzUmR5vqTfJwAAAABJRU5ErkJggg==) no-repeat 50%
}

.sm-content .next[data-v-945f6a22] {
    right: -80px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAkCAYAAACJ8xqgAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjJCMDgzQTVFRTE2ODExRTc5NEFBQzg3NjlCMzlFODQ5IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjJCMDgzQTVGRTE2ODExRTc5NEFBQzg3NjlCMzlFODQ5Ij4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MkIwODNBNUNFMTY4MTFFNzk0QUFDODc2OUIzOUU4NDkiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MkIwODNBNURFMTY4MTFFNzk0QUFDODc2OUIzOUU4NDkiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz5RiLdWAAAB80lEQVR42qSWaSuEURTHn3lM0lAk8to7SSmEJJGxb1myxGSPlOJryAtJkYhESkwUso0kSVGjyFegSJaaRJb/raOmp3PHc69Tv6bOuf26c5fzXGfAayQahjELsoAP9IFbQzNMsAAKgAtUgUOQ8B9hpiWXBA5AvK7Qz+RTwD6I1RH2gHumlgr2QLSq8BoUg0emng52VKQm/V6CEvDMjMkGmyBSRSjiApSCF2ZcLkldKkIRZ6ACBJix+WAdRKgIRZyEkBYBLwhXEYo4ArXgjamVgRWZ1Awxe3Fk6sE7U6sBSyBMRShiGzRJpA1g0So1bZwEsRGt4JOpNVNjMVWEIlaBRyJtB9PAoSIUsUzX9IupdYMJIXUq3v154AyeUVAMgDtTo0PNgEHwzdSGdYQG7ayDyX/qCMUnYkxSG1EVdoFJyezGwaiK0CPZDIPyQ2Jd7QobwRx31Sjf/7tJdoR1sntLV683eMf/ElbSgebOq+g4HdbbE0oouveapE2JntjGXUWZ0B1CtgVawIfdfpgHNiTfj11a03e7HTuHZsDJfH/JrMJMaqhRzLhjUC35zrDCNPo73Af9lHY7YOfACmEyyWKY+jkoB68qL4cpEMfU/PREeVZ922Qw+SuSPem8vs4tuRtQCB5034edtPBvtJZuyfPOVvwIMAAmCGOmemt8wAAAAABJRU5ErkJggg==) no-repeat 50%
}

.sm-content .next[data-v-945f6a22]:hover,
.sm-content .prev[data-v-945f6a22]:hover {
    background-color: hsla(0, 0%, 100%, .5)
}

.sm-header[data-v-945f6a22] {
    font-size: 38px;
    font-weight: 900;
    color: #000;
    text-align: center;
    background-color: #fff
}

.sm-header img[data-v-945f6a22] {
    width: 300px
}

.sm-step[data-v-945f6a22] {
    position: absolute;
    width: 2400px;
    -webkit-transition: all .3s;
    transition: all .3s
}

.sm-step li[data-v-945f6a22] {
    float: left
}

.sm-close[data-v-945f6a22] {
    width: 30px;
    height: 30px;
    line-height: 30px;
    position: absolute;
    right: 0;
    top: 0;
    font-size: 30px;
    color: #000;
    text-align: center;
    cursor: pointer
}

.big-img[data-v-945f6a22] {
    position: relative;
    overflow: hidden;
    height: 710px
}

.big-img img[data-v-945f6a22] {
    width: 400px
}

.download-wrapper[data-v-00bca01d] {
    width: 100%
}

.chat_window.left-top {
    left: 0;
    top: 0
}

.chat_window.left-bottom {
    left: 0;
    bottom: 0
}

.chat_window.right-top {
    right: 0;
    top: 0
}

.chat_window.right-bottom {
    right: 0;
    bottom: 0
}

.chatRoom_wrap {
    width: 56px;
    height: 56px;
    z-index: 9995;
    position: fixed;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.open_chat_btn {
    width: 56px;
    height: 56px;
    background: #08c15e url(data:image/png;base64,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) no-repeat 50%;
    border-radius: 56px;
    cursor: pointer;
    -webkit-transition: .5s ease-in;
    transition: .5s ease-in;
    -webkit-transform-origin: center;
    transform-origin: center;
    position: relative
}

.open_chat_btn.active {
    -webkit-transform: scale(0);
    transform: scale(0)
}

.unread_message {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #ff5153;
    text-align: center;
    line-height: 20px;
    color: #fff;
    font-size: 12px;
    position: absolute;
    right: -2px;
    top: -2px
}

.unread_message .circle {
    display: inline-block;
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background-color: #fff;
    vertical-align: middle
}

.chat_window {
    width: 330px;
    height: 462px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    position: absolute;
    border: 1px solid #d3d3d3;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    -webkit-box-shadow: 0 0 3px 1px rgba(0, 0, 0, .375);
    box-shadow: 0 0 3px 1px rgba(0, 0, 0, .375);
    -webkit-transition: .3s ease-in;
    transition: .3s ease-in;
    overflow: hidden;
    -webkit-transform: scale(0);
    transform: scale(0);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.chat_window.active {
    -webkit-transform: scale(1);
    transform: scale(1)
}

.chat_window .btn-resize {
    width: 15px;
    height: 15px;
    position: absolute;
    z-index: 10
}

.chat_window .btn-resize svg {
    display: block;
    width: 100%;
    height: 100%
}

.btn-resize.left-bottom {
    right: 0;
    top: 0;
    cursor: sw-resize;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    color: #fff
}

.btn-resize.left-top {
    bottom: 0;
    right: 0;
    cursor: se-resize;
    color: #333
}

.btn-resize.right-top {
    left: 0;
    bottom: 0;
    cursor: nesw-resize;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
    color: #333
}

.btn-resize.right-bottom {
    left: 0;
    top: 0;
    cursor: nw-resize;
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    color: #fff
}

.chat_iframe {
    position: relative;
    width: 100%;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.chat_iframe .iframe-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0
}

.chat_window_title {
    height: 40px;
    background-color: #3e3e3e;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 20px
}

.chat_window_title,
.chat_window_title .title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.chat_window_title .title {
    font-size: 16px;
    color: #fff
}

.window_control span {
    display: inline-block;
    position: relative;
    width: 16px;
    height: 16px;
    margin: 0 1px;
    border-radius: 4px;
    cursor: pointer
}

.window_control span.large {
    background-color: #61c23d
}

.window_control span.hidden {
    background-color: #f85a5d
}

.window_control span.small {
    background-color: #efb734
}

.window_control span.large:after {
    position: absolute;
    content: "";
    width: 8px;
    height: 6px;
    border: 1px solid #444;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.window_control span.small:before {
    left: 3px;
    top: 7px;
    z-index: 2;
    background-color: #efb734
}

.window_control span.small:after,
.window_control span.small:before {
    position: absolute;
    content: "";
    width: 6px;
    height: 4px;
    border: 1px solid #444
}

.window_control span.small:after {
    left: 5px;
    top: 4px
}

.window_control span.hidden:after {
    position: absolute;
    content: "";
    width: 8px;
    height: 2px;
    background-color: #444;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.mini-game-container {
    position: fixed;
    top: 75%;
    left: 2%;
    width: auto;
    height: auto
}

@-webkit-keyframes grad-14d5dfc4 {
    0% {
        background-position: 0 50%
    }
    50% {
        background-position: 100% 50%
    }
    to {
        background-position: 0 50%
    }
}

@keyframes grad-14d5dfc4 {
    0% {
        background-position: 0 50%
    }
    50% {
        background-position: 100% 50%
    }
    to {
        background-position: 0 50%
    }
}

@-webkit-keyframes ballAnimated-14d5dfc4 {
    0% {
        right: -180px;
        top: 180px
    }
    to {
        right: 300px;
        top: -200px
    }
}

@keyframes ballAnimated-14d5dfc4 {
    0% {
        right: -180px;
        top: 180px
    }
    to {
        right: 300px;
        top: -200px
    }
}

@-webkit-keyframes adornAnimated-14d5dfc4 {
    0% {
        opacity: .2
    }
    50% {
        opacity: 1
    }
    to {
        opacity: .2
    }
}

@keyframes adornAnimated-14d5dfc4 {
    0% {
        opacity: .2
    }
    50% {
        opacity: 1
    }
    to {
        opacity: .2
    }
}

@-webkit-keyframes move_wave-14d5dfc4 {
    0% {
        -webkit-transform: translateX(0) translateZ(0) scaleY(1);
        transform: translateX(0) translateZ(0) scaleY(1)
    }
    50% {
        -webkit-transform: translateX(-25%) translateZ(0) scaleY(.55);
        transform: translateX(-25%) translateZ(0) scaleY(.55)
    }
    to {
        -webkit-transform: translateX(-50%) translateZ(0) scaleY(1);
        transform: translateX(-50%) translateZ(0) scaleY(1)
    }
}

@keyframes move_wave-14d5dfc4 {
    0% {
        -webkit-transform: translateX(0) translateZ(0) scaleY(1);
        transform: translateX(0) translateZ(0) scaleY(1)
    }
    50% {
        -webkit-transform: translateX(-25%) translateZ(0) scaleY(.55);
        transform: translateX(-25%) translateZ(0) scaleY(.55)
    }
    to {
        -webkit-transform: translateX(-50%) translateZ(0) scaleY(1);
        transform: translateX(-50%) translateZ(0) scaleY(1)
    }
}

.waveWrapper[data-v-14d5dfc4] {
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
    margin: auto;
    z-index: 2
}

.waveWrapperInner[data-v-14d5dfc4] {
    position: absolute;
    width: 100%;
    overflow: hidden;
    height: 100%;
    bottom: -1px;
    z-index: 2
}

.bgTop[data-v-14d5dfc4] {
    z-index: 15;
    opacity: .5
}

.bgMiddle[data-v-14d5dfc4] {
    z-index: 10;
    opacity: .75
}

.bgBottom[data-v-14d5dfc4] {
    z-index: 5
}

.wave[data-v-14d5dfc4] {
    position: absolute;
    left: 0;
    width: 200%;
    height: 100%;
    background-repeat: repeat-x;
    background-position: 0 bottom;
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom;
    opacity: .2;
    z-index: 2
}

.waveTop[data-v-14d5dfc4] {
    background-size: 50% 100px
}

.waveAnimation .waveTop[data-v-14d5dfc4] {
    background-image: url(../img/wave_top.05d52988.png);
    -webkit-animation: move_wave-14d5dfc4 5s linear infinite;
    animation: move_wave-14d5dfc4 5s linear infinite
}

.waveMiddle[data-v-14d5dfc4] {
    background-size: 50% 120px
}

.waveAnimation .waveMiddle[data-v-14d5dfc4] {
    background-image: url(../img/wave_mid.ba75e0c9.png);
    -webkit-animation: move_wave-14d5dfc4 10s linear infinite;
    animation: move_wave-14d5dfc4 10s linear infinite
}

.waveBottom[data-v-14d5dfc4] {
    background-size: 50% 100px
}

.waveAnimation .waveBottom[data-v-14d5dfc4] {
    background-image: url(../img/wave_bot.6cd18bc0.png);
    -webkit-animation: move_wave-14d5dfc4 15s linear infinite;
    animation: move_wave-14d5dfc4 15s linear infinite
}

.br_bg_adorn[data-v-14d5dfc4] {
    position: absolute;
    bottom: 30%;
    left: 0;
    width: 207px;
    height: 142px;
    background: url(../img/br_bg_adorn.c82dec17.png) no-repeat;
    -webkit-animation: adornAnimated-14d5dfc4 3.5s cubic-bezier(.785, .135, .15, .86) infinite;
    animation: adornAnimated-14d5dfc4 3.5s cubic-bezier(.785, .135, .15, .86) infinite
}

.br_bg_adorn.adorn_top[data-v-14d5dfc4] {
    left: 45%;
    top: -40px
}

.br_bg_ball[data-v-14d5dfc4] {
    position: absolute;
    width: 200px;
    height: 200px;
    background: #fff;
    opacity: .25;
    border-radius: 50%;
    right: -180px;
    top: 80px;
    -webkit-animation: ballAnimated-14d5dfc4 15s infinite;
    animation: ballAnimated-14d5dfc4 15s infinite
}

.game-banner .swiper-slide {
    width: 100%;
    height: 100%
}

.game-banner .default_img {
    display: block;
    width: 100%
}

.game-banner .swiper-slide img {
    display: block;
    width: 100%;
    height: 100%
}

.game-banner .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
    display: inline-block;
    border-radius: 100%;
    background: #dedede;
    opacity: .2
}

.game-banner .swiper-pagination-bullet-active {
    opacity: 1;
    background: #fff
}

.game-banner .banner-swiper-pagination,
.game-banner .swiper-container-horizontal>.swiper-pagination-bullets,
.game-banner .swiper-pagination-custom,
.game-banner .swiper-pagination-fraction {
    bottom: 10%;
    width: 500px;
    left: 50%;
    -webkit-transform: translate(-50%);
    transform: translate(-50%);
    border-radius: 5px
}

.game-banner .banner-swiper-pagination {
    position: absolute;
    z-index: 11;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.zoom-in-top-enter-active[data-v-4cbf6d7b],
.zoom-in-top-leave-active[data-v-4cbf6d7b] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-4cbf6d7b],
.zoom-in-top-leave-active[data-v-4cbf6d7b] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-4cbf6d7b],
.zoom-in-bottom-leave-active[data-v-4cbf6d7b] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-4cbf6d7b],
.zoom-in-bottom-leave-active[data-v-4cbf6d7b] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.apply-reward[data-v-4cbf6d7b] .v--modal {
    border-radius: 12px
}

.apply-reward .apply-reward-wrap[data-v-4cbf6d7b] {
    background-color: #e6f1fa;
    position: relative;
    border-radius: 12px
}

.apply-reward .apply-reward-wrap .cancel-btn[data-v-4cbf6d7b] {
    position: absolute;
    top: 16px;
    right: 15px;
    width: 37px;
    height: 37px;
    border-radius: 50%;
    background-color: #a8a8a8;
    opacity: .9;
    cursor: pointer
}

.apply-reward .apply-reward-wrap .cancel-btn[data-v-4cbf6d7b]:before {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.apply-reward .apply-reward-wrap .cancel-btn[data-v-4cbf6d7b]:after,
.apply-reward .apply-reward-wrap .cancel-btn[data-v-4cbf6d7b]:before {
    content: "";
    position: absolute;
    top: calc(50% - 1.6px);
    right: calc(50% - 10px);
    width: 20px;
    height: 3.2px;
    border-radius: 3px;
    background-color: #e6f1fa
}

.apply-reward .apply-reward-wrap .cancel-btn[data-v-4cbf6d7b]:after {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.apply-reward .apply-reward-wrap .apply-reward-title[data-v-4cbf6d7b] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-top: 24px;
    font-weight: 500;
    border-radius: 12px
}

.apply-reward .apply-reward-wrap .apply-reward-title span[data-v-4cbf6d7b] {
    display: block
}

.apply-reward .apply-reward-wrap .apply-reward-title span[data-v-4cbf6d7b]:first-of-type {
    width: 50px;
    height: 50px;
    background-image: url(data:image/png;base64,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);
    background-position: 50%;
    background-size: 100%;
    margin-right: 16px
}

.apply-reward .apply-reward-wrap .apply-reward-title span[data-v-4cbf6d7b]:last-child {
    font-size: 26px;
    color: #5f77b5;
    line-height: 50px;
    font-weight: 500
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group[data-v-4cbf6d7b] {
    width: 880px;
    margin: 0 auto 30px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .form-label[data-v-4cbf6d7b] {
    display: block;
    font-size: 20px;
    color: #666;
    margin: 30px 0 10px 0;
    font-weight: 500
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .remark-textarea[data-v-4cbf6d7b] {
    display: block;
    width: 100%;
    height: 157px;
    border-radius: 5px;
    border: none;
    padding-top: 22px;
    padding-left: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #939393;
    resize: none
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img[data-v-4cbf6d7b] {
    padding: 20px 10px 12px;
    border: 1px dashed #d4d4d4;
    border-radius: 5px;
    background-color: #fff;
    border: none
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list[data-v-4cbf6d7b] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 10px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list .up-section[data-v-4cbf6d7b] {
    position: relative;
    width: 33.3333333333%;
    height: 150px;
    border: 1px dashed #d4d4d4;
    border-radius: 5px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list .up-section[data-v-4cbf6d7b]:not(:last-child) {
    margin-right: 12px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list .up-section .up-img[data-v-4cbf6d7b] {
    display: block;
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50%
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list .up-section .close-upimg[data-v-4cbf6d7b] {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 6px;
    right: 8px;
    z-index: 10;
    border-radius: 50%;
    cursor: pointer;
    background: rgba(0, 0, 0, .5)
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list .up-section .close-upimg[data-v-4cbf6d7b]:before {
    position: absolute;
    left: 9px;
    top: 5px;
    content: "";
    width: 2px;
    height: 10px;
    background-color: #fff;
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .img-list .up-section .close-upimg[data-v-4cbf6d7b]:after {
    position: absolute;
    left: 9px;
    top: 5px;
    content: "";
    width: 2px;
    height: 10px;
    background-color: #fff;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-holder[data-v-4cbf6d7b] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-holder .file-img[data-v-4cbf6d7b] {
    width: 63.3px;
    height: 49.7px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-holder .file-img img[data-v-4cbf6d7b] {
    width: 100%
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-holder p[data-v-4cbf6d7b] {
    color: #939393;
    font-size: 16px;
    margin: 13.5px auto 13px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-input[data-v-4cbf6d7b] {
    position: relative;
    width: 116px;
    height: 34px;
    margin: 0 auto 10px
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-input .file_btn[data-v-4cbf6d7b] {
    position: absolute;
    width: 100%;
    text-align: center;
    white-space: nowrap;
    line-height: 35px;
    left: 0;
    top: 0;
    z-index: 10;
    border-radius: 17px;
    font-size: 16px;
    color: #fff;
    background-color: #5f77b5;
    cursor: pointer
}

.apply-reward .apply-reward-wrap .apply-reward-content .form-group .upload-img .file-input #financial_img[data-v-4cbf6d7b] {
    width: 100%;
    height: 100%;
    opacity: 0
}

.apply-reward .apply-reward-wrap .apply-reward-bottom[data-v-4cbf6d7b] {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    padding-bottom: 37px;
    border-radius: 12px
}

.apply-reward .apply-reward-wrap .apply-reward-bottom .button-cancel[data-v-4cbf6d7b] {
    width: 123px;
    height: 35px;
    border-radius: 17px;
    background-color: #a8a8a8;
    font-size: 16px;
    color: #fff;
    margin-right: 15px;
    cursor: pointer
}

.apply-reward .apply-reward-wrap .apply-reward-bottom .button-submit[data-v-4cbf6d7b] {
    width: 221px;
    height: 35px;
    border-radius: 17px;
    background-color: #ff6766;
    font-size: 16px;
    color: #fff;
    cursor: pointer
}

.mcHelp-wrap {
    height: 100%;
    min-height: 100vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    color: #909090;
    background: #fff
}

.mcHelp-wrap .help-icon {
    width: 30px;
    height: 30px;
    margin-right: 20px
}

.mcHelp-wrap .help-title {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.mcHelp-wrap .mcHelp-menu {
    width: 265px;
    height: 100%;
    padding: 20px;
    overflow-y: auto
}

.mcHelp-wrap .mcHelp-menu .title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    min-height: 50px;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 700;
    width: 225px;
    text-align: left;
    padding: 4px 20px;
    border-radius: 10px;
    cursor: pointer
}

.mcHelp-wrap .mcHelp-menu .title span {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    line-height: 1.5
}

.mcHelp-wrap .mcHelp-menu .title.active,
.mcHelp-wrap .mcHelp-menu .title:hover {
    background-color: #ff2351;
    -webkit-box-shadow: 1px 1px 8px rgba(255, 35, 81, .8);
    box-shadow: 1px 1px 8px rgba(255, 35, 81, .8);
    color: #fff
}

.mcHelp-wrap .mcHelp-menu .children-list {
    overflow: hidden;
    -webkit-transition: all .2s ease-in;
    transition: all .2s ease-in
}

.mcHelp-wrap .mcHelp-menu .children-list.on {
    max-height: 1000px
}

.mcHelp-wrap .mcHelp-menu .children-item {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 2px 0 2px 50px;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 700;
    color: #5e5e5e;
    cursor: pointer
}

.mcHelp-wrap .mcHelp-menu .children-item:before {
    display: none;
    position: absolute;
    left: 30px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    content: "";
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: #ff2351
}

.mcHelp-wrap .mcHelp-menu .children-item.active,
.mcHelp-wrap .mcHelp-menu .children-item:hover {
    color: #ff2351
}

.mcHelp-wrap .mcHelp-menu .children-item.active:before,
.mcHelp-wrap .mcHelp-menu .children-item:hover:before {
    display: block
}

.mcHelp-wrap .mcHelp-content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
    background: #f5f5f5;
    padding: 40px;
    overflow-y: auto
}

.mcHelp-wrap .mcHelp-content * {
    margin-bottom: 15px
}

.mcHelp-wrap .mcHelp-content table {
    width: 97%;
    border-collapse: collapse;
    border: 1px solid #656565;
    margin-left: 30px
}

.mcHelp-wrap .mcHelp-content table td,
.mcHelp-wrap .mcHelp-content table th {
    padding: 10px;
    border: 1px solid #656565
}

.mcHelp-wrap .mcHelp-content .content-title {
    color: #404040;
    font-size: 25px;
    font-weight: 700;
    margin-bottom: 30px
}

.mcHelp-wrap .mcHelp-content .content {
    background-color: #fff;
    border-radius: 7px;
    padding: 25px
}

.zoom-in-top-enter-active[data-v-61b41b47],
.zoom-in-top-leave-active[data-v-61b41b47] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-61b41b47],
.zoom-in-top-leave-active[data-v-61b41b47] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-61b41b47],
.zoom-in-bottom-leave-active[data-v-61b41b47] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-61b41b47],
.zoom-in-bottom-leave-active[data-v-61b41b47] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.twofa-tabs[data-v-61b41b47] {
    padding: 0 30px 30px;
    font-size: 16px
}

.twofa-tabs .tab-list[data-v-61b41b47] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.twofa-tabs li[data-v-61b41b47] {
    cursor: pointer
}

.twofa-tabs .tab-list-item[data-v-61b41b47] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    list-style: none;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    border-bottom: 4px solid #e3e3e3;
    margin-left: auto
}

.twofa-tabs .tab-list-item[data-v-61b41b47]:hover {
    background-color: #fafafa
}

.twofa-tabs .tab-list-active[data-v-61b41b47] {
    background-color: #fff;
    border-bottom: 4px solid #17a6ff
}

.twofa-tabs .tab-list-item-wrap[data-v-61b41b47] {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap[data-v-61b41b47],
.twofa-tabs .tab-list-item-wrap[data-v-61b41b47] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap[data-v-61b41b47] {
    height: 33px;
    margin-bottom: 10px;
    margin-top: 25px
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap .tab-list-icon[data-v-61b41b47] {
    width: auto;
    height: 33px
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap .tab-list-icon.email[data-v-61b41b47] {
    height: 27px
}

.twofa-tabs .tab-list-item-wrap .desc-text[data-v-61b41b47] {
    margin-bottom: 19px;
    text-align: center
}

.zoom-in-top-enter-active[data-v-42a14f52],
.zoom-in-top-leave-active[data-v-42a14f52] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-42a14f52],
.zoom-in-top-leave-active[data-v-42a14f52] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-42a14f52],
.zoom-in-bottom-leave-active[data-v-42a14f52] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-42a14f52],
.zoom-in-bottom-leave-active[data-v-42a14f52] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

button[data-v-42a14f52] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border-radius: 5px;
    border: unset;
    padding: 12px 44px;
    font-size: 16px;
    cursor: pointer
}

button[data-v-42a14f52]:disabled {
    cursor: not-allowed
}

button.btn-loading[data-v-42a14f52] {
    background-color: #a2c0d3;
    padding: 12px 44px;
    padding-left: 16px;
    cursor: wait
}

button>span[data-v-42a14f52] {
    display: block
}

button .spinner-grow[data-v-42a14f52] {
    width: 20px;
    height: 20px;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    -webkit-animation: spinner-grow-42a14f52 .75s linear infinite;
    animation: spinner-grow-42a14f52 .75s linear infinite;
    margin-right: 8px
}

@-webkit-keyframes spinner-grow-42a14f52 {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    50% {
        opacity: 1
    }
}

@keyframes spinner-grow-42a14f52 {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    50% {
        opacity: 1
    }
}

.zoom-in-top-enter-active[data-v-3e0736f2],
.zoom-in-top-leave-active[data-v-3e0736f2] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-3e0736f2],
.zoom-in-top-leave-active[data-v-3e0736f2] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-3e0736f2],
.zoom-in-bottom-leave-active[data-v-3e0736f2] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-3e0736f2],
.zoom-in-bottom-leave-active[data-v-3e0736f2] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.country_code[data-v-3e0736f2] {
    font-size: 12px;
    padding: 0 10px;
    height: 100%
}

.country_code .select_val[data-v-3e0736f2],
.country_code[data-v-3e0736f2] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    position: relative;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.country_code .select_val[data-v-3e0736f2]:after {
    position: absolute;
    right: 5px;
    top: 10px;
    content: "";
    width: 0;
    height: 0;
    border-top: 6px solid #ccc;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent
}

.country_code .select_val .active_img[data-v-3e0736f2] {
    width: 35px;
    height: 26px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    position: absolute;
    left: 0;
    top: 0
}

.country_code .select_val span[data-v-3e0736f2] {
    display: inline-block
}

.country_code .select_val img[data-v-3e0736f2] {
    width: 22px;
    height: 22px
}

.country_code .code-input[data-v-3e0736f2] {
    position: relative;
    z-index: 1;
    padding-left: 35px;
    width: 90px;
    height: 26px;
    background: transparent;
    cursor: pointer;
    color: inherit;
    font-size: inherit
}

.country_code .select_list[data-v-3e0736f2] {
    position: absolute;
    top: 110%;
    z-index: 1;
    width: 100%;
    left: 0;
    background: #fff;
    -webkit-box-shadow: 0 10px 32px rgba(0, 0, 0, .18);
    box-shadow: 0 10px 32px rgba(0, 0, 0, .18);
    border: 1px solid #d0d4db;
    border-radius: 3px
}

.country_code .select_list ul[data-v-3e0736f2] {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: auto !important;
    max-height: 160px;
    overflow: hidden auto
}

.country_code .select_list ul[data-v-3e0736f2]::-webkit-scrollbar {
    display: none
}

.country_code .select_list ul[data-v-3e0736f2]::-webkit-scrollbar-thumb {
    background-color: #e7e8eb;
    border: 2px solid transparent;
    border-radius: 6px;
    background-clip: padding-box;
    height: 30%
}

.country_code .select_list ul[data-v-3e0736f2]:hover::-webkit-scrollbar {
    display: block !important;
    width: 11px
}

.country_code .select_list ul li[data-v-3e0736f2] {
    padding: 0 10px;
    width: 100%;
    height: 32px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer
}

.country_code .select_list ul li[data-v-3e0736f2]:hover {
    background: #f3f9ff;
    color: #0a84ff
}

.country_code .select_list ul li .code_num[data-v-3e0736f2] {
    margin-left: 5px;
    display: inline-block
}

img.flag[data-v-3e0736f2] {
    width: 22px;
    height: 22px
}

img.flag[lazy=loading][data-v-3e0736f2] {
    position: unset;
    -webkit-transform: unset;
    transform: unset;
    width: 22px;
    height: 22px
}

.login-2fa-modal-wrap {
    background: transparent;
    border-radius: 15px
}

.zoom-in-top-enter-active[data-v-6c033da6],
.zoom-in-top-leave-active[data-v-6c033da6] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-6c033da6],
.zoom-in-top-leave-active[data-v-6c033da6] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-6c033da6],
.zoom-in-bottom-leave-active[data-v-6c033da6] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-6c033da6],
.zoom-in-bottom-leave-active[data-v-6c033da6] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.login-2fa-modal .login-2fa-header[data-v-6c033da6] {
    position: relative;
    height: 72px;
    line-height: 72px;
    background-color: #f8f8f8;
    border-radius: 15px 15px 0 0;
    padding-left: 5%;
    font-size: 20px;
    border-bottom: 1px solid #dbdbdb
}

.login-2fa-modal .login-2fa-header .close-btn[data-v-6c033da6] {
    position: absolute;
    top: 50%;
    right: 30px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: #797979;
    fill: currentColor;
    width: 28px;
    height: 28px;
    cursor: pointer
}

.login-2fa-modal .login-2fa-header .close-btn[data-v-6c033da6]:hover {
    color: #222
}

.login-2fa-modal .login-2fa-body[data-v-6c033da6] {
    position: relative;
    width: 660px;
    height: auto;
    color: #666;
    background-color: #fff;
    -webkit-box-shadow: 0 0 20px 0;
    box-shadow: 0 0 20px 0;
    border-radius: 15px;
    font-size: 16px
}

.login-2fa-modal .login-2fa-body .desc[data-v-6c033da6] {
    font-size: 16px;
    line-height: 1.4;
    padding: 30px 30px 8px
}

.login-2fa-modal .verify-form[data-v-6c033da6] {
    padding: 37px 0 0
}

.login-2fa-modal .verify-form .form-input-group[data-v-6c033da6] {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 18px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.login-2fa-modal .verify-form .form-input-group_sms input[data-v-6c033da6] {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    margin-right: 15px
}

.login-2fa-modal .verify-form .form-input-group.right-side[data-v-6c033da6] {
    -webkit-box-pack: right;
    -ms-flex-pack: right;
    justify-content: right
}

.login-2fa-modal .verify-form .form-input-group.left-side[data-v-6c033da6] {
    -webkit-box-pack: left;
    -ms-flex-pack: left;
    justify-content: left
}

.login-2fa-modal .verify-form .form-input-group[data-v-6c033da6]:last-of-type {
    margin-bottom: 0
}

.login-2fa-modal .submit-verify-btn[data-v-6c033da6] {
    background-color: #17a6ff;
    color: #fff
}

.login-2fa-modal .submit-verify-btn[data-v-6c033da6]:hover {
    background-color: #53bdff
}

.login-2fa-modal .submit-sms-btn[data-v-6c033da6] {
    background-color: #83a7be;
    color: #fff;
    padding: 12px 24px
}

.login-2fa-modal .submit-sms-btn[data-v-6c033da6]:hover {
    background-color: #a2c0d3
}

.login-2fa-modal .submit-sms-btn.loading[data-v-6c033da6] {
    background-color: #a2c0d3;
    padding-left: 10px
}

.login-2fa-modal .submit-sms-btn.countdown[data-v-6c033da6] {
    min-width: 70px
}

.login-2fa-modal input[type=text][data-v-6c033da6] {
    width: 100%;
    height: 60px;
    background-color: #fff;
    border-radius: 5px;
    border: 1px solid #dbdbdb;
    color: #919191;
    -webkit-transition: border-color .3s ease 0s;
    transition: border-color .3s ease 0s;
    font-family: inherit;
    font-size: 16px;
    padding-left: 31px;
    padding-right: 31px
}

.login-2fa-modal input[type=text][data-v-6c033da6]:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px #fff inset !important;
    -webkit-text-fill-color: #919191 !important
}

.login-2fa-modal input[type=text][data-v-6c033da6]::-webkit-input-placeholder {
    color: #c9c9c9
}

.login-2fa-modal input[type=text][data-v-6c033da6]:focus {
    background-color: #fff;
    border-radius: 5px;
    border: 1px solid #676767
}

.login-2fa-modal input[type=text].error-tip[data-v-6c033da6] {
    border: 1px solid #ef4743
}

.login-2fa-modal[data-v-6c033da6] .country_code {
    height: 60px;
    border: 1px solid #dbdbdb;
    border-radius: 5px;
    color: #919191;
    margin-right: 8px;
    font-size: 16px
}

.zoom-in-top-enter-active,
.zoom-in-top-leave-active {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter,
.zoom-in-top-leave-active {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active,
.zoom-in-bottom-leave-active {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    -webkit-transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter,
.zoom-in-bottom-leave-active {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.flag-icon {
    width: 26px;
    height: auto
}