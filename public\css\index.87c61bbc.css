.announce-popup-container {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 5px
}

.announce-popup-container .popup-title {
    font-size: 40px;
    font-weight: 700;
    color: #fff;
    display: flex;
    gap: 20px;
    align-items: center;
    position: absolute;
    top: -60px;
    display: none
}

.announce-popup-container .popup-title:before {
    content: "";
    width: 44px;
    height: 44px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAMAAABGS8AGAAAANlBMVEVHcEz///////////////////////////////////////////////////////////////////+GUsxbAAAAEXRSTlMA8GBQMOBAwBCg0CCAcLCQr0blnQcAAAN9SURBVFjDtVnZgoMgDBSQSw7l/3923W4b7tMub1adxmQyCWHb+msnWnKO3L0o51KTfXu+dnO9EOOFLvMIXGnqqotqtQiLuessjldgqRtYdBaaDMG+oNmMby83sa5hX2vkphYyQ7Aij9lxc5ex2zDFGNH2yI0WfVyWmEtPkr0lyJk+1aU1jl+QpBpeGT+JJ3CRbn6his1uIkdWnF3HiTN6fgyXD4nBzkeQQz+YUW6avjfwTJADo2kHeQ94K2bSXwS8Lhgk/B/LWcHysaG5Sdc6boh81WMgV7Rb1qKugOvHWlUAPyNRdgQVa8A+RNEXk1ZYB1kHEKHy0+m8aGQKLaQGf1LTIbtJbnDDEQa5Q405g2cG9/TJtk0+Uy/zMlMKPO0w48PZtwGqmxqgBmkxv0g5Tf58puPLlspkSYGiqKiYXDT+gJZ6xTdyHsmQcXvOkqoqxndo9hYJ6fUhNqqQKOwikkKdpMO9UOAL2wzdHlXiUkqQEuNs8Ce4j+ugGHEBwbIlX6DAxQEnhOHvlbRS3lDpTQ7JJLyTSS4d4qi1f4H2MshZU9JlAiwOvki7NrD2z2fB+URMA/V0QaYqwH+irj6PRrzQQAWex851gP88gAFHFKLHAZhNAAvwAElLBgNgtAD8ChEHnBIw3dwKMP9csDQDgL1bgcZ9YPu5UEnc/csPgfc+8LQrELiClCR5LXh0IHgrdFNAN1ynG89blS7wCVyQ9QRZSWkEcEfaRvqU1nkThDvAEjwhsm7E21mQTZCoCrBvmkxWIbxsloR+w/It9Kgm9CfARG1OIPQLpUlYdHoKyEpp6hVT2iimR9ZIhsW0U/5FvfybvGOhAXn3zr6y07CwasPyrMWS9RbL9/izTSFPO18RN4UDbSwvAguTzPTSvtX2TF5svEGRWpvSpa2CV53GcM5QZ8Xk5sabvLjfTczDhd/0dzeQ3uRvb3n9fuf5Jt2WmTI5tsmdmW4WcX1G8mgQEt56NLqxLam5xDJuKURBsZgcj3HXJhX5p4FeXPb1dF60BrIh8jE01I+GpnpwfHx2h/rxmFePD7ylap/rjA+m7wjGjcT1rVF6stv/TdEvDf/vt2R+XHFpzNj97v57XHHlO2I7Rnz8PwcsZaMbS87kKeOjsFOHWOPQnC1oLLM9WMvWys2mTOto06webb4PY23pMNaar5z0vo6P6dzx8Q/vq7x9JUMxBgAAAABJRU5ErkJggg==) no-repeat 50%/contain
}

.announce-popup-container .popup-content {
    width: 1147px;
    height: 648px;
    background: url(../img/announce-bg.da4a86ab.png) no-repeat 50%/contain;
    padding: 30px 24px 30px 17px
}

.announce-popup-container .popup-main {
    height: 100%;
    position: relative;
    display: flex;
    gap: 20px
}

.announce-popup-container .popup-main .left-wrap {
    overflow: hidden;
    width: 242px
}

.announce-popup-container .popup-main .content-nav {
    height: 510px;
    overflow-y: hidden;
    overflow-x: hidden;
    background-color: #21233a;
    border-radius: 14px
}

.announce-popup-container .popup-main .content-nav li {
    cursor: pointer;
    color: #a6a6a6;
    display: flex;
    align-items: center;
    position: relative;
    transition: .2s;
    min-height: 53px
}

.announce-popup-container .popup-main .content-nav li .icon_notice {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAAUVBMVEUAAACcrf6erP6mqPWbq/ydrf6Xqvt3lfezyv2KpfuEm/efr/6cq/uovvuov/18m/ifsv2LpftumPVwmfqRtPpbjPlMgfVejfQ9dvFYiPVLfvH7AgwoAAAAE3RSTlMADCEFNxZe/v23iEopubKgh3T3u4NfYAAAAHZJREFUGNOt0EkOgCAMBVApFAVxxPn+B7U0hhrdGd/yp00/FP/QiuhHhBACYIolA2O9twZuqYLalcTVoPIgGheZM6jz4BBnFm0eVaHbF7b7e3hs7KBQ1seVpXU51EysTYekUlOR9qok5fs+l5dnEpTs/SFCFx+c+UEGFSap2goAAAAASUVORK5CYII=) no-repeat 50%/contain;
    width: 18px;
    height: 18px;
    margin: 0 8px 0 13px
}

.announce-popup-container .popup-main .content-nav li .item-arrow {
    width: 13px;
    height: 13px;
    color: #a6a6a6;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-transform: rotate(-90deg);
    transform: rotate(-90deg);
    transition: .2s
}

.announce-popup-container .popup-main .content-nav li.active, .announce-popup-container .popup-main .content-nav li:hover {
    color: #f5df4b
}

.announce-popup-container .popup-main .content-nav li .nav-title {
    width: calc(100% - 64px);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px
}

.announce-popup-container .popup-main .paginate-wrapper {
    height: 36px;
    margin-top: 23px;
    border-radius: 14px;
    background-color: #21233a;
    display: flex;
    justify-content: center;
    align-items: center
}

.announce-popup-container .popup-main .paginate-wrapper .pagination-container .page-item.active .page-link-item {
    background: none;
    color: #f5df4b
}

.announce-popup-container .content-detail {
    height: 100%;
    width: 850px;
    padding: 60px 20px 20px 220px;
    position: relative
}

.announce-popup-container .content-detail:before {
    position: absolute;
    content: "";
    background: url(data:image/png;base64,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) no-repeat 50%/contain;
    width: 202px;
    height: 60px;
    top: 0
}

.announce-popup-container .detail-text {
    text-overflow: inherit;
    overflow-x: hidden;
    overflow-y: auto
}

.announce-popup-container .content-detail::-webkit-scrollbar, .announce-popup-container .content-nav::-webkit-scrollbar {
    width: 10px;
    height: 1px
}

.announce-popup-container .content-detail::-webkit-scrollbar-thumb, .announce-popup-container .content-nav::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, .2);
    background: #e8e8e8
}

.announce-popup-container .content-detail::-webkit-scrollbar-track, .announce-popup-container .content-nav::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(0, 0, 0, .2);
    border-radius: 10px;
    background: #fff
}

.announce-popup-container .content-title {
    color: #f5df4b;
    padding-top: 25px;
    padding-bottom: 30px;
    font-size: 30px;
    font-weight: 700
}

.announce-popup-container .detail-box {
    padding: 0 20px 0 0;
    border-radius: 4px;
    height: calc(100% - 54px);
    overflow-y: auto;
    overflow-x: hidden
}

.announce-popup-container .detail-box::-webkit-scrollbar {
    width: 3px
}

.announce-popup-container .detail-box::-webkit-scrollbar-thumb, .announce-popup-container .detail-box::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent
}

.announce-popup-container .detail-box.public-modal {
    height: calc(100% - 105px)
}

.announce-popup-container .notice_footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 15px;
    -webkit-column-gap: 20px;
    column-gap: 20px
}

.announce-popup-container .notice_footer button {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    padding: 0 20px;
    height: 30px
}

.announce-popup-container .notice_footer button:hover {
    opacity: .8
}

.zoom-in-top-enter-active[data-v-6eaf9166], .zoom-in-top-leave-active[data-v-6eaf9166] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-6eaf9166], .zoom-in-top-leave-active[data-v-6eaf9166] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-6eaf9166], .zoom-in-bottom-leave-active[data-v-6eaf9166] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-6eaf9166], .zoom-in-bottom-leave-active[data-v-6eaf9166] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.popup_modal_wrap[data-v-6eaf9166] {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.simple-select {
    position: relative
}

.simple-select .select-input {
    cursor: pointer
}

.simple-select .select-input .arrow-icon {
    display: block
}

.simple-select .select-options {
    position: absolute;
    z-index: 1
}

.simple-select .arrow-icon {
    transition: all .3s ease;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
}

.simple-select .arrow-icon.up {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.country_code {
    position: absolute;
    left: 0;
    top: 0;
    width: 90px;
    height: 100%;
    font-size: 12px;
    border-radius: 3px
}

.country_code .simple-select {
    width: 100%;
    height: 100%
}

.country_code .simple-select .select-input {
    position: relative;
    width: 100%;
    height: 100%
}

.country_code .simple-select .selected-option {
    height: 100%;
    padding: 0 10px
}

.country_code .simple-select .arrow-icon {
    position: absolute;
    right: 3px;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    color: #fff
}

.country_code .simple-select .select-options {
    width: 100px;
    flex-direction: column;
    height: auto !important;
    max-height: 180px;
    overflow-y: auto;
    overflow-x: hidden;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #dcdfe6
}

.country_code .simple-select .select-options::-webkit-scrollbar {
    width: 3px
}

.country_code .simple-select .select-options::-webkit-scrollbar-thumb, .country_code .simple-select .select-options::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent
}

.country_code .simple-select .select-options .select-option {
    padding: 0 10px;
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #333
}

.country_code .simple-select .select-options .select-option:hover {
    background-color: hsla(0, 0%, 86.7%, .8666666666666667)
}

.country_code .simple-select .select-options .select-option img {
    width: 22px;
    height: 22px
}

.country_code .simple-select .select-options .select-option .code_num {
    margin-left: 5px;
    display: inline-block
}

.country_code .select_val {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center
}

.country_code .select_val .active_img {
    width: 22px;
    height: 22px
}

.country_code .select_val .active_img img {
    display: block;
    width: 100%;
    height: 100%
}

.country_code .code-input {
    flex: 1;
    text-align: center;
    color: #fff
}

.country_code .select_list {
    position: absolute;
    top: 95%;
    z-index: 510;
    width: 100%;
    left: 0
}

.acmc_icon.icon_agentAnnouncement {
    background-position: -274px -53px
}

.acmc_icon.icon_agentTeamOverview {
    background-position: -14px -49px
}

.acmc_icon.icon_linkRegister, .acmc_icon.icon_registerCenter {
    background-position: -316px -52px
}

.acmc_icon.icon_downlineManagmentView {
    background-position: -228px -15px
}

.acmc_icon.icon_linkManager {
    background-position: -225px -87px
}

.acmc_icon.icon_agentRedRain {
    background-position: -182px -85px
}

.acmc_icon.icon_agentDownlineTransactionDetailsCopy {
    background-position: -59px -48px
}

.acmc_icon.icon_agentHourSalary, .acmc_icon.icon_agentRevenueReport {
    background-position: -57px -84px
}

.acmc_icon.icon_subordinateManagementV2 {
    background-position: -228px -15px
}

.acmc_icon.icon_agentDividendRecordCopy, .acmc_icon.icon_agentDividendRecordCopy2 {
    background-position: -148px -85px
}

.acmc_icon.icon_agentDailyWages, .acmc_icon.icon_agentDailyWages2 {
    background-position: -105px -85px
}

.acmc_icon.icon_personalHistoryCopy {
    background-position: -182px 105px
}

.acmc_icon.icon_palStatementsAgentCopy {
    background-position: -14px -83px
}

.acmc_icon.icon_securityCenter {
    background-position: -228px -15px
}

.acmc_icon.icon_transferFund, .acmc_icon.icon_transferUpline {
    background-position: -274px -15px
}

.acmc_icon.icon_depositv2 {
    background-position: -316px -15px
}

.acmc_icon.icon_personalProfit {
    background-position: -57px -84px
}

.acmc_icon.icon_bonusDetails {
    background-position: -182px -85px
}

.acmc_icon.icon_withdraw {
    background-position: -14px -49px
}

.acmc_icon.icon_personalHistoryCopy {
    background-position: -59px -48px
}

.acmc_icon.icon_transactionHistory {
    background-position: -274px -53px
}

.acmc_icon.icon_rewardCenter {
    background-position: -106px -49px
}

.acmc_icon.icon_messageV2 {
    background-position: -148px -48px
}

.acmc_icon.icon_securityCenter {
    background-position: -148px -84px
}

.acmc_icon.icon_referral {
    /*background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMTM4IDc5LjE1OTgyNCwgMjAxNi8wOS8xNC0wMTowOTowMSAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTcgKFdpbmRvd3MpIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOjQ4RjNFMkY5REQxRjExRUFBOEEwOUIxQUI1RDA2MDlBIiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOjQ4RjNFMkZBREQxRjExRUFBOEEwOUIxQUI1RDA2MDlBIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6NDhGM0UyRjdERDFGMTFFQUE4QTA5QjFBQjVEMDYwOUEiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NDhGM0UyRjhERDFGMTFFQUE4QTA5QjFBQjVEMDYwOUEiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz4hOTE9AAABN0lEQVR42ozTO0sDQRSG4UmwsFWSRiSFlRYWFnaWkj8gQSwEEUT0N4iNTQoJClqEGBQrC2svpQELEUUINkJAtNAmRghYeGF8Jx7hcFzJDDww++3Mmd2dWee9d5Hm8OF/2h1GQh47eVomVrGIBj6RiS3whWOThbaTdnEtjLs0WQvDSast4RYXmJRsAy3k5LqAd0zYyWV5tDPcSD+Pfv+3PdiPOC43CipbVxPOsYkSKnhFUxdYkYE6y0pWTHjVwbAjOhiTwVMq25W9/3eHbHAoRWq4lv5sbIFtvOEZ93hEGy+YMeP2bYFTWW3BrNCHPSyrLOzOky7w+/FGu5zGLZzIE7WlX+3hNK1hFfUup3EAQ+iVkxn66VRn35yr4AgpM6mJmsmukEWuc8X8efmzklo94VUO9I/1LcAArirCocw7wKgAAAAASUVORK5CYII=) !important;*/
    /*background-repeat: no-repeat;*/
    /*background-size: 20px 20px*/
}

.acmc_icon.icon_missionLeaderboard {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAyVpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDYuMC1jMDAyIDc5LjE2NDM1MiwgMjAyMC8wMS8zMC0xNTo1MDozOCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIDIxLjEgKE1hY2ludG9zaCkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6RThGNzAwNkYxQUIwMTFFQkIwRDdFMDdFMDZCMTZGRDIiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6RThGNzAwNzAxQUIwMTFFQkIwRDdFMDdFMDZCMTZGRDIiPiA8eG1wTU06RGVyaXZlZEZyb20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDpFOEY3MDA2RDFBQjAxMUVCQjBEN0UwN0UwNkIxNkZEMiIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDpFOEY3MDA2RTFBQjAxMUVCQjBEN0UwN0UwNkIxNkZEMiIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVuZD0iciI/PqdMjsIAAAGGSURBVHja7NavSwNhHMdxp1P8gQajIBgsgkOX1LItCKsaB0aDcUERTIdlIoY1k/+AwShL4tSgpmlQFFSwDAwi/kLQOd+PfJTzeJ6DgYJhX3hx9+z2PJ/j+XK3RcrlcoOjmtCLLrzAfPHBd70DPWjFI65RsS3U6AhowS6ucIQz3GMVbVjS+BzHuEQJnbWEZDCGO6ygoM9ncIJ5zd3CMm4wiGnbYlFHSL+O65jT+SQ20KexWXBN529YwIArpB2LgcAhHadQVU9M3aIbT4ghr63N+OblfeuYHuUiND6mff2rmjB3H9GgiG3HF1NI4kD9CY5tNYo0mv1bZAI8xwRPi+7rPDi2VVYhP/qQCpmQCrlbL+Tad+OrOk9KLTUiYfVqQi70LIxjWPu8b7kr22Il9dJfcSRwik3smZBnPQt5hRQsW+A5Qora+2AvTMghZsOe+F+tekg9pB7yT0Kijrdn1vFGjetacOyvZFjIu47pr98BSyXENfZX1RaSw07In4taqqIX5Gd9CDAA5RtU4khk3wUAAAAASUVORK5CYII=) no-repeat
}

.br_acmc_mlnav .acmc_icon.icon_manualRebate {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAABmJLR0QA/wD/AP+gvaeTAAACP0lEQVQ4jZWUT2jOcRzHX++np/ZH2zQOtGxL0RxkHmo7yKJcdlOLEg47KDlIUbjsoJZwxMFM7Ug7kFpY/rZIPaWQA0Uyp2cYOzymrbfD7/ur3/P1m/hcvn3e38/n/f38/cJ/iu0e21dtF/Puc8Hg2AxsAlYAVUl3bfcAJ4F3QK9tgFngraRfAMohWgvsB7YAhQD/BPYBp4GtQV8Idw3AV+CMpA+KyAaAg0EtA8+AaWBa0pztFmAYmAdOSFq0XQ8cA1ZJOpolG7R92/bFEOVSpWixfcB2IYN1Bt+WFOgLwDnbDUuR/eWRbtu3bNcVbNcBg8B3YFhSNTIuhcdKGWxZIOm2vR04AjyWNF8E+oBWYETSbMapDtgBrA/QtlCKB0AvcDjgP4CnwCgkY9MLLAIPo0xOkXQ6lV3hfC1pApjIS78IdAAfJc1FdxuAV8CNDDYjaTomsV2UtJASLgc+5Tw2AQwAGyPncUljGb0JuGZ7RNK9IslM1cdsksZs3wdWZuA9QD8wlsHag39jGmEFaLMtSc683AWcz4m8HOlrwlmBZLVehLS7IsMvwDgwFfQp4BJwNrJLm/oyJXwEGNgbpVwJtZoM0KSkO5LmM1msA0pAuaapto+H4e3PSTFXbDfavhw2pCO+bLJ9xfbNfyG13Wr7Qghid01mGaM2YAhYTVLX68CbqFHNwE6SbjcBNSNUQ5hGChwiWUeR7PdnoEry0baT1H0GGJU0RSR/fLCBuDOQbiaZw0bgG/AeeA48SX/oWH4DC9wqh9jgGqMAAAAASUVORK5CYII=) no-repeat 0/20px 20px
}

.br_help_wrap {
    position: relative
}

.br_help_wrap .help_nav_body {
    width: 1200px
}

.br_help_wrap .top-title {
    display: flex;
    align-items: center;
    padding-bottom: 21px;
    font-size: 35px;
    font-weight: 700;
    color: #fff;
    position: absolute;
    top: -60px
}

.br_help_wrap .top-title:before {
    content: "";
    width: 44px;
    height: 44px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABYCAMAAABGS8AGAAAANlBMVEVHcEz///////////////////////////////////////////////////////////////////+GUsxbAAAAEXRSTlMA8GBQMOBAwBCg0CCAcLCQr0blnQcAAAN9SURBVFjDtVnZgoMgDBSQSw7l/3923W4b7tMub1adxmQyCWHb+msnWnKO3L0o51KTfXu+dnO9EOOFLvMIXGnqqotqtQiLuessjldgqRtYdBaaDMG+oNmMby83sa5hX2vkphYyQ7Aij9lxc5ex2zDFGNH2yI0WfVyWmEtPkr0lyJk+1aU1jl+QpBpeGT+JJ3CRbn6his1uIkdWnF3HiTN6fgyXD4nBzkeQQz+YUW6avjfwTJADo2kHeQ94K2bSXwS8Lhgk/B/LWcHysaG5Sdc6boh81WMgV7Rb1qKugOvHWlUAPyNRdgQVa8A+RNEXk1ZYB1kHEKHy0+m8aGQKLaQGf1LTIbtJbnDDEQa5Q405g2cG9/TJtk0+Uy/zMlMKPO0w48PZtwGqmxqgBmkxv0g5Tf58puPLlspkSYGiqKiYXDT+gJZ6xTdyHsmQcXvOkqoqxndo9hYJ6fUhNqqQKOwikkKdpMO9UOAL2wzdHlXiUkqQEuNs8Ce4j+ugGHEBwbIlX6DAxQEnhOHvlbRS3lDpTQ7JJLyTSS4d4qi1f4H2MshZU9JlAiwOvki7NrD2z2fB+URMA/V0QaYqwH+irj6PRrzQQAWex851gP88gAFHFKLHAZhNAAvwAElLBgNgtAD8ChEHnBIw3dwKMP9csDQDgL1bgcZ9YPu5UEnc/csPgfc+8LQrELiClCR5LXh0IHgrdFNAN1ynG89blS7wCVyQ9QRZSWkEcEfaRvqU1nkThDvAEjwhsm7E21mQTZCoCrBvmkxWIbxsloR+w/It9Kgm9CfARG1OIPQLpUlYdHoKyEpp6hVT2iimR9ZIhsW0U/5FvfybvGOhAXn3zr6y07CwasPyrMWS9RbL9/izTSFPO18RN4UDbSwvAguTzPTSvtX2TF5svEGRWpvSpa2CV53GcM5QZ8Xk5sabvLjfTczDhd/0dzeQ3uRvb3n9fuf5Jt2WmTI5tsmdmW4WcX1G8mgQEt56NLqxLam5xDJuKURBsZgcj3HXJhX5p4FeXPb1dF60BrIh8jE01I+GpnpwfHx2h/rxmFePD7ylap/rjA+m7wjGjcT1rVF6stv/TdEvDf/vt2R+XHFpzNj97v57XHHlO2I7Rnz8PwcsZaMbS87kKeOjsFOHWOPQnC1oLLM9WMvWys2mTOto06webb4PY23pMNaar5z0vo6P6dzx8Q/vq7x9JUMxBgAAAABJRU5ErkJggg==) no-repeat 50%/contain;
    margin-right: 20px
}

.br_help_wrap .mcHelp-wrap {
    min-height: 100%;
    background-color: transparent
}

.br_help_wrap .mcHelp-wrap .help-icon {
    width: 12px;
    height: 12px;
    margin-right: 10px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAAUVBMVEUAAACcrf6erP6mqPWbq/ydrf6Xqvt3lfezyv2KpfuEm/efr/6cq/uovvuov/18m/ifsv2LpftumPVwmfqRtPpbjPlMgfVejfQ9dvFYiPVLfvH7AgwoAAAAE3RSTlMADCEFNxZe/v23iEopubKgh3T3u4NfYAAAAHZJREFUGNOt0EkOgCAMBVApFAVxxPn+B7U0hhrdGd/yp00/FP/QiuhHhBACYIolA2O9twZuqYLalcTVoPIgGheZM6jz4BBnFm0eVaHbF7b7e3hs7KBQ1seVpXU51EysTYekUlOR9qok5fs+l5dnEpTs/SFCFx+c+UEGFSap2goAAAAASUVORK5CYII=) no-repeat 50%/contain
}

.br_help_wrap .mcHelp-wrap .help-icon svg {
    display: none
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu {
    width: 220px;
    padding: 12px 10px;
    height: 550px;
    overflow-y: auto;
    overflow-x: hidden;
    margin-right: 20px;
    border-radius: 12px;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .4);
    background-color: #1b2132
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu::-webkit-scrollbar {
    width: 3px
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu::-webkit-scrollbar-thumb, .br_help_wrap .mcHelp-wrap .mcHelp-menu::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .title {
    width: auto;
    min-height: 37px;
    font-size: 14px;
    height: auto;
    color: #808994;
    padding: 0 20px;
    margin-bottom: 5px;
    border-radius: 18.5px
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .title:after {
    content: "";
    display: block;
    width: 12px;
    height: 10px;
    background-image: url(data:image/png;base64,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);
    background-repeat: no-repeat;
    background-size: 12px auto
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .title.active, .br_help_wrap .mcHelp-wrap .mcHelp-menu .title:hover {
    font-weight: 700;
    color: #fff;
    background-color: #2b3248;
    box-shadow: none
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .title.active:after {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMgAAADICAYAAACtWK6eAAAJG0lEQVR4Xu2dfWtcVRCH56b9NMnnSSkoIopoNrak2YiCQgsKitK4G4sRUUQUSlO/Yntl8yI1zWbPPWfmvM3TfzNnzp3nNw+3mwQyCP8gAIG1BAbYQAAC6wkgCNsBgVsIIAjrAQEEYQcgEEeAN0gcN045IYAgToJmzDgCCBLHjVNOCCCIk6AZM44AgsRx45QTAgjiJGjGjCOAIHHcOOWEAII4CZox4wggSBw3TjkhgCBOgmbMOAIIEseNU04IIIiToBkzjgCCxHHjlBMCCOIkaMaMI4Agcdw45YQAglwGvXe42D09PnjpJHfGDCSAICKyN18+H0Tuj+N4D0kCN8dJmXtBZkfLv2SUd6/yRhInmx84pmtBZvOTP0TG96+zQpLA7XFQ5laQ2eHyNxnkw3UZI4mD7Q8Y0aUgs6PFLzIOH2/igySbCPX/dXeC7M+Xz0aR/dBokSSUVJ91rgSZHS6WMgwPp0aJJFOJ9VPvRpD9o+XTcZTD2OiQJJZc2+dcCLI3P/l+kPGz1KiQJJVge+e7F2Q2X34rIl9oRYMkWiTb6NO1IPvzk69HGb/SjgJJtInW269bQWZHyycyymMr9EhiRbauvt0K8mB+sv1K5Exk3LFCjiRWZOvp260gK8RIUs+itfokXQuyCmX/0fHOuHX3BW+SVle07HN3LwiSlF2w1m93IciVJK+37pwNIttWofGZxIpsub5uBEGSckvW8s2uBEGSlle1zLO7EwRJyixaq7e6FARJWl3X/M/tVhAkyb9sLd7oWhAkaXFl8z6ze0GQJO/CtXYbglwmtvqJOz8naW197Z8XQd5gjCT2C9faDQhyLTEkaW2FbZ8XQW7giyS2S9dSdwRZkxaStLTGds+KILewRRK7xWulM4JsSApJWlllm+dEkACuSBIAqdMSBAkMFkkCQXVWhiATAkWSCbA6KUWQiUEiyURgjZcjSESASBIBrdEjCBIZHJJEgmvsGIIkBIYkCfAaOYogiUEhSSLAyo8jiEJASKIAsdIWCKIUDJIogaysDYIoBoIkijAraYUgykEgiTLQwu0QxCAAJDGAWqglghiBRxIjsJnbIoghcCQxhJupNYIYg0YSY8DG7RHEGPCqPZJkgGx0BYIYgb3eFkkygVa+BkGUgd7WDkkywla6CkGUQIa2QZJQUnXUIUiBHJCkAPTIKxEkElzqMSRJJZjnPILk4XzjLUhSEH7g1QgSCMqqLIckIsPuz08f/mM1Q899EaSCdJGkghDWPAKCVJINklQSxLXHQJCKckGSisK4fBQEqSwTJKkrEASpK4/zp1lJIlt3zkaRbZvHG0YRuccH9810EWQzoyIVSFIE+1uXIkgdOaz9OQlvkrIBIUhZ/htv502yEZFpAYKY4tVpjiQ6HGO6IEgMtQJnkKQAdBFBkDLco25FkihsSYcQJAlf/sNIkpc5guTlrXIbkqhgDGqCIEGY6itCkjyZIEgezia3IIkJ1v81RRB7xqY3IIkpXr6LZYs3T3cksePMG8SObdbOSGKDG0FsuBbpiiT62BFEn2nRjvuPnu3I1it+VV4pBQRRAllTGyTRSwNB9FhW1QlJdOJAEB2OVXZBkvRYECSdYdUdkCQtHgRJ41f9aQRJiwhB0vhVfRo50uNBkHSGVXZADp1YEESHY1VdkEMvDgTRY1lFJ+TQjQFBdHkW7YYc+vgRRJ9pkY78HpYNdgSx4Zq1K3LY4UYQO7ZZOiOHLWYEseVr2h05TPGeN0cQe8YmNyCHCda3miJIHs6qtyCHKs5bmyFIPtYqNyGHCsbgJggSjKp8IXLkzwBB8jOPuhE5orAlH0KQZIT2DZDDnvG6GxCkHPugm5EjCJNZEYKYoU1vjBzpDFM7IEgqQaPzyGEEdmJbBJkILEc5cuSgHHYHgoRxyla1kuP11p2zwexvpK9GGXb5G+lhkSJIGKcsVciRBfOkSxBkEi67YuSwY5vSGUFS6CmdRQ4lkAZtEMQA6pSWyDGFVv5aBMnP/L8bc8gxjuO90+ODlwXHbPpqBCkUH3IUAj/xWgSZCEyjHDk0KObpgSB5OPPfqsycta5DEC2SAX14cwRAqqwEQTIFghyZQCtfgyDKQG9qhxwZIBtdgSBGYK/aIocxYOP2CGIIGDkM4WZqjSBGoJHDCGzmtghiABw5DKAWaokgyuCRQxlo4XYIohgAcijCrKQVgigFgRxKICtrgyAKgSCHAsRKWyBIYjDIkQiw8uMIkhAQciTAa+QogkQGhRyR4Bo7hiARgSFHBLRGjyDIxOCQYyKwxssRZEKAyDEBVielCBIYJHIEguqsDEECAkWOAEidliDIhmCRo9PNDxwLQW4BhRyBW9RxGYKsCRc5Ot76CaMhyA2wkGPCBnVeiiDXAkaOzjd+4ngI8gYw5Ji4PQ7KEeQyZORwsO0RIyKIiCBHxOY4OeJeEORwsumRY7oWBDkit8bRMbeCIIejLU8Y1aUgyJGwMc6OuhMEOZxteOK4rgRBjsRtcXjcjSDI4XC7FUZ2IchKjnHr7guRcUeB2Y0t+GuyVmTL9u1eEOQou2Ct3961IA/mJ9uvRM54c7S+puWev1tBkKPcUvV0c7eCzI6WT2SUx1Zh8ZnDimxdfbsVZIV5drT8Rkb5Uhs5cmgTrbdf14KssO8dLr4bhuFzrQiQQ4tkG326F+TyTfKDjHKUGglypBJs77wLQVax7B8tfhzH4SA2IuSIJdf2OTeCnL9JDhc/yTB8OjUy5JhKrJ96V4KcSzJfnorIJ6ERIkcoqT7r3Aly8SY5+VWG8aNNkSLHJkL9f92lIBdvkpPfRcYP1kWMHP0vf8iEbgW5+O7W4k8Zh/eug0KOkNXxUeNakMvPJH+LyDtXcSOHj8UPndK9IBffAl4+H0e5jxyha+OnDkEus947XOyeHh+89BM9k4YQQJAQStS4JYAgbqNn8BACCBJCiRq3BBDEbfQMHkIAQUIoUeOWAIK4jZ7BQwggSAglatwSQBC30TN4CAEECaFEjVsCCOI2egYPIYAgIZSocUsAQdxGz+AhBBAkhBI1bgkgiNvoGTyEAIKEUKLGLQEEcRs9g4cQQJAQStS4JYAgbqNn8BACCBJCiRq3BBDEbfQMHkLgXzwsOBQ2ErpxAAAAAElFTkSuQmCC);
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .children-item {
    font-weight: 400;
    font-size: 14px;
    color: #6a7391;
    padding-left: 38px;
    height: 37px;
    border-radius: 37px
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .children-item:hover:before {
    display: none
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .children-item:hover {
    color: #6a7391
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .children-item.active {
    background-color: #2b3248
}

.br_help_wrap .mcHelp-wrap .mcHelp-menu .children-item:before {
    display: none !important
}

.br_help_wrap .mcHelp-wrap .mcHelp-content {
    padding: 23px 30px;
    height: 550px;
    margin-left: 0;
    border-radius: 12px;
    box-shadow: 0 3px 6px 0 rgba(0, 0, 0, .4);
    background-color: #1b2132;
    color: #6a7391;
    line-height: 22px;
    overflow-y: auto;
    overflow-x: hidden
}

.br_help_wrap .mcHelp-wrap .mcHelp-content::-webkit-scrollbar {
    width: 3px
}

.br_help_wrap .mcHelp-wrap .mcHelp-content::-webkit-scrollbar-thumb, .br_help_wrap .mcHelp-wrap .mcHelp-content::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent
}

.br_help_wrap .mcHelp-wrap .mcHelp-content .content-title {
    display: none
}

.br_help_wrap .mcHelp-wrap .mcHelp-content .content {
    padding: 0;
    background: transparent !important
}

.br_help_wrap .mcHelp-wrap .help-slide-in {
    opacity: 0;
    -webkit-animation-name: slide-in1;
    animation-name: slide-in1;
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    -webkit-animation-duration: .2s;
    animation-duration: .2s
}

#app {
    width: 100%;
    min-height: 100vh;
    background-color: #0e131b
}

.page-banner {
    width: clamp(100% - 330px, 100%, 1920px);
    margin: 0 auto;
    position: relative
}

.page-banner .pointer {
    cursor: pointer
}

.page-banner .swiper-wrap {
    width: 100%;
    justify-content: center
}

.page-banner .swiper-wrap .swiper-wrapper {
    width: 100%;
    transition-timing-function: ease
}

.page-banner .swiper-wrap .swiper-slide {
    width: 100%;
    position: relative
}

.page-banner .swiper-wrap .swiper-slide .banner-img {
    display: block;
    width: 100%;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 10px
}

.zoom-in-top-enter-active[data-v-98f5f836], .zoom-in-top-leave-active[data-v-98f5f836] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-98f5f836], .zoom-in-top-leave-active[data-v-98f5f836] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-98f5f836], .zoom-in-bottom-leave-active[data-v-98f5f836] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-98f5f836], .zoom-in-bottom-leave-active[data-v-98f5f836] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.notice-marquee-container[data-v-98f5f836] {
    height: 39.8px;
    padding: 0 13.5px;
    border-radius: 22.5px;
    background-color: #171d27;
    display: flex;
    align-items: center
}

.notice-marquee-container .notice-marquee-content[data-v-98f5f836] {
    width: 100%;
    height: 39.8px
}

.notice-marquee-container .notice-main[data-v-98f5f836] {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%
}

.notice-marquee-container .notice-main .notice-icon[data-v-98f5f836] {
    flex-shrink: 0;
    margin-right: 19px
}

.notice-marquee-container .notice-main .notice-icon .speaker[data-v-98f5f836] {
    display: block;
    width: 17.8px;
    height: 18.8px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAZCAMAAAAc9R5vAAAA1VBMVEUAAAD6jwr5hwD/0pf/mhL/0pb/0pjbaAP3iwXEXQX/nyrvfAH+lwnxfAD/oyX/qjT/mwn/0pf/x33ufQT/15//1Jz6kxi9WAT/1JnYZwb/1aPwkC3/15L/05j/rDfYYwH/1Z3YZQP8jQXaZAL/oA3/mAfLXAT/16D7igD/05n/2af/yID/zIr/xXj0gQD/tlT/mAD/vmj/1qD/kQD/z5H/qjL/nQT/3rH/ul7/rTv/5cT/wW7/oBHvegDpcwD/4br/s0zbZgD/piL/oB3iawD/69D6kg6XRTruAAAAKHRSTlMAvZ/i0cV/Y04hIPHm187Buqehcm1fX1s9NiQQCPPz2M2sqKaji4qG+zwJggAAASVJREFUKM9tktl6gjAQhaFSi7jbxdp9L1mAEEFWWWvf/5E6yFcSW8/l/MmZM5MoQteLK+WYbjBJz4+BPiaRevu3etfvUWK5zsObcVh/Qh4lxCpS09Rl8MkxRpQSAPZQtuvhOEZgFW3WdnYmBUV4ByAJAZjbVwEuEY5zjySO34BHKSmAgFpuC0oB5gjnWIBBB04RDjpgfx0CLsBSpJWsAIi89zyIASROlDLTzj66vSKe7wIPBiyqBjz/gguEOEcUVgJm9bc5NNrp5l4j2iwxcZ0iquqRqsNxznEQ4P12SdvFtLfZCqK2RtAhdKJqw9YAsnKpLIgFDmEjx0/3ZbhQrhRjOp1MNG02YqyGSGP15QQkPb7OmM/s8UD5pxmDC2JoSe+aKn2sH8YkO6w2L9PkAAAAAElFTkSuQmCC) no-repeat 50%/contain
}

.notice-marquee-container .notice-main .marquee_box[data-v-98f5f836] {
    width: 100%;
    height: 100%;
    flex: 1
}

.notice-marquee-container .notice-main .marquee_box marquee[data-v-98f5f836] {
    height: 100%
}

.notice-marquee-container .notice-main .marquee_box .notice_list[data-v-98f5f836] {
    height: 100%;
    font-size: 18px;
    color: #fff
}

.notice-marquee-container .notice-main .marquee_box .notice_list li[data-v-98f5f836] {
    height: 100%;
    display: inline-block;
    line-height: 41.8px;
    margin-right: 70px;
    cursor: pointer
}

.game-vendor-item {
    width: 209.4px;
    height: 287.1px;
    position: relative
}

.game-vendor-item:hover .game-img > img {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.game-vendor-item:hover .action-wrap {
    opacity: 1
}

.game-vendor-item:hover .action-wrap .action-btn {
    transition: .3s;
    transition-delay: .1s;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.game-vendor-item .game-img {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 16px
}

.game-vendor-item .game-img > img {
    display: block;
    width: 100%;
    height: 100%;
    cursor: pointer;
    -o-object-fit: cover;
    object-fit: cover;
    transition: all .5s
}

.game-vendor-item .action-wrap {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: .2s;
    background: rgba(0, 0, 0, .6);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 15px;
    border-radius: 16px
}

.game-vendor-item .action-wrap .start-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff
}

.game-vendor-item .action-wrap .start-btn:hover {
    opacity: .8
}

.game-vendor-item .action-wrap .free-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 108px;
    height: 39px;
    padding: 0 10px;
    border-radius: 7px;
    background-color: #1678ff;
    font-size: 16px;
    font-weight: 600;
    color: #fff
}

.game-vendor-item .action-wrap .free-btn:hover {
    opacity: .8
}

.game-vendor-item .action-wrap .action-btn {
    min-width: 130px;
    height: 40px;
    -webkit-transform: scale(.5);
    transform: scale(.5)
}

.game-list-item {
    position: relative;
    width: 100%;
    font-size: 14px;
    font-weight: 500;
    color: #808994
}

.game-list-item:hover .game-img > img {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

.game-list-item:hover .action-wrap {
    opacity: 1
}

.game-list-item:hover .action-wrap .action-btn {
    transition: .3s;
    transition-delay: .1s;
    -webkit-transform: scale(1);
    transform: scale(1)
}

.game-list-item .game-img {
    width: 100%;
    aspect-ratio: 1/1;
    overflow: hidden;
    border-radius: 16px;
    position: relative
}

.game-list-item .game-img > img {
    display: block;
    width: 100%;
    cursor: pointer;
    -o-object-fit: contain;
    object-fit: contain;
    transition: all .5s
}

.game-list-item .hot-icon {
    min-width: 58px;
    height: 28px;
    padding: 0 12px;
    border-radius: 6px;
    background-color: #ff4345;
    z-index: 1;
    left: -12px;
    top: 12px;
    pointer-events: none
}

.game-list-item .game-vassalage, .game-list-item .hot-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    font-size: 14px;
    color: #fff
}

.game-list-item .game-vassalage {
    min-width: 49px;
    height: 36px;
    padding: 0 9px;
    border-radius: 32px 0 16px 0;
    background-image: linear-gradient(180deg, #f1a611, #fdd82d);
    bottom: 0;
    right: 0
}

.game-list-item .action-wrap {
    width: 100%;
    aspect-ratio: 1/1;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    background: rgba(0, 0, 0, .6);
    flex-direction: column;
    gap: 15px;
    border-radius: 16px
}

.game-list-item .action-wrap, .game-list-item .action-wrap .start-btn {
    transition: .2s;
    display: flex;
    justify-content: center;
    align-items: center
}

.game-list-item .action-wrap .start-btn {
    white-space: nowrap;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff
}

.game-list-item .action-wrap .start-btn:hover {
    opacity: .8
}

.game-list-item .action-wrap .free-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 108px;
    height: 39px;
    padding: 0 10px;
    border-radius: 7px;
    background-color: #1678ff;
    font-size: 16px;
    font-weight: 600;
    color: #fff
}

.game-list-item .action-wrap .free-btn:hover {
    opacity: .8
}

.game-list-item .action-wrap .action-btn {
    min-width: 130px;
    height: 40px;
    -webkit-transform: scale(.5);
    transform: scale(.5)
}

.game-list-item .fav-btn {
    flex-shrink: 0;
    width: 43.8px;
    height: 43.8px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, .37);
    cursor: pointer;
    position: absolute;
    top: 5px;
    right: 5px;
    color: transparent;
    display: flex;
    justify-content: center;
    align-items: center
}

.game-list-item .fav-btn.active {
    color: #fff
}

.game-list-item .fav-btn svg {
    width: 20px;
    height: 20px
}

.game-list-item .game-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 9.7px 0 0
}

.game-list-item .game-name {
    flex: 1;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    line-height: 1.2
}

@supports (-webkit-line-clamp:2) {
    .game-list-item .game-name {
        display: -webkit-box !important;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal
    }
}

.home-item .main-title {
    margin-bottom: 20px;
    height: 43px;
    font-size: 18px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    text-transform: uppercase
}

.home-item .main-title .title-label {
    margin-top: 12.4px;
    height: 28.3px;
    display: flex;
    align-items: center;
    font-size: 17.3px;
    font-weight: 600;
    color: #9dabd0;
    gap: 7px
}

.home-item .main-title .title-label:before {
    content: "";
    width: 48px;
    height: 30px;
    background-repeat: no-repeat;
    background-position: 50%
}

.home-item .main-title .title-label.recent-icon:before {
    background-size: 33px 29px;
    background-image: url(../img/recent-icon.d406c122.png)
}

.home-item .main-title .title-label.hot-icon:before {
    width: 18.8px;
    height: 23.3px;
    -webkit-filter: drop-shadow(0 0 10px rgba(255, 186, 82, .42));
    filter: drop-shadow(0 0 10px rgba(255, 186, 82, .42));
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAMAAAApWqozAAAA3lBMVEUAAAD4UQ/wUwn1VxD2VA3lSgT9Yhf+Yx/rdw3lSAPqUhDzWxTyTQH7UQXgTgXxUAv6YRf+ayjXSAf+Yhv/UAH/ZyP/Yhz/Yx36TgLhSAT+Uwf7Zxf/ZyTWRgj/Vwz/YBnpSgP/WRDpVgXWRwfvfw7zgQ7XRwj/TgDvgQ7sdw3+WQ/6ohb/UQP+XhX0jhH/VQr6nRP4lRL+YhzaSAbxhxDtSwP7TQD5pxnnSwT3rh3+aSfvgQ/4qhveSQbiSgX1TgH4gg/xTAL6ixP8bw78Zwr8XAT4nhvmZwrwawnhVwhEqMhyAAAAKnRSTlMACkJMEPe6kP5mOif0uVYwG/Xx7erax66Vh3huYebaoo71zbmj7KajlIWVm+ltAAACJElEQVQ4y73V12KqQBAGYAHF3jW2EzXlNEAUEcwmRpFief8XyswuRNgUvMrccPPxM7uzQOZHSiiVs4JwLc4/N7tyfiQ1rtGN5jMU3NETr9C/KF5CyXUhtZGb0M5m7XwjNTqyM0WppmWXu+9WmVezKX1MLnY+7wspfYCNsNaRUvoAHFlN+5sS3Y5ZrVP+Ht8wy7Dd/2ptdXqRwfpKaO3OF31IeZYMuQeHWSiaUJK4e7I9ioUZYnIImLWLQO+tKR/c7OFlpEAZhBx8infT7KBiWUWu4+6yDc8Sx7g4l5CN6qPdV/uWZeVqH4axnIj5O7CIN5v1IrB3u30Fcp9yInfcYlNmWHXA7i2wPC61mWXTcNGqC7Cf4hENjoZhbNaA1RNQsICl5Nt3ORGIqV04e2a3yQUKPWY9ajWV2oUe2m2umJiIzBomClofLGIjtNvXz7BDPJyyu2ZWt06n0xNYM4EFGSyO2QUb0FzEK10vgOXxBBfnb8ja0wI3tIau66sjWjM5wSFuhEcIUQ0jbJjh8yvglpTA0t0cMZ1cPHhFg1v1BG6MYYc9tOuEfUFr3nLHeTjHLePtqoDW/JPhaqxpwSJqwoiCjyZWicdiR9Mczob44eP7OrQvOxzZlzN2jMG8rtq2w9kCBg84yHS/snPQUowWu2gNhAhwnexAG7Hgs3lbu1iu6tWKE7PH1sN33y9BHMi/I3s/Tf21ZBvi6N/j4/8a/OcyP1VvQLScvDDqCugAAAAASUVORK5CYII=)
}

.home-item .main-title .title-label.rng-icon:before {
    width: 48px;
    height: 25px;
    background-size: 100% 100%;
    background-image: url(../img/rng-icon.0b608136.png)
}

.home-item .main-title .title-label.fish-icon:before {
    width: 25px;
    height: 25px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,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)
}

.home-item .main-title .title-label.live-icon:before {
    width: 28px;
    height: 28px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,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)
}

.home-item .main-title .title-label.sports-icon:before {
    width: 25px;
    height: 25px;
    background-size: 100% 100%;
    background-image: url(data:image/png;base64,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)
}

.home-item .main-title .title-label.pvp-icon:before {
    width: 25.9px;
    height: 25px;
    background-size: 100% 100%;
    background-image: url(../img/pvp-icon.ff7490a3.png)
}

.home-item .main-title .title-action {
    margin-top: 10px;
    width: 96px;
    height: 33px;
    border-radius: 6px;
    color: #586376;
    font-size: 14px;
    font-weight: 600;
    background-color: #181f2b;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center
}

.home-item .main-title .title-action:hover {
    background-color: #232c3c
}

.home-item .main-title .title-action .swiper-arrow {
    position: relative
}

.home-item .games-wrapper {
    width: 100%;
    display: grid;
    grid-template-columns:repeat(6, 164.7px);
    gap: 20px 19.4px
}

.home-item .games-wrapper.vendor {
    grid-template-columns:repeat(5, 209.4px);
    gap: 9.5px
}

.game-menu-container {
    border-radius: 10px;
    background-color: #181f2b;
    display: flex;
    align-items: center
}

.game-menu-container .game-menu-swiper {
    position: relative;
    width: 100%;
    padding: 2.8px
}

.game-menu-container .game-menu-swiper .swiper-slide {
    width: auto
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item {
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    padding: 5px;
    width: -webkit-min-content;
    width: min-content;
    min-width: 93px !important;
    border-radius: 7px;
    color: #5c677a;
    font-size: 17px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    flex-direction: column
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item:hover {
    background-color: #232c3c
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item.active {
    background: url(../img/promo-bg.84c5ba16.png) no-repeat 50%/cover;
    color: #fff
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon {
    width: 29px;
    height: 21.3px;
    background-position: 50%;
    background-size: contain;
    background-repeat: no-repeat
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.HOME {
    background-size: 21.3px 19.3px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAABSCAMAAAAM51hjAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAABjUExURUdwTOn1+en2/Oju8d8yYOf3++n2+t80YN83YN8yYN8yYON+mt8yYOr2+uf3/98zYOn2++n1+d80YOj2+Oj0+un2+t8yYMNFKufe5efR3eBLc+A+at/Ixc5zYdKIeclgStmpoNn1G/AAAAAVdFJOUwCkJPvvQN8yIN92PJ/GEMZWgBBwkI9p4D0AAAIQSURBVFjD7djbcoMgEAZgwGiU0RybaFusvv9TNm2i08riHiDTm/53yThf1hUwoNR/InKy++t1U2+fQG+r6z31KTm9uU6pyqfRqfFfdFp8QafEPTodDtCpcJBOgwfoFHiQjsdX6Fh8lY7DEToGR2k5TqClOImW4RD95t5T4CDdXV4T4AH6kgAP0vH4Ch2Lr9JxOELH4Cgtxwm0FCfRMpxIS3AyzccZNBdn0TycSXNwNk3HBTQVF9E0XEhTcDGN42UlplG8jqBhfD/bmxgaxqet4pZKd865Vxpeh2yQdn17Tz92OD41pSTQs/yVYUTxCu43RI/t7/QZgs8P03LpW+kIbudjgA2XvlW+3pYSmJcQ3U219t95fFrtuf0x6cv9Y+hoYIR8LLrwuI3MwyuI/ird1rU9KR0su1v+mFd4Ud4neGXhl48Odftnfx3c8eJ2/1trg6c3OtQS324BezWA3YfsLt4e/u0/sAd/DVjaDWK/hOzWzV9kg8w+B+12dI9MXywnvUbsQ2ju+BmWFx4R2wBvw4DtrScHbFNS0JZvoOyLUfyHCb8bvCW2QfdpOfjnYfxYxPkXHfBNYHERxuD2UUhrwr54VzytbHCIE3KkHRZoAV3saLakK0YRYzIufaYf/HDxo2LEFE+qmolnOfeAcPdCpBuj+DENZezlSpYcG+mNVP6u/aBD1Rf6vFPRMbkfg7ufRodbmy8RxtAAAAAASUVORK5CYII=)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.FAV {
    background-size: 17.8px 14.9px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEsAAAA/CAMAAACfI8UPAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAF6UExURUdwTMQBQcMsX7YHP9FGdLAHPcUZVt12ktNTgb8lWq8KQbkWTrwDPa8LQ80EPb8STbgUTbUGQ88CYNIFPsMMS8UBWMkCWM4BWagFPL4BOLcFPL0STcENSssGQMMRTa8EOMEHQfMJd80DWeMFacIATuUUSf8ASPgARfcAROsAQeMAP+YAQPQAROQAQO0AQf4AR/EAQ9wAPekAQdYAPPAAQ/oARtkAPfwARu4AQt8APuEAP94APf8BMP8ANf8ARf8AP94AXv8DHP8BK90AZv8AR/8AOd0AbP8AQ94Adf8BIvIAQ90AV94AUf8APP8TOfwIf/8pSP8AJtkAT+MAfdkARv8CFt4BRP8nQfEAf9gBYuAAS/UBgtoAcOsAf+AAef8ePP8MJf84Rf0EiP8dRP9fcf8vT+QCcNgAWv8eM/8WJf8SL9gAae4TSOIAaOgAeP9IVv9TY/4MhP8oNv+Pif88VPIkUfcDePoCR/8bT+0Cb/9lZ/4yXPQOff9/ewOBIlUAAAAmdFJOUwDqIa0XlzgGDityQdxh+nxLo+3ok9+u+YP7uFaH1WjDy/bE5/n88KSViwAACG9JREFUWMOlmIdXIlkTxZskSQED5qzToIDkKEkkKBkDQQR0WNSRMa6Kjs7//lW91404q7POfleOB895/Lh1q97rbhnmjaQycR9KLJNKmfcFS+T8GuZDScV9iumRGYFAMDw4rVAK31sqkw+oR+ZhiWBmRD0gl32AkqsnZ4pfvlOtWofnJZpfaeJZ0bz1C12zYnKpZgan3qNJ+yaH7+9bj81m1u/3BwIB/YqqXyJ8s0SJS/LNRjaLKxJuncfePyj+Z3kSQfM538yC/IWIP5Zgo2xU7xKo5dIuSaR6fn5uNZsUFguyrNNos8/P/mJNLhm+b+UbDWRFCpFILAQrWVbvmxiUc6ipmfXWcytfajTgG88j/gRrNjv1Dpu9/y1MLPrSyoNKSIuAAhTm1hlUk0qSuaL/HpeUSmew5jxS8LMsoIyWNZNdMN2Tq1jy5TlfyrfuWqWzo43tSMQfiGUIzKxbs89haAPEdymfPwOdH20UYsjSGR1rtlX7xNRrnyWq53zr+O7q6u749tv+RgF9ZTKZUBRpjuFpoVQzg1+Wz7da8HVn5xvb/hBBJYFl8Nn7BzhnUs3wfb50fHX94/oKWfvIIrAES2ESxfx9q4TGr0F5tBVIsG693pi0IKuomuMaLp/83izdXf/4+ffPa2Bt7BegkQCLcQ0wG+0TmFWpdPcXqkVsRTEto4X48u0sK6ixWdVjtnT119+gH10WgYVCiWgilIgFss1mHmjXiLouYVpRlqIcK8AypU/nSbul/d8fm/mrn4D6eXV7BnltIwvHFWChUCbmLxSOkHaFqKuzo22skEWUxQMok7eYDi+rSYlgq3l8hSVSWzwrBpGBM2Rtb59nsw0o8fruDPocyMBoGTF4QFFWpR+GTCopPmYbxwgDFNiCPpIaCQlZAX8EYEdH5427u9J5oQCbImrW6ZNGx8qKzWQymHzeg/DmBBQpm+vAV5aOUbcwOfv7YAuzR09BUIiDIe1oezsSSwTJlCaT4Mq0urrqQ9bT6SzD9M08BiLZxu1tCUx940uEsEJRoiAk5vdHIgXA4ZdA6gRlTHrQlQ9ZvoPwyeacjJkdfsxEzhuNEpLAFYeKQSE4qqBgCP4MBEhrMyGzmTV3dHo6DSYf1UH44mleyEhciYQfYd/2j3iUHwsMsryCIaIElIzjZu6gLQfaWuVYaWDN9DEibzQYKGwcnW8AiEfhZLE9wmLJG7PZ7Ia9A7bINLyyTp7G1IzIwCZiEaDgi8YeozvxPXU6HZ2OVOhBFGV5vamLk4exEUZkYqOhAAS7sc15ykD/3uOgJ7Ckx21IJouwvPAqpjaBJUEWG8xgoyL0eOhNqofjBhSGDlFBWGtchd51q3d9vcvyYR6ZGG6ZWCYR5JP5heXuOEEcCsPiWFYUtPGSsLysmbYqEUxE38/JzAJKp8PMcRNaPB4bV+E6oorpi5PL3NA0M+gC7+xvZCbFdbA8HHY4GjykQpwtKNEFLIi+ltNOMSOqjtv8GxCYIizwxWXl6eaOJbpc1gNglXPzfYxGYHR+bIyYcrvdTq6BeGR5PPxkoS2Xq5gKX5xUH+bEjLzfpu+YPyJ1KAdniqKSDjwdkAX9sxJb6VQY4nqalMI5YTXq3KQW6qJrEg05OyR1khVFedZsNhtFrVuJLdhAl+VcRS1lpGqVRwftxvlxQ8RuMkiYEpkCOgiAocMABeI4rK56SQchedcODBewlvGQlk8YjHpouLODgs92qLuOk4wB9UQEPfTQkxRtUZR1D9OqVZ/wXGVkEy4HLNQ7eXU6PNWp1+u7JHKl8PBhebE8CGtvJ0xsVch5Lx2ZMFgsSQLr2sNiCYsaSsKAgid0hZO16ltf51g7KWTlXpY15JqmEbg8DocFCtX3iqstmcRJdzhgC64giUwWTR1Z6dPNeL2cW1LQWyfZtMq0AjR0wLWLvOVCSsIYQOSeV5IPC7Tb7cCCtOJbu+WFRe5uiFH22022lRWwxn0cfvBlpLsPU1rD6vhThkw7wFx7e+nw5tZu9WaJv5+Ae5MJH6z08DBLEkVjwuI8fHW4BfmxQl8YfLxeyz2I5D13cmDMZgNnNBoLJ4eDbGSCgmsOTJWVThWS7Pa9dGozvlWr5hY1vfdykztw0TRgahANwBzcGwLiyvPRoFxdU1AghFXNtSVv7pE1kzsHJgjNQwSYNUrpRXm9PSj7Hk4DoGpfF0bkb2+2FQKAGaADpGMcgfyy8a7oVubro6h6rbowqvzl1l04LkilD0hqNv4XL0P3OHZZ37iC3Ks32ql/PFMIx8cqSDPY3mJI/+hV4rXCV1c3Q+PvPC2IJdrKaerAZyKfNxgoCkD8we7qKfAUUVu13MvQ+LsPRfKRoUoqdXBQNOCeMyHPZDBxg87PAWkgRdVhSw+NC99/tALYRTiMhWJRJiIgwfue1JF0WiEdrN6MjYs/elATj4/FNzeRVvR6fV15vZRld9n39pBUAVO7ZchKIfv4qU88rm3HN4m3YhGv714iOg24/8BUpdI+rO/ulnMLo1O/eYCEbiq07cN2u4I0SA5YuAHJFtyj1QHpEOvLnYwqZcxvJRsQteuHbVJpGnhFTkhKISl+CPVV4SAdlEuZf5N4cKi+dUgqJTyqFHjCyONoCupbVMiYT0g8MBo/ARrieFVAOFFbZBJycZHmUyios2/wFGDg7VXtNuZUB0/l3MPi9Cfq607atHbrEmhgjgreEhKUl3vSDsiYP5BUMzf0tFvf4lWnIDD1olXImT+UWKE9Kdcud2GUqMrV6tebl6XFPzPFpaYZHHp4KNeIyjUgfb1ZGFV/Pqk3dQpnRy9yOYRQ5SqiPhnzXyUcGF16uaGkl6X3/7HxeW9KifYEaCSo/1Zeb2xK9ejy8ujI1P9NIjShUikXf4L0PwCbNEV/3ko1AAAAAElFTkSuQmCC)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.FISH {
    background-size: 21.3px 21.3px;
    background-image: url(data:image/png;base64,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)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.LIVE {
    background-size: 16.4px 17.1px;
    background-image: url(data:image/png;base64,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)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.RNG {
    background-size: 29.9px 16.1px;
    background-image: url(../img/rng-icon.0b608136.png)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.SPORTS {
    background-size: 21.3px 21.3px;
    background-image: url(data:image/png;base64,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)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.ESPORTS {
    background-size: 28px 25px;
    background-image: url(data:image/png;base64,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)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.PVP {
    background-size: 21.3px 20.6px;
    background-image: url(../img/pvp-icon.ff7490a3.png)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-icon.COCKFIGHT {
    background-size: 21.3px 21.3px;
    background-image: url(../img/cockfight-icon.6ad81d0d.png)
}

.game-menu-container .game-menu-swiper .swiper-slide .game-menu-item .menu-title {
    line-height: 25px
}

.game-menu-container .game-menu-swiper .swiper-nav {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    transition: all .3s ease;
    display: flex;
    justify-content: center;
    align-items: center
}

.game-menu-container .game-menu-swiper .swiper-nav .svg-icon {
    width: 24px;
    height: 24px
}

.game-menu-container .game-menu-swiper .swiper-nav.swiper-button-disabled {
    opacity: .5;
    cursor: not-allowed
}

.game-menu-container .game-menu-swiper .menu-prev {
    left: -30px;
    display: none
}

.game-menu-container .game-menu-swiper .menu-next {
    right: -30px;
    display: none
}

.game-classify-menu {
    position: relative;
    width: 95%;
    color: #98a0ac;
    height: 34px;
    margin: 26.5px auto 0
}

.game-classify-menu .swiper-container {
    height: 100%
}

.game-classify-menu--item {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    cursor: pointer;
    padding: 0 10px
}

.game-classify-menu--item:hover {
    color: #fff
}

.game-classify-menu--item.on {
    color: #fff;
    background-color: #1678ff;
    border-radius: 8px
}

.game-classify-menu .swiper-slide {
    width: auto
}

.game-classify-menu .swiper-nav {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    transition: all .3s ease;
    display: flex;
    justify-content: center;
    align-items: center
}

.game-classify-menu .swiper-nav .svg-icon {
    width: 24px;
    height: 24px
}

.game-classify-menu .swiper-nav.swiper-button-disabled {
    opacity: .5;
    cursor: not-allowed
}

.game-classify-menu .menu-prev {
    left: -30px
}

.game-classify-menu .menu-next {
    right: -30px
}

.game-vendor-menu {
    font-size: 18px;
    position: relative;
    width: 100%;
    color: #98a0ac;
    height: 36px
}

.game-vendor-menu .swiper-container {
    height: 100%
}

.game-vendor-menu--item {
    height: 100%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 12px
}

.game-vendor-menu--item:hover {
    color: #fff
}

.game-vendor-menu--item.on {
    color: #1678ff
}

.game-vendor-menu .swiper-slide {
    width: auto
}

.game-vendor-menu .swiper-nav {
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    transition: all .3s ease;
    display: flex;
    justify-content: center;
    align-items: center
}

.game-vendor-menu .swiper-nav .svg-icon {
    width: 24px;
    height: 24px
}

.game-vendor-menu .swiper-nav.swiper-button-disabled {
    opacity: .5;
    cursor: not-allowed
}

.game-vendor-menu .menu-prev {
    left: -30px
}

.game-vendor-menu .menu-next {
    right: -30px
}

.zoom-in-top-enter-active[data-v-b3659e94], .zoom-in-top-leave-active[data-v-b3659e94] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-b3659e94], .zoom-in-top-leave-active[data-v-b3659e94] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-b3659e94], .zoom-in-bottom-leave-active[data-v-b3659e94] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-b3659e94], .zoom-in-bottom-leave-active[data-v-b3659e94] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.home-game-lobby[data-v-b3659e94] {
    margin-top: 18px
}

.home-game-lobby .title-wrap[data-v-b3659e94] {
    display: flex;
    justify-content: space-between
}

.home-game-lobby .game-title[data-v-b3659e94] {
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #9dabd0
}

.home-game-lobby .game-title-icon[data-v-b3659e94] {
    width: auto;
    height: 25px;
    margin-right: 8px
}

.home-game-lobby .game-vendor-menu[data-v-b3659e94] {
    /*width: 276px;*/
    width: 630px;
}

.home-game-lobby .game-filter[data-v-b3659e94] {
    display: flex;
    -webkit-column-gap: 44px;
    column-gap: 44px
}

.home-game-lobby .search-game[data-v-b3659e94] {
    display: flex;
    -webkit-column-gap: 8px;
    column-gap: 8px
}

.home-game-lobby .filter-icon[data-v-b3659e94] {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    background-color: #252c39;
    cursor: pointer;
    transition: all .2s ease-in
}

.home-game-lobby .filter-icon.on[data-v-b3659e94] {
    background-color: #1678ff
}

.home-game-lobby .filter-icon svg[data-v-b3659e94] {
    width: 22.2px;
    height: 22.2px;
    fill: #fff
}

.home-game-lobby .game-list[data-v-b3659e94] {
    margin: 20px auto 0;
    width: 1450px;
    display: grid;
    grid-template-columns:repeat(6, 164.7px);
    gap: 20px 19.4px
}

.home-game-lobby .no_data[data-v-b3659e94] {
    display: flex;
    align-items: center;
    height: 200px
}

.home-game-lobby .no_data p[data-v-b3659e94] {
    width: 100%;
    font-size: 30px;
    text-align: center
}

.home-game-lobby .search-form[data-v-b3659e94] {
    position: relative;
    height: 36px;
    font-size: 16px
}

.home-game-lobby .search-form .search-input[data-v-b3659e94] {
    width: 220px;
    height: 100%;
    padding: 17px 50px 17px 12px;
    border-radius: 6px;
    border: none;
    background-color: #252c39;
    outline: none;
    color: #fff
}

.home-game-lobby .search-form .search-input[data-v-b3659e94]::-webkit-input-placeholder {
    color: #5c677a
}

.home-game-lobby .search-form .search-input[data-v-b3659e94]:-ms-input-placeholder {
    color: #5c677a
}

.home-game-lobby .search-form .search-input[data-v-b3659e94]::placeholder {
    color: #5c677a
}

.home-game-lobby .search-form .search-btn[data-v-b3659e94] {
    position: absolute;
    top: 0;
    right: 0;
    cursor: pointer;
    color: #fff
}

.home-game-lobby .paginate-wrapper[data-v-b3659e94] {
    margin-top: 20px
}

.home-game {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px
}

.winner-wrapper {
    width: 620px
}

.winner-title {
    font-size: 23px;
    color: #fff;
    display: flex;
    align-items: center
}

.winner-title .icon-winner-board {
    margin-right: 10px;
    width: 51px;
    height: 45px
}

.winner-board {
    margin-top: 26px;
    width: 100%;
    height: 778px;
    position: relative;
    padding: 11px 0;
    font-size: 20px;
    color: #fff;
    border-radius: 14px;
    background-color: #10161f;
    overflow: hidden
}

.winner-board .list-title {
    padding: 0 30px;
    font-size: 23px;
    color: #596476;
    display: flex;
    justify-content: space-around
}

.winner-board .list-title-item {
    flex: 0 0 30%;
    text-align: center
}

.winner-board .list-warp-container {
    margin-top: 10px;
    width: 100%;
    height: 100%
}

.winner-board .list-warp-container .list-warp {
    overflow: hidden
}

.winner-board .list-warp-container .win-item {
    height: 43px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 30px;
    position: relative;
    margin-bottom: 8px;
    cursor: pointer;
    transition: .2s
}

.winner-board .list-warp-container .win-item:after, .winner-board .list-warp-container .win-item:before {
    content: "";
    width: 33px;
    height: 100%;
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: contain;
    position: absolute;
    top: 0
}

.winner-board .list-warp-container .win-item:before {
    left: 0
}

.winner-board .list-warp-container .win-item:after {
    right: 0;
    -webkit-transform: rotateY(.5turn);
    transform: rotateY(.5turn)
}

.winner-board .list-warp-container .win-item .rank img {
    width: 30px;
    height: 41px
}

.winner-board .list-warp-container .win-item > div {
    flex: 0 0 30%;
    text-align: center
}

.home-container {
    padding: 25.7px 0 0;
    max-width: 1085px
}

.home-container .home-banner {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 22px
}

.agent-login {
    position: absolute;
    top: 100px;
    right: 0
}

.agent-login .agent-login-bg {
    width: 465px;
    padding: 29px 32px;
    background: #fff;
    box-shadow: 0 8px 25px 0 rgba(0, 0, 0, .06);
    color: rgba(165, 169, 179, .8)
}

.agent-login .agent-login-bg .form-title {
    margin-bottom: 37px;
    font-size: 24px;
    font-weight: 700;
    line-height: 1.5;
    text-align: center;
    color: #414755
}

.agent-login .agent-login-bg .btn-wrap {
    padding-top: 40px
}

.agent-login .agent-login-bg .form_item .item_box .input_icon {
    color: #dcdee2
}

.agent-login .agent-login-bg .form_item .item_box input {
    background-color: #fff
}

.agent-login .agent-login-bg .form_item .submit_btn {
    border-radius: 4px;
    box-shadow: none;
    border: none;
    background-image: linear-gradient(91deg, #dccab8, #d2b496 109%)
}

.agent-login .agent-login-bg .form-tip {
    margin-top: 12px;
    font-size: 13px;
    line-height: 1.54;
    text-align: center;
    color: rgba(65, 71, 85, .6)
}

.zoom-in-top-enter-active[data-v-c4cafe56], .zoom-in-top-leave-active[data-v-c4cafe56] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-c4cafe56], .zoom-in-top-leave-active[data-v-c4cafe56] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-c4cafe56], .zoom-in-bottom-leave-active[data-v-c4cafe56] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-c4cafe56], .zoom-in-bottom-leave-active[data-v-c4cafe56] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.agent-container[data-v-c4cafe56] {
    width: 100%;
    background-color: #f7f7f7
}

.agent-container .agent-top[data-v-c4cafe56] {
    position: relative;
    height: 620px;
    width: 100%;
    margin-bottom: 35px
}

.agent-container .agent-top .top_bg[data-v-c4cafe56] {
    position: absolute;
    height: 600px;
    width: 1920px;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background: url(../img/agent-banner.4e06bb97.png) no-repeat 50%/100% 100%
}

.agent-container .agent-top .content[data-v-c4cafe56] {
    height: 100%;
    position: relative
}

.agent-container .agent-top .content .top-title[data-v-c4cafe56] {
    position: absolute;
    left: -12px;
    top: 210px;
    font-family: YouYuan
}

.agent-container .agent-top .content .title[data-v-c4cafe56] {
    width: 520px;
    height: 100px;
    color: #d2b496;
    font-size: 60px
}

.agent-container .agent-top .content .sub_title[data-v-c4cafe56] {
    height: 36px;
    line-height: 36px;
    color: #414755;
    font-size: 36px;
    opacity: .6
}

.agent-container .agent-main[data-v-c4cafe56] {
    padding-bottom: 30px
}

.agent-container .agent-main .main-title[data-v-c4cafe56] {
    height: 57px;
    margin: 50px 0 30px 0;
    font-size: 40px;
    font-style: italic;
    color: #414755;
    text-align: center
}

.agent-container .agent-main .promote[data-v-c4cafe56] {
    width: 100%;
    height: 437px;
    display: flex;
    justify-content: space-between
}

.agent-container .agent-main .promote > div[data-v-c4cafe56] {
    background: hsla(0, 0%, 100%, .7);
    border-radius: 20px;
    position: relative
}

.agent-container .agent-main .promote .box[data-v-c4cafe56] {
    width: 340px;
    height: 392px;
    margin-top: 33px
}

.agent-container .agent-main .promote .box img[data-v-c4cafe56] {
    margin: 88px 0 0 115px;
    width: 110px;
    height: 110px
}

.agent-container .agent-main .promote .b_t[data-v-c4cafe56] {
    width: 100%;
    position: absolute;
    height: 33px;
    line-height: 33px;
    text-align: center;
    top: 248px;
    font-size: 24px;
    color: #414755;
    font-weight: 600
}

.agent-container .agent-main .promote .desc[data-v-c4cafe56] {
    width: 100%;
    position: absolute;
    line-height: 23px;
    text-align: center;
    top: 289px;
    font-family: PingFangSC-Regular;
    font-size: 15px;
    color: #414755;
    opacity: .6
}

.agent-container .agent-main .promote .mid[data-v-c4cafe56] {
    width: 430px;
    height: 437px;
    margin-top: 0
}

.agent-container .agent-main .promote .mid img[data-v-c4cafe56] {
    margin: 98px 0 0 154px;
    width: 123px;
    height: 123px
}

.agent-container .agent-main .promote .mid .b_t[data-v-c4cafe56] {
    top: 275px;
    line-height: 37px
}

.agent-container .agent-main .promote .mid .desc[data-v-c4cafe56] {
    top: 320px
}

.agent-container .agent-copyright[data-v-c4cafe56] {
    padding: 40px 0 50px;
    font-size: 13px;
    color: #808695;
    text-align: center
}

.agent-center-container .user-center-bg {
    padding-top: 0
}

.agent-center-container .agent-center-header {
    background-color: #fff;
    height: 80px
}

.agent-center-container .agent-center-header .agent_info, .agent-center-container .agent-center-header .innner {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%
}

.agent-center-container .agent-center-header .agent_logo img {
    height: 60px
}

.agent-center-container .agent-center-header .agent_info {
    color: #414755
}

.agent-center-container .agent-center-header .agent_info .time {
    font-family: DINPro;
    margin-right: 30px;
    font-size: 14px;
    font-weight: 500
}

.agent-center-container .agent-center-header .agent_info .time .time-zone {
    margin-right: 10px
}

.agent-center-container .agent-center-header .agent_info .name {
    font-size: 14px;
    font-family: DINPro;
    font-weight: 500;
    margin-right: 10px
}

.agent-center-container .agent-center-header .agent_info .logout {
    padding: 0 10px;
    font-family: PingFangSC-Medium;
    font-size: 14px;
    font-weight: 500;
    height: 12px;
    line-height: 12px;
    border-left: 1px solid #414755;
    text-align: center;
    cursor: pointer
}

.agent-center-container .agent-center-header .agent_info .modal_btn {
    margin-left: 30px;
    width: 180px;
    height: 34px;
    border-radius: 23px;
    background-color: rgba(77, 84, 102, .2);
    display: flex;
    padding: 2px;
    justify-content: space-between
}

.agent-center-container .agent-center-header .agent_info .modal_btn span {
    display: inline-block;
    width: 88px;
    text-align: center;
    color: hsla(0, 0%, 100%, .6);
    height: 30px;
    line-height: 30px;
    border-radius: 21px;
    opacity: .7;
    cursor: pointer
}

.agent-center-container .agent-center-header .agent_info .modal_btn span.active {
    opacity: 1;
    color: #fff;
    background-image: linear-gradient(90deg, #dccab8, #d2b496)
}

.agent-center-container .content-left .acmc_icon.agentTeamOverview {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAe1BMVEUAAADbx7PUuJzZw63WvKPVup7YwqvaxbDaxrLZw6zWvaTVup/YwKjXwKjXwKjYwKjYwKnXv6fZw63WvKPbx7PUuJzbyLTTt5vbx7PUuJzZw63WvKPZxK/bx7PUuZ7bx7PUuJzVu6DaxrHYwKjWvaTZxK/UuZ7YwqvZw613dOGVAAAAIXRSTlMA0NB/f/lG/PhtbV1QMC0g8/Px8fDw5ubFxZWVXl1DNzeO2sFrAAAAbElEQVQY043IRxKDMBAAwXGQAJOcsw2I+P8XUluCYo/MbRpfdDwdUEV9XTdKvoNAE8+fBB6qxL8N2gnMX74I3QyVKSG7ugW6W8bdaegepBsN2xQEzjbP7UUAD2+Aj4KdwH41PDW8gF+4gIkZAchnEmtnwgbRAAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.agentAnnouncement {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAPCAMAAADarb8dAAAAk1BMVEUAAADWvqXWvaPVuZ7WvKPVup/Vup/Xv6faxbDXv6fbyLTWvqXTt5vUuJzVup/axrLbx7PVup/YwqvVup/VuqDUuZ7UuZ7Xv6bXv6bWvKPZxK/Vup/Yv6fZxK/ZxK/Zw63Zw63UuJzVup/Tt5vYwKnUuZ7byLTbyLTUuJzVu6HYwKjaxbDYwqvZw63byLTaxrLTt5vyOhKqAAAAKnRSTlMA8AMVDbycg/z8+unp2dbUzMezr5OKUDw3Kh4RCurm08+3pY+JemtoHAb9f9FNAAAAgUlEQVQI102PVxaCUAxEx7yCBVCkKtj7Q9H9r85zCCX3b+7HJAOGrjMPgmrt3ELk7fHt3ATQxLnYf1jY6AncV8tp3YpNopKgxLz51ix0HFF8QTMIUJgVvhSwN/WT4hVmuS86SmPolOIxXkmVDTQA5Luu42xU9+mBRYUezVsE5PVr/+C6EXEe+4NPAAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.depositv2 {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAARCAMAAADnhAzLAAAAvVBMVEUAAADUuJ7axK3WvKPXvqXYwKnVuZ7ZwqvYwarXvaTUuJzbx7LVuqDaxrHWvKPUuZ7ax7LVu6HWvKPbyLTaxrHYwavZxK7bx7PWvaPUuZ/Zw6zUuZ/Zw6zax7LVuqDUuJ3Zw6zZxK7WvqXWvqXYwKnVuqDaxrHWvaPXv6fYwavbyLTVu6LXv6fVuqDVuqDbyLTZxK7VuqDaxa/byLTbx7PUuZ/WvKPZxK3YwarXv6baxrDbx7LVuqDUt5zUuZ9Kheh7AAAANnRSTlMA/pAjDdDCYBED8MzFnYVcUDQZFPr59/Px7+3o5OPczsfGwrqwrqifjIWCe3lzbWBYVE1FPC5GM2P/AAAAoElEQVQY01XOVRKDQBBF0YcFJ0jc3V0Gz/6XlYEQGO7nqequh7zbqL0SwSZ2/CA41UjxKVlQjxO3tIcsyy62ZP0H1TZnkiQtCbkUIuhRdhiGhNyFXLxxVJLRz+xpxhUln4YKHLoMGRbXaOIaM3QWtTSFx1LCcZSg135xqQa8ajR1FAB2q6KhgLxNRW/84iN/sOd3C0piQQo/5wE4PbqJ9gVguSQqmsW6wgAAAABJRU5ErkJggg==)
}

.agent-center-container .content-left .acmc_icon.withdraw {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAOCAMAAAAVBLyFAAAAjVBMVEUAAADaxrHUuZ7VuqHZwqzWvKLYwarUt5zbyLTaxrHXvqXUuJzaxrHVu6HUuJzXvqfbx7Pax7Laxa/Tt5vUuJ3byLTbyLTVup/bx7Pbx7PUuZ/Zw6zVuqDWvaPVu6LWvaPZxK7ZxK7YwavUuJ3Vu6LYwKjUuJzWvqTax7Laxa/VuqDbyLTZw6zYwavVu6L4pbesAAAAJXRSTlMAgIChoIGA6dqgoKBhYD/y6OHg2sygQi/v6ePg4MWXkIdqVhUMprCcRgAAAINJREFUGNONj9sSQzAURU+lqVwppXq/C5Ly/5/nhCfGg/W4Zvaa2XDc13XT/NuyrKrOWhNGwNxUmQPkOed8iwghCCFSSlhgM7BDKKWBB9y8ZdaqE6ZeoyIBzbw6K6Xeg3oWWn/DyVA/rC0yVAw8H1T3H0CEQ+aSJE2vsW/FtwseWrjdA/uuGoUVg4C+AAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.agentDownlineTransactionDetailsCopy, .agent-center-container .content-left .acmc_icon.transactionHistory {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAAzFBMVEUAAADVuZ/YwarVuJ3bx7LYwarZxK7WvKLaxa/Ywaraxa/Xv6bWvKLZxK7YwanWvKLVuZ7Xv6fUuJ3WvaPXvqXVu6HUuJzZxK7XvaTVuZ/WvKPWu6HXvqbVuqDZw6zWvqXUuJ3VuZ7UuZ3YwarYwqzWvKLbx7PUt5zYwarbx7PbyLTax7LbyLTYwarTt5vYwarVuZ/WvKLVuZ/ax7LXwKjWvaTWvKLUuJ3Tt5vXvqbbyLTax7LYwqvWvaPZxa/Zw63Vu6HXwKjXvqbVuJ1S4rJSAAAAOnRSTlMAoCH58KBA/MDAoJ4K8PDe3NjWzsC1s62srJRzcE0vGAkGAfvw6ejj3tjQ0MvLxrh3WFNMPj46MhwPZlrpygAAAKtJREFUGNNVzOUSglAQhuGVkDToTru7gKMo939PMg4H4f23z8x+sEzS9PnKsvcHIcaBsiRZsSzLcZyqTvLOTxaAu1aSGDzP0zQtCAqW/04lU58ooxtCnbplBpbW10Dbt0S6lFdTpIfoWPatIWdRQXlBerXMwUUmcSRnMZYdmIgA0Is7lg3Y6ADhkAyxjAKfYbRhIYv1sgDeICflQoc1RVG93rjfdyEOosjafgFIJycoYi0N5QAAAABJRU5ErkJggg==)
}

.agent-center-container .content-left .acmc_icon.registerCenter {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAAAvVBMVEUAAADZw63WvKPWvKLWvKLXv6bUuJ3Vu6HWvKHWvqTWvKLZwqrYwqvWvqTbyLPVvKLaxa/Ut5zaxa/UuJ3VuZ/ZwqvUt5zVu6LVu6HWvqTUuJ3UuZ7VvKLUt5zYwavZxK7axa/byLTXv6fZxK7axa/Zw6zXv6fbyLTbx7PVu6LaxrHZxK7Vu6LUuZ/Vu6LWvaPbx7PVuqDbyLTWvqXVu6LbyLTYwarZxK3Xv6baxrHbx7Paxa/WvaPUuJ3VuqAshhlAAAAANnRSTlMAMP4f5tSyrYBgSBYP+PTx6t7RvaOIhXBlWldAIxHx3NfUs62mnpmXjG1qVlFINjUsKAoJCAYmL5GDAAAAo0lEQVQY033ORxaCQBRE0W+Tswgo0ZxzJEm7/2Up0JzGCTW8gzoPqimrccxJ0Jo6SeI4TcUWOUlFiKE0qKlwKfUJjTppSmhHaUnoTElZlGT8hR1K4hQauq8jNO8p+2Xaa6tn74qkC/sx8BBgk2c1cQ8WCSeMfdAbWsuF4Ia2yUPekONp8k0NeIvSnEHIDvDvbNZQGoqI5bEZwf3aI2PgKFhiBF+WqSF9/wE5pQAAAABJRU5ErkJggg==)
}

.agent-center-container .content-left .acmc_icon.downlineManagmentView {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAQCAMAAAAs2N9uAAAA0lBMVEUAAADYwKjWu6HZw63YwarYwqrZw63Zw6zaxa/Vu6HZw6vVuqHZxK3UuZ/axa/VuqDZw63Vu6LUuJ3axa/WvaPUuZ7VuqDbxrHVu6HUt5zWvqXaxa/Xv6fYv6jUuJzax7LUuJ3WvqXbyLTaxa/Xv6fYwKnWvaPax7Lax7LaxrHUuZ/ZxK7Xv6fYwKnbyLTTt5vVu6LWvaPbx7PWvaPVuqDaxa/axa/Zw6zTt5vbyLTVu6LZxK7byLTYwKjax7LWvqTaxa/VuqDUuJzZwqzbyLTVu6IzknqLAAAAPXRSTlMAF7H814B0aGVkA/326+LVzb++pKSjnZKSj4duXB0E+fnz7dbVyMW8raennpaJhIR9fHhraWBTSz84MBEEdQxCiAAAALpJREFUGNM9zNUSg1AQA9AtTt0oUqDu7opc5P9/qbtMIU+ZM5MAQFnRlTJQUQ3VBUrHD8MOlW7KmEal5CPFJSwBUoQFHCLBnk3FLpEDmAWSwntecKox1oZsuZpsxBhpfmysaffVB37IS0Rci7Gh6cI+oS9R8LyRWsGv6ABjorosNwXLbhHVIEGqy1UaBgaHFGUkVeOM0mslp374p16D6HVeNgvitLb5BMqtIMhz5/MhFPk8pN3WuryB8gM3VCll3K+J7AAAAABJRU5ErkJggg==)
}

.agent-center-container .content-left .acmc_icon.agentRedRain {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAARCAMAAADaFm2tAAAAgVBMVEXXwKnYwqzWvaXbx7LUuZ7YwKjYwKjVup/YwqzWvaXYwKjVuqDbx7PUuJ3bx7PUuJ3ZxK7WvKLZxK7WvKLbyLTTt5vaxbDaxrHaxbDVuqDYwanXv6fbx7PZxK7ZxK7WvKLbx7PUuJ3axrHbyLTUuZ7Zw63WvaTVuqDTt5vYwanXv6dXfAcxAAAAInRSTlMd8vLa2qWej4KCbkoYGOXl4ODd3aCgkot5eWVlS0lBQTg4dRGBKAAAAF5JREFUCNfNzcURgDAQAMDD3d3hYiT9F0gykyJ47msh9xAZk4pzQf0C+lvTCUNX8xmA7BO2kKbQiSh7gZT5fMZSJddSVJrEGw/DLPKpIWIN6waNoJbMCQKXW9roT/wAe7Id56x5OzkAAAAASUVORK5CYII=)
}

.agent-center-container .content-left .acmc_icon.palStatementsAgentCopy {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAARCAMAAAAMs7fIAAAAmVBMVEUAAADUuJzbyLTax7LZxa/Vu6DUuJ3Yv6faxbDWu6HZxa7WvKLYwKjaxrDZxK7Vu6Hax7LXv6fVup/axrHYwKnUuJ3Vu6HTt5vYwqzWvaTYwqzYwarVuZ/byLTZw63bx7PUt5zVuZ/VuZ/axrHVuZ/YwqzaxrHVuZ/Zw63WvKLbx7PXwKjUt5zZxa/YwarXvqbWvaTVuZ/UuJ0aWWsaAAAAKHRSTlMAHyCgwMCgYPfhzfjs4t7V0qCYbxj05snHx7CgoJ2QgYFqZWFQGQkGdfmESwAAAINJREFUGNN9zscSgjAUheGDEUhCL/beEzC29384MbLQywxn8898q4O0qoxR6lbfH1o/X9cJVkRyDIg4jcgiJsIwbCU5ukRceL/i40/mkgr8fuERV3FYh0IHYmSFPuwKNoylarrIsplOlt4Wn50jfmpSBuKC7wqzts31vpWDLG2d8Q7AG4Z9G6sC+wL9AAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.personalHistoryCopy {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAANCAMAAACTkM4rAAAAyVBMVEUAAADYwKnaxrDWu6LUuJzUuZ3axrDWvKLZwqzbyLTaxa/VuqDbyLPVu6HUuJzYwKjVuqDYwKjaxa/Vu6Daxa/axrDUuZ/VuqDXwKjXwKjYwavWvqXbyLTTt5vbx7PZxK7Vu6LTt5vUuZ/Vu6LaxrHWvaPWvaPVu6LbyLTaxrHTt5vbyLTbx7PUt5zaxa/ZxK7UuZ/byLTTt5vaxrHaxa/ZxK7Tt5vax7LUuJzYwarWvqTbx7PXv6fUuZ/Zw6zVuqDaxa/axa7Vu6LDw1d2AAAAN3RSTlMAgBsZ89fLpgzu6dzYzca3sKmBgGJOTDsiCvf39vb19fXs693c2tLLxLKlpIaGcnBfW1tUMSoOffyXcAAAAKxJREFUCNc9zVUSg0AQRdEXNO5Y3N2th5kQCPtfVAZIcT9PddcDoGyjccsFXHMSNlXIilWKvp9eo9HncRjoRUkHSsj333xpVwJ2lEclolM3JRPmTNNeuApqP56FhPg6VB12w17QXVWshFaOfumwHaqCqHtOH1toBozpGEgql1Pi81DSEELkizxOiGVktAuyTi2jkiTDQ5pXkzTFRtBCwT+1wlgd1sjIBVDqmv0DMtEfhISZeqoAAAAASUVORK5CYII=)
}

.agent-center-container .content-left .acmc_icon.agentRevenueReport {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAgVBMVEUAAADXvaTUuJzVu6DaxrLYwKjZw6zaxbHZw63YwKjWu6HYwKnVvKLbx7PbyLTaxbDVu6DaxbDZxK7Tt5vWvKLWvqXWvKPUuZ7UuZ7axrLZw63UuZ7axrLWvqXbyLTbyLTVup/bx7PUuJzYwKjVup/Zw6zWvqXWvKPVu6HaxbDZxK8o0lY0AAAAIXRSTlMAYOy/kJDh2NKWkIDv6uTj4aZgSzD84drGxcOvqqNSTDB3BgvGAAAAfklEQVQY033PSQ6DMAwF0JTQEFJahtK5DMHM9z8gxoAEROIvsnjSj22WXnXXVxVA3bRlad3ZTa+hsJjeQoHg2P73jFFKvQg+kRNGJ8obwaxgR0oJ4HHOPYKQYVxI4jjnBPYIl6lyCC6+P4L/9MfjGQTzFF8IsYw19zCPy3bnD8PcGiMVULHrAAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.agentDividendRecordCopy2 {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAOCAMAAAAVBLyFAAAAQlBMVEXWvKPVuZ7UuJzVuqDWvKPVu6LUuZ/byLTTt5vbx7PUt5zVu6LWvqXWvaPbyLTTt5vbx7PaxK7YwarXvqbUuJzVuqBMNqHeAAAAEHRSTlP+kYCATurq0dHKyrSvPCgotTyIzQAAAE1JREFUGNOFz0sOwCAIBNAR7f8Hive/atMdFRNZvmTIDO6NWURyLgWoVfcHC/9JV3BLOqIjfGcIV4qRiAydaQqzCXryQf9+3MsP6sx+AUTmDet/b6q/AAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.securityCenter {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAUCAMAAAC3SZ14AAAA1VBMVEUAAADYwanaxK7WvaPZwqzXv6fVuZ/bx7HWvKPXv6bXv6baxa/Vu6Haxa/VuqDWvqXYwavXv6fTt5vYwKjUt5zbx7PZw6zWvaPYwKnZw6zWvaPaxK7byLPZxK7XwKjZxK7YwavVu6LbyLTTt5vZxK3WvKPYwKjbx7PTt5vUuJzXwKjbyLTUt5zYwKjXwKjYwKjYwKjUuJ3ZxK7Vu6LWvaPVuqDUt5zbx7PbyLTTt5vUuZ/byLTYwavaxrDXv6bbx7PUuJ3ZxK3Vu6HUuZ/YwKnYwavWvaNNHtPvAAAAPXRSTlMAGBAP+uORUVH3y8nJxMOGUCQY7Ofl1dTJxMSmkpCNhYWFgoJ+fnZzclA5KSkcDAgF/PHx5qeYl4yLdHJFuDsUjQAAAMxJREFUGNNVz1cSgkAQBNAmixlzzjnnDAoi9z+SuyMW0H/7qrprFn5mijJDOKuB7Fr6cOU/JW1c39kf17K8/fE2lQCh+DRN+0eO834Vchg/o/SaoEdUzQpCrEbUR4NTU+xU4hlJ5aTiwigllnixLKUZXamotflWJt6NUfHOSEgwGhmHZI7mNaL6Y2QoHtEUcyrKWUOxPCougCLNy7o/XwBwjh5xYjQ3w6duF2BphD+kgmedCCi5BkVM/Sktws9mIHPShxsEWbby+dYSlC8rOjM+n1cE0wAAAABJRU5ErkJggg==)
}

.agent-center-container .content-left .acmc_icon.messageV2 {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAMCAMAAABYzB2OAAAAflBMVEXYwKjXwKjXwKjaxK7XwKjaxa7Wu6HYwKjaxrHUuZ/ax7LUuJ3bx7PUt5zbyLTTt5vbx7PUt5zax7LUuJ3axrHUuZ/axa/VuqDZxK7Vu6LYwKjXwKjaxa/VuqDUt5zYwavWvqXXvqXbx7Paxa/YwarVu6HUt5zZw6zTt5vUuZ6zykmhAAAAIXRSTlMgNyccCvLy7erq4+Pc3NDQurqysqWlmZmLi31uLi4cXl79M8RpAAAAb0lEQVQI123PVw6EMBAD0IFQdum9t0xCgPtfEAUEQYLfJ9mywYiQMbZwTimd503EBgQkeVI6hIAeZIpy8FdAdPTiolJ3hST8ddVJdf8XJzFTayQ1mrUpgpbTFiTdQXMkZLKOoKq3bVX/HvGe+nFoB3qZF1WBZO3gAAAAAElFTkSuQmCC)
}

.agent-center-container .content-left .acmc_icon.service {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAMAAABhEH5lAAAA81BMVEUAAADVuZ/YwarWvKPaxrDUuZ7WvKPVu6LYwKjXvqbZxK3XvqbbyLTVuZ7WvKLYwKjWvqTXv6bVu6HYwarXvqXUt5zZxK7bx7Lax7LWvqXaxa/YwKjUuZ/axa/axbDVuqDZw63VuZ/UuJ3Xv6bUuZ/XwKjYwanaxa/WvqXUuJ3UuJ3ax7LWvaPWvKLWvqXVuqDaxa/YwKnZw6zax7LUuJ3VuqDax7LZw6zbx7PUuZ/ZxK7YwavVuqDbyLTYwKnaxrHVuqDbx7PZxK7axa/VuqDYwarax7Laxa/VuqDUt5zbyLTZw6zXv6fWvqXVu6HUuJ3WvaMUh7oIAAAARXRSTlMA+1UG4qygk4IoFgL51rwsGQ4L+ff23tDMxreVjn1ycGpmZFpTQSHx8fDq6eTZ1tDNyr63tLGtpKKamYyFdWdkTEQ7NjRhF+GbAAAA1UlEQVQY003P1bLCMBCA4U1SBaq4HByOG+5eQwrv/zSUNFD+m2y+mUxmgdYaZqzjjyjDvVjB6ZRxQ/r2Z3EmGW4jVyWpEashPrQuty0cLOt4SokrVLnJ2l5yDqWLr5bezYBGuaxzJ1dANYBy4tNmJPgu+vB4SOxtRm+66Lpnz4N9SMn/ogbtiF6rWvi5ciP68AVY/YDSMM8OIuJRPd0Mznjuj94NHZSSrNC5zWGQikLeaKmkV2dbTmSCsUkq5piHR8ZCA9L8FUlEUyuV/xJ28BROqjobryeBJB3JF8nBAAAAAElFTkSuQmCC)
}

.header-nav {
    display: flex;
    align-self: flex-start;
    padding-top: 19px
}

.header-nav .nav-item {
    width: min(5em, 62px);
    font-size: 12px;
    color: #fff;
    text-align: center;
    cursor: pointer
}

.header-nav .nav-icon {
    display: block;
    width: 24px;
    height: 24px;
    margin: 0 auto;
    background-repeat: no-repeat;
    background-size: cover
}

.member-info-container {
    height: 43px;
    display: flex;
    gap: 10px
}

.member-info-container .group {
    padding: 0 4px;
    height: 100%;
    border-radius: 7px;
    background-color: #282f3c;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px
}

.member-info-container .member-info-item {
    cursor: pointer
}

.member-info-container .member-info-item svg {
    width: 35px;
    height: 35px
}

.member-info-container .member-info-item .balance-container {
    height: 40px;
    padding: 0 5px 0 9px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 20px;
    color: #dadada
}

.member-info-container .member-info-item .balance-container .symbol {
    font-weight: 900
}

.member-info-container .member-info-item .balance-container .balance {
    color: #fff;
    flex-shrink: 0
}

.member-info-container .member-info-item .balance-container .eyes {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    width: 16.5px;
    height: 10.7px;
    color: #fff
}

.member-info-container .member-info-item .balance-container .eyes:hover {
    opacity: .8
}

.member-info-container .member-info-item .balance-container .refresh {
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff
}

.member-info-container .member-info-item .balance-container .refresh:hover {
    opacity: .8
}

.member-info-container .member-info-item .balance-container .refresh svg {
    width: 16px;
    height: 16px
}

.member-info-container .member-account {
    margin: 0 5px 0 3px;
    position: relative;
    display: flex;
    align-items: center;
    gap: 13px
}

.member-info-container .member-account:hover .member-menu {
    visibility: visible;
    opacity: 1
}

.member-info-container .member-account:hover .icon-down > svg {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
    transition: all .3s ease
}

.member-info-container .member-account .user-avatar {
    position: relative;
    flex-shrink: 0;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #f0c343;
    display: flex;
    justify-content: center;
    align-items: center
}

.member-info-container .member-account .user-avatar > img {
    width: 30px;
    height: 30px;
    -o-object-fit: contain;
    object-fit: contain;
    border-radius: 50%;
    cursor: pointer
}

.member-info-container .member-account .user-avatar .vip {
    padding: 0 5px 0 2px;
    height: 18.5px;
    position: absolute;
    bottom: 0;
    left: 50%;
    -webkit-transform: translate(-50%, 50%);
    transform: translate(-50%, 50%);
    border-radius: 4px;
    background-image: linear-gradient(278deg, #e8a31d 87%, #dcb05b 0, #ebbb5e 0, #d1992c 0);
    color: #543c00;
    font-size: 20px;
    font-weight: 700;
    font-style: italic;
    display: flex;
    justify-content: center;
    align-items: center
}

.member-info-container .member-account .icon-down {
    flex-shrink: 0;
    width: 16.8px;
    height: 8px
}

.member-info-container .member-account .member-menu {
    visibility: hidden;
    opacity: 0;
    transition: all .3s;
    position: absolute;
    left: 50%;
    top: 100%;
    padding-top: 19px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.member-info-container .member-account .member-menu .member-menu-bg {
    width: 304px;
    padding: 20px 22px;
    border-radius: 21px;
    background-color: #192b39;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .avatar-wrap {
    position: relative;
    width: 68.7px;
    height: 68.7px;
    border-radius: 50%;
    background-color: #f0c343;
    display: flex;
    justify-content: center;
    align-items: center
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .avatar-wrap .avatar {
    width: 62px;
    height: 62px;
    border-radius: 50%;
    -o-object-fit: contain;
    object-fit: contain
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .avatar-wrap .vip-content {
    padding: 2px 8px 0 6px;
    height: 21.8px;
    position: absolute;
    bottom: 0;
    left: 50%;
    -webkit-transform: translate(-50%, 50%);
    transform: translate(-50%, 50%);
    border-radius: 4px;
    background-image: linear-gradient(278deg, #e8a31d 87%, #dcb05b 0, #ebbb5e 0, #d1992c 0);
    color: #543c00;
    font-size: 20px;
    font-weight: 700;
    font-style: italic;
    display: flex;
    justify-content: center;
    align-items: center
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content {
    color: #fff;
    max-width: 100px;
    display: flex;
    flex-direction: column;
    align-items: center
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .account, .member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .id {
    height: 30px;
    font-size: 20px;
    color: #56697a;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    line-height: 1.2;
    display: flex;
    align-items: center
}

@supports (-webkit-line-clamp:2) {
    .member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .account, .member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .id {
        display: -webkit-box !important;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal
    }
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .account svg, .member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .id svg {
    margin-left: 15px;
    width: 18px;
    height: 18px;
    pointer-events: none
}

.member-info-container .member-account .member-menu .member-menu-bg .main-info .info-content .copy-btn {
    cursor: pointer
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item {
    padding-left: 25.4px;
    width: 100%;
    height: 48px;
    border-radius: 10px;
    font-size: 17px;
    color: #879ec0;
    white-space: nowrap;
    background-color: #263548;
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 28.4px
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item:hover {
    background-color: #2b4567
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item .svg-icon.account {
    width: 22.7px;
    height: 21.8px
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item .svg-icon.betting-record {
    width: 25.1px;
    height: 28.6px
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item .svg-icon.message {
    width: 20.5px;
    height: 19px
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item .svg-icon.cs {
    width: 24.3px;
    height: 25.9px
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item .svg-icon.agent {
    width: 22.8px;
    height: 29.6px
}

.member-info-container .member-account .member-menu .member-menu-bg .member-menu-item .svg-icon.logout {
    width: 21.3px;
    height: 24.2px
}

.header-game-container {
    width: 900px;
    margin: 0 auto
}

.header-game-container.large {
    width: 1200px
}

.header-game-container .header-game-list {
    width: 100%;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px
}

.header-game-container .header-game-list .header-game-item {
    position: relative;
    width: 158px;
    padding: 10px 0;
    cursor: pointer
}

.header-game-container .header-game-list .header-game-item:hover {
    background: linear-gradient(0deg, hsla(0, 0%, 84.3%, 0), rgba(111, 158, 226, .34))
}

.header-game-container .header-game-list .header-game-item .vendor-img {
    display: block;
    width: 100%;
    min-height: 158px
}

.header-game-container .header-game-list .header-game-item .vendor-name {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 17px;
    font-size: 24px;
    font-weight: 700;
    color: #fff;
    text-align: center
}

.drop-menu-container {
    display: none;
    position: absolute;
    left: 0;
    top: 63px;
    width: 100%
}

.drop-menu-container.active {
    display: block
}

.drop-menu-container .drop-menu-bg {
    background-image: linear-gradient(180deg, #748bad, rgba(215, 225, 237, .6))
}

.drop-menu-container .drop-menu-item {
    -webkit-animation-name: fadeInDown;
    animation-name: fadeInDown;
    -webkit-animation-duration: .8s;
    animation-duration: .8s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.header-holder {
    height: 63px
}

.header-container {
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2;
    width: 100%;
    background-color: #181f2b
}

.header-container .header-content {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    height: 63px
}

.header-container .header-content .header-left {
    display: flex;
    flex-shrink: 0
}

.header-container .header-content .header-logo {
    padding-top: 14px
}

.header-container .header-content .header-logo img {
    display: block;
    width: 56px;
    height: 56px
}

.header-container .header-content .game-nav {
    position: relative;
    display: flex;
    height: 100%;
    padding: 0 0 0 15px
}

.header-container .header-content .game-nav .game-nav-item {
    position: relative;
    min-width: 60px;
    padding: 25px 10px 14px;
    font-size: 16px;
    color: #fff;
    text-align: center;
    cursor: pointer
}

.header-container .header-content .game-nav .game-nav-item.active:after, .header-container .header-content .game-nav .game-nav-item:hover:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: 35px;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: 33px;
    height: 4px;
    border-radius: 4px;
    background-color: #fff
}

.header-container .header-content .game-nav.game-nav-VI .game-nav-item {
    font-size: 12px
}

.header-container .header-content .header-right {
    margin-right: 25px;
    height: 100%;
    flex-shrink: 0;
    display: flex;
    align-self: flex-start
}

.header-container .header-content .header-right .right-content {
    width: 250px;
    height: 100%;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    align-self: flex-start
}

.header-container .header-content .header-right .login-register {
    display: flex;
    gap: 15px
}

.header-container .header-content .header-right .login-register .btn-login {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 108px;
    height: 39px;
    padding: 0 10px;
    border-radius: 7px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    background-color: #1678ff
}

.header-container .header-content .header-right .login-register .btn-login:hover {
    opacity: .8
}

.header-container .header-content .header-right .login-register .btn-register {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    min-width: 158px
}

.header-container .header-content .header-right .login-register .btn-register:hover {
    opacity: .8
}

.header-container .header-content .header-right .notice, .header-container .header-content .header-right .service {
    min-width: 44px;
    height: 44px;
    border-radius: 7px;
    background: #212937;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 10px;
    cursor: pointer
}

.header-container .header-content .header-right .notice svg, .header-container .header-content .header-right .service svg {
    width: 25px;
    height: 23px
}

.header-container .header-content .header-right .service {
    margin: 0
}

.footer-container {
    font-size: 16px;
    text-align: center;
    background: #181f2b;
    padding-top: 35px
}

.footer-container .footer-content {
    display: flex;
    flex-direction: column;
    align-items: center
}

.footer-container .footer-top {
    display: flex;
    width: 100%;
    gap: 50px;
    border-bottom: 1px solid #3a4557;
    padding: 0 0 44px 13px
}

.footer-container .footer-top .list-wrap {
    display: flex;
    flex-direction: column;
    font-size: 15px;
    color: #5c677a;
    width: 230px;
    text-align: left
}

.footer-container .footer-top .list-title {
    font-size: 18px;
    color: #9dabd0
}

.footer-container .footer-top .list-content {
    display: flex;
    flex-direction: column;
    gap: 14px;
    margin-top: 22px
}

.footer-container .footer-top .list-content .br-hmenu-nav, .footer-container .footer-top .list-content .list-item {
    cursor: pointer
}

.footer-container .help-list li {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer
}

.footer-container .help-list li:hover {
    opacity: .8
}

.footer-container .follow-list {
    margin-right: 172px;
    width: 200px
}

.footer-container .follow-list .list-content {
    line-height: 1.5
}

.footer-container .social-info .list-content {
    flex-direction: row;
    gap: 16px
}

.footer-container .social-info .channel-item {
    width: 46px;
    height: 46px;
    cursor: pointer
}

.footer-container .social-info .channel-item.fb {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAABpCAMAAAAOXP0IAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA2UExURUdwTENZlEJZlLC700NalENZlODk7kRZlERYlkNZlP///0NZlOfp8YuYvXKCr77F2ldroKSvy4OajbAAAAAKdFJOUwCXf7/v37/AHFdVaJuzAAAByUlEQVRo3u3a2W7DIBAFUHCg7Ab+/2fbpk2cONgwLCM15b5G1tGAZSJxCdlHKio4Mw3hgipJMpGLMH3CTzG5MNMx9NDq63xnSQ8kTP/wxFiKmRFh6mXlzKgsWNCOUmZkHhZQsqES214LbsZGYGzS01bJ4dBt/eh46WcohJF+h1IY0nUogSIJpMX7iuyzeGGN3urnuP2HosOb57xOxO6OxeZtClGnY/cb1fglWq0ukzgZM9CrxJqkYHWxZMgoqKt0CvWUokaSgsaSPJaUG6mf5NAkjyZZNCkH6ThSsi4cP9BTsuHsgZ775A2WtE5pSlP631KwW5Jf2Me4Fil7+nU7n0CSf0Mpokl4b8SKJgU0yWBJFk3ybyhFNMmhSesfkVzDSw48c8M9KTS6LaHb6e401v+IKU1pSlOa0pSGSgxJYrV3NWBJ1N4/VUgUSaK194RgSdXefYIlWXufC5VE9R01VFLVNQygxOvrEUBpqe+WwCTe0JeBSffOjBgsUdJQmYFIj5Ut+FYBJPbUDQNT5RLbldAkHySxl7YbkCqVUrU6SQdIB7VExTtLQh0WLcutAunEuVq0DFszEqeKZCPVQkU2l499LrefaLIO+wmsirzoLkiHeQAAAABJRU5ErkJggg==) no-repeat 50%/contain
}

.footer-container .social-info .channel-item.yt {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAMAAAApB0NrAAAAOVBMVEUAAAD/////////////////////////////MAD/mYL/sZ//y7//XDf/8u//2M//fWD/Rxz/5d//vq8FxkBRAAAAB3RSTlMAkBXP359AyPy/8QAAAKVJREFUOMut1EsOhCAQRdEqfqWF0Or+F9utbaJB8DHwDs0JkS9tBXZSy3pD/wxLOzY7sfKU3RDLc0xkBBXIQ8NkoXEkuPdNyqrjkWqMFTMORXMuzTLcmkozDvdSw0wXsxTmc3zX5VSxZUR0xkbSBM0Kx4nF/4B55cJox/pktM54v45SXC/7vubUc37eNw4SSwyNpwCN6brv+N3oe3925etjOQ706wsVViIvjdHq+wAAAABJRU5ErkJggg==) no-repeat 50%/contain
}

.footer-container .footer-info {
    margin: 34px 0 32px 0
}

.footer-container .footer-info .icons {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 10px 10px
}

.footer-container .footer-info .icons .icon-img {
    height: 60px
}

.language-select {
    flex-shrink: 0;
    padding: 9px 0;
    width: 100%;
    border-radius: 7.5px;
    border: 1px solid #525d6e;
    position: relative;
    margin-top: 16.5px;
    display: flex;
    justify-content: center;
    align-items: center
}

.language-select .simple-select {
    width: 100%
}

.language-select .simple-select .select-input {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.language-select .simple-select .select-input .language-selected {
    display: flex;
    font-size: 18px;
    font-weight: 500;
    color: #576274;
    padding-left: 33px
}

.language-select .simple-select .select-input .arrow-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 12.8px;
    height: 7.4px;
    -o-object-fit: contain;
    object-fit: contain;
    color: #525d6e;
    margin-right: 13.5px
}

.language-select .simple-select .select-input .flag-icon {
    display: block;
    width: 23px;
    height: 23px;
    -o-object-fit: contain;
    object-fit: contain;
    margin-right: 9px
}

.language-select .select-options {
    width: 100%;
    left: 0;
    bottom: 50px;
    border-radius: 10px;
    background-color: #2b3248;
    overflow: hidden;
    color: #fff;
    font-size: 16px
}

.language-select .select-options .select-option {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 15px 0 40px;
    cursor: pointer
}

.language-select .select-options .select-option .flag-icon {
    margin-right: 12px;
    width: 31px;
    height: 31px;
    -o-object-fit: contain;
    object-fit: contain
}

.language-select .select-options .select-option.selected {
    background-color: #434b67
}

.promoEnter-wrap {
    width: 100%;
    border-radius: 9.8px;
    background-color: #1e2531;
    color: #97a5c9;
    font-size: 15px;
    font-weight: 600;
    margin-top: 17.3px
}

.promoEnter-wrap .promo-title {
    display: flex;
    align-items: center;
    margin: 15.8px 0 0
}

.promoEnter-wrap .promo-title.active .arrow {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.promoEnter-wrap .promo-title svg {
    width: 21.5px;
    height: 22.6px;
    margin: 0 10px 0 21.8px
}

.promoEnter-wrap .promo-title .arrow {
    width: 12.8px;
    height: 7.4px;
    fill: #525d6e;
    cursor: pointer;
    transition: all .3s ease-in;
    margin-right: 12.8px
}

.promoEnter-wrap .promo-title .title-text {
    flex: 1
}

.promoEnter-wrap .promo-list {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    margin: 0 21px
}

.promoEnter-wrap .promo-list .promo-list-item {
    width: 100%;
    margin-top: 30px;
    cursor: pointer;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    line-height: 1.2
}

@supports (-webkit-line-clamp:2) {
    .promoEnter-wrap .promo-list .promo-list-item {
        display: -webkit-box !important;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        white-space: normal
    }
}

.promoEnter-wrap .promo-list .promo-list-item:hover {
    color: #fff
}

.promoEnter-wrap .more-btn {
    height: 49px;
    color: #fff;
    border-radius: 9px;
    background-color: #252c39;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin: 24.8px 15.8px 11.3px
}

.zoom-in-top-enter-active[data-v-053ff4e4], .zoom-in-top-leave-active[data-v-053ff4e4] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter[data-v-053ff4e4], .zoom-in-top-leave-active[data-v-053ff4e4] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active[data-v-053ff4e4], .zoom-in-bottom-leave-active[data-v-053ff4e4] {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter[data-v-053ff4e4], .zoom-in-bottom-leave-active[data-v-053ff4e4] {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.right-bar[data-v-053ff4e4] {
    right: 9px;
    position: fixed;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    padding: 10px;
    border-radius: 100px;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    transition: all .3
}

.right-bar.hide-menu[data-v-053ff4e4] {
    width: 73px;
    height: 73px;
    transition: all .3
}

.right-bar.hide-menu .menu-control[data-v-053ff4e4] {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%) rotate(180deg);
    transform: translate(-50%, -50%) rotate(180deg)
}

.right-bar .menu-control[data-v-053ff4e4] {
    width: 30px;
    height: 30px;
    margin: 0 auto;
    cursor: pointer
}

.right-bar .right-item[data-v-053ff4e4] {
    width: 50px;
    height: 50px;
    margin-bottom: 10px;
    cursor: pointer;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: contain;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: .2s;
    position: relative;
    border-radius: 50%;
    background-color: #000;
    border: 1px solid #69c1e2
}

.right-bar .right-item svg[data-v-053ff4e4] {
    width: 80%;
    height: 80%;
    fill: #fff
}

.right-bar .right-item[data-v-053ff4e4]:hover {
    background: linear-gradient(180deg, #c8aeff, #8bc1f2);
    border: none
}

.right-bar .right-item:hover svg[data-v-053ff4e4] {
    fill: #212937
}

.qr_img_wrap img {
    display: block
}

.side-wrapper {
    position: fixed;
    flex-direction: column;
    z-index: 3
}

.side-wrapper, .side-wrapper .side-top {
    top: 0;
    left: 0;
    display: flex;
    align-items: center
}

.side-wrapper .side-top {
    height: 63px;
    position: absolute;
    width: 337.9px
}

.side-wrapper .side-top:before {
    position: absolute;
    content: "";
    height: 0;
    width: 85%;
    border-top: 63px solid #14233f;
    border-right: 50px solid transparent;
    z-index: -1
}

.side-wrapper .switch-icon {
    width: 24.2px;
    height: 24.2px;
    margin-left: 39px;
    cursor: pointer
}

.side-wrapper .switch-icon.off svg {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.side-wrapper .nav-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%
}

.side-wrapper .nav-wrapper .nav-item {
    height: 67.5px;
    display: flex;
    align-items: center;
    position: relative;
    margin-top: 17px;
    padding-left: 80px;
    font-size: 17.3px;
    font-weight: 600;
    color: #fff;
    text-align: center;
    z-index: 1;
    border-radius: 11.3px;
    overflow: hidden;
    cursor: pointer
}

.side-wrapper .nav-wrapper .nav-item .label-text {
    width: 120px
}

.side-wrapper .nav-wrapper .nav-item:first-child {
    margin-top: 16.5px
}

.side-wrapper .nav-wrapper .nav-item:before {
    position: absolute;
    content: "";
    width: 54.8px;
    height: 55.5px;
    left: 11.2px
}

.side-wrapper .nav-wrapper .nav-item:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .3);
    left: 0;
    right: 0;
    z-index: -1;
    transition: background .3s ease-out
}

.side-wrapper .nav-wrapper .nav-item:hover:after {
    background: transparent
}

.side-wrapper .nav-wrapper .nav-item.activity {
    background: url(../img/vip-bg.66bb24c9.png) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.activity:before {
    height: 65px;
    width: 68px;
    background: url(../img/entry_icon9.png) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.vip {
    background: url(../img/vip-bg.66bb24c9.png) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.vip:before {
    background: url(data:image/png;base64,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) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.invite {
    background: url(../img/invite-bg.b39a8523.png) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.invite:before {
    background: url(data:image/png;base64,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) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.bet {
    background: url(../img/bet-bg.50e7100d.png) no-repeat 50%/contain
}

.side-wrapper .nav-wrapper .nav-item.bet:before {
    background: url(data:image/png;base64,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) no-repeat 50%/contain
}

.side-menu {
    margin: 63px 0 0;
    width: 247.5px;
    height: calc(100vh - 63px);
    font-size: 16.5px;
    font-weight: 600;
    color: #97a5c9;
    transition: .2s;
    overflow-y: auto;
    overflow-x: hidden
}

.side-menu::-webkit-scrollbar {
    width: 3px
}

.side-menu::-webkit-scrollbar-thumb, .side-menu::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent
}

.side-menu.show {
    width: 500px
}

.side-menu .side-menu-bg {
    padding: 0 14.3px 0 15.8px;
    width: 247.5px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #131922
}

.side-menu .home {
    padding: 10.5px 0;
    display: flex;
    align-items: center;
    border-radius: 9.8px;
    width: 100%;
    margin-top: 27px;
    background: rgba(30, 37, 49, .7);
    cursor: pointer
}

.side-menu .home svg {
    width: 18.8px;
    height: 16.7px;
    margin: 0 28px 0 23px
}

.side-menu .home:hover {
    color: #fff
}

.side-menu .home:hover svg {
    fill: #fff
}

.side-menu.off {
    width: 0;
    display: none
}

.side-menu .side-banner {
    width: 170px;
    height: 56px;
    -o-object-fit: cover;
    object-fit: cover
}

.side-menu .download-group {
    position: relative;
    flex-shrink: 0;
    padding: 9px 0;
    margin-top: 11.3px;
    width: 100%;
    border-radius: 7.5px;
    background-color: #252c39;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer
}

.side-menu .download-group:hover {
    color: #fff
}

.side-menu .download-group:hover .download-content-wrapper {
    opacity: 1;
    display: block
}

.side-menu .download-group svg {
    margin-right: 11.8px;
    width: 15.3px;
    height: 21.3px
}

.side-menu .download-group .download-text {
    /*max-width: 120px;*/
    font-size: 14px;
    text-align: center
}

.side-menu .cs-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    width: 100%;
    height: 40.5px;
    border-radius: 7.5px;
    background-color: #252c39;
    margin-top: 7px;
    font-size: 14px;
    font-weight: 600;
    gap: 10px;
    flex-shrink: 0
}

.side-menu .cs-btn:hover {
    color: #fff
}

.side-menu .cs-btn:before {
    content: "";
    width: 21px;
    height: 21px;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAqCAMAAAAd31JXAAABPlBMVEVHcEyuaO9voOTFUfFtouO9WfCVfujPS/JoqOR3meWCj+S2X+/CWfR9k+WYe+p3meW5W+/GUvFuoeR8leambezLTPKzYu6MhejUQ/NuouNfr+HMS/LQSvOlbuyuZu7NS/J+kua1YO+aeee2X+/QR/OJieiLhueIied4l+aCj+dmqOKDj+fSRfOkbeu5Xe+mbe14mOWsae6+WfC9WPC5Xe+Gi+axY+2Dj+eKieh3meXOSfNyneRer+DOSfN9lObOSfN3mOW7We+rae60Yu7BVPDAVfB8k+KPg+iEjOdso+KVfOlnqOLLTPHQR/OsaO3BVPBhreJ9k+Z3meWKiOhlquOwZO6+V/BvoOR6luZznOTTRPOFjOeAkOdrpOOoa+yOhOnFUfG2X+9opuOzYu6icuy5XO+8WvDJTfKXe+rNSvIbJ3LGAAAATXRSTlMAIIBgQGAgIF8gEL8QYN+/UECfr2C/cI/fj9urMKCPb9/fMNeV70CAcO/kf+9ANd+fv++fz1CAv/fP3/eAz+9/75DPr+ffUM+fUM+/zx97YOkAAAJFSURBVDjLtdR5b6JAFADwcT1YFbuwHq31NlprW+PZbdr0bto9QREPBLzx+v5fYB8oYuNAsk32/aGM/jJ583jzEPp40InIa6Uzr9ReQjZLSD78WYxG9c58Nh5P2+xzwJwmLlYafa15U1Owg0HRRBNBZrVYBBPR9TIdeh4MFCXmwtDoEbNaBWlEOp+qXu9LnCJRoawoysE+jl4wzA2N6GBHzXbahgxiNhQ4WC73MAH0jkCEcTDIViGRax+TRyJzR8J3vVN5oqBYaSpWVBRAnl/L4fd3NiMyN4S2P2X8GCion66D4TC3m4EoMoRZJU+GfNZjLCMi82Be9kOez+1su8kAH56slN9unBCZjNWrP5TkU/05yDBRK3siyX79GVKwbr68fK+ny6wi1vaKE/SEMxHC2r5dl9B/Dtput1OmaXh8EI7NglqM6tBe3/D0XOYEodnbYLt6b6AVQxa091m39aqzOhuncDbJcWGfv9c1rBOh2niKoW6Z+4LQp+7W1jtgvdM2ZiC4OUGz/Q9YZ2eu2jaLs0JTtf1/sRPdzmdgyyzWNjU72dqZWtoyO8DVodm7Vm1rY2kobSjOskVcfR973fDpWUu3yLueHdiXDKXtT1qthm5tKW0k4TvH/97C2IjHC8b/Lt+l21j9DIfPGg2HybUd8pJ0vnvARuPYlII1bjlywLa3WOoCmvshy/elUumrGr9bptvC+IJh4F93bbcPBwPqxts0z1+pbatRrQbHt6a3LytJyTwnJB2bcFvc1Es4GCc8pvf/+QutqoNhOl2QlwAAAABJRU5ErkJggg==) no-repeat 50%/contain
}

.side-menu .download-content-wrapper {
    position: relative;
    position: absolute;
    left: 305px;
    bottom: -45px;
    opacity: 0;
    display: none;
    z-index: 5;
    transition: .2s
}

.side-menu .download-content-wrapper:before {
    position: absolute;
    bottom: 58px;
    left: -20px;
    content: "";
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-left: 10px solid transparent;
    border-right: 10px solid #252c39;
    border-bottom: 10px solid transparent
}

.side-menu .download-content {
    width: 100%;
    padding: 15px;
    border-radius: 10px;
    background-color: #252c39;
    display: flex;
    flex-direction: column;
    gap: 15px
}

.side-menu .download-content .qrcode-info {
    display: flex;
    justify-content: center;
    align-items: center
}

.side-menu .download-content .qrcode-info svg {
    margin-right: 10px;
    width: 18px;
    height: 23px
}

.side-menu .channel-group {
    margin-top: 27.8px
}

.side-menu .channel-group .channel-title {
    font-size: 15px;
    font-weight: 800;
    color: #fff
}

.side-menu .channel-group .channel-list {
    display: flex;
    justify-content: center;
    margin-top: 18px
}

.side-menu .channel-group .channel-list .channel-item {
    width: 26.3px;
    height: 26.3px;
    cursor: pointer
}

.side-menu .channel-group .channel-list .channel-item.fb {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGkAAABpCAMAAAAOXP0IAAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAA2UExURUdwTENZlEJZlLC700NalENZlODk7kRZlERYlkNZlP///0NZlOfp8YuYvXKCr77F2ldroKSvy4OajbAAAAAKdFJOUwCXf7/v37/AHFdVaJuzAAAByUlEQVRo3u3a2W7DIBAFUHCg7Ab+/2fbpk2cONgwLCM15b5G1tGAZSJxCdlHKio4Mw3hgipJMpGLMH3CTzG5MNMx9NDq63xnSQ8kTP/wxFiKmRFh6mXlzKgsWNCOUmZkHhZQsqES214LbsZGYGzS01bJ4dBt/eh46WcohJF+h1IY0nUogSIJpMX7iuyzeGGN3urnuP2HosOb57xOxO6OxeZtClGnY/cb1fglWq0ukzgZM9CrxJqkYHWxZMgoqKt0CvWUokaSgsaSPJaUG6mf5NAkjyZZNCkH6ThSsi4cP9BTsuHsgZ775A2WtE5pSlP631KwW5Jf2Me4Fil7+nU7n0CSf0Mpokl4b8SKJgU0yWBJFk3ybyhFNMmhSesfkVzDSw48c8M9KTS6LaHb6e401v+IKU1pSlOa0pSGSgxJYrV3NWBJ1N4/VUgUSaK194RgSdXefYIlWXufC5VE9R01VFLVNQygxOvrEUBpqe+WwCTe0JeBSffOjBgsUdJQmYFIj5Ut+FYBJPbUDQNT5RLbldAkHySxl7YbkCqVUrU6SQdIB7VExTtLQh0WLcutAunEuVq0DFszEqeKZCPVQkU2l499LrefaLIO+wmsirzoLkiHeQAAAABJRU5ErkJggg==) no-repeat 50%/contain;
    margin-right: 21px
}

.side-menu .channel-group .channel-list .channel-item.yt {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAMAAAApB0NrAAAAOVBMVEUAAAD/////////////////////////////MAD/mYL/sZ//y7//XDf/8u//2M//fWD/Rxz/5d//vq8FxkBRAAAAB3RSTlMAkBXP359AyPy/8QAAAKVJREFUOMut1EsOhCAQRdEqfqWF0Or+F9utbaJB8DHwDs0JkS9tBXZSy3pD/wxLOzY7sfKU3RDLc0xkBBXIQ8NkoXEkuPdNyqrjkWqMFTMORXMuzTLcmkozDvdSw0wXsxTmc3zX5VSxZUR0xkbSBM0Kx4nF/4B55cJox/pktM54v45SXC/7vubUc37eNw4SSwyNpwCN6brv+N3oe3925etjOQ706wsVViIvjdHq+wAAAABJRU5ErkJggg==) no-repeat 50%/contain
}

.maintain-wrap, .regiona-wrap {
    padding-top: 0;
    width: 100%;
    height: 100vh;
    position: relative;
    background: url(../img/background.899d7bd6.jpg) no-repeat top/cover
}

.maintain-wrap .content-wrap, .regiona-wrap .content-wrap {
    width: 1000px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 46px
}

.maintain-wrap .content-wrap:before, .regiona-wrap .content-wrap:before {
    content: "";
    width: 556.3px;
    height: 439.1px;
    background: url(../img/not-allowed.5225157d.png) no-repeat top/cover
}

.maintain-wrap .content-wrap .right-wrap, .regiona-wrap .content-wrap .right-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px
}

.maintain-wrap .content-wrap .right-wrap .logo-wrap img, .regiona-wrap .content-wrap .right-wrap .logo-wrap img {
    height: 46px
}

.maintain-wrap .content-wrap .right-wrap h2, .regiona-wrap .content-wrap .right-wrap h2 {
    font-size: 30px;
    font-weight: 700;
    color: #fff
}

.maintain-wrap .content-wrap .right-wrap h2.subtitle, .regiona-wrap .content-wrap .right-wrap h2.subtitle {
    font-size: 20px;
    font-weight: 700;
    color: #8d8d8d
}

.maintain-wrap .content-wrap .right-wrap p, .regiona-wrap .content-wrap .right-wrap p {
    font-size: 16px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: normal;
    letter-spacing: normal;
    text-align: left;
    color: #fff
}

.maintain-wrap .content-wrap .right-wrap .ip_address span, .regiona-wrap .content-wrap .right-wrap .ip_address span {
    font-weight: 700;
    font-size: 16px
}

.maintain-wrap .content-wrap .right-wrap .ip_address span:first-child, .regiona-wrap .content-wrap .right-wrap .ip_address span:first-child {
    color: #8d8d8d
}

.maintain-wrap .content-wrap .right-wrap .ip_address span:nth-child(2), .regiona-wrap .content-wrap .right-wrap .ip_address span:nth-child(2) {
    color: #da394f
}

.maintain-wrap .content-wrap .right-wrap .service-btn, .regiona-wrap .content-wrap .right-wrap .service-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    min-width: 213px;
    height: 42px;
    font-size: 13px;
    margin-top: 20px
}

.maintain-wrap .content-wrap .right-wrap .service-btn:hover, .regiona-wrap .content-wrap .right-wrap .service-btn:hover {
    opacity: .8
}

.svg-icon[data-v-08edb591] {
    display: block;
    width: 100%;
    height: 100%;
    overflow: hidden;
    fill: currentColor
}

body, button, code, dd, details, div, dl, dt, footer, form, h1, h2, h3, h4, h5, h6, header, html, input, li, ol, p, td, textarea, th, ul {
    margin: 0;
    padding: 0;
    outline: 0;
    -webkit-text-size-adjust: none;
    -webkit-tap-highlight-color: transparent
}

* {
    box-sizing: border-box
}

li, ol, ul {
    list-style: none
}

input {
    outline: none
}

input:-webkit-autofill {
    -webkit-text-fill-color: #dedede;
    -webkit-box-shadow: inset 0 0 0 1000px #3d4145 !important
}

input::-webkit-search-cancel-button {
    display: none
}

input:-ms-input-placeholder {
    -ms-transition: .2s;
    transition: .2s;
    font-family: DinPro, dinpro, PingFangSC-Regular, sans-serif
}

input::placeholder {
    transition: .2s;
    font-family: DinPro, dinpro, PingFangSC-Regular, sans-serif
}

input::-webkit-input-placeholder {
    -webkit-transition: .2s;
    transition: .2s;
    font-family: DinPro, dinpro, PingFangSC-Regular, sans-serif
}

button {
    outline: none;
    border: 0 none
}

body, html {
    width: 100%;
    height: 100%;
    color: #333;
    font-family: DinPro, dinpro, PingFangSC-Regular, sans-serif;
    overflow-x: hidden
}

html {
    overflow-y: auto !important
}

#app *, .mcHelp * {
    -webkit-font-smoothing: initial !important;
    -moz-osx-font-smoothing: initial !important
}

svg {
    width: 100%;
    height: 100%;
    fill: currentColor
}

.hover:hover {
    opacity: .7
}

.br_sbgimg {
    background-position: 50%;
    background-repeat: no-repeat
}

.br_word {
    word-break: break-all;
    word-wrap: break-word
}

.fl {
    float: left
}

.fr {
    float: right
}

#app .v--modal {
    background-color: transparent
}

#app .v--modal-overlay {
    background: rgba(0, 0, 0, .3)
}

#app .v--modal-overlay .v--modal-box {
    overflow: visible
}

@font-face {
    font-family: br_model;
    src: url("data:font/opentype;base64,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") format("truetype"), url("data:font/woff;base64,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") format("woff"), url("data:image/svg+xml;base64,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") format("svg");
    font-weight: 400;
    font-style: normal
}

[class*=" br_dialog_d"], [class^=br_dialog_d] {
    font-family: br_model !important;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.br_dialog_dinfo:before {
    content: ""
}

.br_dialog_derror:before {
    content: ""
}

.br_dialog_dsuccess:before {
    content: ""
}

.br_dialog_dwarning:before {
    content: ""
}

.pp_model_dialog {
    display: none;
    z-index: 99999;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0
}

.pp_model_dialog.active {
    display: block
}

.pp_model_dcover {
    background-color: rgba(0, 0, 0, .4);
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0
}

.pp_model_dmain {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9;
    position: relative
}

.pp_model_dbody {
    max-width: 450px;
    min-width: 260px;
    padding: 20px 0 0;
    background: #2b3248;
    box-shadow: 0 5px 18px 0 rgba(26, 0, 0, .56);
    border-radius: 5px;
    text-align: center;
    overflow: hidden;
    z-index: 9;
    position: relative
}

.pp_model_dbicon {
    padding-bottom: 20px
}

.pp_model_dbicon span {
    font-size: 42px
}

.pp_model_dbicon.info span {
    color: #f8bb86
}

.pp_model_dbicon.error span {
    color: #f11
}

.pp_model_dbicon.success span {
    color: #00ff37
}

.pp_model_dbicon.warning span {
    color: #fbff0b
}

.pp_model_dbtitle {
    color: #fff;
    padding: 0 20px 20px;
    font-size: 27px;
    line-height: normal;
    text-align: center;
    font-weight: 600
}

.pp_model_dbhtml {
    display: inline-block;
    padding: 0 20px 20px;
    color: #fff;
    font-size: 15px;
    font-weight: 400;
    text-align: left
}

.pp_model_dbbtn {
    display: flex
}

.pp_model_dbbtn > div {
    flex: 1;
    height: 52px;
    font-weight: 600;
    font-size: 14px;
    line-height: 52px;
    cursor: pointer
}

.pp_model_dbbtn > div span {
    color: #fff
}

.pp_model_dbbtn > div.cancel {
    background-color: rgba(135, 134, 134, .9)
}

.pp_model_dbbtn > div.cancel:hover {
    background-color: #878686
}

.pp_model_dbbtn > div.confirm {
    background: rgba(218, 57, 79, .9)
}

.pp_model_dbbtn > div.confirm:hover {
    background-color: #da394f
}

.swal-button:focus {
    box-shadow: inherit
}

.br_spinner, .br_spinner * {
    box-sizing: border-box
}

.br_spinner {
    height: 60px;
    width: 60px;
    overflow: hidden
}

.br_spinner .spinner_inner {
    position: relative;
    display: block;
    height: 100%;
    width: 100%
}

.br_spinner .spinner_circle {
    display: block;
    position: absolute;
    color: #ffd800;
    font-size: 14.4px;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.br_spinner .spinner_line {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    border-left-width: 2.4px;
    border-top-width: 2.4px;
    border-left-color: #ffd800;
    border-left-style: solid;
    border-top-style: solid;
    border-top-color: transparent
}

.br_spinner .spinner_line:first-child {
    -webkit-animation: br_spinner_animation_1 1s linear infinite;
    animation: br_spinner_animation_1 1s linear infinite;
    -webkit-transform: rotate(120deg) rotateX(66deg) rotate(0deg);
    transform: rotate(120deg) rotateX(66deg) rotate(0deg)
}

.br_spinner .spinner_line:nth-child(2) {
    -webkit-animation: br_spinner_animation_2 1s linear infinite;
    animation: br_spinner_animation_2 1s linear infinite;
    -webkit-transform: rotate(240deg) rotateX(66deg) rotate(0deg);
    transform: rotate(240deg) rotateX(66deg) rotate(0deg)
}

.br_spinner .spinner_line:nth-child(3) {
    -webkit-animation: br_spinner_animation_3 1s linear infinite;
    animation: br_spinner_animation_3 1s linear infinite;
    -webkit-transform: rotate(1turn) rotateX(66deg) rotate(0deg);
    transform: rotate(1turn) rotateX(66deg) rotate(0deg)
}

@-webkit-keyframes br_spinner_animation_1 {
    to {
        -webkit-transform: rotate(120deg) rotateX(66deg) rotate(1turn);
        transform: rotate(120deg) rotateX(66deg) rotate(1turn)
    }
}

@keyframes br_spinner_animation_1 {
    to {
        -webkit-transform: rotate(120deg) rotateX(66deg) rotate(1turn);
        transform: rotate(120deg) rotateX(66deg) rotate(1turn)
    }
}

@-webkit-keyframes br_spinner_animation_2 {
    to {
        -webkit-transform: rotate(240deg) rotateX(66deg) rotate(1turn);
        transform: rotate(240deg) rotateX(66deg) rotate(1turn)
    }
}

@keyframes br_spinner_animation_2 {
    to {
        -webkit-transform: rotate(240deg) rotateX(66deg) rotate(1turn);
        transform: rotate(240deg) rotateX(66deg) rotate(1turn)
    }
}

@-webkit-keyframes br_spinner_animation_3 {
    to {
        -webkit-transform: rotate(1turn) rotateX(66deg) rotate(1turn);
        transform: rotate(1turn) rotateX(66deg) rotate(1turn)
    }
}

@keyframes br_spinner_animation_3 {
    to {
        -webkit-transform: rotate(1turn) rotateX(66deg) rotate(1turn);
        transform: rotate(1turn) rotateX(66deg) rotate(1turn)
    }
}

@-webkit-keyframes floating {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    61% {
        -webkit-transform: translateY(5px);
        transform: translateY(5px)
    }
    to {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@keyframes floating {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    61% {
        -webkit-transform: translateY(5px);
        transform: translateY(5px)
    }
    to {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@-webkit-keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, -100%, 0);
        transform: translate3d(0, -100%, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 45px, 0);
        transform: translate3d(0, 45px, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 45px, 0);
        transform: translate3d(0, 45px, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes lol {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-300px);
        transform: translateX(-300px)
    }
    33%, 66% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    to {
        opacity: 0;
        -webkit-transform: translateX(300px);
        transform: translateX(300px)
    }
}

@keyframes lol {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-300px);
        transform: translateX(-300px)
    }
    33%, 66% {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    to {
        opacity: 0;
        -webkit-transform: translateX(300px);
        transform: translateX(300px)
    }
}

@-webkit-keyframes iMfa-dD {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    10% {
        -webkit-transform: scale(1.25);
        transform: scale(1.25)
    }
    20% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes iMfa-dD {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    10% {
        -webkit-transform: scale(1.25);
        transform: scale(1.25)
    }
    20% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes breath {
    0% {
        opacity: .2
    }
    50% {
        opacity: 1
    }
    to {
        opacity: .2
    }
}

@keyframes breath {
    0% {
        opacity: .2
    }
    50% {
        opacity: 1
    }
    to {
        opacity: .2
    }
}

@-webkit-keyframes jxwNOZ {
    0% {
        -webkit-transform: translateY(104px);
        transform: translateY(104px)
    }
    25% {
        -webkit-transform: translateY(120px);
        transform: translateY(120px)
    }
    50% {
        -webkit-transform: translateY(144px);
        transform: translateY(144px)
    }
    to {
        -webkit-transform: translateY(176px);
        transform: translateY(176px)
    }
}

@keyframes jxwNOZ {
    0% {
        -webkit-transform: translateY(104px);
        transform: translateY(104px)
    }
    25% {
        -webkit-transform: translateY(120px);
        transform: translateY(120px)
    }
    50% {
        -webkit-transform: translateY(144px);
        transform: translateY(144px)
    }
    to {
        -webkit-transform: translateY(176px);
        transform: translateY(176px)
    }
}

@-webkit-keyframes gamejxwNOZ {
    0% {
        -webkit-transform: translateY(30px);
        transform: translateY(30px)
    }
    25% {
        -webkit-transform: translateY(50px);
        transform: translateY(50px)
    }
    50% {
        -webkit-transform: translateY(100px);
        transform: translateY(100px)
    }
    to {
        -webkit-transform: translateY(120px);
        transform: translateY(120px)
    }
}

@keyframes gamejxwNOZ {
    0% {
        -webkit-transform: translateY(30px);
        transform: translateY(30px)
    }
    25% {
        -webkit-transform: translateY(50px);
        transform: translateY(50px)
    }
    50% {
        -webkit-transform: translateY(100px);
        transform: translateY(100px)
    }
    to {
        -webkit-transform: translateY(120px);
        transform: translateY(120px)
    }
}

@-webkit-keyframes loadRoate {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }
    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes loadRoate {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }
    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes Ip_Lt {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    to {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }
}

@keyframes Ip_Lt {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    to {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }
}

@-webkit-keyframes O5_Kv {
    0% {
        -webkit-transform: scale(1.25);
        transform: scale(1.25)
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes O5_Kv {
    0% {
        -webkit-transform: scale(1.25);
        transform: scale(1.25)
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes fadeOutRight {
    0% {
        opacity: 1
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0)
    }
}

@keyframes fadeOutRight {
    0% {
        opacity: 1
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0)
    }
}

@-webkit-keyframes fadeOutLeft {
    0% {
        opacity: 1
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-100px, 0, 0);
        transform: translate3d(-100px, 0, 0)
    }
}

@keyframes fadeOutLeft {
    0% {
        opacity: 1
    }
    to {
        opacity: 0;
        -webkit-transform: translate3d(-100px, 0, 0);
        transform: translate3d(-100px, 0, 0)
    }
}

@-webkit-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(100px, 0, 0);
        transform: translate3d(100px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-100px, 0, 0);
        transform: translate3d(-100px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-100px, 0, 0);
        transform: translate3d(-100px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes pc_Kk {
    0% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes pc_Kk {
    0% {
        -webkit-transform: scale(1.1);
        transform: scale(1.1)
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes prev_animation {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
    to {
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
        opacity: 0
    }
}

@keyframes prev_animation {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
    to {
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
        opacity: 0
    }
}

@-webkit-keyframes selected_animation {
    0% {
        -webkit-transform: scale(.8);
        transform: scale(.8);
        opacity: 0
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@keyframes selected_animation {
    0% {
        -webkit-transform: scale(.8);
        transform: scale(.8);
        opacity: 0
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1);
        opacity: 1
    }
}

@-webkit-keyframes cf_c5 {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    50% {
        -webkit-transform: translateX(-10px);
        transform: translateX(-10px)
    }
    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes cf_c5 {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    50% {
        -webkit-transform: translateX(-10px);
        transform: translateX(-10px)
    }
    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes cf_cv {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
    50% {
        -webkit-transform: translate3D(-10px, 10px, 0);
        transform: translate3D(-10px, 10px, 0)
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes cf_cv {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
    50% {
        -webkit-transform: translate3D(-10px, 10px, 0);
        transform: translate3D(-10px, 10px, 0)
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes cf_cp {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    50% {
        -webkit-transform: translateY(-10px);
        transform: translateY(-10px)
    }
    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes cf_cp {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    50% {
        -webkit-transform: translateY(-10px);
        transform: translateY(-10px)
    }
    to {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes cf_cr {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
    50% {
        -webkit-transform: translate3D(10px, -10px, 0);
        transform: translate3D(10px, -10px, 0)
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes cf_cr {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
    50% {
        -webkit-transform: translate3D(10px, -10px, 0);
        transform: translate3D(10px, -10px, 0)
    }
    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes cf_KS {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(50px, 0, 0);
        transform: translate3d(50px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes cf_KS {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(50px, 0, 0);
        transform: translate3d(50px, 0, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@-webkit-keyframes cf_ch {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-50px);
        transform: translateX(-50px)
    }
    to {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes cf_ch {
    0% {
        opacity: 0;
        -webkit-transform: translateX(-50px);
        transform: translateX(-50px)
    }
    to {
        opacity: 1;
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@-webkit-keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
    50% {
        opacity: 1
    }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
    50% {
        opacity: 1
    }
}

@-webkit-keyframes roate-full {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes roate-full {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.form_container .form_item {
    width: 100%;
    position: relative
}

.form_container .form_item .errorMsg {
    width: 100%;
    min-height: 15px;
    padding-top: 5px;
    padding-left: 5px;
    margin-bottom: 5px;
    font-size: 12px;
    color: #f56c6c
}

.form_container .form_item .item_box {
    width: 100%;
    position: relative
}

.form_container .form_item .item_box.capt {
    display: flex;
    align-items: center
}

.form_container .form_item .item_box.capt > input {
    padding-right: 80px
}

.form_container .form_item .item_box.hasCountry {
    display: flex;
    align-items: center
}

.form_container .form_item .item_box.hasCountry > input {
    padding-left: 130px
}

.form_container .form_item .item_box .header-forget {
    display: none
}

.form_container .form_item .item_box .input_icon {
    position: absolute;
    top: 50%;
    left: 26px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: #a8a8a8
}

.form_container .form_item .item_box > input {
    /*transition: .2s;*/
    overflow: hidden;
    width: 100%;
    color: #fff;
    padding: 0 16px 0 63px;
    font-size: 14px;
    font-weight: 600;
    height: 47px;
    border-radius: 10px;
    background: #212937
}

.form_container .form_item .item_box > input:focus, .form_container .form_item .item_box > input:hover {
    border-color: #fff
}

.form_container .form_item .item_box > input::-webkit-input-placeholder {
    color: #c0c4cc
}

.form_container .form_item .item_box > input:-ms-input-placeholder {
    color: #c0c4cc
}

.form_container .form_item .item_box > input::placeholder {
    color: #c0c4cc
}

.form_container .form_item .item_box > input.err {
    border: 1px solid #f04646
}

.form_container .form_item .item_box .password-eyes {
    width: 20px;
    height: 13px;
    color: #a7a7a7;
    position: absolute;
    top: 50%;
    right: 16px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer
}

.form_container .form_item .item_box .password-eyes:hover {
    opacity: .8
}

.form_container .form_item .item_box .captcha_box {
    cursor: pointer;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 15px;
    display: flex;
    justify-content: center;
    align-items: center
}

.form_container .form_item .item_box .captcha_box img {
    height: 40px
}

.form_container .form_item .label-box {
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    display: none
}

.form_container .form_item .label-box .errorMsg {
    display: none
}

.form_container .form_item .label-box .label-text {
    font-size: 12px;
    font-weight: 700;
    color: #c4c4c4
}

.form_container .form_item .label-box .label-text.isRequire:after {
    content: "*";
    color: red;
    margin-left: 2px
}

.form_container .form_item:hover .clear-icon {
    display: block
}

.form_container .form_item .forget, .form_container .form_item .online-service {
    cursor: pointer;
    text-decoration: underline;
    transition: .2s;
    font-size: 17px;
    font-weight: 500;
    color: #1678ff
}

.form_container .form_item .forget:hover, .form_container .form_item .online-service:hover {
    opacity: .8
}

.form_container .form_item .sms-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    height: 47px;
    padding: 0 10px;
    border-radius: 9px;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    right: 6px;
    font-size: 17px;
    font-weight: 600;
    color: #fff;
    background-color: #1678ff
}

.form_container .form_item .sms-btn.disabled {
    cursor: not-allowed;
    opacity: .7
}

.form_container .form_item .sms-btn.disabled:active, .form_container .form_item .sms-btn.disabled:hover {
    color: #000;
    box-shadow: inset 0 -6px 10px 0 #9f9f9f, inset 0 3px 5px 0 hsla(0, 0%, 100%, .37);
    background: #ddd
}

.form_container .form_item .country_code {
    left: 38px
}

.form_container .form_item.rem-wrap {
    display: flex;
    justify-content: end;
    align-items: center;
    padding: 10px 0;
    font-size: 14px
}

.form_container .form_item.rem-wrap .remember-item {
    display: flex;
    align-items: center;
    color: #1678ff;
    font-size: 17px;
    font-weight: 500
}

.form_container .form_item.rem-wrap .remember-item .remPass_box {
    position: relative;
    margin-right: 10px
}

.form_container .form_item.rem-wrap .remember-item .remPass_box .remPass_checkbox {
    width: 30px;
    height: 30px;
    display: block;
    border: none;
    outline: none;
    position: relative;
    background-color: transparent;
    opacity: 0;
    z-index: 3;
    cursor: pointer
}

.form_container .form_item.rem-wrap .remember-item .remPass_box.checked .gougou {
    opacity: 1
}

.form_container .form_item.rem-wrap .remember-item .remPass_box.checked .cheched-bg {
    background-color: #599859;
    border-color: transparent
}

.form_container .form_item.rem-wrap .remember-item .remPass_box .cheched-bg {
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 30px;
    height: 30px;
    border-radius: 6px;
    border: 1px solid #fff;
    background: transparent;
    cursor: pointer;
    transition: all .15s linear;
    opacity: 1
}

.form_container .form_item.rem-wrap .remember-item .remPass_box .gougou {
    border-width: 0 2px 2px 0;
    width: 10px;
    height: 18px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    border-style: solid;
    border-color: #fff;
    position: absolute;
    left: 10px;
    top: 4px;
    display: inline-block;
    opacity: 0
}

.form_container .form_item.terms {
    position: relative;
    margin-top: 30px;
    color: #545f71;
    font-size: 14px;
    display: flex;
    align-items: center
}

.form_container .form_item.terms .link {
    position: relative;
    color: #1678ff;
    cursor: pointer
}

.form_container .form_item.terms .link:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 1px;
    width: 100%;
    height: 2px;
    background-color: #1678ff
}

.form_container .form_item.terms .link:hover {
    color: rgba(22, 120, 255, .8)
}

.form_container .form_item.terms .terms_box {
    position: relative;
    margin-right: 10px
}

.form_container .form_item.terms .terms_box.login {
    display: none
}

.form_container .form_item.terms .terms_box .terms_checkbox {
    width: 37px;
    height: 37px;
    display: block;
    border: none;
    outline: none;
    position: relative;
    background-color: transparent;
    opacity: 0;
    z-index: 3;
    cursor: pointer
}

.form_container .form_item.terms .terms_box.checked .gougou {
    opacity: 1
}

.form_container .form_item.terms .terms_box.checked .cheched-bg {
    background-color: #599859;
    border-color: transparent
}

.form_container .form_item.terms .terms_box .cheched-bg {
    position: absolute;
    left: 0;
    top: 0;
    display: block;
    width: 37px;
    height: 37px;
    border-radius: 6px;
    border: 1px solid #fff;
    background: transparent;
    cursor: pointer;
    transition: all .15s linear;
    opacity: 1
}

.form_container .form_item.terms .terms_box .gougou {
    border-width: 0 2px 2px 0;
    width: 10px;
    height: 18px;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    border-style: solid;
    border-color: #fff;
    position: absolute;
    left: 14px;
    top: 8px;
    display: inline-block;
    opacity: 0
}

.form_container .form_item.terms .errorMsg {
    position: absolute;
    left: 0;
    bottom: -20px
}

.form_container .btn-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px
}

.form_container .btn-wrap .or-connect {
    font-size: 16px;
    color: #7b7b7b;
    border-left: 1px solid #7b7b7b;
    padding-left: 15px;
    margin-left: 15px
}

.form_container .freePlay, .form_container .submit_btn {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff;
    min-width: 100%;
    height: 58px;
    font-size: 18px;
    background-color: #1678ff
}

.form_container .freePlay:hover, .form_container .submit_btn:hover {
    opacity: .8
}

.form_container .freePlay button, .form_container .submit_btn button {
    background: transparent;
    color: inherit;
    font-size: inherit
}

.form_container .freePlay {
    margin-left: 5px
}

.form_container .form-footer {
    margin-top: 30px;
    text-align: center
}

.form_container .form-footer .cs-link {
    font-size: 14px;
    color: #347bbc;
    cursor: pointer
}

.method-select {
    position: absolute;
    top: 30px;
    left: 0;
    width: 100%;
    font-size: 18px;
    color: #5c677a;
    gap: 45px;
    border-bottom: 1px solid #2d3746
}

.method-select, .method-select .method-item {
    display: flex;
    justify-content: center;
    align-items: center
}

.method-select .method-item {
    transition: .2s;
    cursor: pointer;
    padding-bottom: 4px;
    position: relative
}

.method-select .method-item.active, .method-select .method-item:hover {
    border-bottom: 4px solid #1678ff;
    color: #fff
}

.form-popup-container {
    display: flex
}

.form-popup-container .form-popup-banner {
    max-width: 426px;
    max-height: 90vh;
    min-height: 390px;
    -o-object-fit: cover;
    object-fit: cover;
    border-radius: 20px 0 0 20px
}

.form-popup-container .form-popup-bg {
    width: 444px;
    max-height: 90vh;
    min-height: 390px;
    padding: 95px 35px 30px;
    position: relative;
    border-radius: 0 20px 20px 0;
    background-color: #181f2b;
    overflow-y: auto;
    overflow-x: hidden
}

.form-popup-container .form-popup-bg::-webkit-scrollbar {
    width: 3px
}

.form-popup-container .form-popup-bg::-webkit-scrollbar-thumb, .form-popup-container .form-popup-bg::-webkit-scrollbar-track {
    border-radius: 2px;
    background: transparent
}

.form-popup-container .form-popup-bg .form-title {
    text-align: left;
    margin-bottom: 20px;
    color: #fff
}

.form-popup-container .form-popup-bg .form-title__text {
    font-size: 30px;
    font-weight: 700;
    text-align: left
}

.form-popup-container .form-popup-bg .form-title .form-logo {
    width: 68px;
    display: none
}

.form-popup-container .form-popup-bg .form-title h5 {
    font-size: 26px
}

.form-popup-container .form-popup-bg .form-title p {
    margin-top: 10px;
    font-size: 14px
}

.form-popup-container .form-popup-bg .form-type {
    margin-bottom: 50px;
    display: flex;
    font-size: 18px;
    font-weight: 600;
    color: #5c677a
}

.form-popup-container .form-popup-bg .form-type .form-type-item {
    min-width: 120px;
    height: 38px;
    border-radius: 10px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center
}

.form-popup-container .form-popup-bg .form-type .form-type-item:hover {
    color: #fff
}

.form-popup-container .form-popup-bg .form-type .form-type-item.active {
    background-color: #1678ff;
    color: #fff
}

.form-popup-container .form-tip {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    font-size: 14px;
    color: #fff;
    padding-top: 19px
}

.form-popup-container .form-tip a {
    position: relative;
    cursor: pointer;
    color: #1678ff;
    transition: .2s
}

.form-popup-container .form-tip a:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 1px;
    width: 100%;
    height: 2px;
    background-color: #1678ff
}

.form-popup-container .form-tip a:hover {
    opacity: .8
}

.firebase-login {
    width: 100%;
    margin-top: 20px
}

.firebase-login .tips {
    width: 100%;
    margin: 30px auto 12px;
    text-align: center;
    display: flex;
    align-items: center;
    color: #545f71
}

.firebase-login .tips span {
    padding: 0 5px;
    font-size: 14px
}

.firebase-login .firebase-group {
    margin-top: 10px;
    display: flex;
    gap: 20px
}

.firebase-login .firebase-group .item {
    width: 48px;
    height: 48px;
    cursor: pointer;
    border-radius: 22px;
    display: flex;
    justify-content: center;
    gap: 15px;
    align-items: center;
    font-size: 13px;
    font-weight: 500;
    color: #fff
}

#ui-datepicker-div.ui-widget-content {
    background: #3b2726
}

#ui-datepicker-div.mc-custom .ui-state-disabled .ui-state-default {
    color: #fff !important
}

#messagePreview #messageLink {
    display: none
}

.user-center-bg {
    padding-top: 100px;
    background: url(../img/game-list-bg.b340bfeb.png) repeat-y 50%/100% auto
}

.user-center-bg .user-center-content {
    width: 1350px;
    margin: 0 auto;
    padding: 40px 0;
    display: flex;
    justify-content: space-between
}

.user-center-bg .user-center-content .content-left {
    width: 220px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, .1);
    border-radius: 4px;
    padding: 20px 0
}

.user-center-bg .user-center-content .content-left li {
    position: relative;
    height: 46px;
    line-height: 46px;
    color: #414755;
    font-size: 14px;
    padding-left: 60px;
    cursor: pointer
}

.user-center-bg .user-center-content .content-left li.active, .user-center-bg .user-center-content .content-left li:hover {
    background-color: #fefbf6;
    color: #d2b79c
}

.user-center-bg .user-center-content .content-left li.active:after, .user-center-bg .user-center-content .content-left li:hover:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 46px;
    background-color: #dccab8
}

.user-center-bg .user-center-content .content-left .acmc_icon {
    position: absolute;
    left: 30px;
    top: 15px;
    width: 16px;
    height: 16px;
    background-size: 100% 100%
}

.user-center-bg .user-center-content .active-content {
    width: 1110px;
    height: 100%;
    position: relative;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0 6px 12px 0 rgba(0, 0, 0, .1);
    overflow: hidden
}

.hover {
    cursor: pointer;
    transition: .25s
}

.hover :hover {
    opacity: .8
}

.cupinter {
    cursor: pointer
}

.width_1200 {
    margin: 0 auto;
    width: calc(100% - 330px)
}

.logo {
    margin-left: 20px;
    height: 55px;
    transition: .2s;
    cursor: pointer
}

.logo:hover {
    -webkit-transform: scale(1.05);
    transform: scale(1.05)
}

.page-center {
    margin: 0 auto;
    transition: .3s;
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

.page-center.off {
    width: calc(100% - 247.5px);
    max-width: 1085px;
    -webkit-transform: translateX(124px);
    transform: translateX(124px)
}

.notice-box {
    background-color: #e0e0e0;
    color: #56575c
}

.paginate-wrapper .pagination-container {
    display: flex;
    align-items: center;
    justify-content: center
}

.paginate-wrapper .pagination-container .page-item {
    margin: 0 3px;
    line-height: 24px
}

.paginate-wrapper .pagination-container .page-item.active .page-link-item {
    border: none;
    color: #fff;
    background: #da394f
}

.paginate-wrapper .pagination-container .page-link-item {
    display: block;
    font-size: 14px;
    color: #fff;
    background: transparent;
    border: 1px solid transparent;
    letter-spacing: 0;
    border-radius: 2px
}

.paginate-wrapper .pagination-container .page-link-item, .paginate-wrapper .pagination-container li:first-child .page-link-item, .paginate-wrapper .pagination-container li:last-child .page-link-item {
    width: 34px;
    height: 24px;
    line-height: 24px;
    text-align: center
}

.paginate-wrapper .pagination-container .page-link-item.break-view-link {
    border: none
}

.paginate-wrapper .pagination-container .next-item, .paginate-wrapper .pagination-container .prev-item {
    width: 34px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center
}

.paginate-wrapper .pagination-container .next-link-item, .paginate-wrapper .pagination-container .prev-link-item {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-top: 2px solid #fff;
    border-left: 2px solid #fff
}

.paginate-wrapper .pagination-container .prev-link-item {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.paginate-wrapper .pagination-container .next-link-item {
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg)
}

.paginate-wrapper .pagination-container .next-item:hover .next-link-item, .paginate-wrapper .pagination-container .prev-item:hover .prev-link-item {
    border-color: #da394f
}

.paginate-wrapper .pagination-container .page-item:hover > a {
    border: none;
    color: #fff;
    background-color: #da394f
}

.vhtml {
    color: #fff;
    font-size: 15px
}

.vhtml img {
    max-width: 100%
}

.vhtml h1, .vhtml h2 {
    color: #da394f
}

.vhtml ul {
    list-style: disc;
    white-space: normal;
    padding-left: 2.5em;
    margin-top: 1em;
    margin-bottom: 1em
}

.vhtml ul li {
    display: list-item !important;
    list-style: disc outside;
    overflow: visible;
    padding-right: 0;
    padding-left: 0;
    margin-right: 0;
    margin-left: 0
}

.vhtml ol {
    white-space: normal;
    list-style: decimal;
    padding-left: 2.5em;
    margin-top: 1em;
    margin-bottom: 1em
}

.vhtml ol li {
    display: list-item !important;
    list-style: decimal outside;
    overflow: visible;
    padding-right: 0;
    padding-left: 0;
    margin-right: 0;
    margin-left: 0
}

@-webkit-keyframes arrow {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes arrow {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.hide_scroll_bar::-webkit-scrollbar {
    width: 0
}

.swiper-arrow {
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: absolute;
    cursor: pointer;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 26px;
    height: 26px;
    border-radius: 7px;
    background-color: #313952;
    color: #ddd
}

.swiper-arrow:hover {
    opacity: .8
}

.swiper-arrow .svg-icon {
    width: 8px;
    height: 8px
}

.swiper-arrow.prev {
    top: 50%;
    left: 23px
}

.swiper-arrow.next {
    top: 50%;
    right: 23px
}

.swiper-arrow.next.swiper-button-disabled, .swiper-arrow.prev.swiper-button-disabled {
    color: #5c6991;
    cursor: not-allowed;
    opacity: .8
}

.swiper-pagination {
    bottom: 12px !important;
    display: flex;
    justify-content: center;
    position: absolute
}

.swiper-pagination .swiper-pagination-bullet {
    width: 50px;
    height: 6px;
    border-radius: unset;
    opacity: 1;
    background-color: rgba(195, 204, 213, .48)
}

.swiper-pagination .swiper-pagination-bullet-active {
    background: #da394f
}

.back_mask_gamepopup .game_popup {
    border-radius: 10px;
    background-color: #1b2132
}

.back_mask_gamepopup .game_popup .title {
    color: #fff
}

.back_mask_gamepopup .game_popup .close_gamepopup_button {
    color: #da394f;
    border-color: #da394f
}

.back_mask_gamepopup .game_popup .close_gamepopup_button:hover {
    opacity: .8
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .game_title {
    color: #fff
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span {
    width: 150px
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span.star_game {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 90px;
    height: 39px;
    padding: 0 24px;
    border-radius: 7px;
    background-color: #da394f;
    font-size: 16px;
    font-weight: 700;
    color: #fff
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span.star_game:hover {
    opacity: .8
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span.free_demo {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    transition: .2s;
    cursor: pointer;
    min-width: 108px;
    height: 39px;
    padding: 0 10px;
    border-radius: 7px;
    background-color: #1678ff;
    font-size: 16px;
    font-weight: 600;
    color: #fff
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item .br_gamePopup_mgulbutton span.free_demo:hover {
    opacity: .8
}

.back_mask_gamepopup .game_popup .game_popup_list .game_item:hover .br_gamePopup_mgulbutton {
    display: flex
}

.back_mask_gamepopup .pagination > li > a, .back_mask_gamepopup .pagination > li > span {
    padding: 0;
    width: 28px;
    height: 28px;
    border-radius: 3px;
    line-height: 28px;
    display: inline-block;
    font-size: 14px;
    margin: 0 5px;
    background-color: transparent;
    border-color: #fff;
    color: #fff
}

.back_mask_gamepopup .pagination > .active > a, .back_mask_gamepopup .pagination > .active > a:focus, .back_mask_gamepopup .pagination > .active > a:hover, .back_mask_gamepopup .pagination > .active > span, .back_mask_gamepopup .pagination > .active > span:focus, .back_mask_gamepopup .pagination > .active > span:hover, .back_mask_gamepopup .pagination > li > a:hover {
    color: #da394f;
    background-color: transparent;
    border-color: #da394f
}

@-webkit-keyframes slide-in1 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 40px, 0);
        transform: translate3d(0, 40px, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes slide-in1 {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(0, 40px, 0);
        transform: translate3d(0, 40px, 0)
    }
    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.popup_modal_close {
    right: 25px;
    top: 25px;
    width: 22px;
    height: 22px
}

.close_btn, .popup_modal_close {
    position: absolute;
    z-index: 10;
    cursor: pointer
}

.close_btn {
    height: 31px;
    width: 31px;
    display: flex;
    align-items: center;
    justify-content: center;
    top: 10px;
    right: 10px;
    background: #2b3248 url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAA9ElEQVQ4T5WTy00DMRiEv+krQBBQAK9AKIErZeSeCnhDSgACAs6caGeQI3vl9XqXxKfVyvPNzG9btn+BS0lLNli2x8Bc8eMBmEh6X4dhewd4BM4UBJtAWmJpuQJEyC5wP5QkE59Legu6BlBATiV95HVsbwNPQCPuAPogfeIqoICcxBTPpXNK16pQRE4zCb9bsfN9Q4AtIDiHdSzps3bEVYDtJJ5G0V0fpAOwPQIWwFTSa5zJHnBbg5TH2BFn9yRBjiR9dYZYcy47294HboAGkq5ycr6Q9DL0HjLIoaTv8JjWFmd1QpLrVRLbP8DVf86VOgfA7A/NsYolh98LxwAAAABJRU5ErkJggg==) no-repeat 10px 11px/10px 10px;
    border-radius: 50%;
    color: #fff
}

.close_btn:hover {
    opacity: .8
}

.iframe-wrapper {
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 100vh !important;
    padding-left: 190px
}

.iframe-wrapper.off {
    padding-left: 67px
}

.iframe-wrapper iframe {
    flex: 1;
    border: none
}

.iframe-wrapper .back-home {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    color: #4e5979
}

.iframe-wrapper .back-home .svg-icon {
    width: 100%;
    height: 100%
}

.iframe-wrapper .drag-mask {
    z-index: 1;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .45)
}

.grecaptcha-badge {
    visibility: hidden
}

.zoom-in-top-enter-active, .zoom-in-top-leave-active {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center top;
    transform-origin: center top
}

.zoom-in-top-enter, .zoom-in-top-leave-active {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.zoom-in-bottom-enter-active, .zoom-in-bottom-leave-active {
    opacity: 1;
    -webkit-transform: scaleY(1);
    transform: scaleY(1);
    transition: opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1);
    transition: transform .3s cubic-bezier(.23, 1, .32, 1), opacity .3s cubic-bezier(.23, 1, .32, 1), -webkit-transform .3s cubic-bezier(.23, 1, .32, 1);
    -webkit-transform-origin: center bottom;
    transform-origin: center bottom
}

.zoom-in-bottom-enter, .zoom-in-bottom-leave-active {
    opacity: 0;
    -webkit-transform: scaleY(0);
    transform: scaleY(0)
}

.br_acmc_body {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .6);
    z-index: 3
}

.acmc_content {
    width: 1290px;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.br_acmc_main {
    height: 620px;
    border-radius: 10px;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden
}

.br_acmc_mleft {
    width: 180px;
    height: 100%;
    float: left;
    background-color: #2b3248
}

.br_acmc_mltitle {
    width: 100%;
    padding: 30px 0;
    font-size: 27px;
    font-weight: 600;
    justify-content: center;
    text-align: center
}

.br_acmc_mlnav li, .br_acmc_mltitle {
    color: #fff;
    display: flex;
    align-items: center
}

.br_acmc_mlnav li {
    padding-left: 10px;
    height: 50px;
    font-size: 15px;
    cursor: pointer;
    transition: all .2s ease;
    position: relative
}

.br_acmc_mlnav li.active, .br_acmc_mlnav li:hover {
    color: #fff;
    background: #da394f
}

.br_acmc_mlnav li span {
    display: inline-block;
    max-width: 136px
}

.br_acmc_mlnav .acmc_icon {
    display: block;
    width: 25px;
    height: 25px;
    margin-right: 3px;
    margin-top: 3px;
    background-image: url(../img/icons.94345a04.png)
}

.br_acmc_mright {
    width: 1110px;
    height: 100%;
    float: right;
    background-color: #f7f7f7;
    border: 1px solid #ccc;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
    border-left: 0 none;
    position: relative
}

.br_acmc_mlnav li i.tip_fixd {
    display: inline-block;
    padding: 2px 7px;
    line-height: 14px;
    background: red;
    border-radius: 9px;
    position: absolute;
    left: 25px;
    top: 1px;
    font-size: 12px;
    font-style: normal
}