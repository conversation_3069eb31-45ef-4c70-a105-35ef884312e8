/*:root {
    --animate-duration: 0.3s;
    --animate-delay: 0.3s;
    --animate-repeat: 1
}

.animate__animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-duration: calc(var(--animate-duration)*1);
    animation-duration: calc(var(--animate-duration)*1);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.animate__animated.animate__infinite {
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}

.animate__animated.animate__forwards {
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards
}

.animate__animated.animate__faster {
    -webkit-animation-duration: .5s;
    animation-duration: .5s;
    -webkit-animation-duration: .15s;
    animation-duration: .15s;
    -webkit-animation-duration: calc(var(--animate-duration)*.5);
    animation-duration: calc(var(--animate-duration)*.5)
}

.animate__animated.animate__fast {
    -webkit-animation-duration: .8s;
    animation-duration: .8s;
    -webkit-animation-duration: .24s;
    animation-duration: .24s;
    -webkit-animation-duration: calc(var(--animate-duration)*.8);
    animation-duration: calc(var(--animate-duration)*.8)
}

.animate__animated.animate__slow {
    -webkit-animation-duration: 2s;
    animation-duration: 2s;
    -webkit-animation-duration: .6s;
    animation-duration: .6s;
    -webkit-animation-duration: calc(var(--animate-duration)*2);
    animation-duration: calc(var(--animate-duration)*2)
}

.animate__animated.animate__slower {
    -webkit-animation-duration: 3s;
    animation-duration: 3s;
    -webkit-animation-duration: .9s;
    animation-duration: .9s;
    -webkit-animation-duration: calc(var(--animate-duration)*3);
    animation-duration: calc(var(--animate-duration)*3)
}

.animate__animated.animate__without_normal_flow {
    position: absolute
}

.animate__animated.animate__repeat-1 {
    -webkit-animation-iteration-count: 1;
    animation-iteration-count: 1;
    -webkit-animation-iteration-count: calc(var(--animate-repeat)*1);
    animation-iteration-count: calc(var(--animate-repeat)*1)
}

.animate__animated.animate__repeat-2 {
    -webkit-animation-iteration-count: 2;
    animation-iteration-count: 2;
    -webkit-animation-iteration-count: calc(var(--animate-repeat)*2);
    animation-iteration-count: calc(var(--animate-repeat)*2)
}

.animate__animated.animate__repeat-3 {
    -webkit-animation-iteration-count: 3;
    animation-iteration-count: 3;
    -webkit-animation-iteration-count: calc(var(--animate-repeat)*3);
    animation-iteration-count: calc(var(--animate-repeat)*3)
}

.animate__animated.animate__repeat-4 {
    -webkit-animation-iteration-count: 4;
    animation-iteration-count: 4;
    -webkit-animation-iteration-count: calc(var(--animate-repeat)*4);
    animation-iteration-count: calc(var(--animate-repeat)*4)
}

.animate__animated.animate__repeat-5 {
    -webkit-animation-iteration-count: 5;
    animation-iteration-count: 5;
    -webkit-animation-iteration-count: calc(var(--animate-repeat)*5);
    animation-iteration-count: calc(var(--animate-repeat)*5)
}

.animate__animated.animate__delay-50ms {
    -webkit-animation-delay: .05s;
    animation-delay: .05s;
    -webkit-animation-delay: 15ms;
    animation-delay: 15ms;
    -webkit-animation-delay: calc(var(--animate-delay)*.05);
    animation-delay: calc(var(--animate-delay)*.05)
}

.animate__animated.animate__delay-100ms {
    -webkit-animation-delay: .1s;
    animation-delay: .1s;
    -webkit-animation-delay: .03s;
    animation-delay: .03s;
    -webkit-animation-delay: calc(var(--animate-delay)*.1);
    animation-delay: calc(var(--animate-delay)*.1)
}

.animate__animated.animate__delay-150ms {
    -webkit-animation-delay: .15s;
    animation-delay: .15s;
    -webkit-animation-delay: 45ms;
    animation-delay: 45ms;
    -webkit-animation-delay: calc(var(--animate-delay)*.15);
    animation-delay: calc(var(--animate-delay)*.15)
}

.animate__animated.animate__delay-200ms {
    -webkit-animation-delay: .2s;
    animation-delay: .2s;
    -webkit-animation-delay: .06s;
    animation-delay: .06s;
    -webkit-animation-delay: calc(var(--animate-delay)*.2);
    animation-delay: calc(var(--animate-delay)*.2)
}

.animate__animated.animate__delay-250ms {
    -webkit-animation-delay: .25s;
    animation-delay: .25s;
    -webkit-animation-delay: 75ms;
    animation-delay: 75ms;
    -webkit-animation-delay: calc(var(--animate-delay)*.25);
    animation-delay: calc(var(--animate-delay)*.25)
}

.animate__animated.animate__delay-300ms {
    -webkit-animation-delay: .3s;
    animation-delay: .3s;
    -webkit-animation-delay: .09s;
    animation-delay: .09s;
    -webkit-animation-delay: calc(var(--animate-delay)*.3);
    animation-delay: calc(var(--animate-delay)*.3)
}

.animate__animated.animate__delay-350ms {
    -webkit-animation-delay: .35s;
    animation-delay: .35s;
    -webkit-animation-delay: .105s;
    animation-delay: .105s;
    -webkit-animation-delay: calc(var(--animate-delay)*.35);
    animation-delay: calc(var(--animate-delay)*.35)
}

.animate__animated.animate__delay-400ms {
    -webkit-animation-delay: .4s;
    animation-delay: .4s;
    -webkit-animation-delay: .12s;
    animation-delay: .12s;
    -webkit-animation-delay: calc(var(--animate-delay)*.4);
    animation-delay: calc(var(--animate-delay)*.4)
}

.animate__animated.animate__delay-450ms {
    -webkit-animation-delay: .45s;
    animation-delay: .45s;
    -webkit-animation-delay: .135s;
    animation-delay: .135s;
    -webkit-animation-delay: calc(var(--animate-delay)*.45);
    animation-delay: calc(var(--animate-delay)*.45)
}

.animate__animated.animate__delay-500ms {
    -webkit-animation-delay: .5s;
    animation-delay: .5s;
    -webkit-animation-delay: .15s;
    animation-delay: .15s;
    -webkit-animation-delay: calc(var(--animate-delay)*.5);
    animation-delay: calc(var(--animate-delay)*.5)
}

.animate__animated.animate__delay-550ms {
    -webkit-animation-delay: .55s;
    animation-delay: .55s;
    -webkit-animation-delay: .165s;
    animation-delay: .165s;
    -webkit-animation-delay: calc(var(--animate-delay)*.55);
    animation-delay: calc(var(--animate-delay)*.55)
}

.animate__animated.animate__delay-600ms {
    -webkit-animation-delay: .6s;
    animation-delay: .6s;
    -webkit-animation-delay: .18s;
    animation-delay: .18s;
    -webkit-animation-delay: calc(var(--animate-delay)*.6);
    animation-delay: calc(var(--animate-delay)*.6)
}

.animate__animated.animate__delay-650ms {
    -webkit-animation-delay: .65s;
    animation-delay: .65s;
    -webkit-animation-delay: .195s;
    animation-delay: .195s;
    -webkit-animation-delay: calc(var(--animate-delay)*.65);
    animation-delay: calc(var(--animate-delay)*.65)
}

.animate__animated.animate__delay-700ms {
    -webkit-animation-delay: .7s;
    animation-delay: .7s;
    -webkit-animation-delay: .21s;
    animation-delay: .21s;
    -webkit-animation-delay: calc(var(--animate-delay)*.7);
    animation-delay: calc(var(--animate-delay)*.7)
}

.animate__animated.animate__delay-750ms {
    -webkit-animation-delay: .75s;
    animation-delay: .75s;
    -webkit-animation-delay: .225s;
    animation-delay: .225s;
    -webkit-animation-delay: calc(var(--animate-delay)*.75);
    animation-delay: calc(var(--animate-delay)*.75)
}

.animate__animated.animate__delay-800ms {
    -webkit-animation-delay: .8s;
    animation-delay: .8s;
    -webkit-animation-delay: .24s;
    animation-delay: .24s;
    -webkit-animation-delay: calc(var(--animate-delay)*.8);
    animation-delay: calc(var(--animate-delay)*.8)
}

.animate__animated.animate__delay-850ms {
    -webkit-animation-delay: .85s;
    animation-delay: .85s;
    -webkit-animation-delay: .255s;
    animation-delay: .255s;
    -webkit-animation-delay: calc(var(--animate-delay)*.85);
    animation-delay: calc(var(--animate-delay)*.85)
}

.animate__animated.animate__delay-900ms {
    -webkit-animation-delay: .9s;
    animation-delay: .9s;
    -webkit-animation-delay: .27s;
    animation-delay: .27s;
    -webkit-animation-delay: calc(var(--animate-delay)*.9);
    animation-delay: calc(var(--animate-delay)*.9)
}

.animate__animated.animate__delay-950ms {
    -webkit-animation-delay: .95s;
    animation-delay: .95s;
    -webkit-animation-delay: .285s;
    animation-delay: .285s;
    -webkit-animation-delay: calc(var(--animate-delay)*.95);
    animation-delay: calc(var(--animate-delay)*.95)
}

.animate__animated.animate__delay-1000ms {
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
    -webkit-animation-delay: .3s;
    animation-delay: .3s;
    -webkit-animation-delay: calc(var(--animate-delay)*1);
    animation-delay: calc(var(--animate-delay)*1)
}

.animate__animated.animate__delay-1050ms {
    -webkit-animation-delay: 1.05s;
    animation-delay: 1.05s;
    -webkit-animation-delay: .315s;
    animation-delay: .315s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.05);
    animation-delay: calc(var(--animate-delay)*1.05)
}

.animate__animated.animate__delay-1100ms {
    -webkit-animation-delay: 1.1s;
    animation-delay: 1.1s;
    -webkit-animation-delay: .33s;
    animation-delay: .33s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.1);
    animation-delay: calc(var(--animate-delay)*1.1)
}

.animate__animated.animate__delay-1150ms {
    -webkit-animation-delay: 1.15s;
    animation-delay: 1.15s;
    -webkit-animation-delay: .345s;
    animation-delay: .345s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.15);
    animation-delay: calc(var(--animate-delay)*1.15)
}

.animate__animated.animate__delay-1200ms {
    -webkit-animation-delay: 1.2s;
    animation-delay: 1.2s;
    -webkit-animation-delay: .36s;
    animation-delay: .36s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.2);
    animation-delay: calc(var(--animate-delay)*1.2)
}

.animate__animated.animate__delay-1250ms {
    -webkit-animation-delay: 1.25s;
    animation-delay: 1.25s;
    -webkit-animation-delay: .375s;
    animation-delay: .375s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.25);
    animation-delay: calc(var(--animate-delay)*1.25)
}

.animate__animated.animate__delay-1300ms {
    -webkit-animation-delay: 1.3s;
    animation-delay: 1.3s;
    -webkit-animation-delay: .39s;
    animation-delay: .39s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.3);
    animation-delay: calc(var(--animate-delay)*1.3)
}

.animate__animated.animate__delay-1350ms {
    -webkit-animation-delay: 1.35s;
    animation-delay: 1.35s;
    -webkit-animation-delay: .405s;
    animation-delay: .405s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.35);
    animation-delay: calc(var(--animate-delay)*1.35)
}

.animate__animated.animate__delay-1400ms {
    -webkit-animation-delay: 1.4s;
    animation-delay: 1.4s;
    -webkit-animation-delay: .42s;
    animation-delay: .42s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.4);
    animation-delay: calc(var(--animate-delay)*1.4)
}

.animate__animated.animate__delay-1450ms {
    -webkit-animation-delay: 1.45s;
    animation-delay: 1.45s;
    -webkit-animation-delay: .435s;
    animation-delay: .435s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.45);
    animation-delay: calc(var(--animate-delay)*1.45)
}

.animate__animated.animate__delay-1500ms {
    -webkit-animation-delay: 1.5s;
    animation-delay: 1.5s;
    -webkit-animation-delay: .45s;
    animation-delay: .45s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.5);
    animation-delay: calc(var(--animate-delay)*1.5)
}

.animate__animated.animate__delay-1550ms {
    -webkit-animation-delay: 1.55s;
    animation-delay: 1.55s;
    -webkit-animation-delay: .465s;
    animation-delay: .465s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.55);
    animation-delay: calc(var(--animate-delay)*1.55)
}

.animate__animated.animate__delay-1600ms {
    -webkit-animation-delay: 1.6s;
    animation-delay: 1.6s;
    -webkit-animation-delay: .48s;
    animation-delay: .48s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.6);
    animation-delay: calc(var(--animate-delay)*1.6)
}

.animate__animated.animate__delay-1650ms {
    -webkit-animation-delay: 1.65s;
    animation-delay: 1.65s;
    -webkit-animation-delay: .495s;
    animation-delay: .495s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.65);
    animation-delay: calc(var(--animate-delay)*1.65)
}

.animate__animated.animate__delay-1700ms {
    -webkit-animation-delay: 1.7s;
    animation-delay: 1.7s;
    -webkit-animation-delay: .51s;
    animation-delay: .51s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.7);
    animation-delay: calc(var(--animate-delay)*1.7)
}

.animate__animated.animate__delay-1750ms {
    -webkit-animation-delay: 1.75s;
    animation-delay: 1.75s;
    -webkit-animation-delay: .525s;
    animation-delay: .525s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.75);
    animation-delay: calc(var(--animate-delay)*1.75)
}

.animate__animated.animate__delay-1800ms {
    -webkit-animation-delay: 1.8s;
    animation-delay: 1.8s;
    -webkit-animation-delay: .54s;
    animation-delay: .54s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.8);
    animation-delay: calc(var(--animate-delay)*1.8)
}

.animate__animated.animate__delay-1850ms {
    -webkit-animation-delay: 1.85s;
    animation-delay: 1.85s;
    -webkit-animation-delay: .555s;
    animation-delay: .555s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.85);
    animation-delay: calc(var(--animate-delay)*1.85)
}

.animate__animated.animate__delay-1900ms {
    -webkit-animation-delay: 1.9s;
    animation-delay: 1.9s;
    -webkit-animation-delay: .57s;
    animation-delay: .57s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.9);
    animation-delay: calc(var(--animate-delay)*1.9)
}

.animate__animated.animate__delay-1950ms {
    -webkit-animation-delay: 1.95s;
    animation-delay: 1.95s;
    -webkit-animation-delay: .585s;
    animation-delay: .585s;
    -webkit-animation-delay: calc(var(--animate-delay)*1.95);
    animation-delay: calc(var(--animate-delay)*1.95)
}

.animate__animated.animate__delay-2000ms {
    -webkit-animation-delay: 2s;
    animation-delay: 2s;
    -webkit-animation-delay: .6s;
    animation-delay: .6s;
    -webkit-animation-delay: calc(var(--animate-delay)*2);
    animation-delay: calc(var(--animate-delay)*2)
}

@media(prefers-reduced-motion:reduce),print {
    .animate__animated {
        -webkit-animation-duration: 1ms!important;
        animation-duration: 1ms!important;
        -webkit-animation-iteration-count: 1!important;
        animation-iteration-count: 1!important;
        -webkit-transition-duration: 1ms!important;
        transition-duration: 1ms!important
    }

    .animate__animated[class*=Out] {
        opacity: 0
    }
}

@-webkit-keyframes vanishIn {
    0% {
        -webkit-filter: blur(.9rem);
        filter: blur(.9rem);
        opacity: 0;
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%
    }

    to {
        -webkit-filter: blur(0);
        filter: blur(0);
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%
    }
}

@keyframes vanishIn {
    0% {
        -webkit-filter: blur(.9rem);
        filter: blur(.9rem);
        opacity: 0;
        -webkit-transform: scale(1.2);
        transform: scale(1.2);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%
    }

    to {
        -webkit-filter: blur(0);
        filter: blur(0);
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%
    }
}

.animate__vanishIn {
    -webkit-animation-name: vanishIn;
    animation-name: vanishIn
}

@-webkit-keyframes streamer {
    0% {
        left: 0
    }

    to {
        left: 100%
    }
}

@keyframes streamer {
    0% {
        left: 0
    }

    to {
        left: 100%
    }
}

.animate__streamer {
    position: relative
}

.animate__streamer:after {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-duration: calc(var(--animate-duration)*1);
    animation-duration: calc(var(--animate-duration)*1);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-name: streamer;
    animation-name: streamer;
    background-color: var(--theme-main-bg-color);
    border-radius: .02rem;
    -webkit-box-shadow: 0 0 .01rem transparent;
    box-shadow: 0 0 .01rem transparent;
    content: "";
    height: 100%;
    opacity: .4;
    position: absolute;
    width: .15rem
}

@-webkit-keyframes wobbleTop {
    16.65% {
        -webkit-transform: skew(-12deg);
        transform: skew(-12deg)
    }

    33.3% {
        -webkit-transform: skew(10deg);
        transform: skew(10deg)
    }

    49.95% {
        -webkit-transform: skew(-6deg);
        transform: skew(-6deg)
    }

    66.6% {
        -webkit-transform: skew(4deg);
        transform: skew(4deg)
    }

    83.25% {
        -webkit-transform: skew(-2deg);
        transform: skew(-2deg)
    }

    to {
        -webkit-transform: skew(0);
        transform: skew(0)
    }
}

@keyframes wobbleTop {
    16.65% {
        -webkit-transform: skew(-12deg);
        transform: skew(-12deg)
    }

    33.3% {
        -webkit-transform: skew(10deg);
        transform: skew(10deg)
    }

    49.95% {
        -webkit-transform: skew(-6deg);
        transform: skew(-6deg)
    }

    66.6% {
        -webkit-transform: skew(4deg);
        transform: skew(4deg)
    }

    83.25% {
        -webkit-transform: skew(-2deg);
        transform: skew(-2deg)
    }

    to {
        -webkit-transform: skew(0);
        transform: skew(0)
    }
}

.animate__wobbleTop,.animate__wobbleTop__hover:active,.animate__wobbleTop__hover:focus,.animate__wobbleTop__hover:hover {
    -webkit-animation-name: wobbleTop;
    animation-name: wobbleTop
}

.animate__wobbleTop__hover {
    -webkit-transform: perspective(.01rem) translateZ(0);
    transform: perspective(.01rem) translateZ(0);
    -webkit-transform-origin: 0 100%;
    transform-origin: 0 100%
}

.animate__wobbleTop__hover:active,.animate__wobbleTop__hover:focus,.animate__wobbleTop__hover:hover {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-duration: calc(var(--animate-duration)*1);
    animation-duration: calc(var(--animate-duration)*1);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.animate__float__hover {
    -webkit-box-shadow: 0 0 .01rem transparent;
    box-shadow: 0 0 .01rem transparent;
    -webkit-transform: perspective(.01rem) translateZ(0);
    transform: perspective(.01rem) translateZ(0);
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform,-webkit-transform;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out
}

.animate__float__hover:active,.animate__float__hover:focus,.animate__float__hover:hover {
    -webkit-transform: translateY(-.08rem);
    transform: translateY(-.08rem)
}

.animate__forward__hover {
    -webkit-box-shadow: 0 0 .01rem transparent;
    box-shadow: 0 0 .01rem transparent;
    -webkit-transform: perspective(.01rem) translateZ(0);
    transform: perspective(.01rem) translateZ(0);
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform,-webkit-transform;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out
}

.animate__forward__hover:active,.animate__forward__hover:focus,.animate__forward__hover:hover {
    -webkit-transform: translateX(.08rem);
    transform: translateX(.08rem)
}

.animate__backward__hover {
    -webkit-box-shadow: 0 0 .01rem transparent;
    box-shadow: 0 0 .01rem transparent;
    -webkit-transform: perspective(.01rem) translateZ(0);
    transform: perspective(.01rem) translateZ(0);
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform,-webkit-transform;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out
}

.animate__backward__hover:active,.animate__backward__hover:focus,.animate__backward__hover:hover {
    -webkit-transform: translateX(-.08rem);
    transform: translateX(-.08rem)
}

@-webkit-keyframes heartBeat {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    50% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }

    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes heartBeat {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    50% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }

    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.animate__heartBeat,.animate__heartBeat__hover:active,.animate__heartBeat__hover:focus,.animate__heartBeat__hover:hover {
    -webkit-animation-duration: .54s;
    animation-duration: .54s;
    -webkit-animation-duration: calc(var(--animate-duration)*1.8);
    animation-duration: calc(var(--animate-duration)*1.8);
    -webkit-animation-name: heartBeat;
    animation-name: heartBeat;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.animate__heartBeat__hover:active,.animate__heartBeat__hover:focus,.animate__heartBeat__hover:hover {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-duration: calc(var(--animate-duration)*1);
    animation-duration: calc(var(--animate-duration)*1);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}

.animate__grow__hover {
    -webkit-box-shadow: 0 0 .01rem transparent;
    box-shadow: 0 0 .01rem transparent;
    -webkit-transition-duration: .3s;
    transition-duration: .3s;
    -webkit-transition-property: -webkit-transform;
    transition-property: -webkit-transform;
    transition-property: transform;
    transition-property: transform,-webkit-transform
}

.animate__grow__hover:active,.animate__grow__hover:focus,.animate__grow__hover:hover {
    -webkit-transform: scale(1.1);
    transform: scale(1.1)
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }

    50% {
        -webkit-transform: scale3d(1.05,1.05,1.05);
        transform: scale3d(1.05,1.05,1.05)
    }

    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@keyframes pulse {
    0% {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }

    50% {
        -webkit-transform: scale3d(1.05,1.05,1.05);
        transform: scale3d(1.05,1.05,1.05)
    }

    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

.animate__pulse {
    -webkit-animation-name: pulse;
    animation-name: pulse;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

@-webkit-keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes spin {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.animate__spin {
    -webkit-animation-name: spin;
    animation-name: spin;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

@-webkit-keyframes spinReverse {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(-1turn);
        transform: rotate(-1turn)
    }
}

@keyframes spinReverse {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }

    to {
        -webkit-transform: rotate(-1turn);
        transform: rotate(-1turn)
    }
}

.animate__spinReverse {
    -webkit-animation-name: spinReverse;
    animation-name: spinReverse;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

@-webkit-keyframes flipInY {
    0% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
        -webkit-transform: perspective(4rem) rotateY(90deg);
        transform: perspective(4rem) rotateY(90deg)
    }

    40% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: perspective(4rem) rotateY(-20deg);
        transform: perspective(4rem) rotateY(-20deg)
    }

    60% {
        opacity: 1;
        -webkit-transform: perspective(4rem) rotateY(10deg);
        transform: perspective(4rem) rotateY(10deg)
    }

    80% {
        -webkit-transform: perspective(4rem) rotateY(-5deg);
        transform: perspective(4rem) rotateY(-5deg)
    }

    to {
        -webkit-transform: perspective(4rem);
        transform: perspective(4rem)
    }
}

@keyframes flipInY {
    0% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        opacity: 0;
        -webkit-transform: perspective(4rem) rotateY(90deg);
        transform: perspective(4rem) rotateY(90deg)
    }

    40% {
        -webkit-animation-timing-function: ease-in;
        animation-timing-function: ease-in;
        -webkit-transform: perspective(4rem) rotateY(-20deg);
        transform: perspective(4rem) rotateY(-20deg)
    }

    60% {
        opacity: 1;
        -webkit-transform: perspective(4rem) rotateY(10deg);
        transform: perspective(4rem) rotateY(10deg)
    }

    80% {
        -webkit-transform: perspective(4rem) rotateY(-5deg);
        transform: perspective(4rem) rotateY(-5deg)
    }

    to {
        -webkit-transform: perspective(4rem);
        transform: perspective(4rem)
    }
}

.animate__flipInY {
    -webkit-animation-name: flipInY;
    animation-name: flipInY;
    -webkit-backface-visibility: visible!important;
    backface-visibility: visible!important
}

@-webkit-keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    50% {
        opacity: 1
    }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    50% {
        opacity: 1
    }
}

.animate__zoomIn {
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}

@-webkit-keyframes zoomOut {
    0% {
        opacity: 1
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    to {
        opacity: 0
    }
}

@keyframes zoomOut {
    0% {
        opacity: 1
    }

    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    to {
        opacity: 0
    }
}

.animate__zoomOut {
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}

@-webkit-keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

.animate__fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn
}

@-webkit-keyframes fadeOut {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.animate__fadeOut {
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

@-webkit-keyframes bounceIn {
    0%,20%,40%,60%,80%,to {
        -webkit-animation-timing-function: cubic-bezier(.215,.61,.355,1);
        animation-timing-function: cubic-bezier(.215,.61,.355,1)
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    20% {
        -webkit-transform: scale3d(1.1,1.1,1.1);
        transform: scale3d(1.1,1.1,1.1)
    }

    40% {
        -webkit-transform: scale3d(.9,.9,.9);
        transform: scale3d(.9,.9,.9)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03,1.03,1.03);
        transform: scale3d(1.03,1.03,1.03)
    }

    80% {
        -webkit-transform: scale3d(.97,.97,.97);
        transform: scale3d(.97,.97,.97)
    }

    to {
        opacity: 1;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@keyframes bounceIn {
    0%,20%,40%,60%,80%,to {
        -webkit-animation-timing-function: cubic-bezier(.215,.61,.355,1);
        animation-timing-function: cubic-bezier(.215,.61,.355,1)
    }

    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }

    20% {
        -webkit-transform: scale3d(1.1,1.1,1.1);
        transform: scale3d(1.1,1.1,1.1)
    }

    40% {
        -webkit-transform: scale3d(.9,.9,.9);
        transform: scale3d(.9,.9,.9)
    }

    60% {
        opacity: 1;
        -webkit-transform: scale3d(1.03,1.03,1.03);
        transform: scale3d(1.03,1.03,1.03)
    }

    80% {
        -webkit-transform: scale3d(.97,.97,.97);
        transform: scale3d(.97,.97,.97)
    }

    to {
        opacity: 1;
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

.animate__bounceIn {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
    -webkit-animation-duration: .225s;
    animation-duration: .225s;
    -webkit-animation-duration: calc(var(--animate-duration)*.75);
    animation-duration: calc(var(--animate-duration)*.75);
    -webkit-animation-name: bounceIn;
    animation-name: bounceIn
}

@-webkit-keyframes bounceOut {
    20% {
        -webkit-transform: scale3d(.9,.9,.9);
        transform: scale3d(.9,.9,.9)
    }

    50%,55% {
        opacity: 1;
        -webkit-transform: scale3d(1.1,1.1,1.1);
        transform: scale3d(1.1,1.1,1.1)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }
}

@keyframes bounceOut {
    20% {
        -webkit-transform: scale3d(.9,.9,.9);
        transform: scale3d(.9,.9,.9)
    }

    50%,55% {
        opacity: 1;
        -webkit-transform: scale3d(1.1,1.1,1.1);
        transform: scale3d(1.1,1.1,1.1)
    }

    to {
        opacity: 0;
        -webkit-transform: scale3d(.3,.3,.3);
        transform: scale3d(.3,.3,.3)
    }
}

.animate__bounceOut {
    -webkit-animation-duration: .75s;
    animation-duration: .75s;
    -webkit-animation-duration: .225s;
    animation-duration: .225s;
    -webkit-animation-duration: calc(var(--animate-duration)*.75);
    animation-duration: calc(var(--animate-duration)*.75);
    -webkit-animation-name: bounceOut;
    animation-name: bounceOut
}

@-webkit-keyframes slideInLeft {
    0% {
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0);
        visibility: visible
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes slideInLeft {
    0% {
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0);
        visibility: visible
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__slideInLeft {
    -webkit-animation-name: slideInLeft;
    animation-name: slideInLeft
}

@-webkit-keyframes slideOutLeft {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0);
        visibility: hidden
    }
}

@keyframes slideOutLeft {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0);
        visibility: hidden
    }
}

.animate__slideOutLeft {
    -webkit-animation-name: slideOutLeft;
    animation-name: slideOutLeft
}

@-webkit-keyframes slideInRight {
    0% {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0);
        visibility: visible
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes slideInRight {
    0% {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0);
        visibility: visible
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__slideInRight {
    -webkit-animation-name: slideInRight;
    animation-name: slideInRight
}

@-webkit-keyframes slideInBottom {
    0% {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes slideInBottom {
    0% {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0)
    }

    to {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__slideInBottom {
    -webkit-animation-name: slideInBottom;
    animation-name: slideInBottom
}

@-webkit-keyframes slideOutBottom {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0);
        visibility: visible
    }
}

@keyframes slideOutBottom {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,100%,0);
        transform: translate3d(0,100%,0);
        visibility: visible
    }
}

.animate__slideOutBottom {
    -webkit-animation-name: slideOutBottom;
    animation-name: slideOutBottom
}

@-webkit-keyframes slideOutRight {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0);
        visibility: hidden
    }
}

@keyframes slideOutRight {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0);
        visibility: hidden
    }
}

.animate__popEnter,.animate__popLeave,.animate__pushEnter,.animate__pushLeave {
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.animate__popEnter.v-leave-to,.animate__popLeave.v-leave-to,.animate__pushEnter.v-leave-to,.animate__pushLeave.v-leave-to {
    position: absolute;
    width: 100%
}

.animate__pushEnter {
    -webkit-animation-name: pushEnter;
    animation-name: pushEnter
}

@-webkit-keyframes pushEnter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes pushEnter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__pushLeave {
    -webkit-animation-name: pushLeave;
    animation-name: pushLeave
}

@-webkit-keyframes pushLeave {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }
}

@keyframes pushLeave {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }
}

.animate__popEnter {
    -webkit-animation-name: popEnter;
    animation-name: popEnter
}

@-webkit-keyframes popEnter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes popEnter {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__popLeave {
    -webkit-animation-name: popLeave;
    animation-name: popLeave
}

@-webkit-keyframes popLeave {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }
}

@keyframes popLeave {
    0% {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }
}

.animate__slideOutRight {
    -webkit-animation-name: slideOutRight;
    animation-name: slideOutRight
}

@-webkit-keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInRight {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__fadeInRight {
    -webkit-animation-name: fadeInRight;
    animation-name: fadeInRight
}

@-webkit-keyframes fadeOutRight {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }
}

@keyframes fadeOutRight {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(100%,0,0);
        transform: translate3d(100%,0,0)
    }
}

.animate__fadeOutRight {
    -webkit-animation-name: fadeOutRight;
    animation-name: fadeOutRight
}

@-webkit-keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

@keyframes fadeInLeft {
    0% {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }

    to {
        opacity: 1;
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }
}

.animate__fadeInLeft {
    -webkit-animation-name: fadeInLeft;
    animation-name: fadeInLeft
}

@-webkit-keyframes fadeOutLeft {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }
}

@keyframes fadeOutLeft {
    0% {
        opacity: 1
    }

    to {
        opacity: 0;
        -webkit-transform: translate3d(-100%,0,0);
        transform: translate3d(-100%,0,0)
    }
}

.animate__fadeOutLeft {
    -webkit-animation-name: fadeOutLeft;
    animation-name: fadeOutLeft
}

@-webkit-keyframes scrollUpHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,-50%,0);
        transform: translate3d(0,-50%,0)
    }
}

@keyframes scrollUpHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,-50%,0);
        transform: translate3d(0,-50%,0)
    }
}

@-webkit-keyframes scrollRightHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(50%,0,0);
        transform: translate3d(50%,0,0)
    }
}

@keyframes scrollRightHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(50%,0,0);
        transform: translate3d(50%,0,0)
    }
}

@-webkit-keyframes scrollDownHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,50%,0);
        transform: translate3d(0,50%,0)
    }
}

@keyframes scrollDownHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(0,50%,0);
        transform: translate3d(0,50%,0)
    }
}

@-webkit-keyframes scrollLeftHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(-50%,0,0);
        transform: translate3d(-50%,0,0)
    }
}

@keyframes scrollLeftHalf {
    0% {
        -webkit-transform: translateZ(0);
        transform: translateZ(0)
    }

    to {
        -webkit-transform: translate3d(-50%,0,0);
        transform: translate3d(-50%,0,0)
    }
}

.animate__scrollUpHalf {
    -webkit-animation-name: scrollUpHalf;
    animation-name: scrollUpHalf;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.animate__scrollRightHalf {
    -webkit-animation-name: scrollRightHalf;
    animation-name: scrollRightHalf;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.animate__scrollDownHalf {
    -webkit-animation-name: scrollDownHalf;
    animation-name: scrollDownHalf;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

.animate__scrollLeftHalf {
    -webkit-animation-name: scrollLeftHalf;
    animation-name: scrollLeftHalf;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear
}

@-webkit-keyframes rotateScaleUpX {
    0% {
        -webkit-transform: scale(1) rotate(0);
        transform: scale(1) rotate(0)
    }

    50% {
        -webkit-transform: scale(2) rotate(180deg);
        transform: scale(2) rotate(180deg)
    }

    to {
        -webkit-transform: scale(1) rotate(1turn);
        transform: scale(1) rotate(1turn)
    }
}

@keyframes rotateScaleUpX {
    0% {
        -webkit-transform: scale(1) rotate(0);
        transform: scale(1) rotate(0)
    }

    50% {
        -webkit-transform: scale(2) rotate(180deg);
        transform: scale(2) rotate(180deg)
    }

    to {
        -webkit-transform: scale(1) rotate(1turn);
        transform: scale(1) rotate(1turn)
    }
}

@-webkit-keyframes beatUpAndDown {
    0% {
        translate: 0 -.04rem
    }

    to {
        translate: 0 0
    }
}

@keyframes beatUpAndDown {
    0% {
        translate: 0 -.04rem
    }

    to {
        translate: 0 0
    }
}

.animate__beatUpAndDown {
    -webkit-animation-name: beatUpAndDown;
    animation-name: beatUpAndDown
}*/
/*

@font-face {
    font-display: swap;
    font-family: Avenir;
    src: url(/assets/Avenir.06e288a725c488f45f92.ttf)
}

@font-face {
    font-display: swap;
    font-family: EncodeBold;
    src: url(/assets/EncodeBold.b249e5453a7625590e9e.ttf)
}

@font-face {
    font-display: swap;
    font-family: Irr3v;
    src: url(/assets/Irr3v.cf5360e4ec1080db9f28.ttf)
}

@font-face {
    font-display: swap;
    font-family: Motel;
    src: url(/assets/Motel.03a1270998a286443088.ttf)
}

@font-face {
    font-display: swap;
    font-family: Ravenna;
    src: url(/assets/Ravenna.ad6630eb873dd01c59cc.ttf)
}

html body {
    line-height: normal
}

html h1,html h2,html h3,html h4,html h5,html h6 {
    color: var(--theme-hx-color)
}
*/

html .ant-input,html .ant-select-selection {
    background-color: var(--theme-main-bg-color);
    border-color: var(--theme-color-line);
    color: var(--theme-text-color-lighten)
}

html .ant-input:focus,html .ant-select-selection:focus {
    border-color: var(--theme-primary-color)
}

html .ant-input:focus,html .ant-select-open .ant-select-selection,html .ant-select-selection:focus {
    -webkit-box-shadow: 0 0 0 .02rem rgba(var(--theme-primay-color-hex),.2)!important;
    box-shadow: 0 0 0 .02rem rgba(var(--theme-primay-color-hex),.2)!important
}

html .ant-input-search-icon {
    color: var(--theme-search-icon-color)
}

html .ant-input::-webkit-input-placeholder {
    color: var(--theme-text-color-placeholder)
}

html .ant-input::-moz-placeholder {
    color: var(--theme-text-color-placeholder)
}

html .ant-input:-ms-input-placeholder {
    color: var(--theme-text-color-placeholder)
}

html .ant-input::-moz-selection {
    background: var(--theme-primary-color);
    color: var(--theme-primary-font-color)
}

html .ant-input::selection {
    background: var(--theme-primary-color);
    color: var(--theme-primary-font-color)
}

html .ant-input::-webkit-input-safebox-button {
    display: none
}

html .ant-spin-container:after {
    background: transparent none repeat 0 0/auto auto padding-box border-box scroll;
    background: initial
}

html .ant-spin-nested-loading>div>.ant-spin {
    max-height: 100%
}

html .ant-input-affix-wrapper .ant-input-clear-icon {
    color: var(--theme-text-color-lighten)
}

html .ant-input-affix-wrapper .ant-input-clear-icon i,html .ant-input-affix-wrapper .ant-input-clear-icon svg {
    font-size: .24rem
}

html .ant-input-affix-wrapper .ant-input-clear-icon+i {
    margin-left: .15rem
}

html .ant-select-selection__placeholder {
    color: var(--theme-text-color-placeholder)
}

html .ant-btn {
    border-radius: .1rem;
    padding: 0 .1rem
}

html .ant-btn:not(.ant-btn-link).disabled,html .ant-btn:not(.ant-btn-link).disabled:active,html .ant-btn:not(.ant-btn-link).disabled:focus,html .ant-btn:not(.ant-btn-link).disabled:hover,html .ant-btn:not(.ant-btn-link)[disabled],html .ant-btn:not(.ant-btn-link)[disabled]:active,html .ant-btn:not(.ant-btn-link)[disabled]:focus,html .ant-btn:not(.ant-btn-link)[disabled]:hover {
    background-color: var(--theme-disabled-bg-color);
    border-color: var(--theme-disabled-bg-color);
    color: var(--theme-disabled-font-color)
}

html .ant-btn.ant-btn-link {
    padding: 0
}

html .ant-btn.ant-btn-link.disabled,html .ant-btn.ant-btn-link[disabled] {
    color: var(--theme-text-color-lighten)
}

html .ant-btn.ant-btn-default {
    background-color: transparent;
    border-color: var(--theme-primary-color);
    color: var(--theme-primary-color)
}

html .ant-btn.ant-btn-default.disabled,html .ant-btn.ant-btn-default.disabled:hover,html .ant-btn.ant-btn-default[disabled],html .ant-btn.ant-btn-default[disabled]:hover {
    background-color: transparent;
    border-color: var(--theme-text-color-lighten);
    color: var(--theme-text-color-lighten)
}

html .ant-btn.ant-btn-primary {
    color: var(--theme-primary-font-color);
    text-shadow: none
}

html .ant-btn.ant-btn-primary,html .ant-btn.ant-btn-primary.active,html .ant-btn.ant-btn-primary:active,html .ant-btn.ant-btn-primary:focus,html .ant-btn.ant-btn-primary:hover {
    background-color: var(--theme-primary-color);
    border-color: var(--theme-primary-color)
}

html .ant-btn.ant-btn-finance {
    color: var(--theme-primary-font-color);
    color: #fff
}

html .ant-btn.ant-btn-finance,html .ant-btn.ant-btn-finance.active,html .ant-btn.ant-btn-finance:active,html .ant-btn.ant-btn-finance:focus,html .ant-btn.ant-btn-finance:hover {
    background-color: var(--theme-secondary-color-finance);
    border-color: var(--theme-secondary-color-finance)
}

html .ant-btn.ant-btn-success {
    color: var(--theme-primary-font-color);
    color: var(--theme-secondary-color-success-text)
}

html .ant-btn.ant-btn-success,html .ant-btn.ant-btn-success.active,html .ant-btn.ant-btn-success:active,html .ant-btn.ant-btn-success:focus,html .ant-btn.ant-btn-success:hover {
    background-color: var(--theme-secondary-color-success);
    border-color: var(--theme-secondary-color-success)
}

html .ant-message {
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 2000
}

html .ant-message .ant-message-notice-content {
    word-wrap: break-word;
    background-color: rgba(0,0,0,.8);
    border-radius: .1rem;
    color: #fff;
    font-size: .2rem;
    max-width: 8rem;
    padding: .2rem .3rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-loading>.anticon {
    border-radius: 100%;
    display: inline-block;
    font-size: .24rem;
    height: .3rem;
    margin-right: .2rem;
    position: relative;
    width: .3rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-loading>.anticon>svg {
    left: 50%;
    margin-left: -.5em;
    margin-top: -.5em;
    position: absolute;
    top: 50%
}

html .ant-message .ant-message-notice-content .ant-message-custom-content>.anticon {
    margin-right: .2rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-success .anticon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(var(--theme-secondary-color-success-linear-gradient)),to(var(--theme-secondary-color-success)));
    background-image: linear-gradient(to bottom,var(--theme-secondary-color-success-linear-gradient),var(--theme-secondary-color-success));
    background-image: -webkit-gradient(linear,left top,left bottom,from(#04be02),to(#04be02));
    background-image: linear-gradient(180deg,#04be02,#04be02);
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    height: .3rem;
    justify-content: center;
    width: .3rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-success .anticon:before {
    border-radius: 50%;
    content: "";
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_pay_1&scale=0.4285714286";
    display: inline-block;
    visibility: hidden
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-success .anticon svg {
    display: none
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-error .anticon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(var(--theme-secondary-color-error-linear-gradient)),to(var(--theme-secondary-color-error)));
    background-image: linear-gradient(to bottom,var(--theme-secondary-color-error-linear-gradient),var(--theme-secondary-color-error));
    background-image: -webkit-gradient(linear,left top,left bottom,from(#ea4e3d),to(#ea4e3d));
    background-image: linear-gradient(180deg,#ea4e3d,#ea4e3d);
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    height: .3rem;
    justify-content: center;
    width: .3rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-error .anticon:before {
    border-radius: 50%;
    content: "";
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_pay_2&scale=0.4285714286";
    display: inline-block;
    visibility: hidden
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-error .anticon svg {
    display: none
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-warning .anticon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(var(--theme-secondary-color-finance-linear-gradient)),to(var(--theme-secondary-color-finance)));
    background-image: linear-gradient(to bottom,var(--theme-secondary-color-finance-linear-gradient),var(--theme-secondary-color-finance));
    background-image: -webkit-gradient(linear,left top,left bottom,from(#ffaa09),to(#ffaa09));
    background-image: linear-gradient(180deg,#ffaa09,#ffaa09);
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    height: .3rem;
    justify-content: center;
    width: .3rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-warning .anticon:before {
    border-radius: 50%;
    content: "";
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_pay_4&scale=0.4285714286";
    display: inline-block;
    visibility: hidden
}

html .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-warning .anticon svg {
    display: none
}

html .ant-message .ant-message-notice-content .ant-message-custom-content:not(.ant-message-loading):not(.ant-message-success):not(.ant-message-error):not(.ant-message-warning)>.anticon {
    border-radius: 100%;
    display: inline-block;
    font-size: .3rem;
    height: .3rem;
    margin-right: .2rem;
    position: relative;
    width: .3rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content:not(.ant-message-loading):not(.ant-message-success):not(.ant-message-error):not(.ant-message-warning)>.anticon>svg {
    left: 50%;
    margin-left: -.5em;
    margin-top: -.5em;
    position: absolute;
    top: 50%
}

html .ant-message .ant-message-notice-content .ant-message-custom-content:not(.ant-message-loading):not(.ant-message-success):not(.ant-message-error):not(.ant-message-warning)>.anticon:before {
    background-color: #fff;
    border-radius: 50%;
    content: "";
    display: inline-block;
    height: .15rem;
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    width: .15rem
}

html .ant-message .ant-message-notice-content .ant-message-custom-content>span {
    -ms-flex: 1;
    flex: 1;
    overflow: hidden
}

html .ant-modal {
    padding: 0;
    position: relative;
    -webkit-transform-origin: center center!important;
    transform-origin: center center!important
}

html .ant-modal .ant-modal-content {
    background-color: var(--theme-main-bg-color);
    border: thin solid;
    border-color: var(--theme-color-line);
    border-radius: .2rem;
    max-height: calc(100vh - .1rem);
    max-height: calc(var(--vh,1vh)*100 - .1rem);
    overflow: auto;
    position: static
}

html .ant-modal .ant-modal-content .ant-modal-header {
    background-color: var(--theme-main-bg-color);
    border-bottom: none;
    border-radius: .2rem .2rem 0 0;
    padding: .2rem .3rem
}

html .ant-modal .ant-modal-content .ant-modal-header .ant-modal-title {
    color: var(--theme-text-color-darken);
    font-size: .24rem;
    font-weight: revert;
    line-height: .34rem;
    text-align: center
}

html .ant-modal .ant-modal-content .ant-modal-header+.ant-modal-body {
    padding-top: 0
}

html .ant-modal .ant-modal-content .ant-modal-close-x {
    color: var(--theme-text-color);
    font-size: .16rem;
    height: .56rem;
    line-height: .56rem;
    width: .56rem
}

html .ant-modal-confirm.my-modal-base .ant-modal-body {
    padding: .2rem .3rem .3rem
}

html .ant-modal-confirm.my-modal-base .ant-modal-body .ant-modal-confirm-title {
    font-size: .3rem
}

html .ant-modal-confirm.my-modal-base.hidden-btns .ant-modal-confirm-body-wrapper>.ant-modal-confirm-btns {
    display: none
}

html .ant-modal-confirm .ant-modal-confirm-title {
    color: var(--theme-text-color-darken);
    font-size: .24rem;
    font-weight: revert
}

html .ant-modal-confirm .ant-modal-confirm-content {
    color: var(--theme-text-color);
    font-size: .18rem;
    margin: .3rem 0 .65rem
}

html .ant-modal-confirm .ant-modal-confirm-btns {
    border-top: none;
    display: -ms-flexbox;
    display: flex;
    float: none;
    text-align: center
}

html .ant-modal-confirm .ant-modal-confirm-btns button:not([style*="display: none"])+button {
    margin-left: .3rem
}

html .ant-modal-confirm .ant-modal-confirm-btns button[style*="display: none"]+button {
    margin-left: 0
}

html .ant-modal-confirm .ant-modal-confirm-btns .ant-btn {
    -ms-flex: 1;
    flex: 1;
    font-size: .2rem;
    height: .55rem
}

html .ant-modal-confirm .ant-modal-confirm-btns .ant-btn.ant-btn-primary:not(.disabled):not([disabled]) {
    background-color: var(--theme-primary-color);
    border-color: var(--theme-primary-color);
    color: var(--theme-primary-font-color)
}

html .ant-progress-inner {
    background-color: var(--theme-text-color-placeholder)
}

html .ant-layout {
    background-color: var(--theme-bg-color)
}

html .ant-form .ant-input-password-icon.anticon-eye-invisible {
    color: var(--theme-color-line)!important
}

html .ant-form .ant-form-explain {
    -ms-flex-align: center;
    align-items: center;
    color: var(--theme-text-color-lighten);
    display: -ms-flexbox;
    display: flex;
    min-height: .3rem;
    width: 100%
}

html .ant-form .has-error .ant-form-explain:not(.show-help-leave) {
    color: var(--theme-secondary-color-error)
}

html .ant-form .has-error .ant-form-explain:not(.show-help-leave):before {
    background-color: var(--theme-secondary-color-error);
    border-radius: 50%;
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_tip1&scale=0.9";
    height: 0;
    margin-right: .1rem;
    visibility: hidden;
    width: 0
}

html .ant-form .has-error .ant-input-affix-wrapper .ant-input:focus {
    -webkit-box-shadow: 0 0 0 .02rem rgba(245,34,45,.2);
    box-shadow: 0 0 0 .02rem rgba(245,34,45,.2)
}

html .ant-form .has-success .ant-form-explain:not(.show-help-leave) {
    color: var(--theme-secondary-color-success)
}

html .ant-form .has-success .ant-form-explain:not(.show-help-leave):before {
    background-color: var(--theme-secondary-color-success);
    border-radius: 50%;
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_verified&scale=1.2";
    margin-right: .1rem;
    visibility: hidden
}

html .ant-form .is-validating .ant-form-explain:not(.show-help-leave):before {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-duration: .3s;
    animation-duration: .3s;
    -webkit-animation-duration: calc(var(--animate-duration)*1);
    animation-duration: calc(var(--animate-duration)*1);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: spin;
    animation-name: spin;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    background-color: var(--theme-primary-color);
    content: "";
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 1em;
    height: 1.2em;
    margin-right: .1rem;
    -webkit-mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiPjxwYXRoIGQ9Ik05ODggNTQ4Yy0xOS45IDAtMzYtMTYuMS0zNi0zNiAwLTU5LjQtMTEuNi0xMTctMzQuNi0xNzEuM2E0NDAuNDUgNDQwLjQ1IDAgMCAwLTk0LjMtMTM5LjkgNDM3LjcxIDQzNy43MSAwIDAgMC0xMzkuOS05NC4zQzYyOSA4My42IDU3MS40IDcyIDUxMiA3MmMtMTkuOSAwLTM2LTE2LjEtMzYtMzZzMTYuMS0zNiAzNi0zNmM2OS4xIDAgMTM2LjIgMTMuNSAxOTkuMyA0MC4zQzc3Mi4zIDY2IDgyNyAxMDMgODc0IDE1MGM0NyA0NyA4My45IDEwMS44IDEwOS43IDE2Mi43IDI2LjcgNjMuMSA0MC4yIDEzMC4yIDQwLjIgMTk5LjMuMSAxOS45LTE2IDM2LTM1LjkgMzZ6Ii8+PC9zdmc+) no-repeat 50% 50%;
    mask: url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiPjxwYXRoIGQ9Ik05ODggNTQ4Yy0xOS45IDAtMzYtMTYuMS0zNi0zNiAwLTU5LjQtMTEuNi0xMTctMzQuNi0xNzEuM2E0NDAuNDUgNDQwLjQ1IDAgMCAwLTk0LjMtMTM5LjkgNDM3LjcxIDQzNy43MSAwIDAgMC0xMzkuOS05NC4zQzYyOSA4My42IDU3MS40IDcyIDUxMiA3MmMtMTkuOSAwLTM2LTE2LjEtMzYtMzZzMTYuMS0zNiAzNi0zNmM2OS4xIDAgMTM2LjIgMTMuNSAxOTkuMyA0MC4zQzc3Mi4zIDY2IDgyNyAxMDMgODc0IDE1MGM0NyA0NyA4My45IDEwMS44IDEwOS43IDE2Mi43IDI2LjcgNjMuMSA0MC4yIDEzMC4yIDQwLjIgMTk5LjMuMSAxOS45LTE2IDM2LTM1LjkgMzZ6Ii8+PC9zdmc+) no-repeat 50% 50%;
    -webkit-mask-size: cover;
    mask-size: cover;
    width: 1em;
    width: 1.2em
}

html .ant-form .ant-input-password .ant-input-suffix>.anticon-eye {
    color: var(--theme-primary-color)
}

html .ant-dropdown-menu {
    background-color: var(--theme-main-bg-color);
    border-radius: .1rem
}

html .ant-dropdown-menu .ant-dropdown-menu-item {
    background-color: var(--theme-main-bg-color);
    color: var(--theme-text-color);
    font-size: .16rem;
    line-height: .38rem;
    margin: .08rem 0;
    padding: 0 .19rem
}

html .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-title>div,html .ant-dropdown-menu .ant-dropdown-menu-item>div {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap
}

html .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-title.ant-dropdown-menu-item-selected,html .ant-dropdown-menu .ant-dropdown-menu-item .ant-dropdown-menu-submenu-title:hover,html .ant-dropdown-menu .ant-dropdown-menu-item.ant-dropdown-menu-item-selected,html .ant-dropdown-menu .ant-dropdown-menu-item:hover {
    background-color: var(--theme-bg-color)
}

html .ant-select .ant-select-selection--single {
    background-color: var(--theme-main-bg-color);
    border-radius: .06rem;
    color: var(--theme-text-color-lighten);
    font-size: .18rem
}

html .ant-select .ant-select-selection--single .ant-select-selection__rendered {
    margin-left: .15rem;
    margin-right: .45rem
}

html .ant-select .ant-select-arrow {
    color: var(--theme-text-color-lighten);
    font-size: .17rem;
    right: .15rem
}

html .ant-select-dropdown-search {
    max-width: 3.15rem;
    min-width: 1.6rem;
    width: auto!important
}

html .ant-select-dropdown {
    background-color: var(--theme-main-bg-color);
    border: .01rem solid var(--theme-color-line)!important;
    border-radius: .1rem;
    -webkit-box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.1);
    box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.1);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden
}

html .ant-select-dropdown .ant-select-dropdown-menu {
    background-color: var(--theme-main-bg-color);
    max-height: 3.7rem;
    overflow: auto!important;
    padding: .1rem 0
}

html .ant-select-dropdown .ant-select-dropdown-menu-item {
    background-color: var(--theme-main-bg-color);
    color: var(--theme-text-color-lighten);
    font-size: .2rem;
    line-height: .55rem;
    min-height: .55rem;
    padding: 0 .15rem;
    text-align: left
}

html .ant-select-dropdown .ant-select-dropdown-menu-item .ant-empty {
    font-size: .2rem;
    height: 100%;
    line-height: inherit
}

html .ant-select-dropdown .ant-select-dropdown-menu-item:not(.ant-select-dropdown-menu-item-disabled):hover {
    background-color: var(--theme-main-bg-color)
}

html .ant-select-dropdown .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected {
    background-color: var(--theme-bg-color);
    color: var(--theme-primary-color);
    font-weight: 400
}

html .ant-select-dropdown .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected:hover {
    background-color: var(--theme-bg-color)
}

html .ant-table .ant-empty .ant-empty-description {
    color: var(--theme-text-color-lighten);
    font-size: .22rem
}

html .ant-table .ant-table-column-sorter-down.on,html .ant-table .ant-table-column-sorter-up.on {
    color: var(--theme-primary-color)!important
}

html .ant-pagination.ant-table-pagination {
    margin-top: .2rem
}

html .ant-pagination .ant-pagination-item,html .ant-pagination .ant-pagination-jump-next,html .ant-pagination .ant-pagination-jump-prev,html .ant-pagination .ant-pagination-next,html .ant-pagination .ant-pagination-prev {
    background-color: var(--theme-main-bg-color);
    border-color: var(--theme-color-line);
    font-size: .18rem;
    height: .44rem;
    line-height: .44rem;
    min-width: .44rem
}

html .ant-pagination .ant-pagination-item svg,html .ant-pagination .ant-pagination-jump-next svg,html .ant-pagination .ant-pagination-jump-prev svg,html .ant-pagination .ant-pagination-next svg,html .ant-pagination .ant-pagination-prev svg {
    font-size: .18rem
}

html .ant-pagination .ant-pagination-item:not(.ant-pagination-next),html .ant-pagination .ant-pagination-jump-next:not(.ant-pagination-next),html .ant-pagination .ant-pagination-jump-prev:not(.ant-pagination-next),html .ant-pagination .ant-pagination-next:not(.ant-pagination-next),html .ant-pagination .ant-pagination-prev:not(.ant-pagination-next) {
    margin-right: .1rem
}

html .ant-pagination .ant-pagination-item:not(.ant-pagination-prev):not(.ant-pagination-next):not(.ant-pagination-item),html .ant-pagination .ant-pagination-jump-next:not(.ant-pagination-prev):not(.ant-pagination-next):not(.ant-pagination-item),html .ant-pagination .ant-pagination-jump-prev:not(.ant-pagination-prev):not(.ant-pagination-next):not(.ant-pagination-item),html .ant-pagination .ant-pagination-next:not(.ant-pagination-prev):not(.ant-pagination-next):not(.ant-pagination-item),html .ant-pagination .ant-pagination-prev:not(.ant-pagination-prev):not(.ant-pagination-next):not(.ant-pagination-item) {
    border-color: var(--theme-color-line)
}

html .ant-pagination .ant-pagination-item a,html .ant-pagination .ant-pagination-jump-next a,html .ant-pagination .ant-pagination-jump-prev a,html .ant-pagination .ant-pagination-next a,html .ant-pagination .ant-pagination-prev a {
    color: var(--theme-text-color-darken)
}

html .ant-pagination .ant-pagination-item .ant-pagination-item-ellipsis,html .ant-pagination .ant-pagination-jump-next .ant-pagination-item-ellipsis,html .ant-pagination .ant-pagination-jump-prev .ant-pagination-item-ellipsis,html .ant-pagination .ant-pagination-next .ant-pagination-item-ellipsis,html .ant-pagination .ant-pagination-prev .ant-pagination-item-ellipsis {
    font-size: .12rem;
    letter-spacing: 0
}

html .ant-pagination .ant-pagination-next,html .ant-pagination .ant-pagination-prev {
    line-height: .44rem;
    vertical-align: middle
}

html .ant-pagination .ant-pagination-next .ant-pagination-item-link,html .ant-pagination .ant-pagination-prev .ant-pagination-item-link {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border-color: var(--theme-color-line);
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

html .ant-pagination .ant-pagination-next a,html .ant-pagination .ant-pagination-prev a {
    color: var(--theme-text-color-lighten)
}

html .ant-pagination .ant-pagination-next:not(.ant-pagination-disabled):focus .ant-pagination-item-link,html .ant-pagination .ant-pagination-next:not(.ant-pagination-disabled):hover .ant-pagination-item-link,html .ant-pagination .ant-pagination-prev:not(.ant-pagination-disabled):focus .ant-pagination-item-link,html .ant-pagination .ant-pagination-prev:not(.ant-pagination-disabled):hover .ant-pagination-item-link {
    border-color: var(--theme-primary-color)
}

html .ant-pagination .ant-pagination-next.ant-pagination-disabled svg,html .ant-pagination .ant-pagination-prev.ant-pagination-disabled svg {
    color: var(--theme-color-line)
}

html .ant-pagination .ant-pagination-item {
    border-color: var(--theme-color-line)
}

html .ant-pagination .ant-pagination-item a {
    color: var(--theme-text-color-darken)
}

html .ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):focus,html .ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover {
    border-color: var(--theme-primary-color)
}

html .ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):focus a,html .ant-pagination .ant-pagination-item:not(.ant-pagination-item-active):hover a {
    color: var(--theme-primary-color)
}

html .ant-pagination .ant-pagination-item-active {
    background-color: var(--theme-primary-color);
    border-color: var(--theme-primary-color)
}

html .ant-pagination .ant-pagination-item-active a {
    color: var(--theme-primary-font-color)
}

html .ant-table-column-title {
    color: var(--theme-text-color-darken);
    white-space: pre-wrap!important;
    word-break: break-word
}

html .ant-calendar {
    background-color: var(--theme-main-bg-color);
    border: thin solid var(--theme-color-line);
    color: var(--theme-text-color-lighten);
    width: 4.6rem
}

html .ant-calendar .ant-calendar-ym-select a {
    color: var(--theme-text-color-darken)
}

html .ant-calendar .ant-calendar-table .ant-calendar-column-header .ant-calendar-column-header-inner {
    color: var(--theme-text-color)
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-cell:not(.ant-calendar-last-month-cell):not(.ant-calendar-next-month-btn-day) .ant-calendar-date {
    color: var(--theme-text-color-darken)
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-date[aria-selected=true] {
    border-radius: .06rem;
    color: var(--theme-primary-font-color)!important
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-date[aria-selected=true]:hover {
    color: var(--theme-text-color-darken)!important
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-disabled-cell .ant-calendar-date {
    color: var(--theme-text-color-placeholder)!important
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-disabled-cell .ant-calendar-date:before {
    background: 0 0!important;
    height: .3rem;
    top: .04rem;
    width: .3rem
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-disabled-cell .ant-calendar-selected-day .ant-calendar-date {
    color: var(--theme-text-color-lighten)!important
}

html .ant-calendar .ant-calendar-tbody .ant-calendar-disabled-cell .ant-calendar-selected-day .ant-calendar-date:before {
    background: 0 0!important
}

html .ant-calendar .ant-calendar-disabled-cell .ant-calendar-date {
    background-color: transparent
}

html .ant-calendar .ant-calendar-input {
    background-color: transparent;
    color: var(--theme-text-color)
}

html .ant-calendar .ant-calendar-selected-day .ant-calendar-date {
    background: var(--theme-primary-color)
}

html .ant-calendar .ant-calendar-date:hover {
    background: 0 0
}

html .ant-calendar-picker-container-content {
    background: 0 0;
    border: none;
    -webkit-box-shadow: none;
    box-shadow: none
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-input-wrap {
    display: none!important
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header {
    border: none
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-next-month-btn,html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-prev-month-btn {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border: .01rem solid var(--theme-color-line);
    border-radius: .06rem;
    display: -ms-flexbox;
    display: flex;
    height: .4rem;
    justify-content: center;
    padding: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    width: .4rem
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-next-month-btn:before,html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-prev-month-btn:before {
    border-width: 0;
    border-bottom: 0 solid var(--theme-primary-color);
    border-left: .035rem solid var(--theme-primary-color);
    border-right: 0 solid var(--theme-primary-color);
    border-top: .035rem solid var(--theme-primary-color);
    height: .15rem;
    width: .15rem
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-prev-month-btn {
    left: 0
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-next-month-btn {
    right: 0
}

html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-next-year-btn,html .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-prev-year-btn {
    display: none
}

html .ant-calendar-picker-container-content .ant-calendar-body {
    padding: 0
}

html .ant-calendar-picker-container-content .ant-calendar-body .ant-calendar-column-header .ant-calendar-column-header-inner {
    font-weight: 700
}

html .ant-calendar-picker-container-content .ant-calendar-body .ant-calendar-cell {
    height: .4rem;
    width: .4rem
}

html .ant-calendar-picker-container-content .ant-calendar-body .ant-calendar-cell .ant-calendar-date {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: .2rem;
    height: .4rem;
    justify-content: center;
    width: .4rem
}

html .ant-calendar-picker-container-content .ant-calendar-date-panel {
    background-color: var(--theme-main-bg-color);
    border: .01rem solid var(--theme-color-line);
    border-radius: .14rem;
    -webkit-box-shadow: 0 .04rem .12rem 0 rgba(0,0,0,.1);
    box-shadow: 0 .04rem .12rem 0 rgba(0,0,0,.1);
    padding: .2rem
}

html .ant-checkbox-wrapper-checked .ant-checkbox-inner {
    background-color: var(--theme-secondary-color-success)!important
}

html .ant-checkbox-wrapper-checked .ant-checkbox-inner:after {
    background-color: #fff!important
}

html .ant-checkbox .ant-checkbox-inner:after {
    background-color: var(--theme-primary-color);
    bottom: 0;
    content: "/lobby_asset/common/web/common/comm_icon_gou.svg?mode=mask";
    display: -ms-inline-flexbox;
    display: inline-flex;
    height: 1em;
    height: 2em;
    left: 0;
    margin: auto;
    -webkit-mask-position: 50% 50%;
    mask-position: 50% 50%;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: cover;
    mask-size: cover;
    -webkit-mask-size: .18rem;
    mask-size: .18rem;
    -o-object-fit: cover;
    object-fit: cover;
    position: absolute;
    right: 0;
    top: 0;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    visibility: hidden;
    width: 1em;
    width: 2em
}

html[data-skin-bg="1"] .ant-message .ant-message-notice-content {
    background-color: #fff;
    color: #333
}

html[data-skin-bg="1"] .ant-message .ant-message-notice-content .ant-message-custom-content:not(.ant-message-loading):not(.ant-message-success):not(.ant-message-error):not(.ant-message-warning)>.anticon:before {
    background-color: #333
}

.ant-modal-confirm .closeIcon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background: 0 0;
    border-radius: 50%;
    bottom: -.9rem;
    display: -ms-flexbox;
    display: flex;
    height: .6rem;
    justify-content: center;
    margin-right: -.3rem;
    position: absolute;
    right: 50%;
    top: auto;
    width: .6rem
}

.ant-modal-confirm .closeIcon img {
    height: 100%;
    width: 100%
}

html[data-device=mobile] .ant-calendar {
    width: 5.4rem
}

html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-next-month-btn,html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-prev-month-btn {
    height: .5rem;
    width: .5rem
}

html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-next-month-btn:before,html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-panel .ant-calendar-header .ant-calendar-prev-month-btn:before {
    border-width: 0;
    border-bottom: 0 solid var(--theme-primary-color);
    border-left: .035rem solid var(--theme-primary-color);
    border-right: 0 solid var(--theme-primary-color);
    border-top: .035rem solid var(--theme-primary-color);
    height: .15rem;
    width: .15rem
}

html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-body .ant-calendar-cell {
    height: .5rem;
    width: .5rem
}

html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-body .ant-calendar-cell .ant-calendar-date {
    font-size: .2rem;
    height: .5rem;
    width: .5rem
}

html[data-device=mobile] .ant-calendar .ant-calendar-picker-container-content .ant-calendar-date-panel {
    padding: .2rem
}

html[data-device=mobile] .ant-layout {
    -ms-flex: 1;
    flex: 1
}

html[data-device=mobile] .ant-modal-wrap {
    -webkit-overflow-scrolling: auto!important
}

html[data-device=mobile] .ant-modal .ant-modal-content {
    background-clip: inherit;
    max-height: 100vh;
    max-height: calc(var(--vh,1vh)*100)
}

html[data-device=mobile] .ant-modal .ant-modal-content .ant-modal-header .ant-modal-title {
    font-size: .3rem;
    font-weight: revert
}

html[data-device=mobile] .ant-modal .ant-modal-content .ant-modal-close-x {
    font-size: .2rem;
    height: .6rem;
    line-height: .6rem;
    width: .6rem
}

html[data-device=mobile] .ant-modal-confirm .ant-modal-confirm-title {
    font-size: .3rem
}

html[data-device=mobile] .ant-modal-confirm .ant-modal-confirm-content {
    font-size: .24rem
}

html[data-device=mobile] .ant-modal-confirm .ant-modal-confirm-btns .ant-btn {
    font-size: .24rem;
    height: .7rem
}

html[data-device=mobile] .ant-modal-confirm .closeIcon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background: 0 0;
    border-radius: 50%;
    bottom: -.9rem;
    display: -ms-flexbox;
    display: flex;
    height: .6rem;
    justify-content: center;
    margin-right: -.3rem;
    position: absolute;
    right: 50%;
    top: auto;
    width: .6rem
}

html[data-device=mobile] .ant-modal-confirm .closeIcon img {
    height: 100%;
    width: 100%
}

html[data-device=mobile] .ant-message .ant-message-notice-content {
    border-radius: .14rem;
    font-size: .28rem;
    max-width: 6.9rem;
    padding: .24rem .4rem
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-success .anticon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(var(--theme-secondary-color-success-linear-gradient)),to(var(--theme-secondary-color-success)));
    background-image: linear-gradient(to bottom,var(--theme-secondary-color-success-linear-gradient),var(--theme-secondary-color-success));
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    height: .42rem;
    justify-content: center;
    width: .42rem
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-success .anticon:before {
    border-radius: 50%;
    content: "";
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_pay_1&scale=0.6";
    display: inline-block;
    visibility: hidden
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-success .anticon svg {
    display: none
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-error .anticon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(var(--theme-secondary-color-error-linear-gradient)),to(var(--theme-secondary-color-error)));
    background-image: linear-gradient(to bottom,var(--theme-secondary-color-error-linear-gradient),var(--theme-secondary-color-error));
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    height: .42rem;
    justify-content: center;
    width: .42rem
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-error .anticon:before {
    border-radius: 50%;
    content: "";
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_pay_2&scale=0.6";
    display: inline-block;
    visibility: hidden
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-error .anticon svg {
    display: none
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-warning .anticon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-image: -webkit-gradient(linear,left top,left bottom,from(var(--theme-secondary-color-finance-linear-gradient)),to(var(--theme-secondary-color-finance)));
    background-image: linear-gradient(to bottom,var(--theme-secondary-color-finance-linear-gradient),var(--theme-secondary-color-finance));
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    height: .42rem;
    justify-content: center;
    width: .42rem
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-warning .anticon:before {
    border-radius: 50%;
    content: "";
    content: "/lobby_asset/{layout}-{bg}-{skin}/main.sprites.png?key=sprite_main_comm_icon_pay_4&scale=0.6";
    display: inline-block;
    visibility: hidden
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content.ant-message-loading>.anticon {
    font-size: .336rem;
    height: .42rem;
    margin-right: .24rem;
    width: .42rem
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content>.anticon {
    height: .42rem;
    margin-right: .24rem;
    width: .42rem
}

html[data-device=mobile] .ant-message .ant-message-notice-content .ant-message-custom-content:not(.ant-message-loading):not(.ant-message-success):not(.ant-message-error):not(.ant-message-warning)>.anticon {
    font-size: .42rem;
    height: .42rem;
    margin-right: .24rem;
    width: .42rem
}

html[data-device=mobile] .ant-btn,html[data-device=mobile] .ant-select-dropdown {
    border-radius: .14rem
}

html[data-device=mobile] .ant-select-dropdown .ant-select-dropdown-menu {
    max-height: 5.1rem;
    padding: .14rem 0
}

html[data-device=mobile] .ant-select-dropdown .ant-select-dropdown-menu-item {
    font-size: .24rem;
    line-height: .8rem;
    min-height: .8rem;
    padding: 0 .2rem
}

html[data-device=mobile] .ant-select .ant-select-selection--single {
    font-size: .2rem
}

html[data-device=mobile] .ant-select .ant-select-selection--single .ant-select-selection__rendered {
    margin-left: .2rem;
    margin-right: .55rem
}

html[data-device=mobile] .ant-select .ant-select-selection--single .ant-select-selection__placeholder {
    height: .3rem;
    line-height: .3rem;
    margin-top: -.15rem
}

html[data-device=mobile] .ant-input-affix-wrapper .ant-input-clear-icon i,html[data-device=mobile] .ant-input-affix-wrapper .ant-input-clear-icon svg {
    font-size: .34rem
}

html[data-device=mobile] .ant-input-affix-wrapper .ant-input-clear-icon+i {
    margin-left: .2rem
}

html[data-device=mobile] .ant-checkbox .ant-checkbox-inner:after {
    -webkit-mask-size: .22rem;
    mask-size: .22rem
}

html[data-device=mobile] .loginRegisterModal .ant-form.ant-form-horizontal .ant-col.ant-col-5.ant-form-item-label,html[data-device=mobile] .loginRegisterSettingModal .ant-form.ant-form-horizontal .ant-col.ant-col-5.ant-form-item-label {
    width: 20.83333333%
}

html[data-device=mobile] .loginRegisterModal .ant-form.ant-form-horizontal .ant-col.ant-col-19.ant-form-item-control-wrapper,html[data-device=mobile] .loginRegisterSettingModal .ant-form.ant-form-horizontal .ant-col.ant-col-19.ant-form-item-control-wrapper {
    width: 79.166667%
}

html[data-device=mobile] .loginRegisterModal .ant-form.ant-form-horizontal .ant-form-item-label>label,html[data-device=mobile] .loginRegisterSettingModal .ant-form.ant-form-horizontal .ant-form-item-label>label {
    height: .7rem;
    line-height: .7rem
}

html[data-device=mobile] .loginRegisterModal .ant-form.ant-form-horizontal .ant-form-item-label>label:before,html[data-device=mobile] .loginRegisterSettingModal .ant-form.ant-form-horizontal .ant-form-item-label>label:before {
    line-height: .7rem!important
}

html[data-device-os=android] .ant-select-dropdown-search {
    -webkit-animation: none!important;
    animation: none!important
}

.ant-input[readonly] {
    background: 0 0
}

/*.van-picker.van-datetime-picker {
    background: 0 0;
    margin: .25rem 0;
    width: 40vw
}

.van-picker__mask {
    background: 0 0!important
}

.van-hairline-unset--top-bottom:after {
    border-bottom: .01rem solid var(--theme-color-line)!important;
    border-top: .01rem solid var(--theme-color-line)!important
}

.van-picker-column__item {
    color: var(--theme-text-color-lighten)!important
}

.van-picker-column__item.van-picker-column__item--selected {
    color: var(--theme-text-color-darken)!important
}

.van-ellipsis {
    font-size: .24rem
}

.van-tabs__nav {
    background: 0 0
}

html[data-device=mobile] .van-popup {
    position: absolute
}*/

/*html[data-device=mobile] .overlayAside {
    z-index: 250!important
}

html[data-device=mobile] .overlayTransparentAside {
    background: 0 0;
    opacity: 1;
    z-index: 230!important
}

html[data-device=mobile] .h5-aside-ZIndex {
    z-index: 250!important
}*/
/*
html {
    -webkit-text-size-adjust: none;
    border: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    height: 100%;
    margin: 0;
    overflow: hidden;
    padding: 0;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    width: 100%
}

html ::-webkit-scrollbar {
    width: .06rem;
    z-index: 11
}

html ::-webkit-scrollbar-thumb {
    background: var(--theme-scroll-bar-bg-color);
    border-radius: .06rem;
    width: .06rem
}

html ::-webkit-scrollbar-corner,html ::-webkit-scrollbar-track {
    background: 0 0
}

html ::-webkit-scrollbar-track-piece {
    background: 0 0;
    width: .06rem
}

html ::-webkit-scrollbar:horizontal {
    height: .06rem
}

html ::-webkit-scrollbar:horizontal-thumb {
    border-radius: .06rem
}*/
/*

html .ps.ps--active-x>.ps__rail-x,html .ps.ps--active-x>.ps__rail-y,html .ps.ps--active-y>.ps__rail-x,html .ps.ps--active-y>.ps__rail-y,html .ps:hover.ps--active-x>.ps__rail-x,html .ps:hover.ps--active-x>.ps__rail-y,html .ps:hover.ps--active-y>.ps__rail-x,html .ps:hover.ps--active-y>.ps__rail-y {
    opacity: 0!important
}

html .ps__rail-x {
    bottom: 0!important;
    height: .06rem!important
}

html .ps__rail-y {
    right: .1rem!important;
    width: .06rem!important
}

html .ps .ps__rail-x.ps--clicking,html .ps .ps__rail-x:focus,html .ps .ps__rail-x:hover,html .ps .ps__rail-y.ps--clicking,html .ps .ps__rail-y:focus,html .ps .ps__rail-y:hover,html .ps--focus>.ps__rail-x,html .ps--focus>.ps__rail-y,html .ps--scrolling-x>.ps__rail-x,html .ps--scrolling-y>.ps__rail-y,html .ps:hover>.ps__rail-x,html .ps:hover>.ps__rail-y {
    opacity: 1!important
}

html .ps .ps__rail-x.ps--clicking,html .ps .ps__rail-x:focus,html .ps .ps__rail-x:hover,html .ps .ps__rail-y.ps--clicking,html .ps .ps__rail-y:focus,html .ps .ps__rail-y:hover {
    background-color: transparent
}

html .ps__thumb-x,html .ps__thumb-y {
    background-color: var(--theme-scroll-bar-bg-color)!important;
    opacity: 1
}

html .ps__thumb-x.ps__thumb-x,html .ps__thumb-y.ps__thumb-x {
    bottom: .1rem!important;
    height: .06rem!important
}

html .ps__thumb-x.ps__thumb-y,html .ps__thumb-y.ps__thumb-y {
    right: 0!important;
    width: .06rem!important
}
*/
/*

html #nprogress {
    left: 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 10;
    z-index: 1000000000000
}

html #nprogress .bar {
    background: var(--theme-primary-color);
    height: .03rem
}

html #nprogress .bar .peg {
    -webkit-box-shadow: 0 0 .1rem var(--theme-primary-color),0 0 .05rem var(--theme-primary-color);
    box-shadow: 0 0 .1rem var(--theme-primary-color),0 0 .05rem var(--theme-primary-color)
}

html #nprogress .spinner .spinner-icon {
    border-left-color: var(--theme-primary-color);
    border-top-color: var(--theme-primary-color)
}

html[lang=vi_VN] body {
    font-family: Arial,-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol
}
*/

/*
html body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    border: 0;
    color: var(--theme-text-color);
    font-size: .14rem;
    height: 100%;
    margin: 0;
    overflow: hidden;
    padding: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 100%
}

html body #app {
    position: relative
}

html body #app,html body #app>div:not(#google-btn-help) {
    border: 0;
    height: 100%;
    height: auto;
    margin: 0;
    padding: 0;
    width: 100%
}

html body input:-webkit-autofill,html body input:-webkit-autofill:active,html body input:-webkit-autofill:focus,html body input:-webkit-autofill:hover {
    background-color: var(--theme-main-bg-color);
    -webkit-transition: color 99999999s,background-color 99999999s;
    transition: color 99999999s,background-color 99999999s;
    -webkit-transition-delay: 99999999s;
    transition-delay: 99999999s
}

html body #google-btn-help {
    bottom: 2%;
    left: 50%;
    position: fixed;
    -webkit-transform: translate(-50%);
    transform: translate(-50%);
    visibility: visible;
    z-index: 999999999999999
}

html body p {
    margin-bottom: 0
}

html div:focus {
    outline: none
}
*/

html input[disabled] {
    background-color: var(--theme-main-bg-color);
    color: var(--theme-text-color)
}

html input[disabled]::-webkit-input-placeholder {
    color: var(--theme-text-color-placeholder)!important
}

html input[disabled]::-moz-placeholder {
    color: var(--theme-text-color-placeholder)!important
}

html input[disabled]:-ms-input-placeholder {
    color: var(--theme-text-color-placeholder)!important
}

html input::-webkit-contacts-auto-fill-button,html input::-webkit-credentials-auto-fill-button {
    display: none!important;
    pointer-events: none;
    position: absolute;
    right: 0;
    visibility: hidden
}

p {
    margin-bottom: 0
}

#google-btn-help {
    -webkit-transition: all .3s;
    transition: all .3s
}

.googleInitError .ant-modal-mask,.googleInitError .ant-modal-wrap,.notic-user-tip-warp,.tokenExpiredModal .ant-modal-mask,.tokenExpiredModal .ant-modal-wrap {
    z-index: 9999
}

.clubTipsModal {
    padding: .3rem 0
}

.clubTipsModal :global .ant-modal-confirm-content {
    margin-bottom: .35rem!important
}

.clubTipsModal :global .ant-modal-confirm-btns {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

.clubTipsModal :global .ant-btn {
    line-height: 1.2;
    white-space: normal
}

.geetest_bind_container,.geetest_bind_tips {
    text-align: center
}

.wg-ping-box {
    display: none!important
}

.modal-account-info .ant-modal-wrap {
    z-index: 1003!important
}

.modal-account-bind .ant-modal-mask {
    z-index: 1001!important
}

.modal-account-bind .ant-modal-wrap {
    z-index: 1002!important
}

.modal-register-success .ant-modal-wrap {
    z-index: 1001!important
}

@-webkit-keyframes qzTXLXxmi6cwc5mHlUHL {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes qzTXLXxmi6cwc5mHlUHL {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.ZuLywNAJTm4ylsXALdMq {
    color: var(--theme-primary-color)
}

.A9aJJVL3PADhKFOGUDNt {
    color: var(--theme-secondary-color-error)
}

.HI77FtRdvO4aiGOC1sgp {
    color: var(--theme-secondary-color-success)
}

.XGEOwGXgDL9a2SebPqvj {
    color: var(--theme-secondary-color-finance)
}

.aZ9nabXWlhiDc8xNLSC4 {
    color: var(--theme-text-color)
}

.UwyNP8CcObxeFMJmAhCj {
    color: var(--theme-text-color-darken)
}

.hSnKt39QEO2fma653Cu1 {
    color: var(--theme-text-color-lighten)
}

.V4LeFhG0b9lZSmJT8Ynm {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

/*body {*/
/*    --z-paymodal: 1000*/
/*}*/

@-webkit-keyframes XyE3D0Y_1dZqAyZYmnUU {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes XyE3D0Y_1dZqAyZYmnUU {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.umskZObCyqgQ16w1sRjo {
    height: 100%;
    position: relative;
    width: 100%
}

.umskZObCyqgQ16w1sRjo .ioFH2SdYFy9KbEA35rqr {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    left: 50%;
    position: absolute;
    top: 50%
}

html[data-device=mobile] .umskZObCyqgQ16w1sRjo .ioFH2SdYFy9KbEA35rqr {
    opacity: 1
}

@-webkit-keyframes oODjhNVXI2p8KmlPzyS5 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes oODjhNVXI2p8KmlPzyS5 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.N4LsWhJBhqqyTkv6V0NE {
    display: inline-block;
    line-height: 0;
    overflow: hidden
}

.N4LsWhJBhqqyTkv6V0NE .ke1kCzBARxq5HSxbDQ6h {
    display: inline-block;
    width: 100%
}

.N4LsWhJBhqqyTkv6V0NE .ke1kCzBARxq5HSxbDQ6h .TMZWYZcIv5YQXVdFMVrw {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    line-height: 1.2;
    overflow: hidden;
    overflow: initial;
    text-align: center;
    text-overflow: ellipsis;
    vertical-align: middle;
    word-break: break-word
}

.N4LsWhJBhqqyTkv6V0NE .ke1kCzBARxq5HSxbDQ6h .TMZWYZcIv5YQXVdFMVrw:lang(my_mm),.N4LsWhJBhqqyTkv6V0NE .ke1kCzBARxq5HSxbDQ6h .TMZWYZcIv5YQXVdFMVrw:lang(ta_lk) {
    line-height: 1.5
}

.N4LsWhJBhqqyTkv6V0NE .ke1kCzBARxq5HSxbDQ6h .TMZWYZcIv5YQXVdFMVrw:lang(vi_vn) {
    line-height: 1.4
}

html[data-device=mobile] .N4LsWhJBhqqyTkv6V0NE,html[data-device=mobile] .N4LsWhJBhqqyTkv6V0NE .ke1kCzBARxq5HSxbDQ6h {
    vertical-align: middle
}

@-webkit-keyframes pYKVWDaJF65t8cwKVLgr {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes pYKVWDaJF65t8cwKVLgr {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.gD0G2pAAJZZTJ1n0KQXv {
    margin-bottom: .4rem
}

.gD0G2pAAJZZTJ1n0KQXv .saPGQcGOJEokiNYHytih span {
    text-align: left!important
}

.gD0G2pAAJZZTJ1n0KQXv .EooByEciSijZepo23r0Q {
    color: var(--theme-secondary-color-error);
    display: inline-block;
    margin-left: .04rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-explain:not(.show-help-leave):before {
    -ms-flex: none;
    flex: none
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label>label {
    height: .5rem;
    line-height: .5rem
}

.gD0G2pAAJZZTJ1n0KQXv .custom-suffix-input .custom-suffix-span {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.gD0G2pAAJZZTJ1n0KQXv .custom-suffix-input .custom-suffix-span .ant-input-password-icon {
    color: var(--theme-primary-color);
    margin-left: .2rem
}

.gD0G2pAAJZZTJ1n0KQXv .custom-suffix-input .custom-suffix-span[data-show-edit=true] .ant-input-password-icon {
    margin-right: .2rem
}

.gD0G2pAAJZZTJ1n0KQXv .custom-suffix-input .ant-input-suffix .custom-suffix-edit-btn {
    color: var(--theme-primary-color);
    cursor: pointer;
    font-size: .18rem;
    line-height: 1.5
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-control {
    line-height: normal
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children {
    display: block
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection {
    background-color: var(--theme-main-bg-color);
    border-color: var(--theme-color-line)!important;
    border-radius: .1rem;
    color: var(--theme-text-color-darken);
    font-size: .18rem;
    height: .5rem;
    padding: 0 .5rem 0 .15rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:focus,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:focus,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection:focus {
    font-weight: 700
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input::-webkit-input-placeholder,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input::-webkit-input-placeholder,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection::-webkit-input-placeholder {
    color: var(--theme-text-color-placeholder);
    font-weight: 400
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input::-moz-placeholder,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input::-moz-placeholder,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection::-moz-placeholder {
    color: var(--theme-text-color-placeholder);
    font-weight: 400
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:-ms-input-placeholder,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:-ms-input-placeholder,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection:-ms-input-placeholder {
    color: var(--theme-text-color-placeholder);
    font-weight: 400
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:hover,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:hover,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection:hover {
    background-color: var(--theme-main-bg-color)
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:active,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:focus,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:active,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:focus,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection:active,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select-combobox .ant-select-selection:focus {
    border-color: var(--theme-primary-color)!important
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-search .ant-input {
    border-radius: .25rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection {
    background-color: var(--theme-main-bg-color)!important;
    background-color: var(--theme-main-bg-color);
    border-radius: .1rem;
    color: var(--theme-text-color-darken);
    font-size: .18rem;
    font-weight: 400;
    height: .5rem;
    padding: 0 .5rem 0 .15rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection:focus {
    font-weight: 700
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection .ant-select-selection__rendered {
    height: 100%;
    line-height: .5rem;
    margin-left: 0
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection .ant-select-arrow {
    margin-top: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection .ant-select-arrow .ant-select-arrow-icon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    color: var(--theme-text-color-lighten);
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    justify-content: center
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label {
    display: -ms-flexbox;
    display: flex;
    line-height: .26rem;
    margin-bottom: .12rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label {
    color: var(--theme-text-color-darken);
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label .anticon {
    margin-right: .13rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label span {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    text-align: left;
    /*width: 1rem*/
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label>div {
    line-height: .25rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label>div,.gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label>div>div {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.gD0G2pAAJZZTJ1n0KQXv .anticon {
    color: var(--theme-text-color-lighten);
    font-size: .18rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix {
    left: .16rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input {
    padding: 0 .48rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(kn_in),.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(mr_in),.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(ta_lk),.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(te_in) {
    padding-right: 1.6rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix i,.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix svg {
    color: var(--theme-text-color-lighten);
    height: .2rem;
    width: .2rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix {
    right: .15rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix .ant-input-password-icon {
    font-size: .26rem;
    padding-right: .01rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(kn_in),.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(mr_in),.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(ta_lk),.gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(te_in) {
    display: inline-block;
    text-align: right;
    width: 1.4rem
}

.gD0G2pAAJZZTJ1n0KQXv .ant-form-explain {
    font-size: .14rem;
    line-height: 1;
    margin-bottom: -.15rem;
    margin-top: .05rem
}

.gD0G2pAAJZZTJ1n0KQXv.XqjLK8ZRmJFdy7TPEkqA {
    background-color: transparent
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .ant-input-prefix {
    left: 0;
    padding-left: 0
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    width: 1.68rem
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .T7pOuBjbpP7xd9vf0fF4 {
    position: relative;
    right: 0
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .yhveExiY_McVCWreUEUc {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    left: .16rem;
    line-height: normal;
    position: absolute
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .KXaFIpNByJILtL37pVwO {
    max-width: 5.78rem!important
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: .5rem;
    line-height: normal
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection {
    -ms-flex-align: center;
    align-items: center;
    background: 0 0!important;
    border: none;
    border-radius: 0;
    border-right: .01rem solid var(--theme-color-line);
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
    color: var(--theme-text-color-darken);
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    height: calc(100% - .2rem);
    outline: none;
    padding-right: .15rem
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-selection__rendered {
    line-height: normal;
    margin-left: .6rem;
    margin-right: .16rem;
    width: .56rem
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-selection-selected-value {
    color: var(--theme-text-color-lighten)
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-arrow {
    margin: 0;
    position: static
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-arrow .anticon {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: .17rem
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-arrow .anticon.ant-select-arrow-icon {
    color: var(--theme-primary-color);
    font-size: .15rem
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm .EbaAWQ8q4vTBAVvDYmAQ>div {
    display: none
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A[data-show-prefix] .BAs0zmlVRfkSDB9E2ybL>input {
    padding-left: 1.8rem
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .ant-input:focus {
    font-weight: 400!important
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .ant-input-prefix {
    z-index: 3
}

.gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .ant-input-prefix .ant-select-dropdown {
    max-width: 100%;
    max-width: var(--inject-auto-height,100%)
}

.gD0G2pAAJZZTJ1n0KQXv.Ut03_AZgjiusqwnZAjET .ant-select-selection {
    padding-right: .3rem!important
}

.gD0G2pAAJZZTJ1n0KQXv.Ut03_AZgjiusqwnZAjET .ant-select-selection__rendered {
    margin-right: 0!important
}

.SNUE2gynoXwrenio9H5F {
    margin-bottom: 0!important
}

.SNUE2gynoXwrenio9H5F .ant-form-item-control {
    min-height: .88rem
}

.SNUE2gynoXwrenio9H5F .ant-form-item-control .ant-form-explain {
    height: auto;
    margin-bottom: .1rem;
    min-height: 0;
    position: relative
}

.SGxgklNsAb1aQjWuZhH2 ::-webkit-scrollbar-thumb,.gD0G2pAAJZZTJ1n0KQXv ::-webkit-scrollbar-thumb {
    background: var(--theme-scroll-bar-bg-color)!important
}

.SGxgklNsAb1aQjWuZhH2 .ant-select-dropdown-menu .ant-select-dropdown-menu-item,.gD0G2pAAJZZTJ1n0KQXv .ant-select-dropdown-menu .ant-select-dropdown-menu-item {
    color: var(--theme-text-color-lighten);
    font-size: .2rem;
    height: .55rem;
    line-height: .55rem
}

.SGxgklNsAb1aQjWuZhH2 .ant-select-dropdown-menu .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected,.gD0G2pAAJZZTJ1n0KQXv .ant-select-dropdown-menu .ant-select-dropdown-menu-item.ant-select-dropdown-menu-item-selected {
    background-color: transparent!important;
    background-color: initial!important;
    color: var(--theme-primary-color)
}

.SGxgklNsAb1aQjWuZhH2 .ant-select-dropdown-menu .ant-select-dropdown-menu-item:hover,.gD0G2pAAJZZTJ1n0KQXv .ant-select-dropdown-menu .ant-select-dropdown-menu-item:hover {
    background-color: var(--theme-bg-color)!important
}

.SGxgklNsAb1aQjWuZhH2 .ant-select-dropdown-menu .ant-select-dropdown-menu-item-disabled,.gD0G2pAAJZZTJ1n0KQXv .ant-select-dropdown-menu .ant-select-dropdown-menu-item-disabled {
    color: var(--theme-text-color-lighten)
}

.SGxgklNsAb1aQjWuZhH2 .EbaAWQ8q4vTBAVvDYmAQ,.gD0G2pAAJZZTJ1n0KQXv .EbaAWQ8q4vTBAVvDYmAQ {
    height: 100%
}

.SGxgklNsAb1aQjWuZhH2 .EbaAWQ8q4vTBAVvDYmAQ>div,.gD0G2pAAJZZTJ1n0KQXv .EbaAWQ8q4vTBAVvDYmAQ>div {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.SGxgklNsAb1aQjWuZhH2 .EbaAWQ8q4vTBAVvDYmAQ>div .OvBKpt9R5BeYM3_KzcAh,.gD0G2pAAJZZTJ1n0KQXv .EbaAWQ8q4vTBAVvDYmAQ>div .OvBKpt9R5BeYM3_KzcAh {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin-right: .15rem
}

.SGxgklNsAb1aQjWuZhH2 .EbaAWQ8q4vTBAVvDYmAQ>div .ffF9IuhXtmJll41BZvw2,.gD0G2pAAJZZTJ1n0KQXv .EbaAWQ8q4vTBAVvDYmAQ>div .ffF9IuhXtmJll41BZvw2 {
    display: none
}

.SGxgklNsAb1aQjWuZhH2 .EbaAWQ8q4vTBAVvDYmAQ>div span,.gD0G2pAAJZZTJ1n0KQXv .EbaAWQ8q4vTBAVvDYmAQ>div span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.DlTldn4hc5FWrc8jc0ow {
    left: .16rem;
    line-height: 1;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 2
}

.DlTldn4hc5FWrc8jc0ow i,.DlTldn4hc5FWrc8jc0ow svg {
    color: var(--theme-text-color-lighten)!important;
    height: .22rem;
    width: .22rem
}

.RtduVXFT2jqp8gCtZV4g i,.RtduVXFT2jqp8gCtZV4g svg {
    height: .2rem;
    width: .2rem
}

.Bac6dWqa6pZ6MEil1AKg .ant-select-selection {
    padding-left: .48rem!important
}

.opOxOwip3J3EfQE8YzAV {
    color: var(--theme-primary-color);
    cursor: pointer;
    font-size: .18rem
}

html[data-device=mobile] .SGxgklNsAb1aQjWuZhH2 .EbaAWQ8q4vTBAVvDYmAQ>div .OvBKpt9R5BeYM3_KzcAh {
    margin-right: .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv {
    margin-bottom: .5rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .custom-suffix-input .ant-input-suffix .custom-suffix-edit-btn {
    font-size: .22rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input {
    border-radius: .14rem;
    font-size: .22rem;
    height: .7rem;
    padding: 0 .6rem 0 .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:focus::-webkit-input-placeholder,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:focus::-webkit-input-placeholder {
    padding-left: .05rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:focus::-moz-placeholder,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:focus::-moz-placeholder {
    padding-left: .05rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-affix-wrapper .ant-input:focus:-ms-input-placeholder,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input:focus:-ms-input-placeholder {
    padding-left: .05rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection {
    border-radius: .14rem;
    font-size: .22rem;
    height: .7rem;
    padding: 0 .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection .ant-select-selection__rendered {
    line-height: .7rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-select .ant-select-selection .ant-select-arrow .ant-select-arrow-icon {
    font-size: .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-children>.ant-input-search .ant-input {
    border-radius: .25rem;
    height: .5rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label {
    margin-bottom: .15rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label {
    font-size: .22rem;
    height: inherit
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label .anticon {
    margin-right: .13rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label .anticon i,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-item-label label .anticon svg {
    height: .25rem;
    width: .25rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix {
    left: .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input {
    /*padding: 0 .6rem 0 0*/
    padding-left : .8rem;
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(kn_in),html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(mr_in),html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(ta_lk),html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix+.ant-input:lang(te_in) {
    padding-right: 1.9rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix i,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-prefix svg {
    height: .25rem;
    width: .25rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix {
    right: .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix .ant-input-password-icon {
    font-size: .36rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(kn_in),html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(mr_in),html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(ta_lk),html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-input-affix-wrapper .ant-input-suffix:lang(te_in) {
    word-wrap: break-word;
    display: inline-block;
    text-align: right;
    width: 1.5rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv .ant-form-explain {
    font-size: .2rem;
    height: .4rem;
    margin-bottom: -.4rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .ant-input-prefix {
    left: 0;
    padding-left: 0
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B {
    width: 1.88rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .yhveExiY_McVCWreUEUc {
    left: .2rem;
    position: absolute
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .T7pOuBjbpP7xd9vf0fF4 {
    right: 0
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: .7rem;
    line-height: normal
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection {
    font-size: .22rem;
    padding-right: .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-selection__rendered {
    margin-left: .6rem;
    margin-right: .16rem;
    width: .68rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-arrow {
    margin: 0;
    position: static
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-arrow i,html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A .BAs0zmlVRfkSDB9E2ybL .HlSfZpZxovZIrfUHeA1B .MjMC7nhQVhzXHrTYaTlm>.ant-select-selection .ant-select-arrow svg {
    height: .2rem;
    width: .2rem
}

html[data-device=mobile] .gD0G2pAAJZZTJ1n0KQXv.PFwVVZ_QB6SZSfgf8I9A[data-show-prefix] .BAs0zmlVRfkSDB9E2ybL>input {
    padding-left: 2rem
}

html[data-device=mobile] .DlTldn4hc5FWrc8jc0ow {
    left: .2rem
}

html[data-device=mobile] .DlTldn4hc5FWrc8jc0ow .EooByEciSijZepo23r0Q {
    display: inline-block;
    font-size: .26rem;
    margin-left: .07rem
}

html[data-device=mobile] .RtduVXFT2jqp8gCtZV4g i,html[data-device=mobile] .RtduVXFT2jqp8gCtZV4g svg {
    height: .25rem;
    width: .25rem
}

html[data-device=mobile] .Bac6dWqa6pZ6MEil1AKg .ant-select-selection {
    padding-left: .6rem!important
}

html[data-device=mobile] .SNUE2gynoXwrenio9H5F .ant-form-item-control {
    min-height: 1.2rem
}

html[data-device=mobile] .SNUE2gynoXwrenio9H5F .ant-form-item-control .ant-form-explain {
    height: auto;
    margin-bottom: .1rem;
    min-height: 0;
    position: relative
}

html[data-device=mobile] .opOxOwip3J3EfQE8YzAV {
    font-size: .22rem
}

.v7IyH8dwr1W3q1vpyVPs {
    height: .5rem;
    width: 100%
}

html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children>.ant-input-affix-wrapper .ant-input::-webkit-input-placeholder,html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children>.ant-input::-webkit-input-placeholder {
    color: #999
}

html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children>.ant-input-affix-wrapper .ant-input::-moz-placeholder,html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children>.ant-input::-moz-placeholder {
    color: #999
}

html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children>.ant-input-affix-wrapper .ant-input:-ms-input-placeholder,html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children>.ant-input:-ms-input-placeholder {
    color: #999
}

html[data-device=desktop][data-skin-layout="5"] .ant-form-item-children .ant-input-prefix svg,html[data-device=desktop][data-skin-layout="5"] .ant-form-item-children .anticon,html[data-device=desktop][data-skin-layout="5"] .ant-form-item-children .anticon i svg,html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children .ant-input-prefix svg,html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children .anticon,html[data-device=desktop][data-skin-layout="6"] .ant-form-item-children .anticon i svg {
    color: var(--theme-text-color-placeholder)!important
}

[data-skin-layout="2"][data-skin-bg="0"] .ant-select-arrow .ant-select-arrow-icon {
    color: var(--theme-text-color-lighten)!important
}

@-webkit-keyframes kxwxCBGuT1hFawoVDrX6 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes kxwxCBGuT1hFawoVDrX6 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.URYVffVETVSIkPGB9P5E .ant-spin-blur,.URYVffVETVSIkPGB9P5E.lTXdcXGZu_Yguh1MG5vS .ant-spin-spinning {
    background-color: var(--theme-load-bg-color)
}

.URYVffVETVSIkPGB9P5E .ant-spin-blur {
    opacity: 1;
    overflow: visible;
    overflow: initial
}

.URYVffVETVSIkPGB9P5E .ant-spin-dot-item {
    background: #fff
}

.URYVffVETVSIkPGB9P5E .ant-spin-spinning {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox!important;
    display: flex!important;
    justify-content: center
}

.URYVffVETVSIkPGB9P5E .ant-spin-dot {
    margin: 0!important;
    position: static!important
}

.URYVffVETVSIkPGB9P5E.wkokwJLUZ11M0pvkCY2s[spinning=true] .ant-spin-spinning {
    opacity: 0
}

.URYVffVETVSIkPGB9P5E.wkokwJLUZ11M0pvkCY2s[spinning=true] .ant-spin-blur {
    background: transparent none repeat 0 0/auto auto padding-box border-box scroll;
    background: initial;
    opacity: 1
}

.URYVffVETVSIkPGB9P5E .GEEJRXBxV6AROsCivfht {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    color: var(--theme-alt-neutral-2);
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    font-size: .22rem;
    gap: .2rem;
    justify-content: center;
    padding: .2rem;
    position: relative
}

.URYVffVETVSIkPGB9P5E .GEEJRXBxV6AROsCivfht .ByR1p0qkRMeFg83lrUOF {
    color: var(--theme-primary-color);
    cursor: pointer
}

.URYVffVETVSIkPGB9P5E .GEEJRXBxV6AROsCivfht .BM9_svVMyz6TYDPSQ14d {
    margin-top: .8rem;
    text-align: center
}

.URYVffVETVSIkPGB9P5E .GEEJRXBxV6AROsCivfht .BM9_svVMyz6TYDPSQ14d button {
    height: .55rem;
    line-height: .55rem;
    margin: 0 .1rem;
    width: 2.9rem
}

.URYVffVETVSIkPGB9P5E .GEEJRXBxV6AROsCivfht .BM9_svVMyz6TYDPSQ14d .ant-btn.ant-btn-primary.ant-btn-background-ghost {
    color: var(--theme-primary-color)
}

html[data-device=mobile] .URYVffVETVSIkPGB9P5E .ant-spin.ant-spin-show-text .ant-spin-text {
    font-size: .24rem
}

html[data-device=mobile] .GEEJRXBxV6AROsCivfht {
    padding-bottom: 1.3rem
}

html[data-device=mobile] .GEEJRXBxV6AROsCivfht .BM9_svVMyz6TYDPSQ14d {
    background-color: var(--theme-main-bg-color);
    bottom: 0;
    -webkit-box-shadow: 0 -.03rem .1rem 0 rgba(0,0,0,.1);
    box-shadow: 0 -.03rem .1rem 0 rgba(0,0,0,.1);
    left: 0;
    padding: .2rem .2rem .4rem;
    position: absolute;
    width: 100%
}

.Bu03QaKrjqcXZq0qBMYo {
    -webkit-animation: pPm7V8PTKiLD6zwXGuGA .75s linear infinite;
    animation: pPm7V8PTKiLD6zwXGuGA .75s linear infinite;
    border: .02rem solid #fff;
    border-left-color: #1678ff;
    border-radius: 100%;
    height: .3rem;
    position: relative;
    width: .3rem
}

@-webkit-keyframes pPm7V8PTKiLD6zwXGuGA {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes pPm7V8PTKiLD6zwXGuGA {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes L_t2fHr9CZZECG3xr9T7 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes L_t2fHr9CZZECG3xr9T7 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.Z1ee9QJd81eYwAsBfJ5D {
    bottom: -.85rem;
    position: absolute;
    text-align: center;
    width: 100%
}

.Z1ee9QJd81eYwAsBfJ5D,.Z1ee9QJd81eYwAsBfJ5D>span {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

.Z1ee9QJd81eYwAsBfJ5D>span {
    color: var(--theme-text-color-lighten);
    font-size: .16rem;
    margin-left: .2rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.Z1ee9QJd81eYwAsBfJ5D>span .ant-input {
    border-radius: .06rem!important;
    margin: 0 .1rem;
    width: .68rem
}

.Z1ee9QJd81eYwAsBfJ5D input {
    text-align: center
}

.Z1ee9QJd81eYwAsBfJ5D .ant-select-selection--single {
    border-radius: .06rem!important;
    color: var(--theme-text-color-lighten);
    height: .44rem;
    line-height: .44rem;
    margin-left: .1rem
}

.Z1ee9QJd81eYwAsBfJ5D .ant-select-selection--single .ant-select-selection__rendered {
    height: .4rem;
    line-height: .4rem
}

.Z1ee9QJd81eYwAsBfJ5D .ant-spin-blur {
    overflow: visible!important;
    overflow: initial!important
}

.Z1ee9QJd81eYwAsBfJ5D .ant-pagination-jump-next-custom-icon,.Z1ee9QJd81eYwAsBfJ5D .ant-pagination-jump-prev-custom-icon {
    background-color: transparent!important
}

.Z1ee9QJd81eYwAsBfJ5D .ant-pagination-jump-next-custom-icon .ant-pagination-item-ellipsis,.Z1ee9QJd81eYwAsBfJ5D .ant-pagination-jump-prev-custom-icon .ant-pagination-item-ellipsis {
    color: var(--theme-text-color-darken)!important
}

.Z1ee9QJd81eYwAsBfJ5D .ant-select-arrow {
    top: .2rem
}

.Z1ee9QJd81eYwAsBfJ5D .ant-input {
    height: .44rem
}

.Z1ee9QJd81eYwAsBfJ5D .uIPZGFvlbc73vTKKPw6f {
    white-space: nowrap
}

.Tg_bNUie8utWvNyBH9JN {
    position: relative
}

.Tg_bNUie8utWvNyBH9JN table {
    padding: .01rem
}

.Tg_bNUie8utWvNyBH9JN .ant-table-body {
    background: 0 0!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-placeholder {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border: none;
    border-color: var(--theme-color-line);
    display: -ms-flexbox;
    display: flex;
    height: var(--empty-height);
    justify-content: center
}

.Tg_bNUie8utWvNyBH9JN .ant-empty-image {
    height: auto;
    margin-bottom: 0
}

.Tg_bNUie8utWvNyBH9JN .ant-empty-description {
    color: var(--theme-text-color-lighten);
    font-size: .22rem;
    margin-top: .2rem
}

.Tg_bNUie8utWvNyBH9JN .ant-table-content {
    border-radius: .06rem;
    color: var(--theme-text-color-lighten);
    font-size: .16rem;
    min-height: calc(.62rem + var(--empty-height));
    overflow: hidden
}

.Tg_bNUie8utWvNyBH9JN .ant-table-thead {
    height: .7rem
}

.Tg_bNUie8utWvNyBH9JN .ant-table-thead>tr:first-child>th:first-child {
    border-bottom-left-radius: .06rem;
    border-left: .01rem solid var(--theme-color-line)!important;
    border-top-left-radius: .06rem
}

.Tg_bNUie8utWvNyBH9JN .ant-table-thead>tr:last-child>th:last-child {
    border-bottom-right-radius: .06rem;
    border-right: .01rem solid var(--theme-color-line)!important;
    border-top-right-radius: .06rem
}

.Tg_bNUie8utWvNyBH9JN .ant-table-thead tr>th {
    background-color: var(--theme-bg-color);
    border-bottom: .01rem solid var(--theme-color-line)!important;
    border-top: .01rem solid var(--theme-color-line)!important;
    color: var(--theme-text-color);
    font-size: .2rem;
    font-weight: 400
}

.Tg_bNUie8utWvNyBH9JN .ant-table-column-has-sorters:hover {
    background-color: var(--theme-bg-color)!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-column-sorter-inner-full {
    margin-top: -.15em!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-column-sorter-inner {
    color: var(--theme-text-color-lighten)!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-column-sorter-inner i {
    font-size: .14rem!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-header {
    background: 0 0!important;
    margin-bottom: .01rem!important;
    padding-bottom: 0!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr>th,.Tg_bNUie8utWvNyBH9JN .ant-table-thead tr>th {
    padding: 0
}

.Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr>td,.Tg_bNUie8utWvNyBH9JN .ant-table-thead tr>td {
    border-bottom: none;
    padding: 0;
    white-space: pre
}

.Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr>td.ant-table-column-sort,.Tg_bNUie8utWvNyBH9JN .ant-table-thead tr>td.ant-table-column-sort {
    background: 0 0!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr {
    height: .5rem
}

.Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr:not(.kaR5fMl5DssvxlvqjM5j):hover td {
    background-color: transparent!important
}

.Tg_bNUie8utWvNyBH9JN .ant-table-hide-scrollbar {
    background-color: var(--theme-bg-color);
    overflow: hidden!important
}

.Tg_bNUie8utWvNyBH9JN .ant-list-items {
    color: var(--theme-text-color-lighten)
}

.Tg_bNUie8utWvNyBH9JN .ant-list-items>div,.Tg_bNUie8utWvNyBH9JN .ant-list-items>li {
    border-radius: .1rem;
    padding: .22rem .2rem
}

.Tg_bNUie8utWvNyBH9JN .ant-list-items>:nth-child(2n) {
    background-color: var(--theme-bg-color)
}

.Tg_bNUie8utWvNyBH9JN .CoRxptnO0pNFo_rooL0s .ant-list-items>:nth-child(2n),.Tg_bNUie8utWvNyBH9JN .ant-list-items>:nth-child(odd) {
    background-color: var(--theme-main-bg-color)
}

.Tg_bNUie8utWvNyBH9JN .CoRxptnO0pNFo_rooL0s .ant-list-items>:nth-child(odd) {
    background-color: var(--theme-bg-color)
}

.Tg_bNUie8utWvNyBH9JN .ant-list .ant-spin-nested-loading,.Tg_bNUie8utWvNyBH9JN .ant-list .ant-spin-nested-loading .ant-spin-container {
    height: 100%
}

.Tg_bNUie8utWvNyBH9JN .ant-list .ant-spin-nested-loading .ant-spin-container .ant-list-empty-text {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    justify-content: center
}

.Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j {
    background-color: var(--theme-bg-color);
    height: .5rem
}

.Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j td:first-of-type {
    border-radius: .06rem 0 0 .06rem
}

.Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j td:last-of-type {
    border-radius: 0 .06rem .06rem 0
}

.Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j:hover td {
    background-color: var(--theme-bg-color)!important
}

.Tg_bNUie8utWvNyBH9JN .bPj6UldtSTOxBE3axVum {
    background-color: var(--theme-main-bg-color);
    height: .5rem
}

.Tg_bNUie8utWvNyBH9JN .YbtYo1IvajB_51_BmelL {
    background-color: var(--theme-main-bg-color);
    padding-top: .2rem;
    width: 100%
}

.Tg_bNUie8utWvNyBH9JN .TNJJNBEWFE6ehM7F9gHA {
    -ms-flex-align: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border-top: thin solid var(--theme-color-line);
    bottom: 0;
    display: -ms-flexbox;
    display: flex;
    left: 0;
    min-height: .76rem;
    position: fixed;
    width: 100%;
    z-index: 20
}

.Tg_bNUie8utWvNyBH9JN .ant-table-scroll .ps.ps--active-y>.ps__rail-y,.Tg_bNUie8utWvNyBH9JN .ant-table-scroll .ps:hover.ps--active-y>.ps__rail-y {
    opacity: 1!important
}

.s08mlF5L198_7acrxhpz .ytVSADsCVukDjfIoBsIt {
    margin-right: .09rem
}

.s08mlF5L198_7acrxhpz .ipXNk3pNGqHRyvtV0uok {
    color: var(--theme-primary-color);
    line-height: .2rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-empty-image {
    padding-bottom: .1rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-list-items>div,html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-list-items>li {
    padding: .2rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-placeholder {
    background-color: transparent
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-empty-description {
    color: var(--theme-text-color-lighten);
    font-size: .26rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr {
    font-size: .2rem;
    height: .7rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-tbody tr:not(.kaR5fMl5DssvxlvqjM5j):hover td {
    background-color: transparent!important
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-thead {
    height: .8rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-thead tr>th {
    background: var(--theme-main-bg-color);
    font-size: .24rem;
    height: .8rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-thead>tr:first-child>th:first-child {
    border-bottom-left-radius: .1rem;
    border-top-left-radius: .1rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .ant-table-thead>tr:last-child>th:last-child {
    border-bottom-right-radius: .1rem;
    border-top-right-radius: .1rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j {
    background-color: var(--theme-main-bg-color);
    height: .5rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j td:first-of-type {
    border-radius: .1rem 0 0 .1rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .kaR5fMl5DssvxlvqjM5j td:last-of-type {
    border-radius: 0 .1rem .1rem 0
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN .bPj6UldtSTOxBE3axVum,html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN.SpcrGzVJUnDEf7RK9uqg .ant-table-thead tr>th,html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN.SpcrGzVJUnDEf7RK9uqg .kaR5fMl5DssvxlvqjM5j {
    background-color: var(--theme-bg-color)
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN.SpcrGzVJUnDEf7RK9uqg .bPj6UldtSTOxBE3axVum {
    background-color: var(--theme-main-bg-color)
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN.SpcrGzVJUnDEf7RK9uqg .bPj6UldtSTOxBE3axVum td:first-of-type {
    border-radius: .1rem 0 0 .1rem
}

html[data-device=mobile] .Tg_bNUie8utWvNyBH9JN.SpcrGzVJUnDEf7RK9uqg .bPj6UldtSTOxBE3axVum td:last-of-type {
    border-radius: 0 .1rem .1rem 0
}

@-webkit-keyframes JEMLsGh12_Gg3VgoA57u {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes JEMLsGh12_Gg3VgoA57u {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.sdRXNYUQUhH925OxQhB9 {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: .44rem;
    line-height: .44rem
}

.sdRXNYUQUhH925OxQhB9 .wyX9RJQa0WRtfEdUUvnN {
    background-color: var(--theme-primary-color);
    height: .3rem;
    margin-right: .2rem;
    width: .04rem
}

.sdRXNYUQUhH925OxQhB9 span {
    color: var(--theme-text-color-darken);
    font-size: .3rem
}

.sdRXNYUQUhH925OxQhB9 div {
    color: var(--theme-text-color-darken);
    line-height: 1
}

@-webkit-keyframes _TiWrFu0Gxx_V2tfwTr7 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes _TiWrFu0Gxx_V2tfwTr7 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

._ojhUxKWGyFdJIV2Lodx {
    bottom: -.01rem;
    display: block;
    height: .3rem;
    line-height: .3rem;
    overflow: hidden;
    position: absolute;
    right: -.01rem;
    width: .3rem
}

._ojhUxKWGyFdJIV2Lodx .ZmbOmk0ieSKOdeIimJCA {
    color: var(--theme-active-bg-color);
    height: 100%;
    width: 100%
}

._ojhUxKWGyFdJIV2Lodx .ZmbOmk0ieSKOdeIimJCA>svg {
    height: 100%;
    width: 100%
}

._ojhUxKWGyFdJIV2Lodx .orcE9bnrEF9xDTLnxVZg {
    bottom: .04rem;
    color: var(--theme-active-gou-color);
    color: var(--theme-web_filter_gou);
    height: .096rem;
    position: absolute;
    right: .04rem;
    width: .125rem
}

._ojhUxKWGyFdJIV2Lodx .orcE9bnrEF9xDTLnxVZg>svg {
    height: 100%;
    width: 100%
}

[data-skin-layout="2"][data-skin-bg="0"] ._ojhUxKWGyFdJIV2Lodx .ZmbOmk0ieSKOdeIimJCA {
    color: var(--theme-filter-active-color)
}

@-webkit-keyframes zW6uO8MsQZWXZ4vwl5gE {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes zW6uO8MsQZWXZ4vwl5gE {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.noUD6_uyKc5IKGLrkGoc {
    max-width: 100%;
    position: relative
}

.noUD6_uyKc5IKGLrkGoc .DdjaXiUDtWGedTXUpCI4 {
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    z-index: 1
}

.noUD6_uyKc5IKGLrkGoc .DdjaXiUDtWGedTXUpCI4 .my-scrollbar-bar {
    display: none
}

.noUD6_uyKc5IKGLrkGoc .DdjaXiUDtWGedTXUpCI4 .my-scrollbar-thumb {
    background-color: transparent!important
}

.noUD6_uyKc5IKGLrkGoc .ant-pagination-item-active a {
    color: var(--theme-primary-font-color)!important
}

.noUD6_uyKc5IKGLrkGoc .ant-pagination-item,.noUD6_uyKc5IKGLrkGoc .ant-pagination-next a,.noUD6_uyKc5IKGLrkGoc .ant-pagination-prev a {
    background-color: transparent;
    background-color: initial
}

.noUD6_uyKc5IKGLrkGoc .ant-pagination-jump-next-custom-icon {
    border: .01rem solid
}

.noUD6_uyKc5IKGLrkGoc .ant-pagination-item-ellipsis {
    color: var(--theme-text-color-lighten)!important
}

.uXbMSZOr0Ns9MiQerTqf {
    max-width: 100%
}

.oI1REZeHrjT3mJsfCKtR {
    background-color: var(--theme-main-bg-color)
}

.oI1REZeHrjT3mJsfCKtR,.oI1REZeHrjT3mJsfCKtR .Xoao1azDlzGxhjfesuwG {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.oI1REZeHrjT3mJsfCKtR .Xoao1azDlzGxhjfesuwG {
    -ms-flex-pack: center;
    color: var(--theme-text-color);
    font-size: .24rem;
    height: .8rem;
    justify-content: center;
    width: .8rem
}

.qXolBALZo3WF_OOhxAwr {
    -ms-flex-align: end;
    -ms-flex-align: center;
    -ms-flex-pack: start;
    align-items: flex-end;
    align-items: center;
    border-radius: .1rem .1rem 0 0;
    display: -ms-flexbox;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 0;
    position: relative;
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content
}

.qXolBALZo3WF_OOhxAwr:before {
    border-radius: .1rem .1rem 0 0;
    bottom: 0;
    content: "";
    height: .54rem;
    left: 0;
    pointer-events: none;
    position: absolute;
    width: 100%;
    z-index: 0
}

.qXolBALZo3WF_OOhxAwr[data-len="0"] {
    display: none
}

.qXolBALZo3WF_OOhxAwr.A_05GnS4hxQ1y9GHxJyb[data-len="1"] {
    height: 0
}

.qXolBALZo3WF_OOhxAwr.A_05GnS4hxQ1y9GHxJyb[data-len="1"] .QSmYUjhIbIn5kACuBsKR {
    display: none!important
}

.qXolBALZo3WF_OOhxAwr.G4qzHSehGtU5HcXe5Bnv {
    display: none
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border: thin solid var(--theme-color-line);
    border-radius: .1rem;
    color: var(--theme-text-color-darken);
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    justify-content: center;
    line-height: .6rem;
    margin-right: .3rem;
    padding: 0 .2rem;
    position: relative;
    text-align: center;
    white-space: nowrap;
    word-break: break-word
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR :lang(hi_in) {
    font-size: .16rem
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR:not(.tavNREqvslKfnyAQiSMs):hover {
    color: var(--theme-primary-color)
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR>* {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR.tavNREqvslKfnyAQiSMs {
    border: thin solid var(--theme-filter-active-color);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--theme-primary-color)
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR.tavNREqvslKfnyAQiSMs .subscript .bg {
    color: var(--theme-filter-active-color)
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR.tavNREqvslKfnyAQiSMs:lang(hi_in) {
    font-size: .22rem
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR.DHBzhSXxHpGFcFXtimXQ>* {
    display: inline-block!important;
    line-height: .6rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.qXolBALZo3WF_OOhxAwr .QSmYUjhIbIn5kACuBsKR:last-of-type {
    margin-right: 0!important
}

.icVyW_GwIBX6OVKNKcUi {
    -ms-flex-align: end;
    -ms-flex-align: center;
    -ms-flex-pack: start;
    align-items: flex-end;
    align-items: center;
    border-radius: .1rem .1rem 0 0;
    display: -ms-flexbox;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 0;
    position: relative;
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content
}

.icVyW_GwIBX6OVKNKcUi:before {
    border-radius: .1rem .1rem 0 0;
    bottom: 0;
    content: "";
    height: .54rem;
    left: 0;
    pointer-events: none;
    position: absolute;
    width: 100%;
    z-index: 0
}

.icVyW_GwIBX6OVKNKcUi[data-len="0"] {
    display: none
}

.icVyW_GwIBX6OVKNKcUi.A_05GnS4hxQ1y9GHxJyb[data-len="1"] {
    height: 0
}

.icVyW_GwIBX6OVKNKcUi.A_05GnS4hxQ1y9GHxJyb[data-len="1"] .QSmYUjhIbIn5kACuBsKR {
    display: none!important
}

.icVyW_GwIBX6OVKNKcUi.G4qzHSehGtU5HcXe5Bnv {
    display: none
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    border-bottom: .03rem solid transparent;
    color: var(--theme-text-color-darken);
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    justify-content: center;
    line-height: .24rem;
    margin-right: .3rem;
    position: relative;
    text-align: center;
    white-space: nowrap;
    word-break: break-word
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR :lang(hi_in) {
    font-size: .16rem
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR:not(.tavNREqvslKfnyAQiSMs):hover {
    color: var(--theme-primary-color)
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR>* {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    justify-content: center;
    width: 100%
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR.tavNREqvslKfnyAQiSMs {
    border-bottom: .03rem solid var(--theme-primary-color);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--theme-primary-color)
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR.tavNREqvslKfnyAQiSMs:lang(hi_in) {
    font-size: .22rem
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR.DHBzhSXxHpGFcFXtimXQ>* {
    display: inline-block!important;
    line-height: .6rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR:last-of-type {
    margin-right: 0!important
}

.hRl3tr5NTmp46npyZXX8 {
    bottom: 0;
    min-height: .5rem;
    position: absolute;
    right: 0
}

.mHNTQstHkiQnQQdCvtBf {
    background-color: var(--theme-color-line);
    height: .01rem;
    -webkit-transform: translateY(-.015rem);
    transform: translateY(-.015rem);
    width: 100%
}

html[data-device=mobile] .noUD6_uyKc5IKGLrkGoc .DdjaXiUDtWGedTXUpCI4 {
    background-color: var(--theme-main-bg-color)
}

html[data-device=mobile] .noUD6_uyKc5IKGLrkGoc .DdjaXiUDtWGedTXUpCI4 .my-scrollbar {
    padding: 0 .2rem
}

html[data-device=mobile] .noUD6_uyKc5IKGLrkGoc .VZmcWRZC0JfUYwuXj_B7 {
    background-color: transparent
}

html[data-device=mobile] .icVyW_GwIBX6OVKNKcUi {
    padding: 0
}

html[data-device=mobile] .icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR {
    border-bottom: .04rem solid transparent;
    font-size: .22rem;
    height: .72rem
}

html[data-device=mobile] .icVyW_GwIBX6OVKNKcUi .QSmYUjhIbIn5kACuBsKR.tavNREqvslKfnyAQiSMs {
    border-bottom: .04rem solid var(--theme-primary-color)
}

html[data-device=mobile] .icVyW_GwIBX6OVKNKcUi .Xoao1azDlzGxhjfesuwG {
    font-size: .22rem
}

html[data-device=mobile] .mHNTQstHkiQnQQdCvtBf {
    -webkit-transform: translateY(-.005rem);
    transform: translateY(-.005rem)
}

@-webkit-keyframes hAtw1VJveuaawAy_ii6Y {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes hAtw1VJveuaawAy_ii6Y {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.ETlYzQW0ZLPLfrXjv6oO {
    position: relative
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__nav {
    -webkit-overflow-scrolling: touch;
    background-color: var(--theme-main-bg-color);
    display: inline-block;
    overflow-x: auto;
    padding: 0;
    white-space: nowrap;
    width: 100%
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__nav::-webkit-scrollbar {
    display: none
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__nav .van-tab {
    color: var(--theme-text-color-darken);
    display: inline-block;
    font-size: .24rem;
    padding: 0;
    position: relative
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__nav .van-tab .tabTitle {
    display: -webkit-box;
    padding: 0
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__nav .van-tab .van-tab__text--ellipsis {
    overflow: visible;
    overflow: initial
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__nav .van-tab--active {
    color: var(--theme-primary-color);
    -webkit-transition: all .3s;
    transition: all .3s
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs--line .van-tabs__wrap {
    border-bottom: thin solid var(--theme-color-line);
    display: -ms-flexbox;
    display: flex;
    height: auto;
    overflow-x: auto;
    overflow-y: hidden
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs--line .van-tabs__wrap::-webkit-scrollbar {
    display: none
}

.ETlYzQW0ZLPLfrXjv6oO .van-tabs__line {
    background-color: var(--theme-primary-color);
    bottom: 0;
    height: auto;
    height: .03rem
}

.ETlYzQW0ZLPLfrXjv6oO .JZSpgORc0FCw7pRZyhDT {
    background-color: var(--theme-main-bg-color);
    height: .9rem;
    left: 0;
    position: absolute;
    top: .02rem;
    width: .95rem
}

.ETlYzQW0ZLPLfrXjv6oO .JZSpgORc0FCw7pRZyhDT .VVps4zS1xSbu6HW5I0rX {
    color: var(--theme-text-color);
    font-size: .24rem;
    left: .2rem;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.l6qVBgUGAjapYIuAR_Vw .JZSpgORc0FCw7pRZyhDT,.l6qVBgUGAjapYIuAR_Vw .van-tabs__wrap {
    display: none
}

.VKC7gMos6kDr8F5Ej6It .JZSpgORc0FCw7pRZyhDT,.VKC7gMos6kDr8F5Ej6It .van-tabs__nav {
    background-color: transparent
}

.jNAMpmEIhxiMZURGl06G .van-tabs__nav,.jNAMpmEIhxiMZURGl06G .van-tabs__nav .van-tab {
    display: -ms-flexbox;
    display: flex
}

.XorgNaPq9Iwv1NE5A2AU .van-tab {
    line-height: normal;
    vertical-align: middle;
    white-space: normal
}

.XorgNaPq9Iwv1NE5A2AU .van-tab .tabTitle {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle
}

.XorgNaPq9Iwv1NE5A2AU .van-tab .van-tab__text--ellipsis {
    display: inline-block;
    left: 50%;
    position: relative;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%)
}

.UEwaOCPqOtBUuaMjXFTH .van-tabs__nav {
    margin-left: .95rem;
    width: 6.55rem
}

@-webkit-keyframes fQvCQd1fmmgvbjKGomEG {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes fQvCQd1fmmgvbjKGomEG {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

._VPAQ83_NVbbKcmA02CT {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    position: relative;
    top: .02rem
}

._VPAQ83_NVbbKcmA02CT>span {
    top: .02rem
}

._VPAQ83_NVbbKcmA02CT .vr29ZB5A3ne61b7BLuI4 {
    background-color: var(--theme-secondary-color-error);
    border-radius: .1rem;
    border-bottom-left-radius: 0;
    color: #fff;
    display: block;
    font-size: .14rem;
    font-style: normal;
    height: .2rem;
    line-height: .2rem;
    min-width: .24rem;
    padding: 0 .08rem;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    -webkit-transform: translate(50%,-50%);
    transform: translate(50%,-50%);
    -webkit-transform-origin: 100% 0;
    transform-origin: 100% 0;
    width: auto;
    z-index: 10
}

._VPAQ83_NVbbKcmA02CT .vr29ZB5A3ne61b7BLuI4:after {
    border-bottom: .08rem solid transparent;
    border-left: .08rem solid var(--theme-secondary-color-error);
    border-top: .08rem solid transparent;
    content: "";
    height: 0;
    left: 0;
    position: absolute;
    top: .1rem;
    width: 0
}

html[data-device=mobile] .vr29ZB5A3ne61b7BLuI4 {
    border-radius: .132rem;
    font-size: .18rem;
    height: .264rem;
    line-height: .264rem;
    min-width: .31rem
}

html[data-device=mobile] .vr29ZB5A3ne61b7BLuI4:after {
    border-bottom: .1rem solid transparent;
    border-left: .1rem solid var(--theme-secondary-color-error);
    border-top: .1rem solid transparent;
    top: .12rem
}

@-webkit-keyframes MSzhVx1EFGp8iynTXUU1 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes MSzhVx1EFGp8iynTXUU1 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.ojl53lOx6NCYgQqtWmHF {
    -ms-overflow-style: none;
    height: 100%;
    overflow: auto;
    scrollbar-width: none;
    width: 100%
}

.ojl53lOx6NCYgQqtWmHF::-webkit-scrollbar {
    display: none
}

.eb9a0d6eWbnSe6p7J_7h {
    background-color: transparent;
    display: none;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -9999
}

.eb9a0d6eWbnSe6p7J_7h.VQrCZF0sjMOj_sm49oA7 {
    display: block;
    z-index: 9999
}

@-webkit-keyframes f2GYfO5Uv3TducEpOKGK {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes f2GYfO5Uv3TducEpOKGK {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.Hd1G0bqklE_jZuzf4NqR {
    bottom: 0;
    height: 100%;
    right: 0;
    width: 100%;
    z-index: 2
}

.Hd1G0bqklE_jZuzf4NqR,.Hd1G0bqklE_jZuzf4NqR .Ikg2rPSog8AFFtqfD9W7 {
    left: 0;
    position: absolute;
    top: 0
}

.Hd1G0bqklE_jZuzf4NqR .W5Kxs038l2ysMgykCqa_ {
    position: absolute;
    right: 0;
    top: 0
}

.Hd1G0bqklE_jZuzf4NqR .SgrkZ8Ax_CqBRjIS4h4g {
    bottom: 0;
    left: 0;
    position: absolute
}

.Hd1G0bqklE_jZuzf4NqR .h97sR8iDQdcjWAJRovFb {
    bottom: 0;
    position: absolute;
    right: 0
}

.jd0EDsI7XThFtt58gZMa {
    margin-left: -.02rem;
    position: relative;
    top: .08rem
}

.WqEfQ_t03Tpi2sd9USS1 {
    left: 50%;
    top: .04rem;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

@-webkit-keyframes ILB3xRhR0PWSCydzTZ6J {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes ILB3xRhR0PWSCydzTZ6J {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.Vlll4UyXTvDB3Ntca3mq {
    -ms-flex-pack: justify;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    justify-content: space-between;
    position: relative
}

.Vlll4UyXTvDB3Ntca3mq .gQqOsAU90xupxk4GPxPl {
    height: 100%;
    position: relative;
    z-index: 1
}

.Vlll4UyXTvDB3Ntca3mq .gQqOsAU90xupxk4GPxPl .my-scrollbar-bar {
    display: none
}

.Vlll4UyXTvDB3Ntca3mq .gQqOsAU90xupxk4GPxPl .my-scrollbar-thumb {
    background-color: transparent!important
}

.Vlll4UyXTvDB3Ntca3mq .gQqOsAU90xupxk4GPxPl .my-scrollbar-content {
    height: 100%
}

.WpPzUNpbv7IIV9OhhCvg {
    max-height: 100%
}

.P8RuaFULZKF3ME9gfgKl {
    -ms-flex-align: start;
    -ms-flex-pack: start;
    align-items: flex-start;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    justify-content: flex-start;
    margin: 0;
    position: relative
}

.P8RuaFULZKF3ME9gfgKl[data-len="0"] {
    display: none
}

.P8RuaFULZKF3ME9gfgKl.uxYYjiDd7u0QtHMh5yW3[data-len="1"] {
    height: 0
}

.P8RuaFULZKF3ME9gfgKl.uxYYjiDd7u0QtHMh5yW3[data-len="1"] .bNrPaNsp97Sa9hmzMKZl {
    display: none!important
}

.P8RuaFULZKF3ME9gfgKl.AlUEgyqvlog1PbE1FC96 {
    display: none
}

.P8RuaFULZKF3ME9gfgKl>div:first-of-type .common-badge {
    top: 0
}

.P8RuaFULZKF3ME9gfgKl .tab-1:lang(de_de),.P8RuaFULZKF3ME9gfgKl .tab-1:lang(it_it) {
    font-size: .18rem
}

.P8RuaFULZKF3ME9gfgKl .subItem+.subItem {
    margin-top: .2rem
}

.bNrPaNsp97Sa9hmzMKZl {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    color: var(--theme-left-nav-text-color);
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    font-size: .2rem;
    justify-content: center;
    line-height: .24rem;
    overflow: visible;
    position: relative;
    text-align: center;
    width: 100%;
    word-break: break-word
}

.bNrPaNsp97Sa9hmzMKZl:not(:first-child) {
    margin-top: .2rem
}

.bNrPaNsp97Sa9hmzMKZl:lang(es_es) {
    font-size: .18rem
}

.bNrPaNsp97Sa9hmzMKZl button {
    line-height: 1.1;
    padding: 0 .05rem;
    white-space: pre-wrap
}

.bNrPaNsp97Sa9hmzMKZl .pomqyICLrCuQUttlh76S {
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -1
}

.bNrPaNsp97Sa9hmzMKZl .vi_TBZvr4AMy0V63Nni7 {
    padding-left: .03rem;
    position: relative;
    width: 100%;
    z-index: 5
}

.bNrPaNsp97Sa9hmzMKZl .vi_TBZvr4AMy0V63Nni7>span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    line-height: 1.2;
    overflow: hidden;
    padding: .02rem 0;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: pre-line;
    width: 100%
}

.bNrPaNsp97Sa9hmzMKZl .vi_TBZvr4AMy0V63Nni7>span:lang(tl_ph) {
    -webkit-line-clamp: 3
}

.bNrPaNsp97Sa9hmzMKZl .vi_TBZvr4AMy0V63Nni7>span:lang(my_mm) {
    padding-top: 0
}

.bNrPaNsp97Sa9hmzMKZl .vi_TBZvr4AMy0V63Nni7>span .inner-text:lang(my_mm) {
    line-height: 1.5!important;
    padding-top: 0
}

.bNrPaNsp97Sa9hmzMKZl.FS1An2VNXciHFAijTFUB .vi_TBZvr4AMy0V63Nni7 {
    width: auto
}

.bNrPaNsp97Sa9hmzMKZl.FS1An2VNXciHFAijTFUB .vi_TBZvr4AMy0V63Nni7 .inner-text:lang(my_mm) {
    line-height: 1.5!important
}

.bNrPaNsp97Sa9hmzMKZl:not(.FS1An2VNXciHFAijTFUB) {
    border-radius: .1rem;
    -webkit-box-shadow: 0 .02rem .04rem 0 var(--theme-aside-no-active-box-shadow);
    box-shadow: 0 .02rem .04rem 0 var(--theme-aside-no-active-box-shadow);
    color: var(--theme-left-nav-text-color);
    height: .6rem;
    padding: 0 .1rem;
    white-space: pre-wrap
}

.bNrPaNsp97Sa9hmzMKZl:not(.FS1An2VNXciHFAijTFUB).TAg1HOhNrjpbcFzq75mo {
    color: var(--theme-left-nav-text-active-color);
    pointer-events: none
}

.bNrPaNsp97Sa9hmzMKZl:not(.FS1An2VNXciHFAijTFUB).TAg1HOhNrjpbcFzq75mo svg {
    fill: var(--theme-left-nav-text-active-color)
}

@media(hover: hover) {
    .bNrPaNsp97Sa9hmzMKZl:not(.TAg1HOhNrjpbcFzq75mo):hover {
        color:var(--theme-primary-color)
    }

    .bNrPaNsp97Sa9hmzMKZl:not(.TAg1HOhNrjpbcFzq75mo):hover svg {
        fill: var(--theme-primary-color)
    }
}

.bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) {
    padding: 0 .05rem 0 .08rem
}

.bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .uCwxPG6beyU9xacNcHuw {
    font-size: .44rem;
    width: .28rem
}

.bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .vi_TBZvr4AMy0V63Nni7 {
    -ms-flex: 1;
    flex: 1;
    position: relative
}

.bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ {
    -ms-flex-pack: start;
    border-radius: .16rem;
    -ms-flex-direction: column;
    flex-direction: column;
    height: .9rem;
    justify-content: flex-start;
    padding: .12rem .1rem 0
}

.bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .uCwxPG6beyU9xacNcHuw {
    font-size: .44rem
}

.bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .vi_TBZvr4AMy0V63Nni7 {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: .22rem;
    justify-content: center;
    position: relative
}

.bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .vi_TBZvr4AMy0V63Nni7>span {
    padding: .02rem 0 0
}

html[data-device=mobile][data-skin-layout="2"] .bNrPaNsp97Sa9hmzMKZl .uCwxPG6beyU9xacNcHuw i {
    font-size: .44rem
}

html[data-device=mobile][data-skin-layout="2"] .sZKC80YroX_W1LCaI5C_ .uCwxPG6beyU9xacNcHuw i {
    font-size: .52rem
}

html[data-device=mobile][data-skin-layout="2"] .event-tab-list [data-key=side-item1] .uCwxPG6beyU9xacNcHuw i,html[data-device=mobile][data-skin-layout="2"] [data-key=side-item0] .uCwxPG6beyU9xacNcHuw i {
    font-size: .44rem
}

[data-skin-layout="2"] .bNrPaNsp97Sa9hmzMKZl:not(.FS1An2VNXciHFAijTFUB).TAg1HOhNrjpbcFzq75mo {
    border: initial;
    color: var(--theme-primary-font-color)
}

[data-skin-layout="2"] .bNrPaNsp97Sa9hmzMKZl .uCwxPG6beyU9xacNcHuw i {
    font-size: .42rem
}

[data-skin-layout="2"] .event-tab-list [data-key=side-item1] .uCwxPG6beyU9xacNcHuw i,[data-skin-layout="2"] [data-key=side-item0] .uCwxPG6beyU9xacNcHuw i {
    font-size: .28rem
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    font-size: .24rem
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl .uCwxPG6beyU9xacNcHuw {
    position: relative;
    z-index: 1
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl:not(.FS1An2VNXciHFAijTFUB) {
    -webkit-box-shadow: 0 .03rem .09rem 0 var(--theme-aside-no-active-box-shadow);
    box-shadow: 0 .03rem .09rem 0 var(--theme-aside-no-active-box-shadow);
    min-height: .7rem;
    padding: 0 .04rem 0 .06rem
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .uCwxPG6beyU9xacNcHuw {
    font-size: .48rem;
    width: .44rem
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .vi_TBZvr4AMy0V63Nni7 {
    width: .96rem
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .vi_TBZvr4AMy0V63Nni7:lang(my_mm) {
    line-height: 1.6;
    padding-top: 0
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ {
    height: 1.05rem;
    padding: .12rem .1rem 0;
    position: relative
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .uCwxPG6beyU9xacNcHuw {
    font-size: .44rem
}

html[data-device=mobile] .bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .vi_TBZvr4AMy0V63Nni7 span {
    line-height: 1.1
}

html[data-device=mobile][data-skin-layout="15"] .bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .uCwxPG6beyU9xacNcHuw {
    font-size: .52rem;
    width: .52rem
}

html[data-device=mobile][data-skin-layout="15"] .bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ {
    -ms-flex-pack: center;
    justify-content: center;
    padding: 0
}

html[data-device=mobile][data-skin-layout="15"] .bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .uCwxPG6beyU9xacNcHuw {
    font-size: .52rem
}

html[data-device=mobile][data-skin-layout="15"] .bNrPaNsp97Sa9hmzMKZl.sZKC80YroX_W1LCaI5C_ .uCwxPG6beyU9xacNcHuw svg {
    height: .52rem;
    width: .52rem
}

[data-holiday-theme="101"] .Vlll4UyXTvDB3Ntca3mq .gQqOsAU90xupxk4GPxPl,[data-holiday-theme="102"] .Vlll4UyXTvDB3Ntca3mq .gQqOsAU90xupxk4GPxPl {
    margin-right: -.1rem;
    padding-right: .1rem
}

html[data-device=desktop][data-skin-layout="15"] .bNrPaNsp97Sa9hmzMKZl.JOWqBceNWiyZuqdBAXLg:not(.sZKC80YroX_W1LCaI5C_) .uCwxPG6beyU9xacNcHuw {
    font-size: .42rem;
    width: .42rem
}

@-webkit-keyframes FBRno9k8eXOo4xZQivZC {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes FBRno9k8eXOo4xZQivZC {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.WUhuhqmZmF6jIMZymVBJ {
    width: 100%
}

.TiR5Afd0l3YoMxbINowZ .Q7vvTIrPmFwPigX1RicO {
    margin: 0 auto;
    width: 12rem
}

.TiR5Afd0l3YoMxbINowZ .W_Dh20YFkNn94oSozvXJ {
    width: 100%
}

html[data-device=desktop][data-skin-layout="18"] .Q7vvTIrPmFwPigX1RicO {
    height: auto;
    margin: 0 auto;
    width: 12rem
}

html[data-device=desktop][data-skin-layout="18"] .layout-container .container-content {
    width: calc(100vw - 3.4rem)
}

html[data-device=mobile] .TiR5Afd0l3YoMxbINowZ,html[data-device=mobile] .TiR5Afd0l3YoMxbINowZ .Q7vvTIrPmFwPigX1RicO {
    width: 100%
}

@-webkit-keyframes ZeNAs3wYEjhdi_NGcNDI {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes ZeNAs3wYEjhdi_NGcNDI {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.cNvGQvy_BCwkfCd6pd6q {
    -ms-flex-pack: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    padding-top: .2rem
}

.cNvGQvy_BCwkfCd6pd6q .m0FblQU8qzDajDNxXyZN {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: .2rem
}

.cNvGQvy_BCwkfCd6pd6q .m0FblQU8qzDajDNxXyZN .VIEXrTi116TkIsAN2nwG {
    margin-left: auto
}

.cNvGQvy_BCwkfCd6pd6q .Wv7iu4kk06YDMBB93WZA {
    text-align: center;
    width: 100%
}

.cNvGQvy_BCwkfCd6pd6q .p1v1Kofv8FGkIrX7dQth {
    background-color: var(--theme-main-bg-color);
    border-radius: .1rem;
    -webkit-box-shadow: 0 .03rem .06rem 0 rgba(0,0,0,.06);
    box-shadow: 0 .03rem .06rem 0 rgba(0,0,0,.06)
}

html[data-device=mobile] .cNvGQvy_BCwkfCd6pd6q {
    padding-top: 0
}

html[data-device=mobile] .cNvGQvy_BCwkfCd6pd6q .m0FblQU8qzDajDNxXyZN {
    margin-bottom: 0
}

@-webkit-keyframes G9SLpUbDmvrvPYwNEqH0 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes G9SLpUbDmvrvPYwNEqH0 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.qqhDfh0Yw8XfaLbPnS1v {
    color: var(--theme-primary-color)
}

@-webkit-keyframes VB9IM0D_OwkISLCEk0BE {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes VB9IM0D_OwkISLCEk0BE {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.yUCly6epdXnWW54zhUvY>input {
    padding-right: 1.6rem!important
}

.yUCly6epdXnWW54zhUvY>input::-webkit-input-placeholder {
    max-width: 4rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.yUCly6epdXnWW54zhUvY>input::-moz-placeholder {
    max-width: 4rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.yUCly6epdXnWW54zhUvY>input:-ms-input-placeholder {
    max-width: 4rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.yUCly6epdXnWW54zhUvY>input::-ms-input-placeholder {
    max-width: 4rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.yUCly6epdXnWW54zhUvY>input::placeholder {
    max-width: 4rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.yUCly6epdXnWW54zhUvY .ant-input-suffix {
    right: .05rem
}

.yUCly6epdXnWW54zhUvY .YmZ41aCpqDBzcf3FjcbL {
    -ms-flex-align: center;
    align-items: center;
    color: var(--theme-text-color-lighten);
    display: -ms-flexbox;
    display: flex
}

.yUCly6epdXnWW54zhUvY .YmZ41aCpqDBzcf3FjcbL .azPeMRijDwzVflkZzc5w {
    color: var(--theme-secondary-color-error);
    margin-left: .04rem
}

.yUCly6epdXnWW54zhUvY .gDjlVPiPc_pVvkYClVBg {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    height: auto;
    margin-left: .2rem;
    max-width: 3rem;
    padding: 0!important
}

.yUCly6epdXnWW54zhUvY .gDjlVPiPc_pVvkYClVBg>span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    line-height: 1.2!important;
    margin-left: 0;
    overflow: hidden;
    text-align: right;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: normal
}

.yUCly6epdXnWW54zhUvY .gDjlVPiPc_pVvkYClVBg[disabled]>span {
    color: var(--theme-text-color-lighten);
    text-align: right
}

.yUCly6epdXnWW54zhUvY .gDjlVPiPc_pVvkYClVBg:before {
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content
}

.yUCly6epdXnWW54zhUvY .QmQdt_huUbYqoTF5Vxsn {
    border: .01rem solid var(--theme-color-line);
    border-radius: .1rem;
    cursor: pointer;
    height: .4rem;
    margin-left: .15rem;
    width: 1rem
}

html[data-device=mobile] .yUCly6epdXnWW54zhUvY>input::-webkit-input-placeholder {
    max-width: 4.7rem
}

html[data-device=mobile] .yUCly6epdXnWW54zhUvY>input::-moz-placeholder {
    max-width: 4.7rem
}

html[data-device=mobile] .yUCly6epdXnWW54zhUvY>input:-ms-input-placeholder {
    max-width: 4.7rem
}

html[data-device=mobile] .yUCly6epdXnWW54zhUvY>input::-ms-input-placeholder {
    max-width: 4.7rem
}

html[data-device=mobile] .yUCly6epdXnWW54zhUvY>input::placeholder {
    max-width: 4.7rem
}

html[data-device=mobile] .gDjlVPiPc_pVvkYClVBg {
    font-size: .22rem
}

html[data-device=mobile] .QmQdt_huUbYqoTF5Vxsn {
    margin-left: .2rem
}

@-webkit-keyframes QZsXFVPw9d6ijtzk9iBC {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes QZsXFVPw9d6ijtzk9iBC {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.PmwtECGo9wfGBMP0uy71 {
    color: var(--theme-primary-color);
    cursor: pointer
}

@-webkit-keyframes WvDUiBVLqgWf2yxeo2mo {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes WvDUiBVLqgWf2yxeo2mo {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.Z9FTE8VLKdrJJyfWuzJY {
    color: var(--theme-primary-color);
    cursor: pointer;
    font-size: .18rem
}

html[data-device=mobile] .Z9FTE8VLKdrJJyfWuzJY {
    font-size: .22rem
}

@-webkit-keyframes ImHRWQuGpIJmFY5AxqKY {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes ImHRWQuGpIJmFY5AxqKY {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.rM039pSymXZ1j3TOKsM4 {
    background-color: var(--theme-main-bg-color);
    display: -ms-flexbox;
    display: flex;
    height: .5rem;
    line-height: .5rem;
    margin: 0;
    overflow: hidden;
    text-align: center
}

.rM039pSymXZ1j3TOKsM4[data-len="1"] {
    width: 1rem
}

.rM039pSymXZ1j3TOKsM4[data-len="2"] {
    width: 2rem
}

.rM039pSymXZ1j3TOKsM4[data-len="3"] {
    width: 3rem
}

.rM039pSymXZ1j3TOKsM4[data-len="4"] {
    width: 4rem
}

.rM039pSymXZ1j3TOKsM4[data-len="5"] {
    width: 5rem
}

.rM039pSymXZ1j3TOKsM4[data-len="6"] {
    width: 6rem
}

.rM039pSymXZ1j3TOKsM4 .Qv6s8kdZfMcsNoljkOTK .ps__rail-y {
    z-index: 100
}

.rM039pSymXZ1j3TOKsM4 .BoRqmwDz1UNyF7cg8cB5 {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border-color: var(--theme-color-line);
    border-style: solid;
    border-width: .01rem;
    border-right: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--theme-text-color);
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    height: .5rem;
    justify-content: center;
    line-height: .18rem;
    padding: 0 .05rem;
    width: 1rem;
    word-break: break-word
}

.rM039pSymXZ1j3TOKsM4 .BoRqmwDz1UNyF7cg8cB5:first-child {
    border-bottom-left-radius: .1rem;
    border-top-left-radius: .1rem
}

.rM039pSymXZ1j3TOKsM4 .BoRqmwDz1UNyF7cg8cB5:last-child {
    border-bottom-right-radius: .1rem;
    border-right: .01rem solid var(--theme-color-line);
    border-top-right-radius: .1rem
}

.rM039pSymXZ1j3TOKsM4 .BoRqmwDz1UNyF7cg8cB5:not(.DEbb9br4ZSP3UMFwicwR):hover {
    color: var(--theme-primary-color)
}

.rM039pSymXZ1j3TOKsM4 .BoRqmwDz1UNyF7cg8cB5:lang(hi_in) {
    line-height: .2rem
}

.rM039pSymXZ1j3TOKsM4 .DEbb9br4ZSP3UMFwicwR {
    background-color: var(--theme-primary-color);
    border-color: var(--theme-primary-color)!important;
    color: var(--theme-primary-font-color)
}

.rM039pSymXZ1j3TOKsM4 .Ek71VrfJn1cpYKdrrTf_:lang(en_us) {
    font-size: .16rem
}

.ZScynNvNJbJq6YHEY8pA .ant-select-arrow-icon {
    font-size: .18rem
}

.ZScynNvNJbJq6YHEY8pA .ant-form-item {
    height: .5rem
}

.ZScynNvNJbJq6YHEY8pA div.ant-select-selection {
    background-color: var(--theme-main-bg-color)!important;
    border-radius: .5rem;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: var(--theme-text-color);
    font-size: .18rem;
    font-weight: 400;
    height: .5rem
}

.ZScynNvNJbJq6YHEY8pA div.ant-select-selection:focus {
    font-weight: 700
}

.ZScynNvNJbJq6YHEY8pA div.ant-select-selection .ant-select-selection__rendered {
    height: 100%;
    line-height: .5rem
}

.ZScynNvNJbJq6YHEY8pA div.ant-select-selection .ant-select-arrow {
    margin-top: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.ZScynNvNJbJq6YHEY8pA div.ant-select-selection .ant-select-arrow .ant-select-arrow-icon {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    color: var(--theme-text-color-lighten);
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem;
    justify-content: center
}

.ZScynNvNJbJq6YHEY8pA .ant-form-item-children>.ant-select .ant-select-selection {
    color: var(--theme-text-color)
}

.ZScynNvNJbJq6YHEY8pA .F5DuQ644tHoRMqnUO1O2 {
    font-size: .2rem
}

.ZScynNvNJbJq6YHEY8pA .F5DuQ644tHoRMqnUO1O2 .ant-select-dropdown-menu-item {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: .18rem;
    height: .4rem;
    line-height: .4rem;
    padding: 0 .15rem
}

html[data-device=mobile] .ZScynNvNJbJq6YHEY8pA div.ant-select-selection {
    font-size: .2rem;
    height: .5rem!important
}

html[data-device=mobile] .ZScynNvNJbJq6YHEY8pA div.ant-select-selection .ant-select-selection__rendered {
    color: var(--theme-text-color-lighten);
    font-size: .2rem;
    line-height: .48rem!important
}

html[data-device=mobile] .ZScynNvNJbJq6YHEY8pA div.ant-select-selection .ant-select-arrow .ant-select-arrow-icon {
    font-size: .2rem
}

@-webkit-keyframes FJisw2VBcLuANlQz73LP {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes FJisw2VBcLuANlQz73LP {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.PFr_a_F2Hp5B4yxj3xvh {
    border-radius: .1rem;
    margin: 0 .1rem;
    overflow: hidden
}

@-webkit-keyframes HNwOeA9BdOXQ8cLhf58t {
    0% {
        opacity: 0
    }

    50% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

@keyframes HNwOeA9BdOXQ8cLhf58t {
    0% {
        opacity: 0
    }

    50% {
        opacity: 1
    }

    to {
        opacity: 0
    }
}

.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab {
    height: .15rem;
    opacity: 0;
    position: fixed;
    width: .15rem;
    z-index: -1
}

.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li {
    position: relative
}

.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li.qywCxukpVNuYyPfI4XVq:after {
    border: .01rem solid var(--theme-primary-color);
    content: "";
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%
}

.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li.qywCxukpVNuYyPfI4XVq:not(.mzfnT1F3zRDTZOXCiEGs) i,.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li.qywCxukpVNuYyPfI4XVq:not(.mzfnT1F3zRDTZOXCiEGs) span {
    -webkit-animation: HNwOeA9BdOXQ8cLhf58t 1s infinite;
    animation: HNwOeA9BdOXQ8cLhf58t 1s infinite;
    background: var(--theme-text-color-darken);
    border-radius: 0;
    display: inline-block;
    height: .3rem;
    opacity: 1;
    -webkit-transition: none;
    transition: none;
    width: .02rem
}

.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li:first-child.qywCxukpVNuYyPfI4XVq:after {
    border-bottom-left-radius: .1rem;
    border-top-left-radius: .1rem
}

.PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li:last-child.qywCxukpVNuYyPfI4XVq:after {
    border-bottom-right-radius: .1rem;
    border-top-right-radius: .1rem
}

.PFr_a_F2Hp5B4yxj3xvh .WeJIDzHw_dbFN6u7T59A {
    left: -100%;
    opacity: 0
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy {
    border: thin solid var(--theme-color-line);
    border-radius: .1rem;
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    height: .8rem;
    margin: 0;
    max-width: 100%;
    overflow: hidden
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy.disabled {
    cursor: not-allowed
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    background: var(--theme-main-bg-color);
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1;
    flex: 1;
    height: 100%;
    justify-content: center
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li:not(:last-child) {
    border-right: .01rem solid var(--theme-color-line)
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li.mzfnT1F3zRDTZOXCiEGs i {
    opacity: 1
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li i {
    background: var(--theme-text-color-darken);
    border-radius: 50%;
    display: inline-block;
    height: .22rem;
    opacity: 0;
    width: .22rem
}

.PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li span {
    color: var(--theme-text-color-darken);
    font-size: .22rem
}

.Gj0Iih8M0bKElM2QGtHO {
    border: .01rem solid var(--theme-secondary-color-error)
}

.JWExyL7wP1w73iOHOxyc {
    border: .01rem solid var(--theme-primary-color)
}

.X7dUlEkj1ycuuUbQ_2Vf {
    border: .01rem solid var(--theme-secondary-color-success)
}

.Ou1aviPriFX421w97dGA {
    -ms-flex-pack: justify;
    color: var(--theme-text-color-darken);
    font-size: .18rem;
    height: .26rem;
    justify-content: space-between;
    margin-bottom: .12rem;
    width: 100%
}

.Ou1aviPriFX421w97dGA,.Ou1aviPriFX421w97dGA .R1tVtJPfBJA944lSRyyP,.Ou1aviPriFX421w97dGA>div {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.Ou1aviPriFX421w97dGA .R1tVtJPfBJA944lSRyyP {
    cursor: pointer;
    font-size: .26rem
}

.Ou1aviPriFX421w97dGA .MfYbH3dAPBLOkaKUtRe7 {
    pointer-events: none
}

.MOFLcj7nVRZHKLivuWpP {
    color: var(--theme-primary-color)
}

.M2AXg2u2tA9h8un7RCbH {
    color: var(--theme-color-line)
}

html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li.qywCxukpVNuYyPfI4XVq:not(.mzfnT1F3zRDTZOXCiEGs) i,html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li.qywCxukpVNuYyPfI4XVq:not(.mzfnT1F3zRDTZOXCiEGs) span {
    height: .32rem;
    width: .025rem
}

html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li:first-child.qywCxukpVNuYyPfI4XVq:after {
    border-bottom-left-radius: .14rem;
    border-top-left-radius: .14rem
}

html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .k54PxY4rjAorQBZf12Ab:focus+.zNd0y3FNYltxAiKhPbAy li:last-child.qywCxukpVNuYyPfI4XVq:after {
    border-bottom-right-radius: .14rem;
    border-top-right-radius: .14rem
}

html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy {
    border-radius: .14rem;
    height: 1rem
}

html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li i {
    height: .26rem;
    width: .26rem
}

html[data-device=mobile] .PFr_a_F2Hp5B4yxj3xvh .zNd0y3FNYltxAiKhPbAy li span {
    color: var(--theme-text-color-darken);
    font-size: .5rem
}

html[data-device=mobile] .Ou1aviPriFX421w97dGA {
    font-size: .22rem;
    height: .32rem;
    margin-bottom: .15rem
}

html[data-device=mobile] .Ou1aviPriFX421w97dGA .R1tVtJPfBJA944lSRyyP {
    font-size: .36rem
}

@-webkit-keyframes H6oMkpbGat3MADrJ1DvY {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes H6oMkpbGat3MADrJ1DvY {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.p7wkLfrEyEqq8aSd9D_F {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    width: 100%
}

.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW {
    border: .01rem solid var(--theme-secondary-color-error);
    border-radius: .1rem;
    height: .5rem;
    margin: 0 .1rem;
    overflow: hidden
}

.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input {
    background: 0 0!important;
    border: none!important;
    border-bottom: .01rem solid var(--theme-color-line)!important;
    border-radius: 0;
    color: var(--theme-text-color-darken)!important;
    font-size: .26rem;
    height: 100%;
    margin: 0 .05rem;
    padding: 0;
    width: 2rem
}

.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input.focus-visible,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:active,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:focus,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:hover {
    border: none!important;
    border-bottom: .01rem solid var(--theme-color-line)!important;
    -webkit-box-shadow: none!important;
    box-shadow: none!important;
    font-weight: 700;
    outline: none
}

.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:active,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:focus,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:focus-visible,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:hover {
    border: none!important;
    border-bottom: .01rem solid var(--theme-color-line)!important;
    -webkit-box-shadow: none!important;
    box-shadow: none!important
}

.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:active,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:focus,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:focus-visible,.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input:hover {
    font-weight: 700;
    outline: none
}

.p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input[disabled] {
    color: var(--theme-text-color-darken)
}

.p7wkLfrEyEqq8aSd9D_F .fkLbPRk1X4cx1Y8IvOgb {
    border: .01rem solid var(--theme-secondary-color-success)
}

.p7wkLfrEyEqq8aSd9D_F .ZNHJgVsYgkxwwQyJW9ZO {
    -ms-flex-align: center;
    align-items: center;
    color: var(--theme-text-color-darken);
    display: -ms-flexbox;
    display: flex;
    font-size: .26rem;
    font-weight: 700
}

.p7wkLfrEyEqq8aSd9D_F .ZNHJgVsYgkxwwQyJW9ZO i {
    color: var(--theme-text-color-lighten);
    margin-right: .1rem;
    margin-top: .04rem
}

.w4KRSA0dCRF4nuXvJUWT .M4zOZsY5GVWIEjh2t8EW {
    border: .01rem solid var(--theme-primary-color)
}

html[data-device=mobile] .p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW {
    height: .7rem
}

html[data-device=mobile] .p7wkLfrEyEqq8aSd9D_F .M4zOZsY5GVWIEjh2t8EW input {
    font-size: .3rem;
    margin: 0 .1rem
}

html[data-device=mobile] .p7wkLfrEyEqq8aSd9D_F .ZNHJgVsYgkxwwQyJW9ZO {
    font-size: .3rem;
    font-weight: 700;
    line-height: .44rem;
    margin-top: 0
}

@-webkit-keyframes bzzCDWtFJsMMA847m3Ag {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes bzzCDWtFJsMMA847m3Ag {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.ObXUdqvr2O_T59WnYfIP {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center;
    width: 100%
}

.ObXUdqvr2O_T59WnYfIP .number-input__text {
    color: var(--theme-text-color-lighten)!important;
    font-size: .26rem!important;
    font-weight: 700
}

.ObXUdqvr2O_T59WnYfIP .pwd-input-item-fill .number-input__text {
    color: var(--theme-text-color)!important
}

.ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap {
    -ms-flex-pack: center!important;
    border: none;
    border-bottom: .01rem solid var(--theme-color-line);
    border-radius: 0;
    display: -ms-flexbox!important;
    display: flex!important;
    height: .5rem;
    justify-content: center!important;
    max-width: 2rem;
    min-width: .9rem;
    padding: .06rem
}

.ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap .pwd-input-item {
    background: transparent none repeat 0 0/auto auto padding-box border-box scroll;
    background: initial;
    -ms-flex: 0 1 auto;
    flex: 0 1 auto
}

.ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap .pwd-input-item-active:after {
    border: none!important
}

.ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap li:not(:last-child) {
    border-right: none!important
}

.ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap li {
    border-bottom: .01rem solid var(--theme-text-color-placeholder);
    margin-right: .04rem;
    min-width: .2rem
}

.ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl input:focus+.pwd-wrap .pwd-input-item.pwd-input-item-active:after {
    border-radius: .05rem
}

.ObXUdqvr2O_T59WnYfIP .AOy9_spDwFfaJ1KyoXaO {
    color: var(--theme-text-color);
    font-size: .26rem;
    font-weight: 700;
    letter-spacing: .05rem;
    margin-right: -.05rem
}

.ObXUdqvr2O_T59WnYfIP .AOy9_spDwFfaJ1KyoXaO .rS7A0MEjX5QYCuc8DBRj {
    color: var(--theme-text-color-lighten);
    margin-right: .08rem
}

html[data-device=mobile] .ObXUdqvr2O_T59WnYfIP .number-input__text {
    font-size: .3rem!important
}

html[data-device=mobile] .ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap {
    border-radius: 0;
    height: .7rem
}

html[data-device=mobile] .ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl .pwd-wrap .pwd-input-item span {
    font-size: .3rem
}

html[data-device=mobile] .ObXUdqvr2O_T59WnYfIP .JOQ3xKe3o2PATB3sIlSl input:focus+.pwd-wrap .pwd-input-item.pwd-input-item-active:after {
    border-radius: .08rem
}

html[data-device=mobile] .ObXUdqvr2O_T59WnYfIP .AOy9_spDwFfaJ1KyoXaO {
    color: var(--theme-text-color);
    font-size: .3rem;
    font-weight: 700;
    letter-spacing: .05rem;
    line-height: .44rem;
    margin-right: -.05rem
}

html[data-device=mobile] .ObXUdqvr2O_T59WnYfIP .AOy9_spDwFfaJ1KyoXaO .rS7A0MEjX5QYCuc8DBRj {
    margin-right: .1rem
}

@-webkit-keyframes AVEoqjxlGEw3FlNH_O9u {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes AVEoqjxlGEw3FlNH_O9u {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.on1CNo54XAiFeXkANsKU {
    -ms-flex-align: var(--column-align);
    -ms-flex-pack: var(--row-align);
    align-items: var(--column-align);
    border: .01rem solid var(--theme-color-line);
    border-radius: .2rem;
    display: var(--mode);
    height: .4rem;
    justify-content: var(--row-align);
    line-height: normal;
    padding: 0 .05rem 0 .02rem;
    width: -webkit-min-content;
    width: -moz-min-content;
    width: min-content
}

.on1CNo54XAiFeXkANsKU .LpgLT8H8XiTu9f8wSCN3 {
    height: .32rem;
    width: .32rem
}

.on1CNo54XAiFeXkANsKU .LpgLT8H8XiTu9f8wSCN3,.on1CNo54XAiFeXkANsKU .LpgLT8H8XiTu9f8wSCN3 .s7buSuyfMZa4NRKx9go2 {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    border-radius: 50%;
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

.on1CNo54XAiFeXkANsKU .LpgLT8H8XiTu9f8wSCN3 .T5FdmfEZfjC0dwWeHhTA {
    border-radius: 100%
}

.on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg>div {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem
}

.on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .PVyEsfr4c912z000EVrX {
    color: var(--theme-text-color-lighten);
    font-size: .18rem;
    padding: 0 .13rem;
    width: -webkit-max-content;
    width: -moz-max-content;
    width: max-content
}

.on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    color: var(--theme-secondary-color-finance);
    cursor: pointer;
    font-size: .22rem;
    margin: 0 .1rem;
    text-decoration: underline;
    white-space: nowrap
}

.on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSYrjrFkdeGEfKeXOPu3 {
    text-decoration: none!important
}

.on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .NKLcc8z9N8zQBqlEQHzQ {
    color: var(--theme-primary-color);
    cursor: pointer;
    font-size: .22rem
}

[data-skin-layout="3"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    margin-top: 0;
    white-space: nowrap
}

[data-skin-layout="3"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .NKLcc8z9N8zQBqlEQHzQ {
    color: var(--theme-primary-font-color)
}

[data-skin-layout="9"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    text-decoration: none
}

.IMl873FTgqqNlSitFJrT {
    height: .3rem
}

html[data-device=mobile] .on1CNo54XAiFeXkANsKU {
    padding: 0 .084rem 0 .05rem
}

html[data-device=mobile] .on1CNo54XAiFeXkANsKU .LpgLT8H8XiTu9f8wSCN3 {
    height: .34rem;
    width: .34rem
}

html[data-device=mobile] .on1CNo54XAiFeXkANsKU .Ci0oKAAtemG6hrydf9BX {
    border-radius: 0
}

html[data-device=mobile] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

html[data-device=mobile] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    color: var(--theme-secondary-color-finance);
    font-size: .26rem;
    margin: 0 .073rem 0 .1rem;
    white-space: nowrap
}

html[data-device=mobile] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .NKLcc8z9N8zQBqlEQHzQ {
    color: var(--theme-alt-primary);
    font-size: .293rem;
    position: relative;
    z-index: 10
}

html[data-device=mobile][data-skin-layout="2"] .on1CNo54XAiFeXkANsKU {
    height: .4rem;
    padding: 0 .084rem 0 .02rem
}

html[data-device=mobile][data-skin-layout="2"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

html[data-device=mobile][data-skin-layout="2"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    display: block;
    margin: -.06rem .073rem 0 .1rem;
    max-width: 1.64rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

html[data-device=mobile][data-skin-layout="2"] .IMl873FTgqqNlSitFJrT {
    height: .3rem
}

html[data-device=mobile][data-skin-layout="2"] .IMl873FTgqqNlSitFJrT .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    margin-top: 0;
    white-space: nowrap
}

html[data-device=mobile][data-skin-layout="3"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .NKLcc8z9N8zQBqlEQHzQ {
    color: var(--theme-primary-color)
}

html[data-device=mobile][data-skin-layout="4"] .on1CNo54XAiFeXkANsKU {
    height: .5rem;
    padding-left: .15rem;
    padding-right: .15rem
}

html[data-device=mobile][data-skin-layout="4"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    color: #fff;
    font-size: .24rem
}

html[data-device=mobile][data-skin-layout="9"] .on1CNo54XAiFeXkANsKU {
    background-color: #fff
}

[data-skin-layout="4"] .on1CNo54XAiFeXkANsKU {
    border: .01rem solid var(--theme-alt-border);
    border-radius: .04rem;
    padding: 0 .1rem
}

[data-skin-layout="4"] .on1CNo54XAiFeXkANsKU .NKLcc8z9N8zQBqlEQHzQ {
    color: var(--theme-text-color-darken);
    font-size: .15rem
}

[data-skin-layout="4"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    color: var(--theme-alt-border);
    font-size: .16rem;
    text-decoration: none
}

[data-skin-layout="4"][data-skin-bg="0"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .NKLcc8z9N8zQBqlEQHzQ,[data-skin-layout="4"][data-skin-bg="0"] .on1CNo54XAiFeXkANsKU .iw9FDOIygJyoQtUHSJAg .SSAbrhtT3U690CrzLUd5 {
    color: #fff
}

html[data-device=desktop] .iw9FDOIygJyoQtUHSJAg {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

html[data-device=desktop] .iw9FDOIygJyoQtUHSJAg>div>span {
    padding-bottom: .02rem
}

html[data-device=desktop][data-skin-layout="5"] .LpgLT8H8XiTu9f8wSCN3,html[data-device=desktop][data-skin-layout="6"] .LpgLT8H8XiTu9f8wSCN3 {
    height: .2rem;
    width: .2rem
}

html[data-device=desktop][data-skin-layout="5"] .on1CNo54XAiFeXkANsKU,html[data-device=desktop][data-skin-layout="6"] .on1CNo54XAiFeXkANsKU {
    border-radius: 0;
    height: .3rem
}

html[data-device=desktop][data-skin-layout="5"] .on1CNo54XAiFeXkANsKU .SSAbrhtT3U690CrzLUd5,html[data-device=desktop][data-skin-layout="6"] .on1CNo54XAiFeXkANsKU .SSAbrhtT3U690CrzLUd5 {
    font-size: .16rem;
    margin-left: .06rem
}

html[data-device=desktop] [data-skin-layout="5"] .on1CNo54XAiFeXkANsKU {
    background-color: var(--theme-main-bg-color)
}

html[data-device=desktop] [data-skin-layout="5"] .on1CNo54XAiFeXkANsKU .SSAbrhtT3U690CrzLUd5 {
    color: var(--theme-text-color-darken)
}

html[data-device=desktop] [data-skin-layout="5"] .on1CNo54XAiFeXkANsKU .NKLcc8z9N8zQBqlEQHzQ {
    color: #d8cdb9
}

html[data-device=desktop][data-skin-layout="4"] .LpgLT8H8XiTu9f8wSCN3 {
    height: .22rem;
    width: .22rem
}

html[data-device=desktop][data-skin-layout="9"] .on1CNo54XAiFeXkANsKU {
    background-color: #fff;
    height: .35rem
}

html[data-device=desktop][data-skin-layout="9"] .on1CNo54XAiFeXkANsKU .SSAbrhtT3U690CrzLUd5 {
    color: #ffaa09;
    font-size: .16rem
}

html[data-device=desktop][data-skin-layout="9"] .on1CNo54XAiFeXkANsKU .NKLcc8z9N8zQBqlEQHzQ {
    font-size: .16rem
}

html[data-device=desktop][data-skin-layout="9"] .on1CNo54XAiFeXkANsKU .LpgLT8H8XiTu9f8wSCN3 {
    height: .22rem;
    width: .2rem
}

@-webkit-keyframes _o0HSUnfsBIApNLbSKHg {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes _o0HSUnfsBIApNLbSKHg {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.uh6csli2MHzFz87__Phx {
    height: .32rem;
    left: var(--cu-left);
    position: absolute;
    right: var(--cu-right);
    top: var(--cu-top);
    width: -webkit-fit-content;
    width: -moz-fit-content;
    width: fit-content;
    z-index: 100
}

.uh6csli2MHzFz87__Phx p {
    margin-bottom: 0
}

.uh6csli2MHzFz87__Phx .YiNQnpSJUes7u5sqBZhp {
    border-bottom: .07rem solid transparent;
    border-left: .07rem solid var(--theme-secondary-color-error);
    border-right: .07rem solid transparent;
    height: 0;
    width: 0
}

.uh6csli2MHzFz87__Phx .T5xNMynUJQws22ZME9hV {
    border-bottom: .07rem solid transparent;
    border-left: .07rem solid transparent;
    border-right: .07rem solid var(--theme-secondary-color-error);
    float: right
}

.uh6csli2MHzFz87__Phx .sNgyWmhVEZVrbSGKDBra {
    background-color: var(--theme-secondary-color-error);
    border-radius: .125rem .125rem .125rem 0;
    color: #fff;
    font-size: .14rem;
    height: .22rem;
    line-height: .22rem;
    padding: 0 .08rem;
    width: 100%
}

.uh6csli2MHzFz87__Phx .sNgyWmhVEZVrbSGKDBra:lang(my_mm) {
    height: .26rem
}

.uh6csli2MHzFz87__Phx .CmN8CVsfet8ieqBIT7mi {
    color: #ff0
}

.uh6csli2MHzFz87__Phx .nc_NIDTLmXeUSFFfADOl {
    border-radius: .125rem .125rem 0 .125rem
}

html[data-device=mobile] .uh6csli2MHzFz87__Phx .sNgyWmhVEZVrbSGKDBra {
    font-size: .18rem;
    height: .26rem;
    line-height: .26rem;
    padding: 0 .07rem
}

@-webkit-keyframes EeZKUvi8xPTo3gPzZ2CP {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes EeZKUvi8xPTo3gPzZ2CP {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.MkSy5eDdoOSfXU3d7o2M {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    overflow: hidden;
    -webkit-transition: height .5s;
    transition: height .5s
}

.MkSy5eDdoOSfXU3d7o2M.EHDOc_GbMyJ1c8_nYpyg {
    border: none!important;
    height: 0!important
}

.MkSy5eDdoOSfXU3d7o2M .ODpmAuZ5P5dWvxbKwcOf {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    width: 100%
}

html[data-device=mobile] .MkSy5eDdoOSfXU3d7o2M {
    height: .7rem
}

html[data-device=mobile] .MkSy5eDdoOSfXU3d7o2M.DTQ77GFvQ2Ey4_fFDXqQ {
    border-bottom: .01rem solid var(--theme-color-line)
}

html[data-device=mobile] .MkSy5eDdoOSfXU3d7o2M .ODpmAuZ5P5dWvxbKwcOf {
    height: .7rem
}

html[data-device=desktop] .MkSy5eDdoOSfXU3d7o2M {
    border: .01rem solid var(--theme-color-line);
    height: .6rem
}

html[data-device=desktop] .MkSy5eDdoOSfXU3d7o2M.DTQ77GFvQ2Ey4_fFDXqQ {
    border-top: none
}

html[data-device=desktop] .MkSy5eDdoOSfXU3d7o2M.MwaZK18hPnR_rSJNsAYS {
    border: none
}

html[data-device=desktop] .MkSy5eDdoOSfXU3d7o2M.C2IwkhcvFsxyExDTfIz6 {
    border-bottom: none
}

html[data-device=desktop] .MkSy5eDdoOSfXU3d7o2M.J0giKf1K2ORtvdaWRQxj {
    border: none!important
}

html[data-device=desktop] .MkSy5eDdoOSfXU3d7o2M .ODpmAuZ5P5dWvxbKwcOf {
    height: .6rem
}

@-webkit-keyframes t6h1G3BzOtqETknPxagj {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes t6h1G3BzOtqETknPxagj {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.DAEXSUBjuzUY1EoIAeEn {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.DAEXSUBjuzUY1EoIAeEn>img {
    will-change: auto!important
}

.DAEXSUBjuzUY1EoIAeEn img.use-skeleton {
    min-width: .5rem
}

.DAEXSUBjuzUY1EoIAeEn.LubvNYW6hJ8aFPwvFVVB {
    cursor: pointer
}

.DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw,.DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw {
    -ms-flex-positive: 1;
    flex-grow: 1
}

.DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .v4LQgsaGCn3mMTQ4S4GC {
    color: var(--theme-text-color-darken)
}

.DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .EgqAwCEBwZ2ciGJxH_i1 {
    position: relative
}

.DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .EgqAwCEBwZ2ciGJxH_i1 .M_eD9YXTUs1oz0fphBVA {
    bottom: 0;
    color: var(--theme-text-color);
    left: 0;
    line-height: 1.2;
    overflow: hidden;
    position: absolute;
    right: 0;
    text-overflow: ellipsis;
    top: 0;
    word-break: keep-all
}

html[data-device=desktop] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw {
    margin-left: .14rem
}

html[data-device=desktop] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .v4LQgsaGCn3mMTQ4S4GC {
    font-size: .24rem;
    line-height: .29rem
}

html[data-device=desktop] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .EgqAwCEBwZ2ciGJxH_i1 {
    font-size: .16rem;
    height: .18rem;
    word-break: keep-all
}

html[data-device=desktop] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .EgqAwCEBwZ2ciGJxH_i1 .M_eD9YXTUs1oz0fphBVA {
    top: -.02rem
}

html[data-device=mobile] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw {
    margin-left: .1rem
}

html[data-device=mobile] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .v4LQgsaGCn3mMTQ4S4GC {
    font-size: .22rem;
    line-height: 1.1
}

html[data-device=mobile] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .EgqAwCEBwZ2ciGJxH_i1 {
    font-size: .16rem;
    height: .18rem
}

html[data-device=mobile] .DAEXSUBjuzUY1EoIAeEn.ElKyPNTBKztA5502vWkw .peeZHNmW5IsW4t8ZUpLw .EgqAwCEBwZ2ciGJxH_i1 .M_eD9YXTUs1oz0fphBVA {
    top: 0
}

.LB_OoEBDIiHR_orQWjZi {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: 100%;
    width: 100%
}

.LB_OoEBDIiHR_orQWjZi .v7Gu3VSoEuypS8B9EgaJ {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    height: 100%;
    width: 100%
}

.LB_OoEBDIiHR_orQWjZi .H7OjK05ObkC96xbKoAya,.LB_OoEBDIiHR_orQWjZi .kuDrCs5zwNC6jnKugppe {
    -ms-flex-positive: 1;
    flex-grow: 1;
    height: 100%
}

.aG5W8rsptmZ9RO66S3cI {
    width: 100%
}

.aG5W8rsptmZ9RO66S3cI,.aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: 100%
}

.aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v {
    -ms-flex-pack: center;
    cursor: pointer;
    justify-content: center
}

.aG5W8rsptmZ9RO66S3cI .MzKJCo_kccEXopglGrWF {
    background-color: var(--theme-table-bg-color);
    cursor: pointer
}

.aG5W8rsptmZ9RO66S3cI .MzKJCo_kccEXopglGrWF img {
    will-change: auto!important
}

.aG5W8rsptmZ9RO66S3cI .DvuNDsU5DEFvvcIAXWAe {
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-left: auto;
    margin-right: .2rem
}

.aG5W8rsptmZ9RO66S3cI .DvuNDsU5DEFvvcIAXWAe .ant-btn {
    border-radius: .1rem;
    font-family: MicrosoftYaHeiLobby;
    text-align: center
}

html[data-device=desktop] .aG5W8rsptmZ9RO66S3cI {
    gap: .2rem
}

html[data-device=desktop] .aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v {
    padding: 0 .2rem;
    position: relative
}

html[data-device=desktop] .aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v:after {
    border-right: .01rem solid var(--theme-color-line);
    content: "";
    height: .4rem;
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

html[data-device=desktop] .aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v svg {
    font-size: .16rem
}

html[data-device=desktop] .aG5W8rsptmZ9RO66S3cI .DvuNDsU5DEFvvcIAXWAe .ant-btn {
    font-size: .16rem;
    height: .4rem;
    padding: 0 .18rem
}

html[data-device=mobile] .aG5W8rsptmZ9RO66S3cI {
    gap: .2rem
}

html[data-device=mobile] .aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v {
    margin-right: -.2rem;
    padding: 0 .3rem
}

html[data-device=mobile] .aG5W8rsptmZ9RO66S3cI .pxU3k4yaAHPfT3wK4R8v svg {
    font-size: .2rem
}

html[data-device=mobile] .aG5W8rsptmZ9RO66S3cI .DvuNDsU5DEFvvcIAXWAe .ant-btn {
    font-size: .18rem;
    height: -webkit-fit-content;
    height: -moz-fit-content;
    height: fit-content;
    line-height: .2rem;
    max-width: 1.6rem;
    min-height: .45rem;
    min-width: 1.2rem;
    padding-left: .1rem;
    padding-right: .1rem;
    white-space: normal;
    word-break: break-all
}

@-webkit-keyframes Q9DbvCGytbQh8GrWhfvS {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes Q9DbvCGytbQh8GrWhfvS {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.UOaT0FQcVoTJmdoV6b7n {
    position: relative;
    z-index: 101
}

@-webkit-keyframes ZZb2NEYk70vAlooEGj02 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes ZZb2NEYk70vAlooEGj02 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.BZueILEUJ92PYiiHMh_e {
    bottom: 0;
    left: 0;
    margin: auto;
    max-width: 12rem;
    position: fixed;
    right: 0;
    width: 100%
}

html[data-device=desktop] .mfggbdrmjwcm2RZQzXZV {
    padding-top: .2rem
}

html[data-device=desktop][data-skin-layout="3"] .mfggbdrmjwcm2RZQzXZV {
    padding-top: .3rem
}

html[data-device=desktop][data-skin-layout="3"] .mfggbdrmjwcm2RZQzXZV.wwX_yLtm582gYvPXFl8E {
    padding-top: 0
}

html[data-device=desktop][data-skin-layout="6"] .mfggbdrmjwcm2RZQzXZV {
    background: #fff;
    margin-bottom: -1rem;
    padding-top: 0
}

html[data-device=desktop][data-skin-layout="5"] .mfggbdrmjwcm2RZQzXZV {
    padding-top: 0
}

html[data-device=mobile] .SV2T718iUhR9ZuXZqwjQ {
    padding: 0 .2rem
}

html[data-device=mobile] .mfggbdrmjwcm2RZQzXZV {
    overflow: hidden;
    padding: .2rem 0 0
}

html[data-device=mobile][data-skin-layout="3"] .mfggbdrmjwcm2RZQzXZV {
    padding: 0
}

html[data-skin-layout="20"][data-device=mobile] .mfggbdrmjwcm2RZQzXZV {
    background-color: #fff
}

@-webkit-keyframes gzNfqxohcPIUmQs26Vg4 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes gzNfqxohcPIUmQs26Vg4 {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.sft1FrYIdQNDkygBE2_w,.sft1FrYIdQNDkygBE2_w .ant-modal-wrap {
    z-index: 2000!important
}

.T4E7NfyEERjxZvEE9o3K,.T4E7NfyEERjxZvEE9o3K:active,.T4E7NfyEERjxZvEE9o3K:focus,.T4E7NfyEERjxZvEE9o3K:hover {
    background-color: transparent;
    border: .01rem solid var(--theme-primary-color);
    color: var(--theme-primary-color)
}

.T4E7NfyEERjxZvEE9o3K:lang(es_es)>span,.T4E7NfyEERjxZvEE9o3K:lang(fr_fr)>span,.T4E7NfyEERjxZvEE9o3K:lang(hi_in)>span,.T4E7NfyEERjxZvEE9o3K:lang(it_it)>span,.T4E7NfyEERjxZvEE9o3K:lang(km_kh)>span,.T4E7NfyEERjxZvEE9o3K:lang(ko_kr)>span,.T4E7NfyEERjxZvEE9o3K:lang(pt_pt)>span,.T4E7NfyEERjxZvEE9o3K:lang(tl_ph)>span {
    -webkit-transform: scale(.8)!important;
    transform: scale(.8)!important
}

.IaFuytXsVvfmDVlBM2bn {
    position: relative
}

.IaFuytXsVvfmDVlBM2bn .ant-form-item-children {
    padding-left: .15rem;
    position: relative
}

.IaFuytXsVvfmDVlBM2bn .ant-form-item-children:after {
    color: var(--theme-secondary-color-error);
    content: "*";
    display: block;
    left: 0;
    position: absolute;
    top: .18rem
}

.IaFuytXsVvfmDVlBM2bn .ant-form-item-label {
    margin-bottom: 0
}

.IaFuytXsVvfmDVlBM2bn .ant-form-item-label .l7fBL945zxYK6Avbm5hA span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: inline-block;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    width: 100%
}

html[data-device=mobile] .IaFuytXsVvfmDVlBM2bn {
    position: relative
}

html[data-device=mobile] .IaFuytXsVvfmDVlBM2bn .ant-form-item-children {
    padding-left: .2rem
}

html[data-device=mobile] .IaFuytXsVvfmDVlBM2bn .ant-form-item-children:after {
    top: .25rem
}

html[data-device=mobile] .sft1FrYIdQNDkygBE2_w .ant-modal-confirm-btns {
    margin-bottom: .24rem
}

html[data-device=mobile] .uwvenAZWGyD5RdZJBDtY {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

html[data-device=mobile] .uwvenAZWGyD5RdZJBDtY button+button {
    margin-left: .2rem
}

html[data-device=mobile] ._4Xj9NqT3RlWoXOF9sIB {
    height: 100vh;
    padding-bottom: 1.5rem
}

html[data-device=mobile] ._4Xj9NqT3RlWoXOF9sIB .K9PMAylPFhLQvjAV0rzb {
    background-color: var(--theme-bg-color)
}

html[data-device=mobile] .eCrtkyvaEw5EGq1iTkpb {
    padding: 0
}

html[data-device=mobile] .djvqyB9IgkiFd8Rww9SE {
    font-size: .22rem!important
}

html[data-device=mobile] .KijbywXOWoHmMhQVYbMW span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    width: 100%
}

html[data-device=mobile] .Qxd3eeaOHzsm5OavWHD6 {
    padding: 0
}

html[data-device=mobile] .Qxd3eeaOHzsm5OavWHD6 .OJKnuX1SdrakdqWua5Ix {
    font-size: .24rem;
    height: .7rem;
    line-height: .7rem;
    width: 7.1rem
}

html[data-device=mobile] .M2oU7LFq2dpRdr40epb_ .ant-form-item,html[data-device=mobile] .M2oU7LFq2dpRdr40epb_ .ant-form-item-label {
    padding: 0
}

html[data-device=mobile] .M2oU7LFq2dpRdr40epb_ .ant-form-item .ant-form-item-label label {
    height: auto
}

html[data-device=mobile] .M2oU7LFq2dpRdr40epb_ .G60ovJtuaDb1n92ATPH7 {
    font-size: .3rem;
    height: .7rem
}

html[data-device=mobile] .Jj0VvYFH3Jv3P4ZzMrq5 {
    font-size: .22rem;
    line-height: .3rem
}

html[data-device=mobile] .yWSWJBRH15O8UNBwi6Hc {
    padding: 0 .2rem
}

html[data-device=mobile] .yWSWJBRH15O8UNBwi6Hc.Ci5BesSJeFF7V3p9ekNM {
    padding-bottom: 0
}

html[data-device=mobile] .HuseTF0vc0ANwMtFI5Jt {
    padding-bottom: .1rem
}

html[data-device=mobile] .mRWJ3dwis6LX5Cigw20q {
    font-size: .24rem;
    line-height: .4rem
}

html[data-device=mobile] .f6Hdppzziw5Ocz_4b7HG {
    background-color: var(--theme-main-bg-color);
    bottom: 0;
    -webkit-box-shadow: 0 -.03rem .1rem 0 rgba(0,0,0,.1);
    box-shadow: 0 -.03rem .1rem 0 rgba(0,0,0,.1);
    left: 0;
    padding: .2rem;
    position: fixed;
    width: 100%;
    z-index: 9
}

html[data-device=mobile] .f6Hdppzziw5Ocz_4b7HG button {
    border-radius: .14rem;
    font-size: .24rem!important;
    height: .7rem;
    width: 100%
}

html[data-device=mobile] .f5Ruu8CIOUwvNyM9R1uY {
    border-radius: .14rem;
    margin: .2rem;
    padding: .3rem 0 .5rem
}

.F1i63mo89ZlxLi8VQ6nK span {
    color: var(--theme-primary-font-color)
}

._4Xj9NqT3RlWoXOF9sIB {
    min-height: 5rem
}

._4Xj9NqT3RlWoXOF9sIB .vue-portal-target .ant-btn {
    font-size: .16rem
}

._4Xj9NqT3RlWoXOF9sIB .nFkHDrNVzucOSjOVSkVo {
    -ms-flex-pack: justify;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-between
}

._4Xj9NqT3RlWoXOF9sIB .l33Ka3g_LqqyxWZG6i6s {
    -ms-flex-align: end;
    align-items: flex-end;
    display: -ms-flexbox;
    display: flex;
    font-size: .24rem;
    line-height: .3rem
}

._4Xj9NqT3RlWoXOF9sIB .l33Ka3g_LqqyxWZG6i6s .ant-btn {
    height: .3rem;
    line-height: .3rem
}

._4Xj9NqT3RlWoXOF9sIB .l33Ka3g_LqqyxWZG6i6s button {
    padding: 0 .2rem
}

._4Xj9NqT3RlWoXOF9sIB .HUnR0IXcIWtwiRH69sAz {
    color: var(--theme-text-color-lighten)
}

.eCrtkyvaEw5EGq1iTkpb {
    padding: 0
}

.AzSfD8qP8gM2kUfA2vid {
    padding: 0 .05rem!important;
    width: auto!important
}

.K9JimfJdAKv0fvE8GgcR {
    background-color: var(--theme-bg-color);
    border-radius: .1rem;
    overflow: hidden
}

.KijbywXOWoHmMhQVYbMW {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-items: center
}

.KijbywXOWoHmMhQVYbMW span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    width: 100%
}

.Qxd3eeaOHzsm5OavWHD6 {
    padding: .2rem 0;
    text-align: center
}

.Qxd3eeaOHzsm5OavWHD6 .OJKnuX1SdrakdqWua5Ix {
    font-size: .24rem;
    height: .6rem;
    min-width: 3rem
}

.Jj0VvYFH3Jv3P4ZzMrq5 {
    color: var(--theme-text-color-lighten);
    font-size: .24rem;
    line-height: .32rem;
    padding-bottom: .2rem
}

.Jj0VvYFH3Jv3P4ZzMrq5 span {
    margin-left: .01rem
}

.M2oU7LFq2dpRdr40epb_ .ant-modal-confirm-body .ant-modal-confirm-content {
    margin-bottom: 0
}

.M2oU7LFq2dpRdr40epb_ .ant-modal-body {
    padding-right: .1rem!important
}

.M2oU7LFq2dpRdr40epb_ .ant-form {
    padding-right: .2rem
}

.M2oU7LFq2dpRdr40epb_ .my-scrollbar-content {
    max-height: 6.8rem
}

.G60ovJtuaDb1n92ATPH7 {
    font-size: .2rem;
    height: .55rem;
    margin-top: .1rem;
    width: 100%
}

.yWSWJBRH15O8UNBwi6Hc {
    padding-bottom: .2rem
}

.mRWJ3dwis6LX5Cigw20q {
    background-color: var(--theme-main-bg-color);
    border-radius: .1rem;
    -webkit-box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.06);
    box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.06);
    color: var(--theme-text-color);
    font-size: .18rem;
    line-height: 1.56;
    padding: .2rem;
    white-space: pre-wrap;
    word-break: break-word
}

.mRWJ3dwis6LX5Cigw20q .r62waCRxFhGGf1TU0ZsQ {
    font-weight: 700
}

.lzu35D2wRxqVmh4J0_A3 {
    background-color: var(--theme-primary-color)!important;
    border: none;
    border-radius: .1rem;
    color: var(--theme-primary-font-color)!important;
    font-size: .16rem;
    height: .4rem;
    padding: 0;
    width: 1rem
}

.lzu35D2wRxqVmh4J0_A3 span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    width: 100%
}

.lzu35D2wRxqVmh4J0_A3.ant-btn-loading {
    padding: 0!important
}

.lzu35D2wRxqVmh4J0_A3.ant-btn-loading>.anticon-loading {
    display: none
}

.lzu35D2wRxqVmh4J0_A3.H5yJT30fplLKopY3Ucle {
    background-color: var(--theme-secondary-color-finance)!important
}

.lzu35D2wRxqVmh4J0_A3.d4npa_nICeiD5SVDAHEz {
    background-color: var(--theme-primary-color)!important
}

.lzu35D2wRxqVmh4J0_A3:disabled {
    background-color: var(--theme-disabled-bg-color)!important
}

.l7fBL945zxYK6Avbm5hA {
    padding-right: .12rem;
    position: relative
}

.f5Ruu8CIOUwvNyM9R1uY {
    background-color: var(--theme-main-bg-color);
    border-radius: .1rem;
    -webkit-box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.06);
    box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.06);
    margin-bottom: .2rem;
    padding: .3rem 0
}

.Db4cclGbAnf47LltiM4b .ant-modal-confirm-body>.anticon {
    display: none
}

.Db4cclGbAnf47LltiM4b .ant-modal-confirm-body>.anticon+.ant-modal-confirm-title+.ant-modal-confirm-content {
    margin-left: 0
}

@-webkit-keyframes AFE26BNLYIucdCYHCy7C {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes AFE26BNLYIucdCYHCy7C {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 {
    margin-top: .3rem
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .lGnlRSIZfUfxjiUc14_A {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .lGnlRSIZfUfxjiUc14_A img {
    width: 1.2rem
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .lGnlRSIZfUfxjiUc14_A .oNDGweuzetCS0hxIzOcw {
    color: var(--theme-text-color-darken);
    font-size: .3rem;
    margin-left: .2rem;
    text-align: left
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .e6hddgiwMrk5WpAEo3V2 {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1 0 45%;
    flex: 1 0 45%;
    height: .7rem
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .e6hddgiwMrk5WpAEo3V2.ant-btn-background-ghost {
    color: var(--theme-primary-color)
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .e6hddgiwMrk5WpAEo3V2 .jdy4sIbvZxrPWDxtE80t {
    font-size: .4rem
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .e6hddgiwMrk5WpAEo3V2 .TzdT_UTWDmd6r7V4BvUR {
    word-wrap: break-word;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    font-size: .22rem;
    margin-left: .08rem;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    white-space: pre-wrap
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .e6hddgiwMrk5WpAEo3V2:nth-of-type(2n) {
    margin-left: 5%
}

html[data-device=mobile] .oqfbbxoCre0pueYszQv1 .e6hddgiwMrk5WpAEo3V2:nth-of-type(n+3) {
    margin-top: .4rem
}

html[data-device=mobile] .czlmG4_Ngk6Wo2o3NKbe .ant-modal-body {
    padding: .2rem
}

html[data-device=mobile] .czlmG4_Ngk6Wo2o3NKbe .ant-modal-confirm-content {
    margin: 0
}

html[data-device=mobile] .czlmG4_Ngk6Wo2o3NKbe .ant-modal-mask,html[data-device=mobile] .czlmG4_Ngk6Wo2o3NKbe .ant-modal-wrap {
    z-index: 1001
}

.nduGDrfBPVKOsdv6ccSP {
    margin-top: .67rem
}

.GhOY_Cf255_fjkwHiKmt,.nduGDrfBPVKOsdv6ccSP {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap
}

.GhOY_Cf255_fjkwHiKmt {
    margin-top: .6rem
}

._eZlk5sU3Xnsxjh_NoZG {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    color: var(--theme-secondary-color-error);
    display: -ms-flexbox;
    display: flex;
    font-size: .3rem;
    justify-content: center
}

._eZlk5sU3Xnsxjh_NoZG .oNDGweuzetCS0hxIzOcw {
    margin-left: .1rem
}

.Tf7rAAAGD7CbH1LsSdvg {
    font-size: .22rem;
    line-height: .36rem;
    text-align: center
}

.Tf7rAAAGD7CbH1LsSdvg .xFclxQLTvl_biyfAOIQr {
    color: var(--theme-secondary-color-error)
}

.Tf7rAAAGD7CbH1LsSdvg .eBPDSb1cHtW_rAfoU5Vl {
    color: var(--theme-text-color-darken)
}

.Tf7rAAAGD7CbH1LsSdvg .eBPDSb1cHtW_rAfoU5Vl b {
    color: var(--theme-secondary-color-finance);
    font-weight: 400
}

@-webkit-keyframes Xg8KjC0JFrOZ4yvoxXtS {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes Xg8KjC0JFrOZ4yvoxXtS {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.asTDeZQVTSoaVdovWUTZ .jCZe88QvO8Hfp6KAOC42 {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.asTDeZQVTSoaVdovWUTZ .jCZe88QvO8Hfp6KAOC42 img {
    width: 1.2rem
}

.asTDeZQVTSoaVdovWUTZ .jCZe88QvO8Hfp6KAOC42 .ej6ngx9C8zSTfQTNRSUM {
    color: var(--theme-text-color-darken);
    font-size: .3rem;
    margin-left: .2rem;
    text-align: left
}

.asTDeZQVTSoaVdovWUTZ .tKLxwYQnT1zATd1dPesJ {
    height: .7rem;
    margin-top: .55rem;
    width: 100%
}

.asTDeZQVTSoaVdovWUTZ .tKLxwYQnT1zATd1dPesJ.ant-btn-background-ghost {
    color: var(--theme-primary-color)
}

.asTDeZQVTSoaVdovWUTZ .tKLxwYQnT1zATd1dPesJ .ptTu0COioDA4zYBffBln {
    font-size: .24rem
}

.asTDeZQVTSoaVdovWUTZ .tKLxwYQnT1zATd1dPesJ:nth-of-type(2n) {
    margin-left: 5%
}

.asTDeZQVTSoaVdovWUTZ .tKLxwYQnT1zATd1dPesJ:nth-of-type(n+3) {
    margin-top: .4rem
}

.kQ37Shg5LZ7YLzE7Hb6d .ant-modal-body {
    padding: .3rem .2rem .2rem
}

.kQ37Shg5LZ7YLzE7Hb6d .ant-modal-confirm-content {
    margin: 0!important
}

.kQ37Shg5LZ7YLzE7Hb6d .ant-modal-mask,.kQ37Shg5LZ7YLzE7Hb6d .ant-modal-wrap {
    z-index: 1001
}

.DZMZswKmQwQFdz9NOc0A {
    font-size: .28rem;
    line-height: .44rem
}

.DZMZswKmQwQFdz9NOc0A .Ztbrwm5CcLKslsR9QoDQ {
    color: var(--theme-secondary-color-error)
}

.DZMZswKmQwQFdz9NOc0A .Ojepl3dwjdpbA9z9pBs3 {
    color: var(--theme-text-color-darken)
}

.DZMZswKmQwQFdz9NOc0A .Ojepl3dwjdpbA9z9pBs3 b {
    color: var(--theme-secondary-color-finance);
    font-weight: 400
}

@-webkit-keyframes Gbp0eCLPVubG9jgCWvmY {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes Gbp0eCLPVubG9jgCWvmY {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.t1dO2In0hWSY_CXn1ztr {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    height: 100vh;
    justify-content: center;
    width: 100%
}

@-webkit-keyframes rXmDbySTHc21xSyZHmwa {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes rXmDbySTHc21xSyZHmwa {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.goDRiiBsuEuXD3W1NphN {
    opacity: 1
}

.goDRiiBsuEuXD3W1NphN[data-status=wait] {
    opacity: 0
}

.goDRiiBsuEuXD3W1NphN[data-blur="1"][data-status=loading],.goDRiiBsuEuXD3W1NphN[data-blur="1"][data-status=wait] {
    -webkit-filter: blur(.15rem);
    filter: blur(.15rem)
}

.goDRiiBsuEuXD3W1NphN[data-blur="1"][data-status=error],.goDRiiBsuEuXD3W1NphN[data-blur="1"][data-status=loaded],.goDRiiBsuEuXD3W1NphN[data-blur="1"][data-status=success] {
    -webkit-filter: blur(0);
    filter: blur(0);
    -webkit-transition: -webkit-filter .3s;
    transition: -webkit-filter .3s;
    transition: filter .3s;
    transition: filter .3s,-webkit-filter .3s
}

.goDRiiBsuEuXD3W1NphN.GgAalyCT_nMrUn3ge8Q8[data-status=loading] {
    -webkit-animation: rXmDbySTHc21xSyZHmwa .9s linear infinite;
    animation: rXmDbySTHc21xSyZHmwa .9s linear infinite;
    background-image: -webkit-gradient(linear,left top,right top,from(var(--theme-color-line)),color-stop(var(--theme-color-line)),color-stop(var(--theme-color-line)),color-stop(var(--theme-main-bg-color)),color-stop(var(--theme-color-line)),color-stop(var(--theme-color-line)),to(var(--theme-color-line)))!important;
    background-image: linear-gradient(90deg,var(--theme-color-line),var(--theme-color-line),var(--theme-color-line),var(--theme-main-bg-color),var(--theme-color-line),var(--theme-color-line),var(--theme-color-line))!important;
    background-size: 200% 100%!important;
    border-radius: .04rem;
    opacity: 1;
    opacity: .5
}

@-webkit-keyframes CFaIJMjFtG0KzPYvMy5t {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes CFaIJMjFtG0KzPYvMy5t {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.xa2jfZFLpcLwCZDzriVA {
    min-height: 5.52rem
}

.xa2jfZFLpcLwCZDzriVA .common-tab-item {
    font-size: .22rem;
    margin-right: .6rem!important
}

.xa2jfZFLpcLwCZDzriVA .globalLine {
    display: none
}

.xa2jfZFLpcLwCZDzriVA .globalTabsWrapper {
    border-bottom: thin solid var(--theme-color-line);
    margin: 0 .2rem
}

.xa2jfZFLpcLwCZDzriVA .JW9l1M000tsWqSNVub_5 {
    margin-left: .1rem
}

.oy_btoIN5U4mvU1zTNPZ {
    -ms-flex-align: center;
    align-items: center;
    color: var(--theme-text-color);
    display: -ms-flexbox;
    display: flex;
    font-size: .18rem
}

.oy_btoIN5U4mvU1zTNPZ .RzeNXAY_9PXmbEZna10z {
    margin-left: .15rem
}

.oy_btoIN5U4mvU1zTNPZ .LWG_uOeEk1tLI5OhDfNg {
    margin-left: .1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.oy_btoIN5U4mvU1zTNPZ i {
    line-height: .28rem
}

.sq0qjWno1fLg0tSVUxwn {
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    z-index: 999
}

.xneU_qxKf_AeBC1A5Esa .ant-modal-wrap {
    z-index: var(--z-paymodal)
}

.xneU_qxKf_AeBC1A5Esa .ant-modal-title {
    line-height: .4rem!important
}

.xneU_qxKf_AeBC1A5Esa .ant-modal-header {
    padding-bottom: 0!important
}

.xneU_qxKf_AeBC1A5Esa .ant-modal-body {
    padding: .2rem 0
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 {
    -ms-flex-pack: center;
    display: -ms-flexbox;
    display: flex;
    height: .4rem;
    justify-content: center;
    position: relative
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1;
    flex: 1;
    justify-content: center;
    left: 0;
    position: absolute;
    top: 0
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I {
    background-color: var(--theme-main-bg-color);
    color: var(--theme-text-color);
    font-size: .18rem;
    font-weight: 400;
    height: .4rem;
    width: 1.6rem
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I .ant-select-selection--single {
    border-radius: .2rem;
    height: 100%
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I .ant-select-selection--single .ant-select-selection__rendered {
    line-height: .4rem;
    margin-right: .3rem
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I .ant-select-selection--single .ant-select-arrow {
    margin-top: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I .ant-select-selection--single .ant-select-arrow .ant-select-arrow-icon {
    display: -ms-flexbox;
    display: flex
}

.xneU_qxKf_AeBC1A5Esa .uKyP975BYTzKm22WhHCb {
    color: var(--theme-primary-color);
    cursor: pointer;
    font-size: .2rem;
    position: absolute;
    right: .6rem;
    top: .15rem
}

.xneU_qxKf_AeBC1A5Esa .OD4Fl6Mnge8fqvNdex8I {
    color: red
}

.xneU_qxKf_AeBC1A5Esa .AtevBPCcmg72US_4Sp7T {
    padding-top: .2rem
}

.xneU_qxKf_AeBC1A5Esa .hEV4MX0LdHhpegWyYW83 {
    color: var(--theme-text-color-lighten);
    font-size: .18rem;
    height: .6rem;
    line-height: .6rem
}

.xneU_qxKf_AeBC1A5Esa .hEV4MX0LdHhpegWyYW83 .thepeAZkNol7RRsSYBrb {
    color: var(--theme-secondary-color-finance);
    margin-left: .05rem
}

.xneU_qxKf_AeBC1A5Esa .eXOyUJ1Q7qC8CgKXC53w {
    max-width: 100%
}

.xneU_qxKf_AeBC1A5Esa .eXOyUJ1Q7qC8CgKXC53w p:first-child {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .TAjt83FN3slG7AreW8gm {
    border-radius: 0 0 .1rem .1rem;
    height: 6.8rem
}

.xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .TAjt83FN3slG7AreW8gm .my-scrollbar {
    overflow: visible
}

.xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .TAjt83FN3slG7AreW8gm[no-bg=true] {
    background-color: transparent
}

.xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .pgyoV_NNm7L6S53HAOWC {
    -ms-flex-align: end;
    -ms-flex-pack: center;
    align-items: flex-end;
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

.xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .pgyoV_NNm7L6S53HAOWC .Z3d3LY3KzBEXr0Mc4b_l {
    background-color: var(--theme-primary-color);
    border-radius: .1rem;
    color: var(--theme-primary-font-color);
    font-size: .2rem;
    height: .55rem;
    text-align: center;
    width: 4.4rem
}

.IQ3yZnZLFwUjWFCq_4XA {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100%;
    justify-content: center;
    min-height: 6rem
}

.IQ3yZnZLFwUjWFCq_4XA>div {
    height: auto;
    margin-bottom: .2rem
}

.IQ3yZnZLFwUjWFCq_4XA>div img {
    height: 2.1rem!important;
    width: 2.1rem!important
}

.IQ3yZnZLFwUjWFCq_4XA p {
    color: var(--theme-text-color-lighten);
    font-size: .22rem
}

.IQ3yZnZLFwUjWFCq_4XA p span {
    color: var(--theme-primary-color);
    cursor: pointer
}

._9tRruciAyiaCj_pB_HRY {
    max-width: 3.15rem;
    min-width: 1.6rem;
    width: auto!important
}

.zMwg83XrMGKz_Qp14Bam {
    -webkit-transform: rotate(135deg) translateY(.05rem);
    transform: rotate(135deg) translateY(.05rem)
}

.xlXv6_q9c457vZYtrnYW,.zMwg83XrMGKz_Qp14Bam {
    border-right: .02rem solid var(--theme-primary-color);
    border-top: .02rem solid var(--theme-primary-color);
    height: .1rem;
    width: .1rem
}

.xlXv6_q9c457vZYtrnYW {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.Uwuo8Gd2Kh6e_ErouKDf {
    position: relative;
    z-index: 100000
}

.Md8bkOCaRhDr6FF_q5Qg:lang(en_us) {
    font-size: .2rem
}

.Uvgss7YU14EY4fSTdCsp {
    background-color: var(--theme-secondary-color-finance);
    color: var(--theme-secondary-color-finance)
}

.Pw31MAXtYic1LQc2ZrD8 {
    background-color: var(--theme-secondary-color-success);
    color: var(--theme-secondary-color-success)
}

.iSeiyC3bqps4IGGTXatc {
    background-color: var(--theme-secondary-color-error);
    color: var(--theme-secondary-color-error)
}

.TmTlgIIHiQR86nkpcl8M {
    color: var(--theme-secondary-color-finance)
}

.MCxdxRhxa0voqrCEptAa {
    color: var(--theme-secondary-color-success)
}

.Q6NkNwL6l8CDPSdtrO2P {
    color: var(--theme-secondary-color-error)
}

/*html ::-webkit-scrollbar-thumb {*/
/*    background: 0 0*/
/*}*/

html[data-device=mobile] .Md8bkOCaRhDr6FF_q5Qg {
    font-size: .24rem
}

html[data-device=mobile] .Md8bkOCaRhDr6FF_q5Qg>i {
    font-size: .38rem
}

html[data-device=mobile] ._9tRruciAyiaCj_pB_HRY {
    max-width: 3.15rem;
    min-width: 1.7rem!important;
    min-width: 1.6rem;
    width: auto!important
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .ant-modal-title {
    line-height: .5rem!important
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .my-scrollbar {
    padding: 0!important
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .ant-modal {
    margin: auto!important;
    right: 0
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .ant-modal-content {
    border-radius: .2rem .2rem 0 0!important
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .globalTabsWrapper {
    margin: 0 .3rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .AtevBPCcmg72US_4Sp7T {
    padding-top: .2rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 {
    height: .5rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I {
    height: .5rem;
    width: 1.7rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I .ant-select-selection--single {
    border-radius: .25rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .RrZqs7JzsZkdc0Zi38V8 .wh_kHAHhXG8fBlrtlZZK .wAmlyLFHwoRVUjxPh69I .ant-select-selection--single .ant-select-selection__rendered {
    line-height: .5rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .TAjt83FN3slG7AreW8gm {
    height: calc(100vh - 1.92rem);
    height: calc(var(--vh,1vh)*100 - 1.92rem);
    padding-bottom: .8rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .pgyoV_NNm7L6S53HAOWC {
    padding: 0 .3rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .hi_acJpRSbS0W0xdzoS0 .pgyoV_NNm7L6S53HAOWC .Z3d3LY3KzBEXr0Mc4b_l {
    border-radius: .14rem;
    font-size: .24rem;
    height: .7rem;
    width: 100%
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .uKyP975BYTzKm22WhHCb {
    font-size: .24rem;
    right: .3rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .uKyP975BYTzKm22WhHCb:lang(my_mm) {
    font-size: .2rem;
    top: .31rem
}

html[data-device=mobile] .xneU_qxKf_AeBC1A5Esa .jfx5RvtUc1fSLoLZt8JG {
    color: var(--theme-text-color);
    font-size: .24rem;
    left: .24rem;
    position: absolute;
    top: .24rem
}

.gJDpQntk7W2O2Z9dsCv5 {
    -ms-flex-pack: center;
    -ms-flex-direction: column;
    flex-direction: column;
    font-size: .26rem;
    height: calc(100% - .7rem);
    justify-content: center;
    position: absolute;
    width: 100%
}

.gJDpQntk7W2O2Z9dsCv5,.gJDpQntk7W2O2Z9dsCv5 p {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex
}

.gJDpQntk7W2O2Z9dsCv5 p {
    padding-top: .26rem
}

.gJDpQntk7W2O2Z9dsCv5 .kjTUiWJPA0ISLARo7gQR {
    -ms-flex-align: center;
    align-items: center;
    color: var(--theme-primary-color);
    display: -ms-flexbox;
    display: flex
}

.gJDpQntk7W2O2Z9dsCv5 .kjTUiWJPA0ISLARo7gQR .t8fPsIVYFs59F41yS9S1 {
    margin-left: .15rem
}

@-webkit-keyframes E25MY3z53Jkvp8hq3lQi {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes E25MY3z53Jkvp8hq3lQi {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.M1hv08Saat6oRJv0Gy45[data-blur="1"][data-show="0"] {
    -webkit-filter: blur(.15rem);
    filter: blur(.15rem);
    opacity: 0
}

.M1hv08Saat6oRJv0Gy45[data-blur="1"][data-show="1"] {
    -webkit-filter: blur(0);
    filter: blur(0);
    opacity: 1;
    -webkit-transition: opacity .3s,-webkit-filter;
    transition: opacity .3s,-webkit-filter;
    transition: filter,opacity .3s;
    transition: filter,opacity .3s,-webkit-filter
}

@-webkit-keyframes elpc1Ony1uLFPqClndZK {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes elpc1Ony1uLFPqClndZK {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.yj5rHjAv0U7RlHMzWr7F.xvimzcMdMIpAXeYEqzt3 {
    -webkit-animation: elpc1Ony1uLFPqClndZK .9s linear infinite;
    animation: elpc1Ony1uLFPqClndZK .9s linear infinite;
    border-radius: .04rem;
    opacity: .5
}

.yj5rHjAv0U7RlHMzWr7F.qmK8iTLIKtJRQ4pYEXvM,.yj5rHjAv0U7RlHMzWr7F.xvimzcMdMIpAXeYEqzt3 {
    background-image: -webkit-gradient(linear,left top,right top,from(var(--theme-color-line)),color-stop(var(--theme-color-line)),color-stop(var(--theme-color-line)),color-stop(var(--theme-main-bg-color)),color-stop(var(--theme-color-line)),color-stop(var(--theme-color-line)),to(var(--theme-color-line)))!important;
    background-image: linear-gradient(90deg,var(--theme-color-line),var(--theme-color-line),var(--theme-color-line),var(--theme-main-bg-color),var(--theme-color-line),var(--theme-color-line),var(--theme-color-line))!important;
    background-size: 200% 100%!important
}

.yj5rHjAv0U7RlHMzWr7F.qmK8iTLIKtJRQ4pYEXvM {
    -webkit-animation: elpc1Ony1uLFPqClndZK .9s linear infinite;
    animation: elpc1Ony1uLFPqClndZK .9s linear infinite;
    border-radius: .04rem;
    opacity: .5
}

@-webkit-keyframes klu4BWfl4jCiF2Z2qonu {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes klu4BWfl4jCiF2Z2qonu {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.DdGVWTxycItMEAYiShYm {
    overflow: hidden
}

.DdGVWTxycItMEAYiShYm .s6ld3uKmPBmFVTaNrGho {
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0
}

.DdGVWTxycItMEAYiShYm .Gyp2tu7KBps7OIiUzN0B {
    text-align: left
}

.my-scrollbar {
    background: 0 0;
    height: 100%;
    overflow: hidden;
    position: relative;
    width: 100%
}

.my-scrollbar>.my-scrollbar-bar {
    cursor: pointer
}

.my-scrollbar.my-scrollbar-always>.my-scrollbar-bar,.my-scrollbar.my-scrollbar-hover:active>.my-scrollbar-bar,.my-scrollbar.my-scrollbar-hover:focus>.my-scrollbar-bar,.my-scrollbar.my-scrollbar-hover:hover>.my-scrollbar-bar {
    opacity: 1
}

.my-scrollbar.my-scrollbar-none>.my-scrollbar-bar {
    opacity: 0;
    pointer-events: none
}

.my-scrollbar-wrap-all {
    overflow: scroll
}

.my-scrollbar-wrap-lock {
    overflow: hidden!important
}

.my-scrollbar-wrap-x {
    overflow-x: scroll;
    overflow-y: hidden
}

.my-scrollbar-wrap-y {
    overflow-x: hidden;
    overflow-y: scroll
}

.my-scrollbar-wrap {
    height: 100%
}

.my-scrollbar-wrap:not(.my-scrollbar-wrap-use-systembar) {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none;
    scrollbar-width: none
}

.my-scrollbar-wrap:not(.my-scrollbar-wrap-use-systembar)::-webkit-scrollbar {
    display: none;
    height: 0;
    width: 0
}

.masklayer {
    background-color: transparent;
    display: none;
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    z-index: -9999
}

.masklayer.show-mask {
    display: block;
    z-index: 9999
}

@-webkit-keyframes common_bganimation {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes common_bganimation {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

.my-scrollbar-bar {
    border-radius: .03rem;
    position: absolute;
    -webkit-transition: opacity .3s ease-out;
    transition: opacity .3s ease-out;
    z-index: 1
}

.my-scrollbar-bar .my-scrollbar-thumb {
    background-color: var(--theme-color-line)!important;
    border-radius: inherit;
    cursor: pointer;
    height: 0;
    -webkit-transition: background-color .3s;
    transition: background-color .3s;
    width: 0
}

.my-scrollbar-bar.my-scrollbar-vertical {
    bottom: 0;
    right: .06rem;
    top: 0;
    width: .06rem
}

.my-scrollbar-bar.my-scrollbar-vertical .my-scrollbar-thumb {
    background-color: var(--theme-color-line)!important;
    width: 100%
}

.my-scrollbar-bar.my-scrollbar-horizontal {
    bottom: .1rem;
    height: .06rem;
    left: 0;
    right: 0
}

.my-scrollbar-bar.my-scrollbar-horizontal .my-scrollbar-thumb {
    background-color: var(--theme-color-line)!important;
    height: 100%
}

@-webkit-keyframes TV8h5id8AKlfcNjrSVfi {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

@keyframes TV8h5id8AKlfcNjrSVfi {
    0% {
        background-position-x: 125%
    }

    to {
        background-position-x: -32%
    }
}

html[data-device=mobile] .W2tjkzVcGnfs8Q_AXxJn {
    height: .9rem
}

html[data-device=mobile] .W2tjkzVcGnfs8Q_AXxJn.vQXZocCgtfHlvv3Ol5Ug {
    height: 0
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf {
    background: var(--theme-main-bg-color);
    border-bottom: .01rem solid var(--theme-color-line);
    border-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    height: .9rem;
    left: 0;
    position: fixed;
    top: 0;
    z-index: 200
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH {
    padding: 0 .34rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span {
    font-size: .3rem;
    margin-top: -.014rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span:lang(tl_ph) {
    padding-bottom: .05rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .KpLSeBzUw4iMz4wsDtVg {
    font-size: .2rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .JJvY17nDVfe7Tlj0VSrP {
    width: .8rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .JJvY17nDVfe7Tlj0VSrP div {
    font-size: .24rem;
    height: .8rem;
    width: .8rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW {
    width: .9rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .k2R_fEA6y3OqsKE4fL5P button {
    -ms-flex-pack: end;
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    border-radius: .1rem;
    display: -ms-flexbox;
    display: flex;
    font-size: .16rem;
    font-size: .24rem;
    height: .6rem;
    justify-content: flex-end;
    justify-content: center;
    width: 1.2rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .k2R_fEA6y3OqsKE4fL5P button:not(:lang(zh_cn)) {
    padding: 0 .05rem
}

html[data-device=mobile] .mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .k2R_fEA6y3OqsKE4fL5P button:not(:lang(zh_cn)) span {
    word-wrap: break-word;
    line-height: 1.1;
    text-align: center;
    white-space: pre-line
}

html[data-device=mobile][data-skin-layout="3"] .W2tjkzVcGnfs8Q_AXxJn,html[data-device=mobile][data-skin-layout="3"] .mDbpiqAAXCKlSHJtOpBf {
    height: .95rem
}

.W2tjkzVcGnfs8Q_AXxJn {
    width: 100%
}

.mDbpiqAAXCKlSHJtOpBf {
    -ms-flex-align: center;
    align-items: center;
    background-color: var(--theme-main-bg-color);
    border-radius: .1rem;
    -webkit-box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.06);
    box-shadow: 0 .03rem .09rem 0 rgba(0,0,0,.06);
    display: -ms-flexbox;
    display: flex;
    height: .6rem;
    padding: 0 .1rem 0 .2rem;
    width: 100%
}

.mDbpiqAAXCKlSHJtOpBf .JJvY17nDVfe7Tlj0VSrP {
    width: 2.3rem
}

.mDbpiqAAXCKlSHJtOpBf .JJvY17nDVfe7Tlj0VSrP div {
    -ms-flex-align: center;
    align-items: center;
    color: var(--theme-text-color);
    cursor: pointer;
    display: -ms-flexbox;
    display: flex;
    font-size: .2rem
}

.mDbpiqAAXCKlSHJtOpBf .JJvY17nDVfe7Tlj0VSrP div span {
    margin-left: .08rem
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN {
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 2;
    flex: 2;
    -ms-flex-direction: column;
    flex-direction: column
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    justify-content: center
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span {
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    color: var(--theme-text-color-darken);
    display: -webkit-box;
    margin: 0 .12rem;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    vertical-align: middle
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span:not(:lang(zh_cn)) {
    margin-top: -.025rem
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span .KpLSeBzUw4iMz4wsDtVg {
    font-weight: 100
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span:lang(tl_ph) {
    line-height: 1;
    margin-top: 0;
    padding-bottom: .01rem
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .FzcU4idIq33_dAa_YjKH>span:lang(zh_cn) {
    word-break: break-all
}

.mDbpiqAAXCKlSHJtOpBf .vfM4gIQK4xFYOroxyXEN .KpLSeBzUw4iMz4wsDtVg {
    color: var(--theme-text-color-lighten);
    font-size: .16rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW {
    -ms-flex-pack: end;
    display: -ms-flexbox;
    display: flex;
    justify-content: flex-end;
    width: 2.4rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .qPlufRsjcRtuQ5A9Q3xL button {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    border-radius: .1rem;
    display: -ms-flexbox;
    display: flex;
    font-size: .16rem;
    height: .4rem;
    justify-content: center;
    width: 1rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .qPlufRsjcRtuQ5A9Q3xL button:not(:lang(zh_cn)) {
    padding: 0 .05rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .qPlufRsjcRtuQ5A9Q3xL button:not(:lang(zh_cn)) span {
    word-wrap: break-word;
    line-height: 1.1;
    text-align: center;
    white-space: pre-line
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .qPlufRsjcRtuQ5A9Q3xL button:not(:lang(zh_cn)) {
    padding: 0
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .qPlufRsjcRtuQ5A9Q3xL button:lang(ko_kr) {
    font-size: .14rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .k2R_fEA6y3OqsKE4fL5P button {
    -ms-flex-align: center;
    -ms-flex-pack: center;
    align-items: center;
    border-radius: .1rem;
    display: -ms-flexbox;
    display: flex;
    font-size: .16rem;
    font-size: .2rem;
    height: .4rem;
    justify-content: center;
    margin-left: .2rem;
    width: 1.2rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .k2R_fEA6y3OqsKE4fL5P button:not(:lang(zh_cn)) {
    padding: 0 .05rem
}

.mDbpiqAAXCKlSHJtOpBf .zYgxcC7AP2reICGlb3CW .k2R_fEA6y3OqsKE4fL5P button:not(:lang(zh_cn)) span {
    word-wrap: break-word;
    line-height: 1.1;
    text-align: center;
    white-space: pre-line
}
