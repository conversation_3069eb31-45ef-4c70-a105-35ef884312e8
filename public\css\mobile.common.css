
html {
    position: relative;
    height: 100%;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}
body {
    position: absolute;
    top: 0;
    left: 0;
}
.form-control:focus {
    box-shadow:none !important;
}
.van-toast {
    min-width: 80px !important;
    width: unset !important;
    word-break: break-word !important;
}
/*.home-header, .header-content {
    height: calc(1.39rem + (var(--safe-area-inset-bottom))) !important;
    padding-top: var(--safe-area-inset-bottom) !important;
}
.mc-header-wrap, .prev-route-content, .mc-header-wrap.mc-navbar-blue, #root .mc-navbar-blue, .mc-whitespace, #mc-header {
    height: calc(1rem + (var(--safe-area-inset-bottom))) !important;
    padding-top: var(--safe-area-inset-bottom) !important;
}*/
.remove-font-size #root .hide-btn {
    top: calc(.001rem + var(--safe-area-inset-bottom)) !important;
}


:root {
    --animate-duration: 1s;
    --animate-delay: 1s;
    --animate-repeat: 1;

    --login_pg_background_color: #2e2922;
    --theme_gradient_color_2: #8d5419;
    --theme_gradient_color_1: #e0a24c;
    --theme_font_color: #fff;
    --sub_background_color: #131313;
    --main_background_color: #000;
}
@keyframes heartBeat {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    14% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }

    28% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }

    42% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }

    70% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.animate__heartBeat {
    -webkit-animation-name: heartBeat;
    animation-name: heartBeat;
    -webkit-animation-duration: 1.3s;
    animation-duration: 1.3s;
    -webkit-animation-duration: calc(var(--animate-duration) * 1.3);
    animation-duration: calc(var(--animate-duration) * 1.3);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out
}

.common_animate__heartBeat {
    animation-name: heartBeat;
    animation-duration: 1.8s;
    animation-duration: calc(var(--animate-duration) * 1.8);
    animation-timing-function: ease-in-out;
    animation-iteration-count: infinite
}

::-webkit-scrollbar {
    display: none
}

/*.van-dialog__message {*/
/*    user-select: text;*/
/*}*/