/*! normalize.css v7.0.0 | MIT License | github.com/necolas/normalize.css */

html {
    line-height: 1.15;
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%
}

body {
    margin: 0
}

article,
aside,
footer,
header,
nav,
section {
    display: block
}

h1 {
    font-size: 2em;
    margin: .67em 0
}

figcaption,
figure,
main {
    display: block
}

figure {
    margin: 1em .4rem
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

pre {
    font-family: monospace, monospace;
    font-size: 1em
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    text-decoration: underline dotted
}

b,
strong {
    font-weight: inherit;
    font-weight: bolder
}

code,
kbd,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

dfn {
    font-style: italic
}

mark {
    background-color: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -.25em
}

sup {
    top: -.5em
}

audio,
video {
    display: inline-block
}

audio:not([controls]) {
    display: none;
    height: 0
}

img {
    border-style: none
}

svg:not(:root) {
    overflow: hidden
}

button,
input,
optgroup,
select,
textarea {
    font-family: sans-serif;
    font-size: 100%;
    line-height: 1.15;
    margin: 0
}

button,
input {
    overflow: visible
}

button,
select {
    text-transform: none
}

[type=reset],
[type=submit],
button,
html [type=button] {
    -webkit-appearance: button
}

[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner,
button::-moz-focus-inner {
    border-style: none;
    padding: 0
}

[type=button]:-moz-focusring,
[type=reset]:-moz-focusring,
[type=submit]:-moz-focusring,
button:-moz-focusring {
    outline: 1px dotted ButtonText
}

fieldset {
    padding: .35em .75em .625em
}

legend {
    box-sizing: border-box;
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal
}

progress {
    display: inline-block;
    vertical-align: baseline
}

textarea {
    overflow: auto
}

[type=checkbox],
[type=radio] {
    box-sizing: border-box;
    padding: 0
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    -webkit-appearance: textfield;
    outline-offset: -.02rem
}

[type=search]::-webkit-search-cancel-button,
[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

details,
menu {
    display: block
}

summary {
    display: list-item
}

canvas {
    display: inline-block
}

template {
    display: none
}

[hidden] {
    display: none
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-fade-appear,
.am-fade-enter {
    opacity: 0
}

.am-fade-appear,
.am-fade-enter,
.am-fade-leave {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.am-fade-appear.am-fade-appear-active,
.am-fade-enter.am-fade-enter-active {
    -webkit-animation-name: amFadeIn;
    animation-name: amFadeIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.am-fade-leave.am-fade-leave-active {
    -webkit-animation-name: amFadeOut;
    animation-name: amFadeOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

@-webkit-keyframes amFadeIn {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes amFadeIn {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@-webkit-keyframes amFadeOut {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

@keyframes amFadeOut {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

.am-slide-up-appear,
.am-slide-up-enter {
    -webkit-transform: translateY(100%);
    -ms-transform: translateY(100%);
    transform: translateY(100%)
}

.am-slide-up-appear,
.am-slide-up-enter,
.am-slide-up-leave {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.am-slide-up-appear.am-slide-up-appear-active,
.am-slide-up-enter.am-slide-up-enter-active {
    -webkit-animation-name: amSlideUpIn;
    animation-name: amSlideUpIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.am-slide-up-leave.am-slide-up-leave-active {
    -webkit-animation-name: amSlideUpOut;
    animation-name: amSlideUpOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

@-webkit-keyframes amSlideUpIn {
    0% {
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }
    to {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@keyframes amSlideUpIn {
    0% {
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }
    to {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@-webkit-keyframes amSlideUpOut {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    to {
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }
}

@keyframes amSlideUpOut {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    to {
        -webkit-transform: translateY(100%);
        transform: translateY(100%)
    }
}

.am.am-zoom-enter,
.am.am-zoom-leave {
    display: block
}

.am-zoom-appear,
.am-zoom-enter {
    opacity: 0;
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    -webkit-animation-timing-function: cubic-bezier(.18, .89, .32, 1.28);
    animation-timing-function: cubic-bezier(.18, .89, .32, 1.28);
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.am-zoom-leave {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    -webkit-animation-timing-function: cubic-bezier(.6, -.3, .74, .05);
    animation-timing-function: cubic-bezier(.6, -.3, .74, .05);
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.am-zoom-appear.am-zoom-appear-active,
.am-zoom-enter.am-zoom-enter-active {
    -webkit-animation-name: amZoomIn;
    animation-name: amZoomIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.am-zoom-leave.am-zoom-leave-active {
    -webkit-animation-name: amZoomOut;
    animation-name: amZoomOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

@-webkit-keyframes amZoomIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    to {
        opacity: 1;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes amZoomIn {
    0% {
        opacity: 0;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    to {
        opacity: 1;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes amZoomOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    to {
        opacity: 0;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(0);
        transform: scale(0)
    }
}

@keyframes amZoomOut {
    0% {
        opacity: 1;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    to {
        opacity: 0;
        -webkit-transform-origin: 50% 50%;
        transform-origin: 50% 50%;
        -webkit-transform: scale(0);
        transform: scale(0)
    }
}

.am-slide-down-appear,
.am-slide-down-enter {
    -webkit-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    transform: translateY(-100%)
}

.am-slide-down-appear,
.am-slide-down-enter,
.am-slide-down-leave {
    -webkit-animation-duration: .2s;
    animation-duration: .2s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    animation-timing-function: cubic-bezier(.55, 0, .55, .2);
    -webkit-animation-play-state: paused;
    animation-play-state: paused
}

.am-slide-down-appear.am-slide-down-appear-active,
.am-slide-down-enter.am-slide-down-enter-active {
    -webkit-animation-name: amSlideDownIn;
    animation-name: amSlideDownIn;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

.am-slide-down-leave.am-slide-down-leave-active {
    -webkit-animation-name: amSlideDownOut;
    animation-name: amSlideDownOut;
    -webkit-animation-play-state: running;
    animation-play-state: running
}

@-webkit-keyframes amSlideDownIn {
    0% {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
    to {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@keyframes amSlideDownIn {
    0% {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
    to {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
}

@-webkit-keyframes amSlideDownOut {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    to {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
}

@keyframes amSlideDownOut {
    0% {
        -webkit-transform: translate(0);
        transform: translate(0)
    }
    to {
        -webkit-transform: translateY(-100%);
        transform: translateY(-100%)
    }
}

*,
:after,
:before {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

html {
    font-size: 50px
}

body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-size: .32rem;
}

[contenteditable] {
    -webkit-user-select: auto !important
}

:focus {
    outline: none
}

a {
    background: transparent;
    text-decoration: none;
    outline: none
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-modal {
    position: relative
}

.am-modal-mask {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    height: 100%;
    z-index: 999;
    background-color: rgba(0, 0, 0, .4)
}

.am-modal-mask-hidden {
    display: none
}

.am-modal-wrap {
    position: fixed;
    overflow: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    height: 100%;
    z-index: 999;
    -webkit-overflow-scrolling: touch;
    outline: 0;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.am-modal.am-modal-transparent .am-modal-content {
    border-radius: .14rem;
    height: auto;
    padding-top: .3rem
}

.am-modal.am-modal-transparent .am-modal-content .am-modal-body {
    padding: 0 .3rem .3rem
}

.am-modal-title {
    margin: 0;
    font-size: .36rem;
    line-height: 1;
    color: #000;
    text-align: center
}

.am-modal-header {
    padding: .12rem .3rem .3rem
}

.am-modal-content {
    position: relative;
    background-color: #fff;
    border: 0;
    background-clip: padding-box;
    text-align: center;
    height: 100%;
    overflow: hidden
}

.am-modal-close {
    border: 0;
    padding: 0;
    background-color: transparent;
    outline: none;
    position: absolute;
    right: .3rem;
    z-index: 999;
    height: .42rem;
    width: .42rem
}

.am-modal-close-x {
    display: inline-block;
    width: .3rem;
    height: .3rem;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23888' fill-rule='evenodd'%3E%3Cpath d='M1.414 0l28.284 28.284-1.414 1.414L0 1.414z'/%3E%3Cpath d='M28.284 0L0 28.284l1.414 1.414L29.698 1.414z'/%3E%3C/g%3E%3C/svg%3E")
}

.am-modal-body {
    font-size: .3rem;
    color: #888;
    height: 100%;
    line-height: 1.5;
    overflow: auto
}

.am-modal-button-group-h {
    border-top: 1px solid #ddd;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex
}

.am-modal-button-group-h .am-modal-button {
    -webkit-touch-callout: none;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%;
    box-sizing: border-box;
    text-align: center;
    text-decoration: none;
    outline: none;
    color: #108ee9;
    font-size: .36rem;
    height: 1rem;
    line-height: 1rem;
    display: block;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.am-modal-button-group-h .am-modal-button:first-child {
    color: #000;
    border-right: 1px solid #ddd
}

.am-modal-button-group-v .am-modal-button {
    -webkit-touch-callout: none;
    border-top: 1px solid #ddd;
    box-sizing: border-box;
    text-align: center;
    text-decoration: none;
    outline: none;
    color: #108ee9;
    font-size: .36rem;
    height: 1rem;
    line-height: 1rem;
    display: block;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.am-modal-button-active {
    background-color: #ddd
}

.am-modal-input {
    height: .72rem;
    line-height: 1;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd
}

.am-modal-input:first-child {
    margin-top: .18rem;
    border-top: 1px solid #ddd;
    border-top-left-radius: .06rem;
    border-top-right-radius: .06rem
}

.am-modal-input:last-child {
    border-bottom-left-radius: .06rem;
    border-bottom-right-radius: .06rem
}

.am-modal-input input {
    border: 0;
    width: 98%;
    height: 100%;
    box-sizing: border-box;
    margin: 0;
    padding: .06rem 0
}

.am-modal-input input::-webkit-input-placeholder {
    font-size: .28rem;
    color: #ccc;
    padding-left: .16rem
}

.am-modal-input input:-ms-input-placeholder {
    font-size: .28rem;
    color: #ccc;
    padding-left: .16rem
}

.am-modal-input input::placeholder {
    font-size: .28rem;
    color: #ccc;
    padding-left: .16rem
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content {
    border-radius: 0
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-header {
    padding: .18rem .48rem .24rem
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-header .am-modal-title {
    text-align: left;
    font-size: .42rem;
    color: #000
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-body {
    color: #000;
    text-align: left;
    padding: 0 .48rem .3rem
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-body .am-modal-input {
    border-left: 0;
    border-right: 0;
    border-radius: 0
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-body .am-modal-input:first-child {
    border-top: 0
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer {
    padding-bottom: .24rem
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer .am-modal-button-group-h {
    overflow: hidden;
    border-top: 0;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 0 .24rem
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer .am-modal-button-group-h .am-modal-button {
    -webkit-box-flex: initial;
    -ms-flex: initial;
    flex: initial;
    margin-left: .06rem;
    padding: 0 .3rem;
    height: .96rem;
    box-sizing: border-box
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer .am-modal-button-group-h .am-modal-button:first-child {
    border-right: 0;
    color: #777
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer .am-modal-button-group-v.am-modal-button-group-normal {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    overflow: hidden;
    padding: 0 .24rem
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer .am-modal-button-group-v.am-modal-button-group-normal .am-modal-button {
    border-top: 0;
    padding: 0 .3rem;
    margin-left: .06rem;
    height: .96rem;
    box-sizing: border-box
}

.am-modal.am-modal-transparent.am-modal-android .am-modal-content .am-modal-footer .am-modal-button-group-operation .am-modal-button {
    text-align: start;
    padding-left: .3rem
}

.am-modal.am-modal-operation .am-modal-content {
    border-radius: .14rem;
    height: auto;
    padding-top: 0
}

.am-modal.am-modal-operation .am-modal-content .am-modal-body {
    padding: 0 !important
}

.am-modal.am-modal-operation .am-modal-content .am-modal-button {
    color: #000;
    text-align: center
}

.am-icon {
    fill: currentColor;
    background-size: cover;
    width: .44rem;
    height: .44rem
}

.am-icon-xxs {
    width: .3rem;
    height: .3rem
}

.am-icon-xs {
    width: .36rem;
    height: .36rem
}

.am-icon-sm {
    width: .42rem;
    height: .42rem
}

.am-icon-md {
    width: .44rem;
    height: .44rem
}

.am-icon-lg {
    width: .72rem;
    height: .72rem
}

.am-icon-loading {
    -webkit-animation: cirle-anim 1s linear infinite;
    animation: cirle-anim 1s linear infinite
}

@-webkit-keyframes cirle-anim {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes cirle-anim {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-toast {
    position: fixed;
    width: 100%;
    z-index: 1999;
    font-size: .28rem;
    text-align: center
}

.am-toast>span {
    max-width: 50%
}

.am-toast.am-toast-mask {
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    left: 0;
    top: 0
}

.am-toast.am-toast-nomask {
    position: fixed;
    max-width: 50%;
    width: auto;
    left: 50%;
    top: 50%
}

.am-toast.am-toast-nomask .am-toast-notice {
    -webkit-transform: translateX(-50%) translateY(-50%);
    -ms-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%)
}

.am-toast-notice-content .am-toast-text {
    min-width: 1.2rem;
    border-radius: .06rem;
    color: #fff;
    background-color: rgba(58, 58, 58, .9);
    line-height: 1.5;
    padding: .18rem .3rem
}

.am-toast-notice-content .am-toast-text.am-toast-text-icon {
    border-radius: .1rem;
    padding: .3rem
}

.am-toast-notice-content .am-toast-text.am-toast-text-icon .am-toast-text-info {
    margin-top: .12rem
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-popup-mask {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, .4);
    height: 100%;
    z-index: 999
}

.am-popup-close,
.am-popup-mask-hidden {
    display: none
}

.am-popup {
    position: fixed;
    left: 0;
    width: 100%;
    background-color: #fff;
    z-index: 999
}

.am-popup-slide-down {
    top: 0
}

.am-popup-slide-up {
    bottom: 0
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-list-header {
    padding: .3rem .3rem .18rem;
    font-size: .28rem;
    color: #888;
    display: inline-block;
    width: 100%;
    box-sizing: border-box
}

.am-list-footer {
    padding: .18rem .3rem .3rem;
    font-size: .28rem;
    color: #888
}

.am-list-body {
    position: relative;
    background-color: #fff;
    border-top: 1px solid #ddd
}

.am-list-body:after {
    display: block;
    position: absolute;
    content: "";
    width: 100%;
    border-bottom: 1px solid #ddd
}

.am-list-body div:not(:last-child) .am-list-line:after {
    display: block;
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    width: 100%;
    border-bottom: 1px solid #ddd
}

.am-list-item {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    padding-left: .3rem;
    min-height: .88rem;
    background-color: #fff;
    vertical-align: middle;
    overflow: hidden;
    transition: background-color .2s;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.am-list-item .am-list-ripple {
    position: absolute;
    background: transparent;
    display: inline-block;
    overflow: hidden;
    will-change: box-shadow, transform;
    transition: box-shadow .2s cubic-bezier(.4, 0, 1, 1), background-color .2s cubic-bezier(.4, 0, .2, 1), color .2s cubic-bezier(.4, 0, .2, 1);
    outline: none;
    cursor: pointer;
    border-radius: 100%;
    -webkit-transform: scale(0);
    -ms-transform: scale(0);
    transform: scale(0)
}

.am-list-item .am-list-ripple.am-list-ripple-animate {
    background-color: hsla(0, 0%, 62%, .2);
    -webkit-animation: ripple 1s linear;
    animation: ripple 1s linear
}

.am-list-item.am-list-item-top .am-list-line {
    -webkit-box-align: start;
    -ms-flex-align: start;
    align-items: flex-start
}

.am-list-item.am-list-item-top .am-list-line .am-list-arrow {
    margin-top: .04rem
}

.am-list-item.am-list-item-middle .am-list-line {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.am-list-item.am-list-item-bottom .am-list-line {
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end
}

.am-list-item.am-list-item-error .am-list-line .am-list-extra,
.am-list-item.am-list-item-error .am-list-line .am-list-extra .am-list-brief {
    color: #f50
}

.am-list-item.am-list-item-active {
    background-color: #ddd
}

.am-list-item.am-list-item-disabled .am-list-line .am-list-content,
.am-list-item.am-list-item-disabled .am-list-line .am-list-extra {
    color: #bbb
}

.am-list-item img {
    width: .44rem;
    height: .44rem;
    vertical-align: middle
}

.am-list-item .am-list-thumb:first-child {
    margin-right: .3rem
}

.am-list-item .am-list-thumb:last-child {
    margin-left: .16rem
}

.am-list-item .am-list-line {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%;
    -ms-flex-item-align: stretch;
    align-self: stretch;
    padding-right: .3rem;
    min-height: .88rem;
    overflow: hidden
}

.am-list-item .am-list-line .am-list-content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%;
    color: #000;
    font-size: .34rem;
    text-align: left
}

.am-list-item .am-list-line .am-list-content,
.am-list-item .am-list-line .am-list-extra {
    line-height: 1.5;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-top: .14rem;
    padding-bottom: .14rem
}

.am-list-item .am-list-line .am-list-extra {
    -ms-flex-preferred-size: 36%;
    flex-basis: 36%;
    color: #888;
    font-size: .32rem;
    text-align: right
}

.am-list-item .am-list-line .am-list-brief,
.am-list-item .am-list-line .am-list-title {
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.am-list-item .am-list-line .am-list-brief {
    color: #888;
    font-size: .3rem;
    line-height: 1.5;
    margin-top: .12rem
}

.am-list-item .am-list-line .am-list-arrow {
    display: block;
    width: .3rem;
    height: .3rem;
    margin-left: .16rem;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='26' viewBox='0 0 16 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 0L0 2l11.5 11L0 24l2 2 14-13z' fill='%23C7C7CC' fill-rule='evenodd'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    visibility: hidden
}

.am-list-item .am-list-line .am-list-arrow-horizontal {
    visibility: visible
}

.am-list-item .am-list-line .am-list-arrow-vertical {
    visibility: visible;
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.am-list-item .am-list-line .am-list-arrow-vertical-up {
    visibility: visible;
    -webkit-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg)
}

.am-list-item .am-list-line-multiple {
    padding: .25rem .3rem .25rem 0
}

.am-list-item .am-list-line-multiple .am-list-content,
.am-list-item .am-list-line-multiple .am-list-extra {
    padding-top: 0;
    padding-bottom: 0
}

.am-list-item .am-list-line-wrap .am-list-content,
.am-list-item .am-list-line-wrap .am-list-extra {
    white-space: normal
}

.am-list-item select {
    position: relative;
    display: block;
    width: 100%;
    height: 100%;
    padding: 0;
    border: 0;
    font-size: .34rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: transparent
}

@-webkit-keyframes ripple {
    to {
        opacity: 0;
        -webkit-transform: scale(2.5);
        transform: scale(2.5)
    }
}

@keyframes ripple {
    to {
        opacity: 0;
        -webkit-transform: scale(2.5);
        transform: scale(2.5)
    }
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-checkbox {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    width: .42rem;
    height: .42rem
}

.am-checkbox-inner {
    position: absolute;
    right: 0;
    width: .42rem;
    height: .42rem;
    border: .03rem solid #888;
    border-radius: 50%;
    -webkit-transform: rotate(0deg);
    -ms-transform: rotate(0deg);
    transform: rotate(0deg);
    box-sizing: border-box
}

.am-checkbox-inner:after {
    position: absolute;
    display: none;
    top: .03rem;
    right: .12rem;
    z-index: 999;
    width: .1rem;
    height: .22rem;
    border-style: solid;
    border-width: 0 .03rem .03rem 0;
    content: " ";
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg)
}

.am-checkbox-input {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    border: 0 none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none
}

.am-checkbox.am-checkbox-checked .am-checkbox-inner {
    border-color: #108ee9
}

.am-checkbox.am-checkbox-checked .am-checkbox-inner:after {
    display: block;
    border-color: #108ee9
}

.am-checkbox.am-checkbox-disabled {
    opacity: .3
}

.am-checkbox.am-checkbox-disabled.am-checkbox-checked .am-checkbox-inner {
    border-color: #888
}

.am-checkbox.am-checkbox-disabled.am-checkbox-checked .am-checkbox-inner:after {
    border-color: #888
}

.am-list .am-list-item.am-checkbox-item .am-list-thumb {
    width: .42rem;
    height: .42rem
}

.am-list .am-list-item.am-checkbox-item .am-list-thumb .am-checkbox {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: .88rem
}

.am-list .am-list-item.am-checkbox-item .am-list-thumb .am-checkbox-inner {
    left: .3rem;
    top: .24rem
}

.am-list .am-list-item.am-checkbox-item.am-checkbox-item-disabled .am-list-content {
    color: #bbb
}

.am-checkbox-agree {
    position: relative;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: stretch;
    -ms-flex-align: stretch;
    align-items: stretch;
    margin-left: .3rem;
    padding-top: .18rem;
    padding-bottom: .18rem
}

.am-checkbox-agree .am-checkbox {
    position: absolute;
    left: 0;
    top: 0;
    width: .6rem;
    height: 100%
}

.am-checkbox-agree .am-checkbox-inner {
    left: 0;
    top: .24rem
}

.am-checkbox-agree .am-checkbox-agree-label {
    display: inline-block;
    font-size: .3rem;
    color: #000;
    line-height: 1.5;
    margin-left: .6rem;
    margin-top: .02rem
}

.am-checkbox-agree .am-checkbox-agree-label a {
    color: #108ee9;
    transition: color .3s ease
}

.am-checkbox-agree .am-checkbox-agree-label a:active,
.am-checkbox-agree .am-checkbox-agree-label a:hover {
    color: #1284d6
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-button {
    display: block;
    outline: 0 none;
    -webkit-appearance: none;
    box-sizing: border-box;
    padding: 0;
    text-align: center;
    font-size: .36rem;
    height: .94rem;
    line-height: .94rem;
    border-radius: .1rem;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    white-space: nowrap;
    color: #000;
    background-color: #fff;
    border: 1px solid #ddd
}

.am-button.am-button-active {
    background-color: #ddd
}

.am-button-primary {
    color: #fff;
    background-color: #108ee9;
    border: 1px solid #108ee9
}

.am-button-primary.am-button-active {
    background-color: #1284d6
}

.am-button-ghost {
    color: #108ee9;
    background-color: transparent;
    border: 1px solid #108ee9
}

.am-button-ghost.am-button-active {
    color: #fff;
    background-color: #1284d6;
    border: 1px solid #108ee9
}

.am-button-inline {
    display: inline-block;
    padding: 0 .3rem
}

.am-button-small {
    font-size: .26rem;
    height: .6rem;
    line-height: .6rem;
    padding: 0 .3rem;
    border-radius: .1rem
}

.am-button-warning {
    color: #f86e21;
    background-color: #fff
}

.am-button-ghost.am-button-across,
.am-button-primary.am-button-across,
.am-button-warning.am-button-across,
.am-button.am-button-across {
    border-radius: 0;
    border-left: 0;
    border-right: 0
}

.am-button-ghost.am-button-disabled,
.am-button-primary.am-button-disabled,
.am-button-warning.am-button-disabled,
.am-button.am-button-disabled {
    color: #bbb;
    background-color: #ddd;
    border: 0
}

.am-button-icon {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.am-button>.am-icon {
    margin-right: .5em
}

.lazy-load-image-background.blur {
    -webkit-filter: blur(.15rem);
    filter: blur(.15rem)
}

.lazy-load-image-background.blur.lazy-load-image-loaded {
    -webkit-filter: blur(0);
    filter: blur(0);
    transition: -webkit-filter 1s;
    transition: filter 1s;
}

.lazy-load-image-background.blur>img {
    opacity: 1
}

.lazy-load-image-background.blur.lazy-load-image-loaded>img {
    /*opacity: 1;*/
    /*transition: opacity 1s*/
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-navbar {
    -ms-flex-align: center;
    height: .9rem;
    background-color: rgb(1,1,1);
    color: #fff
}

.am-navbar,
.am-navbar-left,
.am-navbar-right,
.am-navbar-title {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    align-items: center
}

.am-navbar-left,
.am-navbar-right,
.am-navbar-title {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%;
    height: 100%;
    -ms-flex-align: center
}

.am-navbar-left {
    padding-left: .3rem;
    font-size: .32rem
}

.am-navbar-left-icon {
    margin-right: .1rem;
    display: inherit
}

.am-navbar-title {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: .36rem;
    white-space: nowrap
}

.am-navbar-right {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    font-size: .32rem;
    margin-right: .3rem
}

.am-navbar-right .anticon {
    margin-right: .1rem
}

.am-navbar-right .anticon:last-child {
    margin-right: 0
}

.am-navbar-right-content {
    margin-right: .1rem
}

.am-navbar-light .am-navbar-title {
    color: #000
}

.shell-download-bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10001;
    transition: top .5s;
    display: flex;
    align-items: center;
    width: 100%;
    height: 1.39rem;
    padding: 0 .26rem;
    background: #242c4a
}

.shell-download-bar.hide-bar {
    top: -1.7rem;
    height: 0
}

.shell-download-bar .download-bar-icon {
    width: .8rem;
    height: .8rem;
    border-radius: .2rem;
    overflow: hidden
}

.shell-download-bar .download-bar-icon img {
    display: block;
    width: 100%;
    height: 100%
}

.shell-download-bar .download-bar-btn {
    min-width: 1.8rem;
    height: .6rem;
    line-height: .6rem;
    position: absolute;
    top: .33rem;
    right: .98rem;
    padding: 0 .32rem;
    border-radius: .12rem;
    background: #1678ff;
    font-size: .28rem;
    color: #fff;
    text-transform: capitalize
}

.shell-download-bar .download-bar-close {
    width: .45rem;
    height: .45rem;
    position: absolute;
    top: .42rem;
    right: .27rem
}

.shell-download-bar .download-bar-gift {
    display: none
}

.shell-download-bar .app-full-name-wrap {
    margin-left: .15rem;
    font-size: .28rem;
    font-weight: 500;
    color: #fff
}

.shell-download-bar .app-star-grade {
    display: flex;
    align-items: center;
    margin-top: .06rem
}

.shell-download-bar .app-star-grade .star-icon {
    width: .28rem;
    height: .28rem;
    margin-right: .05rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAcBAMAAABv4amZAAAAKlBMVEVHcEz/0wX/1AT/0gX/0wb/0gX/0wX/1AX/1wD/3wD/0gP/0wb/0wX/0wZdjgI9AAAADXRSTlMAzjfwf6BfcCAQUOCQTT3o1AAAAMJJREFUGNNjYAAD9usMKGDu5Qko/Ny7CSj82LsBKHzbuwbIXK67d28h81nv3r2KzGe+e/c2Ml/37t27ChDmNBeXJKVYID9IKcXFbQLD3rvIYAMGXxGZe1GBgTMWiR8JcostnGsFtqERrroBYqUvlO8AdQ0bhHsN5jp2CB8eRIxQ7TB+L1Q/1DiGWii/AMqXvXv3cjCQLwDhcgCZQkxAwRtw4+8wMMyBW8B49/ICILUYZgHvXQmwNtm7ED6PCYRmsQESAJzPlz86oO/LAAAAAElFTkSuQmCC) no-repeat 50%/cover
}

.app-win {
    display: none
}

#menu {
    width: 100%;
    bottom: 0;
    position: fixed;
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    height: 1.15rem
}

[data-theme=dark] #menu {
    background: linear-gradient(180deg, rgba(53, 53, 53, 1), rgba(24, 24, 24, 1));
    box-shadow: 0 0 0 0 rgba(255,255,255,0.1);
}

#menu a {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1 1 0%;
    height: 100%;
    font-size: .22rem;
    font-weight: 500;
    text-align: center
}

[data-theme=light] #menu a {
    color: #fff !important
}

[data-theme=dark] #menu a {
    color: #705840; !important
}

#menu a .footer_icon {
    position: relative;
    width: .6rem;
    height: .6rem;
    display: flex;
    align-items: center;
    justify-content: center
}

#menu a .footer_icon svg {
    display: block
}

[data-theme=light] #menu a .footer_icon svg {
    fill: #fff !important
}

[data-theme=dark] #menu a .footer_icon svg {
    fill: #5b667a !important
}

#menu a .footer_icon.home svg {
    width: .41rem;
    height: .375rem
}

#menu a .footer_icon.invite svg,
#menu a .footer_icon.promo svg {
    width: .41rem;
    height: .41rem
}

#menu a .footer_icon.email svg {
    width: .37rem;
    height: .3rem
}

#menu a .footer_icon.member svg {
    width: .32rem;
    height: .32rem
}

#menu a.on {
    color: #ffdf7f !important;
    /*background: url(footer-decorate.bd88ee29.png) no-repeat 50%/cover*/
}

[data-theme=dark] #menu a.on .footer_icon svg {
    fill: #ffdf7f !important
}

#menu a span {
    width: 100%;
    margin-top: .03rem;
    line-height: 1;
    font-size: .23rem;
    font-weight: 500;
    white-space: nowrap
}

#menu a.footer-deposit {
    width: 2.2rem
}

#menu .circle-menu {
    position: absolute;
    left: -.3rem;
    top: -.55rem;
    width: 1.06rem;
    height: 1.06rem;
    padding: .12rem;
    border-radius: 50%;
    box-shadow: 0 0 .1rem 0 rgba(0, 0, 0, .2);
    background-image: linear-gradient(47deg, #212e3b 14%, #3890f9 85%)
}

#menu .circle-menu .circle-1 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: rgba(194, 221, 253, .22);
    display: flex;
    align-items: center;
    justify-content: center
}

#menu .circle-menu .icon-wrap-home svg {
    display: block;
    width: .6rem;
    height: .52rem
}

#menu .circle-menu .icon-wrap-chat {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-image: linear-gradient(159deg, rgba(255, 246, 206, .6) 14%, hsla(45, 88%, 84%, 0) 82%)
}

#menu .circle-menu .icon-wrap-chat .footer__chat {
    width: 100%;
    height: 100%;
    padding-top: 0
}

#menu .circle-menu .icon-wrap-chat .footer__chat .footer__chat-icon {
    margin-top: 0;
    display: flex;
    justify-content: center;
    align-items: center
}

#menu .circle-menu .icon-wrap-chat .footer__chat .footer__chat-icon svg {
    width: .5rem;
    fill: #000
}

#menu .circle-menu .icon-wrap-chat .footer__chat .footer__chat-text {
    display: none
}


.sidebar {
    position: absolute;
    top: 1.24rem;
    right: .36rem
}

.sidebar svg {
    display: block;
    width: 100%;
    height: 100%
}

.sidebar .right-side-menu {
    width: 3.48rem;
    padding: .36rem .26rem .18rem;
    transition: all .3s;
    z-index: 10002;
    box-shadow: 0 0 .12rem 0 rgba(0, 0, 0, .61);
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: .14rem
}

[data-theme=light] .sidebar .right-side-menu {
    background: #fff !important
}

[data-theme=dark] .sidebar .right-side-menu {
    background: #192b39 !important
}

.sidebar .right-side-menu .close-sidebar {
    position: absolute;
    top: .25rem;
    right: .25rem;
    z-index: 3001;
    color: #fff
}

.sidebar .right-side-menu .close-sidebar svg {
    display: block;
    width: .41rem;
    height: .41rem
}

.sidebar .right-side-menu .rightbar-content {
    position: relative;
    width: 100%;
    height: 100%
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap {
    position: relative;
    width: 100%
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info {
    display: flex;
    flex-direction: column
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .user-balance {
    background: none;
    color: #56697a;
    font-size: .24rem;
    font-weight: 400;
    display: flex;
    flex-direction: column;
    height: .07rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .user-balance .member-id {
    display: flex;
    align-items: center;
    gap: .19rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .user-balance .member-id .am-icon {
    width: .22rem;
    height: .22rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .member-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: .247rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .member-wrap img {
    width: .913rem;
    height: .913rem;
    border-radius: 50%;
    border: .04rem solid #f0c343
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .member-wrap .wrap-user {
    width: .913rem;
    height: .913rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .member-wrap .wrap-text {
    padding: 0 .054rem 0 .048rem;
    border-radius: .05rem;
    /*height: .245rem;*/
    font-size: .24rem;
    font-weight: 700;
    margin-top: -.2rem;
    color: #543c00;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHsAAAA4CAMAAADq1XePAAAANlBMVEVHcEzfqULhs1vgrk1jWELfoy5iV0Ljr0zloyCziTHmpipiV0LpuV3oox3ntVTjqDfZojmngTmdf6JRAAAACnRSTlMA////caXWQGAY8x8OAwAABEtJREFUWMPNWduW2yAMNCjJbmxi8P//bG0QIAmJpC9p8WubOaORRpddlv/hPZ8/33jPEfj37uqD+nZ8QT4fvPLa/wP8oXi+tb5Xffc7h/89/2U6PwfXh8gNm6FfuAE/Dn19Bfr84omcweN64b84PEG/p5TBVd7hb3j7yjsi7wxd0F/9rU8CXUhf6IDgF21QYo6ER/zGO//EhY7QOe4vjl7BM+sLPEGl7SpvuGgL4g14DHpBdznkhfYFHLngJOw/KT8UHPXeidwc23fJdeQS84Zeab8E/J1EvKhdIu5Enu+Stip4y3IgvNcedfGejXbCXKtyV73DhHfonBlxR3nXKnvxbMvE76nyTjzN957nuywxjTerMReVXOMvIXbJcySOyEaetxILZsxz9AjtiGm+yqDHnmiQ0FpcUxvCEHLvNWfJAUfm549QvVvMxftZEte7y02cLUjB56aaibeg90QX2L8LK7BiCzM39y3RzRrDiHNbG/O8Ytt6BwmvWzmCN+jKu6aa9DXKW4TcEeJhhDZaCerNba31EivmFZ/ZORRkDVyXu9sa9KDbuVawWwuTXQyUPmbxbm2sIbuSaVFPtca7BJ3UWG1jkveHtKEF3fQ1ondtY8PYEnaDNrPULjeUkYW3sYneMst5+96Fmb+ZmWT/nutN3Lz3b4QOu5Hnqq8hchztfJJrTG82rQ0zkzcnByI4ghPaH/AG0seUHuqnxCU0qW87zzttObaMo6IxOfg+KkagljpMyaOfu5przVab4kr7DsJSgfOmfh4x0VbTW9rUNJscyKBq6132AqH3Otcb27errUTroKbatLyhGyrlPfHUZG0GKm8JD3r7JrzNXpKjnomX8mabwT4Ce5V3LfCrvp3gbfbvBs14g+ItSisBOSq6bued9xtvyeDCWoKyEYVZJ2lTqpiYptjdz510tvfDInh1Ol8785mfszaGuKAugm+I87WkjUxGnrtOHLreVW2Nt9e2UCC9hOv9eqv3YC0wCO5t5tTWYlv9SR+bzKlkFaTDeVCzjROHoY31FmpOqY03cVQyIqsHD6vA2djSPdVONTGvYX07q5NMXJXaOcY88iSf8wayGezT6VzNcnLxqGNq3w1sveVuAEzw8KaHSvAojg4m74PJzRcDJdf8mGwwLP5OriWq3rcLW/i5sNXx6KCFfRjXJO8BO92WTeHN7Xz/SPB+4upZ3tuYEvPjxD760ATKyWPX9DZ8rZe3Gzb/gfd2Wx5I3NEOuutbiQ/hoykVIrHzqCK/ju253E7ijlYZW8d2/eQxOewB0h62klVAP5YTezvKuCYOHuZWIgTnB66hlejbWNrOkC9n0C/mrtOeDQ+WqQK7YPOTplJix7Zt11HzJJ6pt/btiN5hrDHf427ZudzGRMjThXjLt9wMvh0Hv+ztk1vPrLwdWiq53LPjeToy3AMPyQX8q+/RDui3fwd9gj++Cn3jfzH5HvqjIf8BQQXVbfGu/dAAAAAASUVORK5CYII=) no-repeat 50%/cover
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .user-avatar {
    display: block;
    width: 1.05rem;
    height: 1.05rem;
    margin: 0 auto;
    color: #fff
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .memberlevel {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: .32rem;
    color: #fff;
    line-height: 1;
    margin-top: .1rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .memberlevel svg {
    width: .3rem;
    height: .3rem;
    margin-left: .15rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .balance {
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    margin-top: .25rem;
    font-size: .28rem;
    font-weight: 700;
    color: #f1c137
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .balance .sumBalance {
    display: flex;
    align-items: center;
    margin: 0 .14rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .balance .toggle-balance {
    color: #fff;
    opacity: .6
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .user-info .balance .toggle-balance svg {
    display: block;
    width: .4rem;
    height: .24rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .login-register {
    display: flex;
    align-items: center;
    margin-top: .3rem
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .login-register span {
    width: 2rem;
    height: .6rem;
    margin: 0 .1rem;
    border-radius: .3rem;
    font-size: .26rem;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .login-register .login-btn {
    background: linear-gradient(90deg, #f74056, #d326d9);
    box-shadow: 0 .1rem .35rem rgba(247, 64, 86, .5)
}

.sidebar .right-side-menu .rightbar-content .user-info-wrap .login-register .register-btn {
    background: linear-gradient(90deg, #5613ba, #5f88f2);
    box-shadow: 0 .1rem .35rem rgba(86, 19, 186, .5)
}

.sidebar .right-side-menu .rightbar-content .menu-nav {
    padding: .48rem .36rem .42rem;
    display: grid;
    grid-template-columns: repeat(3, 1fr)
}

.sidebar .right-side-menu .rightbar-content .menu-nav .menu-item {
    max-width: 100%
}

.sidebar .right-side-menu .rightbar-content .menu-nav .nav-icon {
    display: flex;
    align-items: center;
    width: .7rem;
    height: .7rem;
    margin: 0 auto;
    background-position: 50%;
    background-repeat: no-repeat
}

.sidebar .right-side-menu .rightbar-content .menu-nav .nav-icon.myacc {
    background-size: .62rem .62rem
}

.sidebar .right-side-menu .rightbar-content .menu-nav .nav-icon.deposit {
    background-size: .66rem .66rem
}

.sidebar .right-side-menu .rightbar-content .menu-nav .nav-icon.withdraw {
    background-size: .68rem .68rem
}

.sidebar .right-side-menu .rightbar-content .menu-nav .nav-name {
    margin-top: .2rem;
    font-size: .24rem;
    text-align: center;
    color: #fff
}

.sidebar .right-side-menu .rightbar-content .member-nav {
    margin-top: .73rem
}

.sidebar .right-side-menu .rightbar-content .member-nav .member-nav-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: .64rem;
    border-radius: .1rem;
    background-color: #263548;
    margin-bottom: .12rem;
    padding: 0 .28rem
}

.sidebar .right-side-menu .rightbar-content .member-nav .member-nav-item .icon-wrap {
    width: .33rem;
    height: .33rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.sidebar .right-side-menu .rightbar-content .member-nav .member-nav-item:last-child {
    margin-bottom: 0
}

.sidebar .right-side-menu .rightbar-content .member-nav .member-nav-name {
    margin-left: .37rem;
    font-size: .22rem;
    color: #879ec0
}

.sidebar .right-side-menu .rightbar-content .switch-language {
    margin-top: .2rem;
    margin-bottom: 2rem
}

.sidebar .right-side-menu .h-line {
    width: 100%;
    height: 1px;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #525d7a
}

.sidebar .right-side-menu .side-menu-bottom {
    padding: 0 .36rem .45rem
}

.sidebar .right-side-menu .switch-container {
    padding-left: .13rem;
    margin-bottom: .3rem
}

.sidebar .right-side-menu .sign-out {
    width: 100%;
    height: 1.14rem;
    padding: 0 .36rem
}

.sidebar .right-side-menu .sign-out .btn-logout {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: .24rem;
    font-weight: 500;
    color: #1678ff
}

.sidebar .right-side-menu .sign-out .btn-logout .logout-inner {
    display: flex;
    align-items: center;
    line-height: 1
}

.sidebar .right-side-menu .sign-out .btn-logout svg {
    width: .32rem;
    height: .37rem;
    margin-right: .22rem
}

.sidebar .left-side-menu {
    width: 4.57rem;
    height: 100vh;
    height: calc(var(--vh, 1vh)*100 - 1.2rem - var(--safe-area-inset-bottom));
    position: fixed;
    top: calc(1.39rem + var(--safe-area-inset-bottom));
    left: 0;
    -webkit-transform: translateX(-110%);
    transform: translateX(-110%);
    transition: all .3s;
    z-index: 10002;
    background: #161b2a;
    padding: 0 .21rem .631rem
}

.sidebar .left-side-menu .close-sidebar {
    position: absolute;
    right: .32rem;
    top: .44rem;
    width: .49rem;
    height: .49rem
}

.sidebar .left-side-menu .side-menu-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: auto;
}

.sidebar .left-side-menu .side-menu-content .main-nav {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: .42rem;
    gap: .15rem
}

.sidebar .left-side-menu .side-menu-content .accordion-arrow {
    position: absolute;
    right: .24rem;
    top: .4rem;
    width: .26rem;
    height: .15rem;
    transition: .3s;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    color: #525d6e
}

.sidebar .left-side-menu .side-menu-content .promo-nav {
    width: 4.16rem;
    background-color: #1e2531;
    border-radius: .1rem;
    padding: 0 .24rem .21rem .32rem;
    margin-top: .3rem
}

.sidebar .left-side-menu .side-menu-content .promo-nav .collapse-accordion .am-accordion-header {
    padding: 0
}

.sidebar .left-side-menu .side-menu-content .promo-nav .collapse-accordion .vendor-header {
    position: relative;
    display: flex;
    align-items: center;
    height: .96rem;
    font-size: .32rem;
    font-weight: 600;
    color: #97a5c9;
    text-transform: capitalize
}

.sidebar .left-side-menu .side-menu-content .promo-nav .collapse-accordion .vendor-header .icon-promo-decorate {
    width: .405rem;
    height: .43rem;
    margin-right: .155rem
}

.sidebar .left-side-menu .side-menu-content .promo-nav .collapse-accordion .vendor-list {
    padding: .25rem 0;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: .2rem
}

.sidebar .left-side-menu .side-menu-content .promo-nav .collapse-accordion .vendor-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: .77rem;
    border-radius: .1rem;
    background-color: #2f6a96;
    font-size: .26rem;
    color: #fff;
    text-align: center
}

.sidebar .left-side-menu .side-menu-content .promo-nav .collapse-accordion .am-accordion-item-active .accordion-arrow {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.sidebar .left-side-menu .side-menu-content .main-nav-item {
    position: relative;
    width: 3.83rem;
    height: 1.18rem;
    font-size: .3rem;
    font-weight: 600;
    color: #fff;
    display: flex;
    align-items: center
}

.sidebar .left-side-menu .side-menu-content .main-nav-item .main-nav-text {
    width: 100%;
    padding-right: .55rem
}

.sidebar .left-side-menu .side-menu-content .main-nav-item .nav-icon-wrap {
    width: 1.042rem;
    height: .989rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: .395rem
}

.sidebar .left-side-menu .side-menu-content .main-nav-item .nav-icon {
    position: absolute
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.vip {
    background: url(nav-vip-bg.2a847be4.png) no-repeat 50%/cover
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.vip .nav-icon {
    width: 1.042rem;
    height: .989rem
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.invite {
    background: url(nav-invite-bg.a26057f5.png) no-repeat 50%/cover
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.invite .nav-icon {
    width: .966rem;
    height: .89rem
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.bonus {
    background: url(nav-bonus-bg.fa37b1c0.png) no-repeat 50%/cover
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.bonus .nav-icon {
    width: .78rem;
    height: .83rem
}

.sidebar .left-side-menu .side-menu-content .main-nav-item.all-promo {
    width: 100%;
    height: .7rem;
    background-color: #252c39;
    border-radius: .13rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .32rem
}

.sidebar .left-side-menu .side-menu-content .sub-nav-item {
    display: flex;
    align-items: center;
    height: .96rem;
    line-height: .96rem;
    color: #97a5c9;
    font-size: .32rem;
    font-weight: 600
}

.sidebar .left-side-menu .side-menu-content .sub-nav-item .sub-nav-item-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.sidebar .left-side-menu .side-menu-content .sub-nav-item svg {
    display: block;
    width: .5rem;
    height: .5rem;
    margin-right: .25rem
}

.sidebar .left-side-menu .side-menu-content .nav-icon {
    width: .6rem;
    height: .6rem;
    margin-left: .2rem
}

.sidebar .left-side-menu .h-line {
    width: 100%;
    height: 1px;
    margin: .3rem auto;
    background-color: #282f40
}

.sidebar .left-side-menu .download-wrap,
.sidebar .left-side-menu .side-service {
    display: flex;
    width: 100%;
    height: .78rem;
    background-color: #252c39;
    border-radius: .13rem;
    align-items: center;
    justify-content: center;
    gap: .17rem;
    margin-top: .17rem
}

.sidebar .left-side-menu .download-wrap .am-icon,
.sidebar .left-side-menu .side-service .am-icon {
    width: .27rem;
    height: .37rem
}

.sidebar .left-side-menu .download-wrap .service-icon,
.sidebar .left-side-menu .side-service .service-icon {
    width: .4rem;
    height: .4rem
}

.sidebar .left-side-menu .download-wrap .download-text,
.sidebar .left-side-menu .side-service .download-text {
    font-size: .28rem;
    font-weight: 600;
    color: #fff
}

.sidebar .left-side-menu .side-language {
    position: relative;
    margin-top: .2rem
}

.sidebar .left-side-menu .side-language .language-select .select-wrap {
    height: .78rem;
    padding: 0 .33rem;
    border-radius: .13rem;
    border: 1px solid #525d6e
}

.sidebar .left-side-menu .side-language .language-select .selected-lang {
    display: flex;
    align-items: center;
    font-size: .32rem;
    color: #576274
}

.sidebar .left-side-menu .side-language .language-select .option-wrap {
    border-radius: .16rem;
    background-color: #2b3248;
    overflow: hidden
}

.sidebar .left-side-menu .side-language .language-select .option-wrap .option-item {
    height: .78rem;
    padding-left: .46rem
}

.sidebar .left-side-menu .side-language .language-select .option-wrap .option-item.selected {
    background-color: #434b67
}

.sidebar .left-side-menu .channel-group {
    display: flex;
    flex-direction: column;
    align-items: center
}

.sidebar .left-side-menu .channel-group .channel-title {
    font-size: .32rem;
    font-weight: 500;
    color: #fff;
    margin: .26rem 0 .13rem
}

.sidebar .left-side-menu .channel-group .channel-list {
    display: flex;
    gap: .19rem
}

.sidebar .left-side-menu .channel-group .channel-list .channel-item .am-icon {
    width: .62rem;
    height: .62rem
}

.main-nav-open .left-side-menu,
.main-nav-open .right-side-menu {
    -webkit-transform: translateX(0);
    transform: translateX(0)
}

.main-nav-open .sidebar-mask {
    display: block
}

.sidebar-mask {
    width: 100vw;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: none;
    z-index: 200;
    overscroll-behavior: none;
    background: rgba(0, 0, 0, .5)
}

.member-balance {
    display: flex;
    align-items: center
}

.member-balance .sum-balance {
    line-height: 1;
    font-size: .26rem;
    font-weight: 600;
    color: #fff
}

.member-balance .sum-balance .symbol {
    color: #dadada;
    margin-right: .054rem
}

.member-balance .balance-control {
    display: flex;
    align-items: center
}

.member-balance .balance-control .toggle-balance {
    opacity: .6;
    color: #fff
}

.member-balance .balance-control .toggle-balance svg {
    display: block;
    width: .37rem;
    height: .37rem
}

.member-balance .balance-control .refresh-balance {
    margin: 0 .08rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.member-balance .balance-control .refresh-balance.rotateFull {
    -webkit-animation: rotate-full .5s linear infinite;
    animation: rotate-full .5s linear infinite
}

.member-balance .balance-control .refresh-balance svg {
    display: block;
    width: .26rem;
    height: .26rem
}

.home-header {
    width: 100%;
    height: 1rem;
    flex-shrink: 0;
    transition: all .5s
}

.home-header img {
    display: block
}

.home-header .header-nav {
    width: 100%;
    position: relative;
    flex: 1 1 0%;
    align-self: center;
    overflow: hidden
}

.home-header .header-logo img {
    display: block;
    height: 1.2rem;
    position: relative;
    left: -.1rem
}

.home-header .header-content {
    position: fixed;
    /*left: 0;*/
    top: 0;
    z-index: 1000;
    justify-content: space-between;
    width: var(--theme-max-width);
    height: 1rem;
    padding: 0 .22rem 0 .3rem;
    box-shadow: 0 .03rem .06rem 0 rgba(0, 0, 0, .4);
    background-color: rgb(1,1,1);
    transition: top .5s
}

.home-header .header-content,
.home-header .header-content .header-left {
    display: flex;
    align-items: center
}

.home-header .header-content .header-menu {
    width: .47rem;
    height: .412rem;
    margin-right: .21rem
}

.home-header .header-content .header-menu svg {
    display: block;
    width: 100%;
    height: 100%
}

.home-header .header-content .header-menu.off svg {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

.home-header .header-content .user-info {
    display: flex;
    align-items: center
}

.home-header .header-content .user-icon svg {
    display: block;
    width: .6rem;
    height: .6rem;
    fill: #aabee2
}

.home-header .header-content .user-balance {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: .84rem;
    padding: 0 .08rem;
    border-radius: .16rem;
    /*background-color: #282f3c*/
}

.home-header .header-content .user-balance .deposit-btn,
.home-header .header-content .user-balance .withdraw-btn {
    width: .6rem;
    height: .6rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.home-header .header-content .user-balance .deposit-btn svg,
.home-header .header-content .user-balance .withdraw-btn svg {
    display: block;
    /*width: .651rem;*/
    /*height: .651rem*/
}

.home-header .header-content .user-balance .deposit-btn {
    margin-right: .2rem
}

.home-header .header-content .user-balance .sum-balance {
    max-width: 2.05rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home-header .header-content .member-icon {
    width: 1.31rem;
    height: .84rem;
    margin-left: .06rem;
    justify-content: center;
    border-radius: .18rem;
    background-color: #282f3c;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 .16rem 0 .08rem
}

.home-header .header-content .member-icon .member-wrap {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: .68rem;
    height: .68rem
}

.home-header .header-content .member-icon .member-wrap img {
    border-radius: 50%;
    border: .04rem solid #f0c343
}

.home-header .header-content .member-icon .member-wrap .wrap-user {
    width: .68rem;
    height: .68rem
}

.home-header .header-content .member-icon .member-wrap .wrap-text {
    padding: 0 .054rem 0 .048rem;
    border-radius: .05rem;
    /*height: .245rem;*/
    font-size: .24rem;
    font-weight: 700;
    margin-top: -.2rem;
    color: #543c00;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHsAAAA4CAMAAADq1XePAAAANlBMVEVHcEzfqULhs1vgrk1jWELfoy5iV0Ljr0zloyCziTHmpipiV0LpuV3oox3ntVTjqDfZojmngTmdf6JRAAAACnRSTlMA////caXWQGAY8x8OAwAABEtJREFUWMPNWduW2yAMNCjJbmxi8P//bG0QIAmJpC9p8WubOaORRpddlv/hPZ8/33jPEfj37uqD+nZ8QT4fvPLa/wP8oXi+tb5Xffc7h/89/2U6PwfXh8gNm6FfuAE/Dn19Bfr84omcweN64b84PEG/p5TBVd7hb3j7yjsi7wxd0F/9rU8CXUhf6IDgF21QYo6ER/zGO//EhY7QOe4vjl7BM+sLPEGl7SpvuGgL4g14DHpBdznkhfYFHLngJOw/KT8UHPXeidwc23fJdeQS84Zeab8E/J1EvKhdIu5Enu+Stip4y3IgvNcedfGejXbCXKtyV73DhHfonBlxR3nXKnvxbMvE76nyTjzN957nuywxjTerMReVXOMvIXbJcySOyEaetxILZsxz9AjtiGm+yqDHnmiQ0FpcUxvCEHLvNWfJAUfm549QvVvMxftZEte7y02cLUjB56aaibeg90QX2L8LK7BiCzM39y3RzRrDiHNbG/O8Ytt6BwmvWzmCN+jKu6aa9DXKW4TcEeJhhDZaCerNba31EivmFZ/ZORRkDVyXu9sa9KDbuVawWwuTXQyUPmbxbm2sIbuSaVFPtca7BJ3UWG1jkveHtKEF3fQ1ondtY8PYEnaDNrPULjeUkYW3sYneMst5+96Fmb+ZmWT/nutN3Lz3b4QOu5Hnqq8hchztfJJrTG82rQ0zkzcnByI4ghPaH/AG0seUHuqnxCU0qW87zzttObaMo6IxOfg+KkagljpMyaOfu5przVab4kr7DsJSgfOmfh4x0VbTW9rUNJscyKBq6132AqH3Otcb27errUTroKbatLyhGyrlPfHUZG0GKm8JD3r7JrzNXpKjnomX8mabwT4Ce5V3LfCrvp3gbfbvBs14g+ItSisBOSq6bued9xtvyeDCWoKyEYVZJ2lTqpiYptjdz510tvfDInh1Ol8785mfszaGuKAugm+I87WkjUxGnrtOHLreVW2Nt9e2UCC9hOv9eqv3YC0wCO5t5tTWYlv9SR+bzKlkFaTDeVCzjROHoY31FmpOqY03cVQyIqsHD6vA2djSPdVONTGvYX07q5NMXJXaOcY88iSf8wayGezT6VzNcnLxqGNq3w1sveVuAEzw8KaHSvAojg4m74PJzRcDJdf8mGwwLP5OriWq3rcLW/i5sNXx6KCFfRjXJO8BO92WTeHN7Xz/SPB+4upZ3tuYEvPjxD760ATKyWPX9DZ8rZe3Gzb/gfd2Wx5I3NEOuutbiQ/hoykVIrHzqCK/ju253E7ijlYZW8d2/eQxOewB0h62klVAP5YTezvKuCYOHuZWIgTnB66hlejbWNrOkC9n0C/mrtOeDQ+WqQK7YPOTplJix7Zt11HzJJ6pt/btiN5hrDHf427ZudzGRMjThXjLt9wMvh0Hv+ztk1vPrLwdWiq53LPjeToy3AMPyQX8q+/RDui3fwd9gj++Cn3jfzH5HvqjIf8BQQXVbfGu/dAAAAAASUVORK5CYII=) no-repeat 50%/cover
}

.home-header .header-content .member-icon .accordion-arrow {
    width: .23rem;
    height: .11rem;
    fill: #575d67
}

.home-header .header-content .member-icon svg {
    display: block;
    width: .29rem;
    height: .34rem
}

.home-header .header-content .login-register {
    display: flex;
    align-items: center;
    gap: .106rem;
    font-size: .28rem;
    font-weight: 700;
    text-align: center;
    color: #fff
}

.home-header .header-content .login-register .login-btn {
    min-width: 1.4rem;
    height: .749rem;
    line-height: .749rem;
    padding: 0 .336rem 0 .34rem;
    border-radius: .16rem;
    background-color: #1678ff
}

.home-header .header-content .login-register .register-btn {
    min-width: 1.4rem;
    height: .749rem;
    line-height: .749rem;
    padding: 0 .35rem 0 .348rem;
    border-radius: .16rem;
    background-color: #da394f
}

.home-header .header-download {
    width: .3rem;
    height: .37rem;
    margin-left: .2rem;
    color: #7bc242
}

.home-header .header-download svg {
    display: block;
    width: 100%;
    height: 100%
}

.home-header .header-logout {
    margin-left: .12rem;
    color: #7bc242
}

.home-header .header-logout .am-icon-icon-logout {
    display: block;
    height: .37rem
}

@-webkit-keyframes hvr-pop {
    6% {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }
    10% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes hvr-pop {
    6% {
        -webkit-transform: scale(1.2);
        transform: scale(1.2)
    }
    10% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    to {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@-webkit-keyframes glow-before {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

@keyframes glow-before {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

@-webkit-keyframes glow-after {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@keyframes glow-after {
    0% {
        opacity: 0
    }
    to {
        opacity: 1
    }
}

@-webkit-keyframes shimmer-short {
    0% {
        background-position: -1rem top
    }
    to {
        background-position: 1rem top
    }
}

@keyframes shimmer-short {
    0% {
        background-position: -1rem top
    }
    to {
        background-position: 1rem top
    }
}

#mc-animate-container {
    width: 100%;
    top: 0
}

.this-mc-header {
    position: fixed;
    top: 0;
    left: 0
}

.shell-header {
    height: 1rem
}

#root .mc-navbar-blue .am-navbar-title {
    font-size: .36rem !important;
    font-weight: 600;
}

[data-theme=light] #root .mc-navbar-blue .am-navbar-title {
    color: #553a7a !important
}

[data-theme=dark] #root .mc-navbar-blue .am-navbar-title {
    color: #fff !important
}

#root .mc-navbar-blue .am-icon-left {
    font-size: .3rem !important
}

[data-theme=light] #root .mc-navbar-blue .am-icon-left {
    color: #553a7a !important
}

[data-theme=dark] #root .mc-navbar-blue .am-icon-left {
    color: #fff !important
}

#root .shell-header .mc-navbar-blue {
    position: fixed;
    top: 0;
    width: 100%;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    z-index: 10
}

#root .shell-header .mc-navbar-blue .shell_return_icon .am-icon-left {
    color: #56575c;
    font-size: .3rem
}

#root .shell-header .mc-navbar-blue .am-navbar-title {
    color: #56575c;
    font-size: .4rem
}

#root .mc-whitespace,
.am-navbar {
    height: .8rem
}

.am-navbar-left-icon .am-icon-md {
    display: none
}

.am-icon-md {
    width: .45rem;
    height: .45rem
}

.logo {
    padding-top: .1rem;
    height: 100%
}

.logo img {
    width: 1.49rem;
    height: .98rem
}

.am-navbar-right {
    position: relative;
    font-size: .3rem;
    margin-right: .13rem
}

.change-language,
.login,
.logout,
.register,
.trail {
    border-radius: .1rem;
    color: #fff;
    text-align: center;
    display: inline-block;
    padding: .16rem
}

.login,
.logout,
.register {
    width: 1.4rem;
    height: .68rem
}

.login,
.logout {
    position: absolute;
    top: .16rem
}

.logout {
    left: -.4rem
}

.login {
    margin-right: .3rem
}

.login,
.register {
    background-size: cover
}

.register {
    position: absolute;
    left: -.3rem;
    top: .18rem;
    padding-left: 0;
    padding-right: 0
}

.change-language {
    position: absolute;
    right: -.5rem;
    top: .12rem;
    width: 1.6rem;
    height: .7rem
}

.change-language span {
    float: left;
    line-height: .7rem;
    margin-top: -.1rem
}

.change-language .language-icon {
    float: left
}

.change-language {
    color: #fff
}

.hide {
    display: none
}

#root .am-navbar-left-content {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center
}

#root .am-navbar-left-content p {
    height: 100%
}

#root .am-navbar-left-content svg {
    display: block;
    width: .68rem;
    height: .68rem
}

.trail {
    display: none
}

.fortest .am-popover-item {
    padding: 0
}

.fortest {
    right: .1rem !important;
    width: auto;
    min-width: 1rem;
    left: auto !important
}

.h-navbar-flex .am-navbar-right {
    flex: 1.5 1 0%
}

.h-navbar-flex .am-navbar-title {
    display: none
}

.h-navbar-right {
    position: relative;
    display: flex;
    height: 100%;
    justify-content: space-between;
    align-items: center;
    color: #fff
}

.h-navbar-right a {
    width: .7rem;
    height: .7rem;
    line-height: .58rem;
    border-radius: 50%;
    text-align: center;
    background: #fff;
    margin-left: .13rem;
    display: flex;
    justify-content: center;
    align-items: center
}

.h-navbar-right a svg {
    width: .5rem
}

.h-navbar-right .h-login svg {
    position: relative;
    top: .03rem
}

.mc-navbar-blue .am-navbar-right {
    font-size: .3rem
}

.h-navbar-right .h-login,
.h-navbar-right .h-register {
    width: 1.56rem;
    height: .6rem;
    line-height: .6rem;
    text-align: center;
    font-size: .26rem;
    border-radius: .1rem
}

.h-navbar-right .h-login {
    background: radial-gradient(#8eb7f1, #fff)
}

.h-navbar-right .h-register {
    background: radial-gradient(#0ac0fa, #90e8ff)
}

.h-navbar-right .h-change-language {
    color: #fff;
    text-align: right;
    margin-left: .1rem;
    height: 100%;
    display: flex;
    align-items: center;
    background: unset !important
}

.h-navbar-right .language-icon {
    width: .6rem;
    height: .6rem
}

.h-navbar-right .h-blank-one {
    flex: .6 1 0%
}

.h-navbar-right .h-blank-two {
    flex: .8 1 0%
}

.h-navbar-right .h-one-language {
    flex: 1 1 0% !important
}

.h-navbar-right .h-ologout {
    flex: .2 1 0%;
    text-align: right;
    padding: 0 .2rem;
    height: 100%;
    margin-right: auto
}

.h-navbar-right .h-ologout img,
.h-navbar-right .h-ologout svg {
    width: .44rem;
    height: .44rem;
    margin-top: .29rem
}

.h-navbar-flex .am-navbar-right {
    margin-right: .2rem
}

.h-navbar-right .down-app,
.h-navbar-right .h-login,
.h-navbar-right .h-register {
    margin-right: .15rem
}

.h-navbar-right .h-freeplay {
    color: #fff !important;
    background: linear-gradient(90deg, #59d0fb, #38b7ff)
}

.h-navbar-right .h-account {
    display: flex;
    align-items: center;
    margin-right: .2rem
}

.h-navbar-right .icon_account {
    width: .48rem;
    height: .46rem
}

#root .mc-navbar-blue .am-navbar-left {
    padding-left: 0
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-accordion {
    position: relative;
    border-top: 1px solid #ddd
}

.am-accordion-anim-active {
    transition: all .2s ease-out
}

.am-accordion .am-accordion-item .am-accordion-header {
    position: relative;
    color: #000;
    font-size: .34rem;
    height: .88rem;
    line-height: .88rem;
    background-color: #fff;
    box-sizing: content-box;
    padding-left: .3rem;
    padding-right: .6rem;
    width: auto;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.am-accordion .am-accordion-item .am-accordion-header:after {
    display: block;
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    width: 100%;
    border-bottom: 1px solid #ddd
}

.am-accordion .am-accordion-item .am-accordion-header i {
    position: absolute;
    display: block;
    top: .3rem;
    right: .3rem;
    width: .3rem;
    height: .3rem;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg width='16' height='26' viewBox='0 0 16 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 0L0 2l11.5 11L0 24l2 2 14-13z' fill='%23C7C7CC' fill-rule='evenodd'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    -webkit-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg)
}

.am-accordion .am-accordion-item .am-accordion-header[aria-expanded~=true] i {
    -webkit-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg)
}

.am-accordion .am-accordion-item .am-accordion-content {
    overflow: hidden;
    background: #fff
}

.am-accordion .am-accordion-item .am-accordion-content .am-accordion-content-box {
    font-size: .3rem;
    color: #333;
    position: relative
}

.am-accordion .am-accordion-item .am-accordion-content .am-accordion-content-box:after {
    display: block;
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    width: 100%;
    border-bottom: 1px solid #ddd
}

.am-accordion .am-accordion-item .am-accordion-content.am-accordion-content-inactive {
    display: none
}

.page-banner {
    width: 100%;
    position: relative;
    z-index: 2
}

.page-banner .swiper-container {
    width: 100%
}

.page-banner .swiper-container .swiper-wrapper {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    transition-timing-function: ease
}

.page-banner .swiper-container .swiper-slide {
    width: 100%;
    /*padding: .28rem .26rem 0;*/
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.page-banner .swiper-container .swiper-inner {
    width: 100%;
    /*border-radius: .2rem;*/
    overflow: hidden
}

.page-banner .swiper-container .swiper-pagination {
    bottom: .12rem !important;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-right: .5rem
}

.page-banner .swiper-container .swiper-pagination-bullet {
    width: .18rem;
    height: .18rem;
    opacity: .5;
    background-color: #fff
}

.page-banner .swiper-container .swiper-pagination-bullet-active {
    opacity: 1;
    border-radius: .2rem
}

.page-banner .swiper-container img {
    display: block;
    height: 3rem;
    -o-object-fit: cover;
    object-fit: cover;
    width: 100%
}

.float-service {
    position: fixed;
    right: .26rem;
    bottom: .4rem;
    z-index: 2000
}

.float-service .scroll-top,
.float-service .service-item {
    width: 1rem;
    height: 1rem;
    border-radius: 50%;
    background-color: #2b3248;
    display: flex;
    align-items: center;
    justify-content: center
}

.float-service .service-item svg {
    width: .5rem;
    height: .5rem
}

.float-service .scroll-top {
    visibility: hidden;
    margin-top: .4rem
}

.float-service .scroll-top.on {
    visibility: visible
}

.float-service .scroll-top svg {
    width: .48rem;
    height: .53rem;
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg)
}

.simple-select {
    position: relative
}

.simple-select .select-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center
}

.simple-select .select-wrap .select-icon {
    width: .23rem;
    height: .23rem;
    margin-left: .12rem;
    transition: .3s;
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    -webkit-transform-origin: center;
    transform-origin: center;
    color: #fff
}

.simple-select .select-wrap .select-icon svg {
    display: block;
    width: 100%;
    height: 100%
}

.simple-select .select-wrap .select-icon.on {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

.simple-select .option-wrap {
    position: absolute;
    display: none;
    width: 100%;
    z-index: 1000;
    border-radius: .1rem
}

.simple-select .option-wrap.on {
    display: block
}

.simple-select .option-wrap .option-item {
    width: 100%;
    display: flex;
    align-items: center;
    color: #fff;
    white-space: nowrap
}

.language-select .simple-select .icon-lang {
    width: .46rem;
    height: .46rem;
    margin-right: .2rem
}

.language-select .simple-select .option-wrap {
    width: 100%;
    left: 0;
    bottom: calc(100% + .12rem)
}

.language-select .select-wrap {
    display: flex;
    align-items: center;
    justify-content: center
}

.language-select .select-wrap .select-icon {
    display: none
}

.language-select .selected-lang {
    flex-shrink: 0;
    margin-right: .1rem
}

.language-select .lang-title {
    display: flex;
    align-items: center
}

.language-select .option-item {
    font-size: .24rem;
    font-weight: 500
}

.language-select .option-item .icon-lang {
    margin-right: .2rem
}

.sm-checkbox-item {
    position: relative;
    display: flex;
    overflow: hidden;
    cursor: pointer;
    font-size: .23rem;
    font-weight: 500;
    color: #1678ff
}

.sm-checkbox-item-select .icon-remember {
    display: block !important
}

.sm-checkbox-item-bg {
    flex-shrink: 0;
    position: relative;
    width: .37rem;
    height: .37rem;
    border-radius: 50%;
    border: 1px solid #1678ff
}

.sm-checkbox-item .icon-remember {
    display: none;
    width: .37rem;
    height: .37rem;
    position: absolute;
    left: -1px;
    top: -1px
}

.sm-checkbox-item .sm-label {
    margin-left: .11rem;
    display: flex;
    align-items: center
}

.btn-goback {
    position: absolute;
    right: .3rem;
    top: .3rem;
    z-index: 1000;
    color: #fff
}

.btn-goback svg {
    display: block;
    width: .3rem;
    height: .3rem
}

.page-goback {
    position: absolute;
    left: .3rem;
    top: .4rem;
    z-index: 1000;
    color: #fff
}

.page-goback svg {
    display: block;
    width: .6rem;
    height: .6rem
}

#page_bg {
    width: 100%;
    height: 100%;
    height: 100vh;
    width: var(--theme-max-width);
    height: calc(100*var(--vh, 1vh));
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: -1
}

#page_bg.home-bg {
    background: rgba(1, 1, 1, 1)
}

#page_bg.login-bg {
    background: rgba(1, 1, 1, 1)
}

#page_bg.common {
    background: #F9F9F9
}

.form-menu {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 .4rem .2rem .8rem;
    height: 1.15rem;
}

.form-menu .menu-links {
    display: flex;
    align-items: flex-end;
    height: 100%
}

.form-menu .menu-links .menu-link {
    display: block;
    font-size: 0.32rem;
    font-weight: 600;
    height: .68rem;
    line-height: .68rem;
    padding: 0 .34rem;
    color: #606060;
}

.form-menu .menu-links .menu-link.on {
    border-bottom: 2px solid #FFB627;;
    color: #FFB627;
}

.form-menu .form-menu-close .am-icon {
    width: .32rem;
    height: .32rem
}

.login_wrap {
    width: 100%
}

.login_wrap .partner_logo {
    margin-top: .7rem;
    text-align: center
}

.login_wrap .partner_logo img {
    width: 6.3rem
}

.login_wrap .forget_password {
    font-size: .23rem;
    font-weight: 500;
    color: #F38F34;
    text-decoration: underline
}

.login_wrap .form_links {
    display: flex;
    justify-content: space-between;
    margin-top: .46rem
}

.login_wrap .form_links .form_link {
    text-decoration: underline;
    font-size: .26rem;
    color: #1678ff
}

.login_wrap .disclaimer-check {
    color: #545f71;
    font-size: .23rem;
    font-weight: 500;
    margin-top: .3rem
}

.login_wrap .disclaimer-check .policy-link {
    color: #1678ff;
    margin-left: .08rem;
    text-decoration: underline
}

.filter-game-container {
    position: fixed;
    top: 100%;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    -webkit-backdrop-filter: blur(.3rem);
    backdrop-filter: blur(.3rem);
    background-color: rgba(0, 0, 0, .7);
    font-size: .28rem;
    color: #fff;
    z-index: 19999 !important;
    padding: 1.1rem .4rem .8rem;
    overflow: auto;
    overscroll-behavior: none
}

.filter-game-container .search-game {
    width: 100%;
    height: .79rem;
    margin: .3rem 0;
    display: flex;
    align-items: center;
    position: relative
}

.filter-game-container .search-game .search-icon {
    position: absolute;
    left: .3rem;
    top: 50%;
    width: .3rem;
    height: .3rem;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: #fff
}

.filter-game-container .search-game .search-input {
    height: 100%;
    padding-left: .7rem;
    color: #fff;
    border-radius: .18rem;
    background-color: #2f3848;
    font-size: .3rem;
    outline: none;
    border: none;
    -webkit-appearance: none
}

.filter-game-container .search-game .search-input:focus {
    border: .02rem solid #4b4b8b
}

.filter-game-container .search-game .search-input::-webkit-input-placeholder {
    color: #cacaca
}

.filter-game-container .search-game .search-input::placeholder {
    color: #cacaca
}

.filter-game-container .filter_title {
    font-size: .4rem;
    font-weight: 800;
    color: #fff;
    text-align: center;
    margin-bottom: .45rem
}

.filter-game-container .category_title {
    font-size: .34rem;
    color: #fff;
    margin-bottom: .2rem
}

.filter-game-container .close_filter {
    position: absolute;
    right: .4rem;
    top: .6rem;
    width: .5rem;
    height: .5rem;
    color: #000
}

.filter-game-container .gameClassify-box,
.filter-game-container .minbet-box {
    margin-top: .4rem;
    text-align: left
}

.filter-game-container .filter_item {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-column-gap: .16rem;
    grid-row-gap: .3rem
}

.filter-game-container .filter_item span {
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 100%;
    height: .86rem;
    text-align: center;
    color: #fff;
    border-radius: .12rem;
    background-color: #2f3848;
    font-size: .3rem;
    font-weight: 700;
    word-break: break-word
}

.filter-game-container .filter_item span img {
    display: block;
    max-width: 90%;
    max-height: 90%
}

.filter-game-container .filter_item span .color {
    display: none
}

.filter-game-container .filter_item span.classactive {
    color: #fff;
    background-color: #4431c5
}

.filter-game-container .filter_item span.classactive .color {
    display: block
}

.filter-game-container .filter_item span.classactive .white {
    display: none
}

.filter-game-container .filter_submit {
    display: inline-block;
    width: 100%;
    height: .8rem;
    line-height: .8rem;
    margin-top: .4rem;
    border-radius: .12rem;
    background-color: #4431c5;
    border: none;
    text-align: center;
    font-size: .3rem;
    color: #fff;
    margin-bottom: .3rem
}

.filter-game-container .gameType-filter {
    margin-top: .3rem
}

.filter-game-container .reset {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .28rem;
    font-weight: 500;
    text-decoration: underline
}

.filter-game-container.open {
    top: 0;
    transition: all .25s ease-in-out
}

.game-list-item {
    position: relative;
    max-width: 100%;
    overflow: hidden
}

.game-list-item .game-background {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    clip-path: inset(0 round .16rem);
    overflow: hidden
}

.game-list-item .game-background .lazy-load-image-background {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%
}

.game-list-item .game-background .img-loading,
.game-list-item .game-background img {
    display: block;
    width: 100%;
    height: 100%;
    margin: 0 auto
}

.game-list-item .game-vendor-name {
    position: absolute;
    right: 0;
    bottom: 0;
    width: .7rem;
    height: .38rem;
    line-height: .38rem;
    border-radius: .34rem 0 0 0;
    background-image: linear-gradient(180deg, #f1a611, #fdd82d);
    font-size: .22rem;
    font-weight: 500;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.game-list-item .game-fav {
    position: absolute;
    right: 0;
    top: 0;
    width: .45rem;
    height: .45rem;
    border-radius: 0 .16rem 0 .16rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.game-list-item .favorite_icon {
    width: .3rem;
    height: .26rem
}

.game-list-item .game-item-name {
    position: relative;
    width: 100%;
    margin: .12rem 0 0;
    font-weight: 600;
    font-size: 0.18rem;
    color: rgb(201,198,203);
    text-align: left;
    display: block;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    line-height: 1.2
}

@supports(-webkit-line-clamp:2) {
    .game-list-item .game-item-name {
        display: -webkit-box !important;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        white-space: normal
    }
}

.noData {
    width: 100%;
    height: 100%;
    font-size: .3rem
}

[data-theme=light] .noData {
    color: #553a7a !important
}

[data-theme=dark] .noData {
    color: #fff !important
}

.noData .no-data-content {
    text-align: center
}

.noData .no-data-content img {
    width: 3.5rem
}

.noData .no-data-content p {
    margin-top: .2rem
}

.game-list-wrapper.hasScroll {
    width: 100%;
    height: 100%;
    overflow-y: auto
}

.game-list-wrapper .scroll-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 1.4rem
}

.game-list-wrapper .scroll-loading .loading-icon {
    width: .6rem;
    height: .6rem;
    fill: #1678ff
}

.game-title {
    width: 100%;
    margin-bottom: .3rem
}

.game-title .title-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.game-title .title-content .see-all {
    font-size: .2rem;
    font-weight: 500;
    color: #6a778c;
    height: .49rem;
    line-height: .49rem;
    padding: 0 .23rem;
    border-radius: .16rem;
    background-color: #181f2b
}

.game-title .title-text {
    display: flex;
    align-items: center;
    font-size: .26rem;
    font-weight: 600;
    color: #FFB627;
}

.game-title .title-text .game-icon {
    width: .38rem;
    height: .476rem;
    margin-right: .1rem
}

.game-title .title-text .game-icon.rng {
    width: .53rem;
    height: .476rem
}

.game-title .title-text .game-icon.live {
    width: .37rem;
    height: .37rem
}

.game-title .title-text .game-icon.fish {
    width: .43rem;
    height: .43rem
}

.game-title .title-text .game-icon.sports {
    width: .36rem;
    height: .36rem
}

.game-title .title-text .game-icon.pvp {
    width: .394rem;
    height: .381rem
}

.game-title .title-text .game-icon.hot {
    width: .3rem;
    height: .372rem
}

.game-title .title-text span {
    line-height: 1
}

.game-list-center-container {
    width: 100%;
    padding: 0 .2rem;
    padding-bottom: calc(1.39rem + var(--safe-area-inset-bottom))
}

.game-list-center-container .game-select {
    width: 100%;
    margin: .2rem 0 0;
    display: flex;
    flex-direction: column
}

.game-list-center-container .game-title {
    margin-bottom: 0
}

.game-list-center-container .vendor-name {
    font-size: .26rem;
    font-weight: 700;
    text-align: left;
    color: #da394f
}

.game-list-center-container .title-group {
    display: flex;
    flex-direction: row;
    align-items: center
}

.game-list-center-container .title-group .game-title {
    flex: 0 1 0%;
    margin-right: .1rem
}

.game-list-center-container .title-group .title-text {
    white-space: nowrap
}

.game-list-center-container .select-bar {
    display: grid;
    grid-auto-columns: max-content;
    grid-template-rows: 1fr;
    width: 100%;
    overflow-x: auto;
    margin-bottom: .36rem;
    -webkit-overflow-scrolling: touch;
    overflow: -webkit-paged-x;
}

.game-list-center-container .select-bar .select-btn {
    grid-row: 1;
    grid-column: auto;
    position: relative;
    padding: 0 .3rem;
    height: .54rem;
    font-size: .28rem;
    font-weight: 700;
    color: #fff;
    text-align: center;
    border-radius: .14rem;
    transition: all .2s ease-in;
    display: flex;
    align-items: center;
    justify-content: center
}

.game-list-center-container .select-bar .select-btn.on {
    /*background: #FFB627;*/
    background-image: url('/m/menu/plat_on.png');
    background-size: cover;
}

.game-list-center-container .select-bar .select-btn.on .filter-icon {
    fill: #fff;
    stroke: #fff
}

.game-list-center-container .select-type {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between
}

.game-list-center-container .select-type .filter-icon {
    width: .387rem;
    height: .387rem;
    fill: #fff
}

.game-list-center-container .select-type .select-btn {
    width: .58rem;
    height: .58rem;
    grid-row: 1;
    grid-column: auto;
    transition: all .2s ease-in;
    display: flex;
    align-items: center;
    justify-content: center
}

.game-list-center-container .select-type .select-btn.on {
    border-radius: .06rem;
    background-color: #4431c5
}

.game-list-center-container .select-type .sub-group {
    display: grid;
    grid-auto-columns: max-content;
    grid-template-rows: 1fr;
    grid-column-gap: .145rem
}

.game-list-center-container .slots-games {
    position: relative;
    min-height: 9rem
}

.game-list-center-container .slots-games .game-list-wrapper .game-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    -webkit-column-gap: .34rem;
    column-gap: .34rem;
    row-gap: .26rem
}

.fav-gameList-wrap {
    padding: 0 .4rem 2rem
}

.fav-gameList-wrap .game-list-wrapper {
    width: 100%
}

.fav-gameList-wrap .game-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: .22rem .25rem
}

.fav-gameList-wrap .game-more-list-item {
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    top: auto
}

.fav-gameList-wrap .game-more-list-item .game-more-text {
    margin: 0;
    width: 100%;
    height: .6rem;
    border-radius: .3rem;
    border: 1px solid #e2e2e2;
    background-image: linear-gradient(180deg, #fff, #f5f2ff 18%, #eee5ff 51%, #fbf4ff);
    color: #714fef
}

.help-nav {
    position: relative;
    width: 100%
}

.help-nav .nav-item {
    position: relative;
    padding: .26rem 0 .22rem
}

.help-nav .nav-item:not(:last-child) {
    border-bottom: 1px solid #3a4557
}

.help-nav .nav-item .nav-name {
    font-size: .32rem;
    font-weight: 400;
    text-align: left;
    color: #9dabd0;
    line-height: .48rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home-footer-container {
    width: 100%;
    background-color: #181f2b;
    padding: .19rem 0 1.8rem
}

.home-footer-container .home-footer-content {
    width: 100%
}

.home-footer-container .channel-list {
    display: flex;
    justify-content: center;
    gap: .272rem
}

.home-footer-container .channel-list .am-icon {
    width: .818rem;
    height: .818rem;
    border-radius: .2rem
}

.home-footer-container .footer-help {
    margin-top: .11rem
}

.home-footer-container .footer-help .help-content {
    padding: 0 .44rem
}

.home-footer-container .footer-help .help-title {
    margin-bottom: .32rem;
    font-size: .28rem;
    line-height: 1.68;
    text-align: left;
    color: #7d90dc
}

.home-footer-container .dash-line {
    height: 0;
    border-bottom: 1px dashed #828eb4
}

.home-footer-container .payment-affiliate {
    margin-top: .4rem;
    display: flex;
    align-items: center
}

.home-footer-container .payment-affiliate .payment-icon {
    display: block;
    margin-right: .35rem
}

.home-footer-container .payment-affiliate .payment-icon:first-child {
    width: 2.06rem
}

.home-footer-container .payment-affiliate .payment-icon:nth-child(2) {
    width: 1.08rem
}

.home-footer-container .payment-affiliate .payment-icon:nth-child(3) {
    width: 2.14rem
}

.home-footer-container .footer-pagcor {
    padding: .2rem .26rem 0
}

.home-footer-container .footer-pagcor .img-title {
    text-align: center
}

.home-footer-container .footer-pagcor .img-title img {
    height: .8rem
}

.home-footer-container .footer-pagcor .info-text {
    margin-bottom: .39rem;
    font-size: .25rem;
    text-align: left;
    color: #5f677e
}

.home-footer-container .game-provider {
    padding: 0 .4rem;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: .5rem .13rem;
    width: 100%;
}

.home-footer-container .game-provider .vendor-img {
    width: 100%
}

.home-footer-container .footer-copyright {
    padding: .26rem;
    font-size: .24rem;
    font-weight: 500;
    color: #b2b2b2;
    text-align: center
}

.home-game-list {
    padding: 0 .2rem;
    margin-bottom: .5rem
}

.home-game-list .game-list-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: .23rem .3rem
}

.hot-game-container {
    width: 100%;
    margin: 0 0 .4rem
}

.hot-game-container .hot-game-content {
    padding: 0 .39rem
}

.hot-game-container .more-hot-game {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: .16rem;
    background-color: #1678ff;
    padding: .15rem .2rem;
    font-size: .2rem;
    color: #fff;
    font-weight: 700;
    width: 50%;
    margin: .3rem auto 0
}

.hot-game-list {
    position: relative;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: .2rem .2rem
}

.hot-game-list .game-list-item .img-loading {
    width: 100%;
    aspect-ratio: 1/1;
    border-radius: .2rem
}

.gameList-wrap .game-list {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: .2rem
}

.gameList-wrap .game-list .new-game-wrap {
    width: 1.184rem;
    height: 1.184rem;
    flex-shrink: 0;
    margin-right: .2rem
}

.gameList-wrap .game-list .img-loading {
    width: 100%;
    aspect-ratio: 1/1;
    border-radius: .2rem
}

.vendor-game-item {
    margin-right : .25rem;
    flex-shrink: 0;
    overflow: hidden
}

.vendor-game-item .vendor-game-img {
    position: relative;
    overflow: hidden;
    clip-path: inset(0 round .16rem);
}

.vendor-game-item .vendor-game-img .img-loading {
    height: 2.85rem;
    -o-object-fit: cover;
    object-fit: cover;
}

.vendor-game-item .vendor-game-name {
    margin-top: .1rem;
    font-size: .24rem;
    font-weight: 500;
    color: #fff
}

.vendor-game-wrap {
    width: 100%
}

.vendor-game-wrap.home-vendor {
    padding-bottom: 2rem
}

.vendor-game-wrap .vendor-game-scroll {
    position: relative;
    width: 100%
}

.vendor-game-wrap .vendor-game-list {
    position: relative;
    display: flex;
    width: 100%;
    overflow: auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /*grid-gap: .22rem .25rem*/
}

.vendor-game-wrap .vendor-game-list .more-game {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    font-size: .24rem;
    font-weight: 700;
    color: #56ceff;
    text-align: center;
    text-transform: uppercase
}

.vendor-game-wrap .vendor-game-list .more-game svg {
    display: block;
    margin: 0 auto .2rem;
    width: .67rem;
    height: .15rem
}

.scroll-menu-wrap {
    position: relative;
    display: flex;
    width: 100%;
    height: .8rem;
    overflow: hidden
}

.scroll-menu-wrap .menu-fav {
    position: relative;
    width: .6rem;
    flex-shrink: 0;
    height: .6rem;
    margin-right: .1rem
}

.scroll-menu-wrap .menu-fav svg {
    display: block;
    width: .5rem;
    height: .5rem;
    margin: 0 auto
}

.scroll-menu-wrap .menu-fav.on:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -.2rem;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: .3rem;
    height: .14rem;
    border-radius: .07rem;
    background-color: #1678ff
}

.scroll-menu-wrap .menu-swiper-box {
    position: relative;
    flex: 1 1 0%;
    width: 100%;
    height: 100%;
    overflow-y: hidden;
    overflow-x: auto
}

.scroll-menu-wrap .menu-swiper-container {
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    width: -webkit-max-content;
    width: max-content;
    height: .6rem
}

.scroll-menu-wrap .menu-list-item {
    display: flex;
    align-items: center;
    height: 100%;
    line-height: 1;
    position: relative;
    flex-shrink: 0;
    margin-right: .2rem;
    font-size: .24rem;
    font-weight: 500;
    color: #fff
}

.scroll-menu-wrap .menu-list-item .menu-icon-wrap .menu-icon {
    display: block;
    min-width: .6rem;
    height: .6rem
}

.scroll-menu-wrap .menu-list-item .menu-icon-wrap .menu-icon.color,
.scroll-menu-wrap .menu-list-item.on .menu-icon-wrap .menu-icon {
    display: none
}

.scroll-menu-wrap .menu-list-item.on .menu-icon-wrap .menu-icon.color {
    display: block
}

.scroll-menu-wrap .menu-list-item.on:after {
    content: "";
    position: absolute;
    left: 50%;
    bottom: -.2rem;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: .3rem;
    height: .14rem;
    border-radius: .07rem;
    background-color: #1678ff
}

.scroll-menu-wrap .menu-list-item .icon-all {
    width: .32rem;
    height: .32rem;
    margin-right: .1rem
}

.more-games {
    width: 100%;
    margin-top: .2rem;
    text-align: right
}

.more-games .see-all {
    font-size: .24rem;
    font-weight: 700;
    color: #c7ad64;
    text-decoration: underline
}

.vendor-game-wrap {
    width: 100%
}

.vendor-game-wrap .vendor-game-scroll {
    position: relative;
    width: 100%
}

.vendor-game-wrap .vendor-game-list {
    position: relative;
    padding: 0 .4rem
}

.vendor-game-wrap .vendor-game-list.home-page {
    display: flex;
    padding: 0
}

.vendor-game-wrap .vendor-game-list.home-page .vendor-game-img {
    width: 2.07rem;
    height: 2.85rem
}

.vendor-game-wrap .vendor-game-list .more-game {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    font-size: .24rem;
    font-weight: 700;
    color: #56ceff;
    text-align: center;
    text-transform: uppercase
}

.vendor-game-wrap .vendor-game-list .more-game svg {
    display: block;
    margin: 0 auto .2rem;
    width: .67rem;
    height: .15rem
}

.winner-board {
    width: 100%;
    margin-top: .52rem;
    position: relative;
    padding: 0 .3rem
}

.winner-board .winner-content {
    position: relative;
    width: 100%;
    height: 9.11rem;
    border-radius: .18rem;
    background-color: #151b25;
    padding-top: .32rem;
    display: flex;
    flex-direction: column;
    align-items: center
}

.winner-board .winner-content .winner-title {
    font-size: .3rem;
    font-weight: 600;
    color: #fff;
    height: .68rem;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 .72rem;
    border-radius: .14rem;
    background-color: #181f2b;
    margin-bottom: .23rem
}

.winner-board .winner-content .winner-head {
    height: .5rem;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: .3rem;
    font-weight: 400;
    color: #5c677a;
    margin-bottom: .35rem
}

.winner-board .winner-content .winner-head img {
    display: block;
    width: 3.78rem
}

.winner-board .winner-content .winner-wrap {
    width: 100%
}

.winner-board .winner-content .winner-swiper {
    width: 100%;
    height: 4.8rem;
    overflow: hidden
}

.winner-board .winner-content .winner-swiper .swiper-wrapper {
    height: 100%;
    transition-timing-function: linear
}

.winner-board .winner-content .winner-list {
    position: relative;
    z-index: 1
}

.winner-board .winner-content .winner-list .winner-item {
    width: 100%;
    height: .78rem;
    margin-bottom: .1rem
}

.winner-board .winner-content .winner-list .swiper-inner {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: .23rem;
    font-weight: 600;
    color: #fff
}

.winner-board .winner-content .winner-list .winner-rank {
    display: block;
    width: .4rem;
    height: .4rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.winner-board .winner-content .winner-list .rank-1 {
    font-size: 0;
    background: url(rank-1.d1df2763.png) no-repeat 50%/contain
}

.winner-board .winner-content .winner-list .rank-2 {
    font-size: 0;
    background: url(rank-2.479bea1f.png) no-repeat 50%/contain
}

.winner-board .winner-content .winner-list .rank-3 {
    font-size: 0;
    background: url(rank-3.d6750435.png) no-repeat 50%/contain
}

.winner-board .winner-content .winner-list .winner-info {
    padding-left: .1rem;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.winner-board .winner-content .winner-list .win-game {
    width: 2.5rem;
    padding-top: .07rem;
    overflow: hidden
}

.winner-board .winner-content .winner-list .win-game .winner-name {
    line-height: 1;
    padding-right: .1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.winner-board .winner-content .winner-list .win-game .btn-game {
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    white-space: nowrap;
    color: #fdd82d
}

.winner-board .winner-content .winner-list .win-game .btn-game span {
    position: relative;
    padding-right: .1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.winner-board .winner-content .winner-list .winner-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: .27rem
}

.winner-board .winner-content .winner-list .winner-amount span {
    flex: 1 1 0%;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.game-enter-container {
    width: 100%
}

.game-enter-container .game-enter-content {
    width: 100%;
    transition: all .5s
}

.game-enter-container .game-enter-content .content-bg,
.game-enter-container .game-enter-content .content-bg.home {
    margin-top: .2rem
}

.game-enter-container .game-enter-content .home-game-container {
    margin-bottom: .304rem;
    padding: 0 .2rem
}

.game-enter-container .game-enter-content .home-game-container.recent {
    padding: 0 0 0 .39rem
}

.game-enter-container .game-enter-content .home-game-container .hot-game-list {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    padding-left: .26rem;
    overflow-x: auto;
    overflow-y: hidden
}

.game-enter-container .game-enter-content .home-game-container .hot-game-list .game-list-item {
    flex-shrink: 0;
    width: 2rem;
    margin-right: .21rem
}

.game-enter-container .game-enter-content .game-menu-list {
    display: flex;
    width: calc(100% - .78rem);
    overflow: auto;
    padding-bottom: .2rem;
    margin: 0 auto
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item {
    width: 1.16rem;
    height: 1.1rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: .18rem;
    background-color: #181f2b;
    flex-shrink: 0
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item:not(:first-child) {
    margin-left: .3rem
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item.on {
    background-color: #1678ff
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon {
    width: .55rem;
    height: .55rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-fish {
    width: .443rem;
    height: .365rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFkAAABJCAMAAABhA3oGAAAAk1BMVEVHcEz/3kb/ygD/yAL+ry7/iFb/30b/wCr/vjb/xgD/0CP/xwD/ygD45Nj/mUX8rYv68u7/30b/vQ//iFf78eb/3kf648P/h1X/iFb+vwD/3kb37+r/9PT/zQD+qyL+2G37vKL/7dD+k2X+yR750b/+4ZH+zUL/57P9nnf/l0QAMHH+zC4KGzicXEpPWVKXhzLGoRuyiv89AAAAGHRSTlMA3HupXuWCIBDaQL/v+jb+LKOWvbW/dpAeKRmbAAADp0lEQVRYw7WZ64KiMAyFVe6K99uAVkBUdNxx9v2fbgvFkjatAovn15DKRzhNCnR6vUYa2v0wDNf2sNethjmWae10yHXtEMruDtwPw4+ghzL4v9CuPe/3++u5bduYS9V2Hp15+Eb9dmA7fC+zDXheAxyuPwUOQ+cjVrQqD6cmOJx/yIvmRutSjqLzObukue7t6k7lcpQl+0IBF8U3JaOGi0oqVRZA+UazPpS4aVCB91EgKh1vZi1tpmdfKvD+IpEzGlvNWpCLs8+ALIEjFt04DckpOx2YkUlesOg58GotIa6QMD+9ZAhi10zyPw2ndm2kz9P1NjMvEvbLOmnPQcKSzXdFyvxqA4yaiFVp0gpG5ysmMJINGsmOjPLgYgiMPsOZAuAEp5ykIOKJ6AVvKLMcWEE/73tNaVwUrSOgTTgyMnvubCUQ4AQKGOZ/GmjRXvA6kUhHThRlCNGmOFAkssOp4aK7K7udol0wfdI6ICaSaYouQjmUMspWlsKamy6VyveSKchlXUtmXPCs6Mo5US2qTENYcvpb3GvIO7XN3OqRgpzpyDsUvyvJhdWeojTOOnKCSgME/v4FB3R18hWlESmWYES+SOTH9/dD8CPoiPz7TfUL6+MgipGFECSDMGtNfvjIyY9qeNH7EnXaUU1h5LgDAvGrGPj5Q/VTDW97loIc1yGz+JEf5+Rq1HLek780ZBa/Vsc/IOMvWhtbkRznPz9pyUcQH+MkuJZ5dyvI41rkE04CeEEfgWLsigAst1LX10kAL+gzTzT6iAAst1LT10nwuii0FKPYPUiOURIKo63nG4GiOMbI+lInfEm1yYVEO6ZqP1XkGDsngIXqsJaT2WyTCPc4BeQxnhOpOqwJeC1YcqyZr9gTn0RwYq66siuTnmrBrDys7YQ9cx2fEBLFmva+4qTHR6UVpSFmFfFIroumoGM83SBmvXrTNQlTcHtbdvx2nsGl++oN1yAIrS0OPnStOk8vj3AdFMUh99yJo8W5e00mweHNFFZj06377n1/RKAON3m1k7uZ3VCd77YBIYh90htdWF3ve9D1CWLHusdKUWm1v2HlpKnSKNFN4W3h1v/qVqDJYLZZjeUl+nY4ENJokwqhfXa6Eeb7G9GNir5QBGzMbbZzYgjc59epp7gZo/FG0mDkl9iBqyr1p1ptM7rOxHF1TVRq1M2mq4Lc0S60gUumo43iwYe8wO3puV2RpaQ7BIsL4aJLMP3G4xZ3/X8P2p4e8b1FDe4/RR+PY0xvK7YAAAAASUVORK5CYII=) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-rng {
    width: .55rem;
    height: .31rem;
    background: url(icon-rng.a61bae53.png) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-pvp {
    width: .394rem;
    height: .381rem;
    background: url(icon-pvp.9fc3a68b.png) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-sports {
    width: .37rem;
    height: .37rem;
    background: url(icon-sports.301e1701.png) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-live {
    width: .32rem;
    height: .34rem;
    background: url(data:image/png;base64,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) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-esports {
    width: .36rem;
    height: .32rem;
    background: url(icon-esports.8427fd6a.png) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-cockfight {
    width: .37rem;
    height: .4rem;
    background: url(icon-cockfight.458907ea.png) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-home {
    width: .41rem;
    height: .37rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFIAAABLCAMAAADUKyD6AAAAS1BMVEVHcEzfMmDp9vrq9vzfNWDq9/rq9vvfN2DfMmDfMmDo8PXfNGDfM2DfMmDp9vrDRSrNcl/n0dzl4uPkk63gPmrbtKzSiXrHWEDXopjpQBvyAAAADXRSTlMAgtseMLlaHd/vgEC5jYAbOgAAAZdJREFUWMPt11l2wyAMBVDjeZ5aD/tfadOQpA4IgYS+evIWcANIJihJ/lGqvOtkQdUMt6hKTqwHnbqSFoehyaVFIfNNFDENUcC0xGgTECNNUIwyHWKE6RTZJiIyTVRkmR6RYXpFsgmIX99RJiSOS4wJimOM6RAjTKfINhGRaaIiy/SIDNMrks0AkWgGiSQzUCSYwWKwSRADTZIYZBLFAJMsek2G6DFZIm4qlgiazzdtwxMhs9di5xfXZ7xmBZKmuO7H9Mq84aaeO3JcPKf3HCtmarJqKKLHzO2Km+I22Tnce6/tvrQqcwDkdDpNZfZ6Y4mLNvZN59S/MLvq3l8+SXVDa1UVI3iS+1/1tWk0U/tYEjQRWuR+Fy6NM0Nkmai+7xU4uLJJdz7khzS+lnUSIqdjfuQQI83EkAsoGtcGjQRuYPMO9pCt/S+4zWZOUxwzhCxHVrC3esYSC/T9X3DIEiU5O08zfPBJ6WTrmaVK8UWCfRRzkveip7Lbpptt0ECeFdLi73mmUpW5LjQALcqElhJV06KlgjH5AeAivqUCkD+iAAAAAElFTkSuQmCC) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-icon i.icon-fav {
    width: .37rem;
    height: .32rem;
    background: url(data:image/png;base64,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) no-repeat 50%/cover
}

.game-enter-container .game-enter-content .game-menu-list .game-menu-item .game-menu-name {
    font-size: .23rem;
    font-weight: 500;
    color: #fff;
    text-align: center;
    height: .46rem;
    display: flex;
    align-items: center
}

.notice_root .notice_bg {
    position: relative;
    width: 100%;
    height: .7rem;
    line-height: 1;
    padding: 0 .26rem;
    display: flex;
    align-items: center;
    z-index: 2
}

.notice_root .notice_icon {
    width: .36rem;
    height: .36rem;
    margin-right: .1rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAMAAADW3miqAAAAnFBMVEVHcEz/wyr/5kT/uB7/zyv/wS3/cFb/i27/dlP/9GX/Zk3/i23/fGD/i23/xSX/yyr/phb/Zk3/zyv/Zk3/i27/i23/ZUz/wi3/82L/kwD/qBv/im3/Zk7/sh//xiz/Zk3/ZU3/i23/5kT/4ET/4kH/nA7/lAD/qBr/wC3/wS3/zyz/5kX/9Wb/qBr/kwD/4Uv/Z0//0zn/0TP/tB8D/PPYAAAAKXRSTlMAKL882uQh/gq+8ps/XBuZNqX2w/DcU/ja9O6420q6i2vH6GvSk76yxxCigREAAAFcSURBVDjL7dTbcoIwEAbgjbRJDMhBAc+i9dTQQtP6/u/WDUk1gjrtff8LJ8x+7i5xRgCdIPXBZj+b7rk9FyKAcwLG5vZ4rKq6PtiHkVJnRVOW2S+HlUY1MU98pAS1KGMbDoGvHTEo1iIJtFoY4+s+QcoGeI5PUp6qysNjrkQOfKGSZhjDMk/1VmFUYiQmCgESPStXijaN5tps0AzLHzQeE7234EiTplEAA8YoeMY0CNkSqFJr/UGxnnGe4URijUUSe+FaHLdawxzr2IhDVF4juQIuVI5yBCnWfeYDKdtIemYhIWDg64uil0YXFOFCeE3F2twUvtqqi17xBQtw89RFz9DOP/oVCoddJEkLXX46B62ujVfeQnJ5Z9gVGrsDtx9OPp1sHdR/d/LmpOegyT308ld0d9zOQaRvMtGLf2F2PRMPuokr84dR3yqeMzNoyh+h2KAlPEx8nE0P7WHfkJxpM2dtTREAAAAASUVORK5CYII=) no-repeat 50%/cover
}

.notice_root .marquee {
    overflow: hidden;
    position: relative;
    flex: 1 1 0%;
    display: flex;
    font-size: .24rem;
    color: #fff
}

.notice_root .marquee .marquee_content {
    white-space: nowrap;
    position: relative;
    will-change: right
}

.notice_root .marquee .marquee_content.empty {
    width: 100%;
    margin-left: 0;
    text-align: center;
    will-change: auto
}

.notice_root .marquee .content-item {
    margin-left: .4rem;
    vertical-align: middle
}

.notice_root .marquee table,
.notice_root .marquee tbody,
.notice_root .marquee td,
.notice_root .marquee tfoot,
.notice_root .marquee th,
.notice_root .marquee thead,
.notice_root .marquee tr,
.notice_root .marquee tt {
    border: 0 !important
}

#mc-animate-container .loading-wrap {
    display: flex;
    align-items: center
}

[data-theme=light] .outter-app {
    background: #957bf1 !important
}

[data-theme=dark] .outter-app {
    background: #181f2b !important
}

.home-container {
    width: 100%;
    background: #050304;
    /*min-height: 100vh;*/
    max-height: 100vh;
    transition: all .5s
}

.home-container svg {
    display: block
}

.home-container #mc-app-home-root,
.home-container svg {
    width: 100%;
    height: 100%
}

.home-container .banner_notice {
    position: relative;
    width: 100%;
    flex-shrink: 0
}

.home-container .banner_notice .home-banner {
    min-height: 2rem
}

/*.home-container .home-content {
    padding: .1rem 0 .25rem
}*/

.home-container .language-wrap {
    position: relative;
    width: 100%;
    height: 1.8rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.home-container .home-section {
    margin-top: .6rem
}

.shell-download-bar.show-bar+.home-header .header-content {
    top: 1.39rem
}

.shell-download-bar.show-bar+.home-header+.home-container {
    padding-top: 1.39rem
}

.shell-download-bar.show-bar~.sidebar .left-side-menu {
    top: 2.78rem;
    height: calc(var(--vh, 1vh)*100 - 1.39rem - 1.39rem - 1.39rem)
}

.home-container .popup_container_v2 {
    background-color: rgba(0, 0, 0, .5)
}

.home-container .popup_container_v2 .close-btn {
    background-color: hsla(0, 0%, 100%, .2)
}

.home-container .popup_container_v2 .popup_title {
    color: #fff
}

.home-container .popup_container_v2 .popup_content {
    border-radius: .12rem;
    box-shadow: 0 .03rem .06rem 0 rgba(0, 0, 0, .4);
    background-color: #1b2132
}

.home-container .popup_container_v2 .text {
    color: #fff
}

.home-container .popup_container_v2 .popup_body .active-btn {
    background: #1678ff;
    box-shadow: none
}

.private-route-footer {
    padding-bottom: 1.39rem
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-picker-col {
    display: block;
    position: relative;
    height: 4.76rem;
    overflow: hidden;
    width: 100%
}

.am-picker-col-content {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    z-index: 1;
    padding: 2.04rem 0
}

.am-picker-col-item {
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    text-align: center;
    font-size: .32rem;
    height: .68rem;
    line-height: .68rem;
    color: #000;
    white-space: nowrap;
    text-overflow: ellipsis
}

.am-picker-col-item-selected {
    font-size: .34rem
}

.am-picker-col-mask {
    top: 0;
    height: 100%;
    margin: 0 auto;
    background-image: linear-gradient(180deg, hsla(0, 0%, 100%, .95), hsla(0, 0%, 100%, .6)), linear-gradient(0deg, hsla(0, 0%, 100%, .95), hsla(0, 0%, 100%, .6));
    background-position: top, bottom;
    background-size: 100% 2.04rem;
    background-repeat: no-repeat
}

.am-picker-col-indicator,
.am-picker-col-mask {
    position: absolute;
    left: 0;
    width: 100%;
    z-index: 3
}

.am-picker-col-indicator {
    box-sizing: border-box;
    height: .68rem;
    top: 2.04rem;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd
}

.am-picker {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center
}

.am-picker-item {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%;
    text-align: center
}

.hairline-remove-right-bottom {
    border-bottom: 0
}

.hairline-remove-right-bottom:after {
    display: none
}

.hairline-remove-right-bottom-bak:after {
    display: none
}

.hairline-remove-left-top:before {
    display: none
}

.am-picker-popup {
    left: 0;
    bottom: 0;
    position: fixed;
    width: 100%;
    background-color: #fff
}

.am-picker-popup-wrap {
    overflow: auto;
    -webkit-overflow-scrolling: touch;
    outline: 0
}

.am-picker-popup-mask,
.am-picker-popup-wrap {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000
}

.am-picker-popup-mask {
    background-color: rgba(0, 0, 0, .4);
    height: 100%
}

.am-picker-popup-mask-hidden {
    display: none
}

.am-picker-popup-header {
    background-image: linear-gradient(180deg, #e7e7e7, #e7e7e7, transparent, transparent);
    background-position: bottom;
    background-size: 100% 1px;
    background-repeat: no-repeat;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative
}

.am-picker-popup-header:after {
    display: block;
    position: absolute;
    content: "";
    left: 0;
    bottom: 0;
    right: auto;
    top: auto;
    width: 100%;
    border-bottom: 1px solid #ddd
}

.am-picker-popup-header .am-picker-popup-header-right {
    text-align: right
}

.am-picker-popup-item {
    color: #108ee9;
    font-size: .34rem;
    padding: .18rem .3rem;
    height: .84rem;
    box-sizing: border-box;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center
}

.am-picker-popup-item-active {
    background-color: #ddd
}

.am-picker-popup-title {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%;
    text-align: center;
    color: #000
}

.am-picker-popup .am-picker-popup-close {
    display: none
}

.am-picker-col {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1 1 0%
}

.register_wrap {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 1.15rem .3rem .6rem
}

.register_wrap .form-btns {
    margin-top: .8rem;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.register_wrap .form-btns .register-btn {
    margin: 0 !important;
    width: 3.66rem !important
}

.register_wrap .form-btns .btn-reset {
    width: 1.72rem;
    height: 1rem;
    line-height: 1rem;
    text-align: center;
    font-size: .32rem;
    border-radius: .3rem;
    border-style: solid;
    border-width: 1px
}

.register_wrap .disclaimer-check {
    color: #545f71;
    font-size: .23rem;
    font-weight: 500;
    margin-top: .3rem;
    display: flex;
    align-items: center
}

.register_wrap .disclaimer-check .sm-checkbox-item {
    width: .8rem
}

.register_wrap .disclaimer-check .policy-link {
    color: #1678ff;
    margin-left: .08rem;
    text-decoration: underline
}

.register_wrap .policy-error {
    color: #ff6969;
    font-size: .24rem;
    margin-left: .5rem
}

.register_wrap input[name=birthday] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    min-height: 1.2em
}

.register_wrap input[name=birthday]::-webkit-datetime-edit {
    display: block;
    padding: 0
}

.register_wrap input[name=birthday]::-webkit-calendar-picker-indicator,
.register_wrap input[name=birthday]::-webkit-inner-spin-button {
    display: none
}

.register_wrap input[name=birthday]::-webkit-datetime-edit-fields-wrapper {
    color: #707070
}

.register_wrap input[name=birthday]::-webkit-datetime-edit {
    flex: 1 1 0%;
    -webkit-user-modify: read-only !important;
    display: inline-block;
    min-width: 0;
    overflow: hidden
}

.prev-route-container .prev-route-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.39rem;
    position: relative;
    background-color: #181f2b
}

.prev-route-container .prev-route-content .header-left {
    position: absolute;
    width: 1.2rem;
    height: 100%;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: center
}

.prev-route-container .prev-route-content .header-left .left-arrow {
    fill: #fff;
    width: .22rem;
    height: .41rem
}

.prev-route-container .prev-route-content .route-nav-item {
    font-size: .4rem;
    font-weight: 600;
    color: #555f7b
}

.prev-route-container .prev-route-content .route-nav-item.current {
    color: #fff
}

.prev-route-container .prev-route-content .am-icon-arrow-right {
    width: .14rem;
    height: .25rem;
    margin: 0 .2rem
}

.activity-detail-container {
    width: 100%;
    padding: .25rem .3rem 1rem
}

.activity-detail-container .activity-detail-content {
    width: 100%
}

.activity-detail-container .activity-detail-content .activity-info {
    padding-bottom: .3rem
}

.activity-detail-container .activity-detail-content .activity-desc {
    padding: 0 .26rem;
    font-size: .3rem;
    color: #fff
}

.activity-detail-container .activity-detail-content .activity-desc img {
    max-width: 100%
}

.activity-detail-container .activity-detail-content .activity-title {
    padding: 0 .26rem;
    margin-top: .3rem;
    font-size: .32rem;
    font-weight: 600;
    color: #fff;
    text-align: left
}

.activity-detail-container .activity-detail-content .activity-img {
    width: 100%
}

.activity-detail-container .activity-detail-content .activity-img img {
    display: block;
    width: 100%;
    border-radius: .15rem
}

.activity_container {
    padding-bottom: .5rem;
    overflow-y: auto
}

.activity_container .activity_title {
    width: 100%;
    z-index: 5;
    overflow: hidden
}

.activity_container .my-activity {
    padding: .35rem .38rem
}

.activity_container .activity_header {
    font-size: .32rem;
    text-align: center;
    height: .88rem;
    line-height: .88rem;
    color: #414655;
    background-color: #fff
}

.activity_container .promo_menu {
    position: relative;
    width: 100%;
    padding-left: .3rem;
    margin: .37rem 0;
    overflow-x: auto;
    overflow-y: hidden
}

.activity_container .promo_menu .promo_menu_list {
    width: -webkit-max-content;
    width: max-content;
    display: flex;
    flex-wrap: nowrap
}

.activity_container .promo_menu .promo_menu_item {
    text-align: center;
    height: .44rem;
    flex-shrink: 0;
    margin-right: .55rem;
    position: relative;
    font-size: .3rem;
    font-weight: 500;
    color: #fff
}

.activity_container .promo_menu .promo_menu_item .promo_border {
    position: absolute;
    left: 50%;
    bottom: 0;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: .5rem;
    height: .03rem;
    border-radius: .03rem;
    background-color: transparent
}

.activity_container .promo_menu .promo_menu_item.on {
    color: #1678ff;
    border-bottom: .04rem solid #1678ff
}

.activity_container .promo_menu .promo_menu_item.on .promo_border {
    background-color: #fff
}

.activity_container .activity_box {
    position: relative;
    overflow: auto;
    height: 100%;
    display: flex;
    flex-direction: column
}

.activity_container .activity_content {
    width: 100%;
    padding: 0 .3rem 1rem
}

.activity_container .activity_content .promo_list {
    margin-bottom: .4rem;
    border-radius: .1rem
}

.activity_container .activity_content .promo_list img {
    max-width: 100%
}

.activity_container .activity_content .am-accordion-item {
    overflow: hidden;
    background-color: #131723
}

.activity_container .activity_content .am-accordion-item .am-accordion-content {
    background: none
}

.activity_container .activity_content .am-accordion-item .am-accordion-content .am-list-body {
    border: none;
    border-radius: 0 0 .12rem .12rem;
    background: none !important
}

.activity_container .activity_content .am-accordion-item .am-accordion-content .am-list-body:after {
    display: none
}

.activity_container .activity_content .am-accordion-item .am-accordion-content .am-accordion-content-box:after {
    display: none
}

.activity_container .activity_content .am-accordion {
    border: none
}

.activity_container .activity_content .am-accordion-header {
    padding: 0 !important;
    height: auto !important;
    background-color: transparent !important
}

.activity_container .activity_content .am-accordion-header:after {
    display: none
}

.activity_container .activity_content .am-accordion-header i.arrow {
    display: none !important
}

.activity_container .activity_content .promo_item {
    position: relative;
    width: 100%;
    margin-bottom: .24rem;
    border-radius: .15rem;
    background-image: linear-gradient(93deg, #ce392f 14%, #451c9d 83%)
}

.activity_container .activity_content .promo_item .activity_type {
    position: absolute;
    top: .04rem;
    left: -.08rem;
    z-index: 99;
    height: .98rem
}

.activity_container .activity_content .promo_item .activity_img {
    width: 100%;
    overflow: hidden;
    /*border-radius: .12rem .12rem 0 0;*/
    border-radius: .12rem;
    position: relative
}

.activity_container .activity_content .promo_item .activity_img img {
    display: block;
    width: 100%;
    min-height: 2rem
}

.activity_container .activity_content .promo_item .activity_info {
    height: .68rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 .139rem 0 .18rem
}

.activity_container .activity_content .promo_item .activity_info .item-title {
    font-size: .28rem;
    font-weight: 600;
    color: #fff;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.activity_container .activity_content .promo_item .activity_info .icon-more {
    width: 1.281rem;
    height: .51rem
}

.activity_container .activity_content .promo_item .activity_info .promo-end-date {
    display: flex;
    justify-content: space-between;
    margin-top: .15rem
}

.activity_container .activity_content .promo_item .activity_info .promo-end-date .section-count.day {
    margin-right: .53rem
}

.activity_container .activity_content .promo_item .activity_info .promo-end-date .promo-no-expiry {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.8rem;
    height: .63rem;
    border-radius: .1rem;
    font-size: .26rem;
    font-weight: 900;
    color: #fff;
    text-align: center;
    background-color: #4b4b8b
}

.activity_container .activity_content .promo_item .activity_info .more-btn {
    min-width: 1.69rem;
    height: .63rem;
    line-height: .63rem;
    padding: 0 .2rem;
    border-radius: .2rem;
    font-size: .24rem;
    font-weight: 900;
    color: #000;
    text-align: center;
    box-shadow: inset 0 0 .06rem 0 #ffb206;
    background-image: linear-gradient(180deg, #fff79d, #ffd800)
}

.activity_container .activity_content .promo_item .activity_info .promo-down {
    width: .5rem;
    height: .48rem;
    transition: all .2s;
    color: #fff
}

.activity_container .activity_content .promo_item .activity_info .promo-down svg {
    display: block;
    width: 100%;
    height: 100%
}

.activity_container .activity_content .am-accordion-item-active .activity_info .promo-down {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg)
}

[data-theme=light] .activity_container .activity_content .am-accordion-item-active .activity_info .promo-down svg {
    fill: url(#footer-gradient2) !important
}

[data-theme=dark] .activity_container .activity_content .am-accordion-item-active .activity_info .promo-down svg {
    fill: url(#footer-gradient4) !important
}

.activity_container .activity_content .wysiwyg {
    padding: .3rem;
    font-size: .24rem;
    color: #8d8dd8
}

.container_activity table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt {
    border: 1px solid #fff
}

.agent-home-container {
    padding-bottom: 1rem
}

.agent-home-container .agent-home-content {
    transition: all .2s
}

.agent-home-container .msg-count {
    display: flex;
    align-items: center;
    justify-content: center;
    width: .36rem;
    height: .36rem;
    border-radius: 50%;
    background-image: linear-gradient(#f55e49, #f55e49), linear-gradient(#ed6b65, #ed6b65);
    box-shadow: 0 .03rem .08rem 0 rgba(235, 72, 97, .5);
    border-radius: .18rem;
    font-size: .24rem;
    color: #fff;
    position: absolute;
    right: 0;
    top: 0
}

.agent-home-container .agent-home-top {
    width: 100%;
    height: 5rem;
    background-image: linear-gradient(90deg, #62aff8, #7cc7f2), linear-gradient(#fff, #fff)
}

.agent-home-container .agent-home-header {
    height: 1rem;
    padding: 0 .34rem;
    display: flex;
    align-items: center;
    justify-content: space-between
}

.agent-home-container .agent-home-header .header-left {
    display: flex;
    align-items: center;
    position: relative;
    font-size: .28rem;
    color: #fff;
    line-height: 1
}

.agent-home-container .agent-home-header .header-left:before {
    display: block;
    content: "";
    width: .37rem;
    height: .43rem;
    margin-right: .2rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACUAAAArCAMAAADISrFBAAAAQlBMVEVHcEz////////////////////////////////////////////////////////////////////////////////////1bZCPAAAAFXRSTlMAjBe27yXC9wzhNIFQ00Jpp5tyW3VbP5t3AAABAUlEQVQ4y42U2RrEEAxG7WvRLe//qlOdzRDDufQdTcSvhJSYxC2ADtSRPruAN1x2HMOhQERcUvCDXjBrhQqBFI3QwFtLtRY0J2WIBLS2FsxSzagwy9aWxyxRW2nqWxGzmlEYjVj7ePTXHbHGkno8LmwWwWDXTasDdhK2l0UV62VVfnJoU8eIebNMG1erv3PKYlXTrVcxTct2Dc1Lq2v7tt8QL7Yex1ac3jtDjPPhu7Q9pQP+c9wNiIElcrsnjDgvyw6tK2YSxkg8pBWpHEOXDX2t7YuzE5YlesLSBGaYtOYqhgkr4D+ICk/YuGR+vcfQugO2/A+YeMXcnKonCnXmDD4AYzFWNhIKJz0AAAAASUVORK5CYII=) no-repeat 50%/cover
}

.agent-home-container .agent-home-header .header-left .agent-username {
    margin-left: .1rem
}

.agent-home-container .agent-home-header .agent-service {
    width: .46rem;
    height: .47rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAvCAMAAACrOwigAAAAS1BMVEVHcEz////////////////////////////////////////////////////////////////////////////////////////////////ScsZwAAAAGHRSTlMAUMGO96/XBe5+b+Q4Hg0rFc2aWaJDYkdG3xhcAAACCklEQVRIx42V2WKEIAxF2TfFcZf//9ImqBUVmMlDbeshCTeLhDyso5w1IQSjZvchX6yVIiSmXFeDWXiacboAa364nO1KJ8fZHkjlU/oofNlYf11jitHElEvE5EIv0Yd7+0aaRc/95qyFfGIS2ub4HrUbwbW36rrmvMArilfY7rhEGuD5IcwABzbgTZ/SE7yRmqzmJSSGXOExpxKqeJ6HnA09wZjthePxiYwhb6rvIaq88AF8EBtKNkR5/uvxQedbKBvvwL09cYuZNxU8tJCoSnKZeY0OA0WNKJb8g5pbkcXcqewaf7KOtBE0Wdpoev52FIGwSg4g3v21IaKCb7tqiZEKPZ7d9AsudqGXH3DDOD2HuqnijbTUp906lnFJ+9ec0SLuciPvS7jML5RU6vSPLY+r5GJp2QorcbgIm/ZMW8YZRWLWuEXN4HpRuumezER6ld4Nx4vlV6eIowEPmqz1Jh3H237bRaBBpMsbe4M/1uZ4xMUne+gM/xbRvZvQjW7neBndwCaItX0owY9CQVjFFGTMcDYd7JmY66uI4H+NfJRV8DjJAgaUaHlEvm/zRixRjInzNTabVxYO6RmlfFtnm3tLtljpTiZL6XGAZGIW6aMyy+SvTye0CK3RXoXDn3fYMNJXv8V7k1ou45iytv7l7tLJ5V8/9FocM87Xr+xRXrieJz+a5iYM+Yz/AG9aWzDA1U0dAAAAAElFTkSuQmCC) no-repeat;
    background-size: 100% 100%
}

.agent-home-container .agent-balance {
    margin-top: .4rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #fff
}

.agent-home-container .agent-balance p {
    font-size: .28rem;
    text-align: center
}

.agent-home-container .agent-balance .balance-amount {
    margin-top: .1rem;
    font-size: .6rem;
    font-weight: 700
}

.agent-home-container .agent-withdrawl-deposit {
    display: flex;
    justify-content: center;
    font-size: .32rem;
    color: #fff;
    margin-top: .4rem
}

.agent-home-container .agent-withdrawl-deposit .agent-withdrawl {
    width: 2.4rem;
    height: .82rem;
    line-height: .82rem;
    text-align: center;
    border-radius: .41rem;
    border: .02rem solid hsla(0, 0%, 100%, .28)
}

.agent-home-container .agent-withdrawl-deposit .agent-deposit {
    width: 2.4rem;
    height: .82rem;
    margin-left: .75rem;
    line-height: .82rem;
    text-align: center;
    background-color: hsla(0, 0%, 100%, .28);
    border-radius: .41rem
}

.agent-home-container .agent-offline {
    position: relative;
    z-index: 1;
    width: 100%;
    height: 1.6rem;
    margin: -.8rem auto 0;
    padding: 0 .3rem
}

.agent-home-container .agent-offline ul {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    box-shadow: 0 .1rem .3rem 0 rgba(0, 0, 0, .12);
    border-radius: .1rem
}

.agent-home-container .agent-offline ul li {
    height: 100%;
    position: relative;
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: .24rem;
    color: #868686;
    text-align: center;
    padding: .2rem .1rem 0
}

.agent-home-container .agent-offline ul li .offline-num {
    line-height: .6rem;
    font-size: .48rem;
    font-weight: 700;
    color: #58a9f8
}

.agent-home-container .agent-offline ul li:not(last-child):after {
    position: absolute;
    right: 0;
    top: .56rem;
    content: "";
    width: .02rem;
    height: .48rem;
    background-color: #f3f3f3
}

.agent-home-container .agent-nav {
    margin-top: .4rem;
    padding: 0 .3rem
}

.agent-home-container .agent-nav ul {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: .2rem
}

.agent-home-container .agent-nav ul li {
    display: flex;
    justify-content: center;
    max-width: 100%;
    overflow: hidden
}

.agent-home-container .agent-nav ul .agent-nav-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center
}

.agent-home-container .agent-nav ul .agent-nav-item img {
    width: 1.1rem;
    height: 1.1rem
}

.agent-home-container .agent-nav ul .agent-nav-item .agent-nav-name {
    margin-top: .1rem;
    font-size: .26rem;
    color: #050505;
    text-align: center
}

.agent-home-container .agent-footer {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    height: .98rem;
    background-color: #fff;
    box-shadow: 0 -1px 0 0 #e0e2e6
}

.agent-home-container .agent-footer ul {
    height: 100%;
    display: flex;
    flex-wrap: nowrap
}

.agent-home-container .agent-footer ul li {
    height: 100%;
    flex: 1 1 0%;
    display: flex;
    justify-content: center
}

.agent-home-container .agent-footer ul .footer-nav {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.agent-home-container .agent-footer ul .footer-nav .am-icon {
    width: .4rem;
    height: .4rem
}

.agent-home-container .agent-footer ul .footer-nav .footer-nav-name {
    margin-top: .1rem;
    font-size: .2rem;
    color: #868686
}

.agent-download-bar.show-bar+.agent-home-content {
    padding-top: 1.12rem
}

@media only screen and (min-device-width:390px)and (max-device-width:844px)and (-webkit-device-pixel-ratio:3) {
    .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:428px)and (max-device-width:926px)and (-webkit-device-pixel-ratio:3) {
    .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:375px)and (max-device-width:812px)and (-webkit-device-pixel-ratio:3) {
    .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:3) {
    .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:2) {
    .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:390px)and (max-device-width:844px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:428px)and (max-device-width:926px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:375px)and (max-device-width:812px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:2) {
    
    #root.app .agent-footer {
        padding-bottom: var(--safe-area-inset-bottom);
        height: calc(.98rem + (var(--safe-area-inset-bottom)))
    }
}

.forgot-password {
    padding-top: .5rem
}

.search-games-container .search-games {
    width: 4.04rem;
    position: relative
}

.search-games-container .search-games .search-input {
    display: block;
    width: 100%;
    height: .68rem;
    padding: 0 .2rem 0 .608rem;
    border-radius: .1rem;
    border: 1px solid #5d6c87;
    background-color: #222a38;
    font-size: .25rem;
    font-weight: 500;
    color: #fff
}

.search-games-container .search-games .search-input::-webkit-input-placeholder {
    font-size: .25rem;
    color: #5c677a
}

.search-games-container .search-games .search-input::placeholder {
    font-size: .25rem;
    color: #5c677a
}

.search-games-container .search-games .search-icon {
    position: absolute;
    top: 50%;
    left: .16rem;
    width: .29rem;
    height: .29rem;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: hsla(0, 0%, 100%, .6)
}

.search-games-container.large {
    margin-bottom: .4rem
}

.search-games-container.large .search-games .search-input {
    height: .72rem;
    padding: 0 .3rem;
    border-radius: .36rem;
    font-size: .3rem;
    font-weight: 500;
    box-shadow: 0 .02rem .06rem 0 rgba(0, 0, 0, .49) !important
}

[data-theme=light] .search-games-container.large .search-games .search-input {
    background: rgba(149, 123, 241, .71) !important
}

[data-theme=dark] .search-games-container.large .search-games .search-input {
    background: rgba(149, 123, 241, .4) !important
}

.search-games-container.large .search-games .search-input::-webkit-input-placeholder {
    font-size: .3rem;
    font-weight: 500;
    color: hsla(0, 0%, 100%, .5) !important
}

.search-games-container.large .search-games .search-input::placeholder {
    font-size: .3rem;
    font-weight: 500;
    color: hsla(0, 0%, 100%, .5) !important
}

.search-games-container.large .search-games .search-icon {
    position: absolute;
    right: .3rem;
    top: .16rem;
    width: .4rem;
    height: .4rem;
    color: hsla(0, 0%, 100%, .5) !important
}

.hot-games-center-container {
    width: 100%;
    position: relative;
    background-color: #181f2b
}

.hot-games-center-container .search-games-container {
    position: absolute;
    right: .2rem;
    top: .35rem
}

.hot-games-center-container .search-games-container .search-games {
    width: 2rem
}

.hot-games-center-container .category-select .simple-select .select-wrap {
    height: .8rem;
    padding: 0 .3rem;
    border-radius: .12rem;
    background-color: #161b2a;
    font-size: .4rem;
    font-weight: 600;
    color: #fff
}

.hot-games-center-container .category-select .simple-select .option-wrap {
    top: calc(100% + .13rem);
    left: 0;
    width: 2.9rem;
    border-radius: .16rem;
    background-color: #2b3248;
    overflow: hidden
}

.hot-games-center-container .category-select .simple-select .option-item {
    height: .8rem;
    line-height: .8rem;
    padding: 0 0 0 .3rem;
    font-size: .24rem;
    color: #fff
}

.hot-games-center-container .category-select .simple-select .option-item.selected {
    background-color: #434b67
}

.hot-games-center-container .slots-games {
    position: relative;
    min-height: 6rem;
    padding-top: .3rem;
    background-color: #0e131b
}

.hot-games-center-container .slots-games .game-list-wrapper {
    padding: 0 .26rem 1.6rem
}

.hot-games-center-container .slots-games .game-list-wrapper .game-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    -webkit-column-gap: .34rem;
    column-gap: .34rem;
    row-gap: .26rem
}

.help-container {
    width: 100%;
    height: calc(100*var(--vh, 1vh) - 1rem);
    overflow: hidden
}

.help-container .help-content {
    height: calc(100%);
    padding: .4rem 0 0;
    display: flex;
    justify-content: flex-start;
    overflow: hidden
}

.help-container .help-content .help-menu {
    flex-shrink: 0;
    height: 100%;
    width: 2.2rem;
    padding: 0 .16rem;
    overflow-y: auto
}

.help-container .help-content .help-menu .menu-item {
    width: 100%;
    height: .72rem;
    margin-bottom: .2rem;
    font-size: .24rem;
    font-weight: 500;
    text-align: center;
    border-radius: .08rem;
    box-shadow: 0 .03rem .2rem 0 rgba(31, 23, 54, .5);
    color: #fff;
    background: #131723;
    display: flex;
    align-items: center;
    justify-content: center
}

.help-container .help-content .help-menu .menu-item.on {
    background: #4b4b8b
}

.help-container .help-content .help-main {
    position: relative;
    flex: 1 1 0%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    color: #fff
}

.help-container .help-content .help-main .main-item {
    display: none;
    padding: 0 .2rem .5rem .1rem
}

.help-container .help-content .help-main .main-item.on {
    display: block
}

.message-center {
    height: 100vh;
    height: calc(var(--vh, 1vh)*100)
}

.message-center header {
    height: 1rem
}

.message-center .message-list {
    width: 100%;
    padding: 0 .4rem
}

.message-center .message-list li {
    width: 100%;
    height: 1.5rem;
    padding: 0 .29rem;
    margin-top: .38rem;
    background-color: #182c15;
    box-shadow: 0 .03rem .06rem 0 rgba(0, 0, 0, .16);
    border-radius: .24rem;
    display: flex;
    align-items: center
}

.message-center .message-list li .content-body {
    display: flex;
    position: relative;
    align-items: center;
    width: 100%;
    font-size: .32rem;
    padding-left: .2rem;
    color: #fff;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.message-center .message-list li .arrow-right {
    width: .19rem;
    height: .34rem;
    color: #5c5c5c
}

.message-center i.icon {
    flex-shrink: 0;
    width: .7rem;
    height: .7rem;
    background-size: 100% 100%;
    background-repeat: no-repeat
}

.message-center i.icon.announcement {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAALVBMVEX/wgBHcEz/wgD/wgD/uAD/vAD/vwD/vQD/wAD/tQD/sgD/wQD/tgD/rAD/nQCt0xbDAAAAC3RSTlMzACoW5nFT0ayEowyclsQAAAIwSURBVEjHnZa9jhMxEMeHjULIVdzeC+RDQlfm4wVSgC4oCKWBJk0KiqA0aai2DwVPcM3p3gIkJxLFXYDsbI2EdvMASNlnwPYmzti7dlZMtVr99Pd/xvZ44JJGv98S0e536V8g370GqGh3i5g+ISSVZ3pgRsVk8shJ6cD4AHYoY66gOJqE6VgY78TYZACeKKZjZeDI2GUyR+CWkY4EA67oSsZ3Mk3JdJxMRTINJwOCMbKqj16bi4FpZ8LYOMc815lg+WVhGALT8mg6HxgVAtNy8ClYGKZNZjZibDjVqwh6WjMm4psGNXWmOpIMe6UzvpF4FjT9tsa8OCJsQ5MHWp7agVixr1ZGLRXimDK0hMFRZoWkRh5llJ0wxI2FqZ5k8JEypMx15Qbxl4W5OCCrEKMtLRthnmUIwzXiHyfDkMtENub6mFSEiVMn5ElhlLh0uBsh49bhbrhM8tdSw2thRrqxM2LbMzdJ+tvCTPg2CDec2aXjIubp55c0bpYF52euNj0UG4Y4VYxvHB6ZG49YHSHCKBGuIpzH39WZV3cn2u2SJBYSguIfg/z9epum+3SfUUImuleMussfUgElccyzF/HzdJcV0/h4y+Mu3ccP73i8n5KeQC+G59V2+/gHQMvTeoveo+rrBLe5HmX0uiDCQa7XGT3z4s0w3zPL9N4zPdwr/Ra4F+uWf5vKvHEOIU+9p36JN7fM211qBigzSxTPJJXzs037v2Ykbvz8rCW1ekUz2z8Sp0jdBg+NMwAAAABJRU5ErkJggg==)
}

.message-center i.icon.inbox {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAMAAABG8BK2AAAAPFBMVEVHcEz7f0f/QQH/ZwD/aQD15NX7qn7/aQD/aQD/aQD7/fz09/b/WAD/RwHs7+zG0c3Y4N3+gzD44Mz5xqFPM251AAAACnRSTlMAYcv/GK3+MykKDncLpwAAAjBJREFUWMOtmIGSgyAMROsFJRSlWv//Xw8Qq0BAUJZ25qZO320WipjXKyUhGEMtbgciY0K86iQ0gFNCJioYPCdkBZALxqYrT6IEcuWoFGKVNCR4lTCRCq8UUoaQ8wYcxnkDzi2K1qN0Uznz22LP4iXiEfyBsIWZ03J+ZOaw45vBTkmtd1Kqo9MJ6H30RXkMjZc9OVnsCuM500Z7siq8wEjfTozh5CazY5SvWb+NmbgoW1U0Tw6jPr7mz0dZMzEGqR9ln5wjuZXVUz9QLMLsudBuxAXmF+97m20aY8LhCcz8iTTLJEakMK4GtQ1l3qmidMaMF2QjXb53MDJYdVJmMFHC3bBB1BzJVTV08YxHmGlwyy+NmQrcwOIKOgftaYECNwDL18smQH0XKMMYjktVHROutgk3FALDCQzAkFp+q71c5sYYOtexO9LpQh3GBhRmqxaoxQCsLtzv9xQujcEMBoZtlRhn5o/1uBK7YWkMLGqvY/3FknCTw8Cy7nUMC2Qx0UYB42mcdfqU2IzDj8ZD4GMOEbsfRhhwLwj181OEIb14fogtnUWYyb6A0jSaMRI3mDDjyYqmaEP2KnG7C2++9h9CUubqRB0pMHYDkONEGOpgYioaRyDHphCD1DHJUrIKMYw6tP1Nl/ojD22NjpCNDrStjteNDvtPOKzF41T4wMmaUG6WRTxs4pO5bv4AXc3BFs2FbJeC4bOCqkCIBc0ggQ3aQBdNqYqulOuQBS2ybI/sH9jkfRo3bMBzAAAAAElFTkSuQmCC)
}

.message-center i.icon.chat {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAMAAABG8BK2AAAAilBMVEVHcExh465N2f5U2u1O1Pl47rZM1/lO1PlO1PlO1PlX3/9W3/9O2qRj481M2f9067x977ps6MI60ss6zZ9e4/9/8bYbvv+F87tQ3P8mw/870P8sx/9I1/80zP9X4P////9C0/9277Bu664Tuf+06f8nyuyW4f9B1s513v/i9v9p2/82z5eE2v8ZxNXOqXpnAAAAI3RSTlMA/4dAGv1VMyYMuuf+Y9uw14rh3f///////////////////qo5KeAAAALRSURBVFjDrdjpeqIwFAZghRgSlcUZQRaxbK7t3P/tzQmghWyAcp4+5Rdvv5NQSLJYqIpShAiUxQquCFG6mFYUNXcLRRAdb1jaImMkSqzhQnMgQ9BohJWyNWpNKiJXkDWxpGNNrOlFP88idd5T+PGhljWHY71d6MPhFYeHfqB02pKGwcZ6s4dy1oY5Ko4sjOHsO7XGI+KIYXAPqSFzMI4YhUf2p9MGD0yW8OStJUoYhoa+KzJOCY9KR9aToVKOGdZ0xc0TVirHzDHVXaGBln6VLDbUXRF9S10ljk3llOvD9JXUGMdgvZI6qjGmup54JU2wYoyppidRSQwF058oZ0CJ/v21pUx/olrgVlU3UE6X6saUR3XJaiWJzt/fO9mMy5jHF9TjFFZwuRyzG1zujcKYrTuSYbfD/bV2T7I7u4a1Asx2u3SnMEnDBEnDlIBEwRmUZSH0xT19m2Zomj+fsvsvQfBTa2UACjCgrFamnlm/hhiaSLNL9RPA3bfqEpVlwOpaK76rb8poZzqLQEnjNGAKpChbBRim+HwcGQMzHWa1EscMARJagh/41SiHw07L4FYJw6xW4rRWkqjJEpxb5eBqGfOpwEMXx/WzCwab6aCOA2Fq5eBxDPfWeikN0z67UZOmPD+Vw0r7r2ltXkomKuXyqfj+EKNSYLKuvwrP8M+fJsu5o6z4T4PAqLJ0Fd/jX6JEZAaz+NxjTPkPzCaUZwm64+Ln+Y7/wHBvUWmW4Fr0suS5zX/u+K9mU4axdp7K+bos+lk4BukXfhj/uV7Zm4HPkudYWArolsTmVlAYkhemwGhXs7Y0S9Fj0IjlrC3L0mfoiPUswZ6YpceQkavrnScohWx9Pbi8tl2PvTk9d2fb2ISS7xrmWezPtfWYayP07rbMmmOrKdtszrNlnWsDPdd2fq7DhSmByBxnJmOOcIYggj4/lCJTD7ieR2SkPSIjuiOy/+09yA4L6BzoAAAAAElFTkSuQmCC)
}

.message-center i.icon.affiliate {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGCAMAAABG8BK2AAAAhFBMVEV6rN9wpt31s37ty616rN96rN/6LDtHcEx6rN96rN++v8L81ajWu6bbZn3+n5GapMblxarta3vz2bn9VmL62bTjbH/9u4Z6rN/1uIhIj9T0bnn7Y25hntr/u4X/5MH9Ym16rN/3q27+0aL/1qo6h9H/3bX8tHv/x5evsrxjndn+t6SJn7egFUdTAAAAHXRSTlMziN6jJBn+AC4LVvZ0ff5EkKm9+9+N8KTH7cbyc/j3ChIAAALySURBVFjDtdjhcqIwEADgBRKbAFZq1dP2QCWHnL33f79LgsQQNhGt3R9OB+s3m02ATeDFF5xTSgjrg1DKufefAScoYYAFo3wqwylO9EH4BIaHjS7oDWYSgkADhhOYHtzHcLgrCM5QuDMYRxgC9wcfMfQB5ZoPfEeRzpDh8GCQAcMeZS4LCB4u76DM8K0hmWEBnky020XIT9afn2s0HUCTif7IGDvrvYw1lg5gyWhl7Mz3Ot6QdBQzUs7nzvmNKWOHasZZeSw6HA6IY5SRwzTjrBml9M7Ouv629zoI0ymds1vY33x4HSoZjirKOcf59YvFsnr1OUwyFFcOh3/H47GD0iJJVlU1cD4cxp7uX5bytzweNVRUJgaONWIOdmmGioyTdERV4c77wmYCioqmqjzObGYcCjyotJUbxnmfzWbmfrAYRKmrcXxdFYsxE8WmKRdHK4ZhQMaTbepy+V3TCOE4nTLHGBY5SlcYUcsQtV3qL0fRkOMYRTONQlo18ZYjXpUyePAwZxFH5TWEUKn0C8g48uKro4BzY0awsZxaiLb7y3JUgsl8DgFGxrZEwzhKWY3fe+6Fogw6SskWt5m0DDi6WFmKvGYAGVVbex1cQZh0oxZb63GyJdYRANJKLPWslm2LOvlERj+m9Ppv2qGhnRgdEkeTaWqhLZuRmagPnBm97GDVXJSqHjAXp8C6E4RJ1WxcFr0Z1enUlWazBZxBpmrZFVml1JhMtBN7ehxAu75EK40ukj2icuPpAAHrkVghGtGVR4hBZeJ7GDlb8vZzyyMnKU49/R/42sdF0pdHqNqqysS+vg1rTPrI1W2oyqMeObrCib85xtqkvs5ZttJFVsM6xkke6Gkh2M7q50tX5CTY0kKwn13JgYk8V8/VItjRQrC7TrM6y1W5t0W4v35me/2kZv9ZW4/Hh8V+Ylv2YHlGm8RnbVmftYF+1nb+zsMF+tNHHbpCU6DREQ52DERuHAPROw6lPJND8AMumHxERij3n5H9B5VLps5JHXl3AAAAAElFTkSuQmCC)
}

.message-center i.icon.telegram {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAALVBMVEVHcExBlNBEktBDk9BFlM9Ek9FFk9FEk9FDk9BDk9FElNBEk9BElNFEk9BEk9Aa9oDRAAAADnRSTlMAKxsmD5sG3T3BdfBfUfy3rJQAAAJVSURBVEjHjZY9axtBEIYVRajOnHSxokSgi0RqnQUpUlkKpFcVQkBYjv+Ay0AQuErizmUKB1KkCgRcBVwYXKiJKxdpAymM7HzMb8js3d7d7N7crgcEe7qH+XhvdnZrNWbN6WAMMH44fFarsmkMmQVDkWhEwC3YKiO7MZjWelTyYiNkFtSMQDAz3FMJgWCbIXWQ7TFjogqGRdupQqDtd1M4qlcjeUYTBxNoJnYwOpgrFMDdhFk4mcBXlTKlddONQL86nYMLvRgRc0tEVnjMpJbU6TxH/MWSFlLufkTEvexJTPnHKSE4K1RsSHGUfSoKq0txjuiX1UWF3bbqUXFO3iP+yf+6Y5aexrl8t0S8Zl9sYcfB9dlbBbJenNj14KxDbvA3YyK7njeg3OAV+/KREUfF6CXujsvMKo2D6ws4TxaHxcbXjbqB2mbQzRY2c18jRwBf09VZiem9SF6cAISa3i8x1FMvSTd6Mdd5MdlqOQ2rJeV5T7u5lhiV1B50tAJMZmLYBgz/pfIp+wtCPmQPLjunGXPFGd6qr7sZUnSqzcwPcuYQhO+l7OdnLPRmDN86Gx9IGFtmYniPhd8oyLklM/UP7+ee0iXpsLUxXYx9sVR5hJbMI4NpzRN5v5gy9819+iRJtbfkMqtpJ0zDkEuohpQ0xr7vm8Nu4Zljbe9YTedYw8Ns+edq6wbzuX2DOT/ynxctfVy+8oZyV5YflhPv8eVy1C/O5Yn3yKVBHjuLch27ffPCIdVfuriUbxzCNWkn9lx/EgUGDNncrriQ7U4Hytl4c2jcfP4DnmaPFOjdDAUAAAAASUVORK5CYII=)
}

.message-center .unread-wrap {
    position: absolute;
    right: .17rem
}

.message-center .unread-wrap i {
    min-width: .4rem;
    height: .4rem;
    align-items: center;
    display: flex;
    justify-content: center;
    color: #fff;
    font-size: .26rem;
    font-weight: 400;
    font-style: normal;
    background: #fe615f;
    border-radius: .3rem;
    padding: 0 .09rem;
    box-shadow: 0 .03rem .08rem 0 rgba(255, 50, 93, .5)
}

.download-bar-shell.show-bar+.this-mc-header+.notice-wrap {
    padding-top: 1.2rem
}

.notice-wrap {
    min-height: calc(100vh - 1rem);
    min-height: calc(var(--vh, 1vh)*100 - 1rem);
    padding-bottom: 1rem
}

@media only screen and (min-device-width:390px)and (max-device-width:844px)and (-webkit-device-pixel-ratio:3) {
    .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:428px)and (max-device-width:926px)and (-webkit-device-pixel-ratio:3) {
    .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:375px)and (max-device-width:812px)and (-webkit-device-pixel-ratio:3) {
    .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:3) {
    .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:2) {
    .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:390px)and (max-device-width:844px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:428px)and (max-device-width:926px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:375px)and (max-device-width:812px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:3) {
    
    #root.app .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

@media only screen and (min-device-width:414px)and (max-device-width:896px)and (-webkit-device-pixel-ratio:2) {
    
    #root.app .notice-wrap {
        padding-bottom: calc(1rem + var(--safe-area-inset-bottom))
    }
}

.shell-notice-container .mc-notice-header {
    position: relative;
    padding: 0 .4rem 0 .29rem;
    display: flex;
    align-items: center;
    color: #fdd82d;
    height: 1.74rem
}

.shell-notice-container .mc-notice-header img {
    width: .369rem;
    height: .39rem;
    margin-right: .241rem
}

.shell-notice-container .mc-notice-header .icon {
    display: block;
    width: .4rem;
    height: .49rem;
    margin-right: .241rem
}

.shell-notice-container .mc-notice-header .icon.unread {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAALVBMVEX/wgBHcEz/wgD/wgD/uAD/vAD/vwD/vQD/wAD/tQD/sgD/wQD/tgD/rAD/nQCt0xbDAAAAC3RSTlMzACoW5nFT0ayEowyclsQAAAIwSURBVEjHnZa9jhMxEMeHjULIVdzeC+RDQlfm4wVSgC4oCKWBJk0KiqA0aai2DwVPcM3p3gIkJxLFXYDsbI2EdvMASNlnwPYmzti7dlZMtVr99Pd/xvZ44JJGv98S0e536V8g370GqGh3i5g+ISSVZ3pgRsVk8shJ6cD4AHYoY66gOJqE6VgY78TYZACeKKZjZeDI2GUyR+CWkY4EA67oSsZ3Mk3JdJxMRTINJwOCMbKqj16bi4FpZ8LYOMc815lg+WVhGALT8mg6HxgVAtNy8ClYGKZNZjZibDjVqwh6WjMm4psGNXWmOpIMe6UzvpF4FjT9tsa8OCJsQ5MHWp7agVixr1ZGLRXimDK0hMFRZoWkRh5llJ0wxI2FqZ5k8JEypMx15Qbxl4W5OCCrEKMtLRthnmUIwzXiHyfDkMtENub6mFSEiVMn5ElhlLh0uBsh49bhbrhM8tdSw2thRrqxM2LbMzdJ+tvCTPg2CDec2aXjIubp55c0bpYF52euNj0UG4Y4VYxvHB6ZG49YHSHCKBGuIpzH39WZV3cn2u2SJBYSguIfg/z9epum+3SfUUImuleMussfUgElccyzF/HzdJcV0/h4y+Mu3ccP73i8n5KeQC+G59V2+/gHQMvTeoveo+rrBLe5HmX0uiDCQa7XGT3z4s0w3zPL9N4zPdwr/Ra4F+uWf5vKvHEOIU+9p36JN7fM211qBigzSxTPJJXzs037v2Ykbvz8rCW1ekUz2z8Sp0jdBg+NMwAAAABJRU5ErkJggg==);
    background-repeat: no-repeat;
    position: relative
}

.shell-notice-container .mc-notice-header .icon.unread .bubble {
    position: absolute;
    display: block;
    width: .14rem;
    height: .14rem;
    right: 0;
    top: 0;
    border-radius: 50%;
    background-color: #fe615f;
    box-shadow: 0 .03rem .08rem 0 rgba(255, 50, 93, .5)
}

.shell-notice-container .mc-notice-header .icon.read {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEYAAABGBAMAAACDAP+3AAAAIVBMVEWfn59HcEyfn5+fn5+fn5+fn5+fn5+fn5+fn5+fn5+fn5+wePh5AAAACnRSTlMzACUHFeJ5rVtH8zoF8QAAAbVJREFUSMfNlr1SwkAQx3cSCNgJ8gDx8AFAfABEHiCNOHOVjYxjReOMWqXSGSoax9ZGo/eUhgTvdvdy4TJj4b+D+U1uv3ehhzQ5m4kYYiHmE/w3IGIGRuKiijkFqpOxxcyAKxgzxkYMBNUPlWphZgDVOkbM1MGUrxVMH1wKNRM7GRjvmIEbgeGOmdYwQckcQZ3OC6Zfy4QFM6plgoKJa5ncM+DmRHLBYw3cnDulrplBwM2Rj08JSyzw6Mh0nTCjgZssF3LFjAZm8otUKktpFIEm601tlW2IY5SJZMEo4n4IfeZ4qZQwh+hX9xdRX9h5wrQ180GYUdVTJNYB4BBKwyQO5sAg6hszKMwRYj6xuw2ZDmKypgz8ERP/M2a6lyH5QmnHiQ9MbXTvL4keDKNrbK2YUrsOJWcSzeia54h61zU/cDKJzVhvrXQP6l5+ZUhmetnMhOdlrptt4yxvc23MTGCzpc2aK5c1oyKrvlr2rJOkt3azjg2gjryyZ6bP7PWa4T67oOexU3x2U+1jYYNd6bVznR9Cu9u5dYcNb4lq/9lN4nXbeN1I+Z7ff2ttL7K5EAICfrP9AHrnJAXHVl4MAAAAAElFTkSuQmCC);
    background-repeat: no-repeat
}

.shell-notice-container .mc-notice-header .notice-body {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    flex: 1 1 0%;
    height: 100%
}

.shell-notice-container .mc-notice-header .notice-body:after {
    position: absolute;
    left: 0;
    bottom: 0;
    display: block;
    content: "";
    height: 1px;
    width: 100%;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
    background-color: #3b3b3b
}

.shell-notice-container .mc-notice-header .notice-body__title {
    display: flex;
    align-items: center;
    margin: .4rem 0
}

.shell-notice-container .mc-notice-header .notice-body__title--text {
    display: inline-block;
    width: calc(100% - 1.7rem);
    overflow: hidden;
    -o-text-overflow: ellipsis;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: .3rem;
    color: #fff
}

.shell-notice-container .mc-notice-header .notice-body__title--time {
    margin-left: auto;
    order: 2;
    font-size: .28rem;
    color: #606060
}

.shell-notice-container .mc-notice-header .notice-body__content .ellipsis {
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    font-size: .26rem;
    color: #7d7d7d;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.shell-notice-container .mc-notice-header .accordion-arrow {
    width: .22rem;
    height: .12rem;
    fill: #777;
    margin-left: .32rem;
    -webkit-transform: rotate(0);
    transform: rotate(0)
}

.shell-notice-container p {
    text-align: justify
}

.shell-notice-container .mc-notice-time {
    margin-top: .1rem
}

.shell-notice-container .mc-notice-content {
    font-size: .26rem;
    padding: .27rem .35rem .26rem .47rem;
    white-space: pre-wrap;
    word-break: break-all;
    overflow: auto;
    color: #adadad
}

.shell-notice-container .double-arrow {
    position: absolute;
    right: .1rem;
    top: 50%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%)
}

.shell-notice-container.on {
    background-color: #202630
}

.shell-notice-container.on .accordion-arrow {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg)
}

.noticeTop {
    margin-top: 1rem;
    margin-bottom: 1rem
}

.setting-container #header {
    box-shadow: none
}

.setting-container .setting-list {
    margin: .2rem 0
}

.setting-container .setting-list li {
    display: flex;
    align-items: center;
    height: 1rem;
    padding-left: .28rem;
    background-color: #fff;
    position: relative;
    background-image: url(data:image/png;base64,UklGRpwAAABXRUJQVlA4WAoAAAAQAAAADwAAGwAAQUxQSFAAAAABL0CYbbTfTGd+GscREcGXQkEjSQ1VQBVAUAIKwL+d7xiI6L/Ctm2b7J20Z6zjANaSETwAvUB1C1Ljs9pITQzYwSVokPE4/D/ov5j/Q//pAFZQOCAmAAAAsAEAnQEqEAAcAAJAOCWcAAQayd9+AAD+9UtX8RbJE/+99ewgAAA=);
    background-repeat: no-repeat;
    background-size: .12rem .2rem;
    background-position: top .4rem right .28rem
}

.setting-container .setting-list li span {
    font-size: .28rem;
    color: #414655
}

.setting-container .setting-list li:first-child:after {
    content: "";
    display: block;
    position: absolute;
    width: 100%;
    height: 1px;
    left: 0;
    bottom: 0;
    border-bottom: 1px solid #eee
}

.setting-container .logout-btn {
    text-align: center;
    height: 1rem;
    line-height: 1rem;
    background-color: #fff;
    color: #414655;
    font-size: .28rem
}

.shell-spinner-loading-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh)*100)
}

.shell-spinner-loading-wrap .shell-spinner-loading {
    -webkit-animation: rotator 1.4s linear infinite;
    animation: rotator 1.4s linear infinite;
    width: .65rem;
    height: .65rem
}

.shell-spinner-loading-wrap .shell-spinner-loading .path {
    stroke-dasharray: 187;
    stroke-dashoffset: 0;
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-animation: dash 1.4s linear infinite, colors 5.6s linear infinite;
    animation: dash 1.4s linear infinite, colors 5.6s linear infinite
}

@-webkit-keyframes rotator {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes rotator {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes colors {
    0% {
        stroke: #fefc41
    }
    20% {
        stroke: #ff5676
    }
    40% {
        stroke: #fa60ff
    }
    60% {
        stroke: #3296ff
    }
    80% {
        stroke: #30f282
    }
    to {
        stroke: #fefc41
    }
}

@keyframes colors {
    0% {
        stroke: #fefc41
    }
    20% {
        stroke: #ff5676
    }
    40% {
        stroke: #fa60ff
    }
    60% {
        stroke: #3296ff
    }
    80% {
        stroke: #30f282
    }
    to {
        stroke: #fefc41
    }
}

@-webkit-keyframes dash {
    0% {
        stroke-dashoffset: 187
    }
    50% {
        stroke-dashoffset: 46.75;
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }
    to {
        stroke-dashoffset: 187;
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes dash {
    0% {
        stroke-dashoffset: 187
    }
    50% {
        stroke-dashoffset: 46.75;
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg)
    }
    to {
        stroke-dashoffset: 187;
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.shell-appDownload {
    min-height: 100%;
    width: 100%;
    position: absolute;
    background: #ededed
}

.shell-appDownload:after {
    content: "";
    display: block;
    height: 1.4rem
}

.shell-appDownload .appDownload-header {
    background: url(appDownload-header.bff4a5f2.png) no-repeat;
    background-size: 100% 100%;
    height: 3.26rem;
    padding: 0 .3rem
}

.shell-appDownload .appDownload-header .app-name {
    text-indent: 1em;
    font-size: .4rem;
    line-height: 1.5rem;
    color: #fff;
    font-weight: 700
}

.shell-appDownload .appDownload-header .appDownload-header-content {
    height: 2.1rem;
    border-radius: .28rem;
    background: #fff;
    position: relative;
    bottom: -.68rem;
    padding-left: 2.25rem;
    padding-top: .33rem
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-icon {
    position: absolute;
    width: 1.75rem;
    height: 1.75rem;
    top: -.34rem;
    left: .3rem;
    border-radius: .28rem;
    background-color: #76bfff;
    z-index: 1px;
    overflow: hidden
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-icon img {
    display: inline-block;
    width: 1.75rem;
    height: 1.75rem
}

.shell-appDownload .appDownload-header .appDownload-header-content .official-logo {
    color: #34bff9;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAAAmCAMAAABK8c3RAAAASFBMVEVHcExj5X84w+42wfQ2wvNj5n43wvNN1L09xuM2w/Jl53pj5n5i5YFh5INl5ns0wPlm53hg44hK0sBQ1rFU2qREzc9Z3plBytmQ1t/UAAAAD3RSTlMA2P6R0SuYB/4ukpk0j7yap409AAAAxklEQVRIx93WSxKEIAxF0QABBLEhfve/045avQGeA6vvgHKSgzgRIs1n07oy2dNdzA1ojJfx0acSqaNYxtY+52gW46k7byTrKg0wznnxNMlIUKNMZKRgSBFDIhFDoogiBKbCDiMq7DuM7C9C1hVFVHgPsiwoosJ7kHlGERX+DNk2FFHhPchxoIgKjyDDgCIqPISgvwxFeEgYkgYmZwOGBOsoWQu9yj0fKgNK4nqdhGsNqe+Sk0KtfF+VXAUKv+2T4z6B3fUlvmEVGD8nR7ELAAAAAElFTkSuQmCC) no-repeat;
    background-size: 100% 100%;
    padding: 0 1em;
    min-width: .68rem;
    font-size: .2rem;
    display: inline-block;
    vertical-align: middle;
    height: .38rem;
    margin-left: .13rem;
    line-height: .38rem
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-full-name {
    max-width: 2rem;
    display: inline-block;
    vertical-align: middle;
    line-height: .38rem;
    font-size: .32rem;
    color: #2a2b2d;
    font-weight: 700;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-star-grade {
    margin-top: .12rem;
    height: .25rem;
    font-size: .24rem;
    font-weight: 700;
    color: #757575
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-star-grade .star-icon {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAZCAMAAAAc9R5vAAAAOVBMVEVHcEz/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wD/2wCkjnzBAAAAEnRSTlMAZpX4PzRzC9nwz6bowyCzVYd5kxm5AAAAm0lEQVQoz22SWRLAIAhD3ZW6tdz/sLXquFTen8kIEWRscjnLKAyiJw1AROrKXXR8CMN9BpxXNFb4YcRmuE20mnvsZK5buSSiA9wAF71mAWlamBNgCUhDlSaS1scLVu6WVv318Uq1VzPLJlY9/Ac4ks5BWnlGorqbdakrYhiij7xnmJvP31GWCryO9NpCiVRjPLh+CQNxJNE51B4vDrIVtuHqpHIAAAAASUVORK5CYII=) no-repeat;
    background-size: .24rem .25rem;
    height: .25rem;
    width: .24rem;
    margin-right: .08rem;
    display: inline-block
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-star-grade .grade-text {
    display: inline-block;
    vertical-align: top
}

.shell-appDownload .appDownload-header .appDownload-header-content .app-download-btn {
    position: absolute;
    top: .39rem;
    right: .3rem;
    padding: 0 .38rem;
    height: .64rem;
    line-height: .64rem;
    font-size: .26rem;
    background: #3887fe;
    color: #fff;
    text-align: center;
    border-radius: .32rem;
    cursor: pointer
}

.shell-appDownload .appDownload-tabs {
    margin-top: .98rem;
    padding: 0 .3rem;
    line-height: .48rem
}

.shell-appDownload .appDownload-tabs p {
    text-align: justify
}

.shell-appDownload .appDownload-tabs .view-item {
    min-width: 50%;
    text-align: center;
    color: #5e5e5e;
    line-height: .88rem;
    float: left;
    position: relative;
    font-weight: 700
}

.shell-appDownload .appDownload-tabs .am-tabs-ink-bar {
    background-color: unset
}

.shell-appDownload .appDownload-tabs .am-tabs-bar,
.shell-appDownload .appDownload-tabs .am-tabs-tab {
    background-color: transparent;
    border: none
}

.shell-appDownload .appDownload-tabs .am-tabs-tab-active .view-item:before {
    content: "";
    border-radius: .03rem;
    background: #2297ff;
    height: .06rem;
    width: .36rem;
    position: absolute;
    bottom: 0;
    left: 50%;
    margin-left: -.18rem
}

.shell-appDownload .appDownload-tabs .tips-red {
    color: #ff3636
}

.shell-appDownload .appDownload-tabs .view-content {
    margin-top: .5em;
    padding: 0 .37rem;
    color: #5e5e5e
}

.shell-appDownload .appDownload-tabs .view-content img {
    width: 100%
}

.shell-appDownload .donw-load-btn {
    position: fixed;
    bottom: .4rem;
    height: .64rem;
    width: calc(100% - .6rem);
    left: .3rem;
    line-height: .64rem;
    font-size: .26rem;
    background: #3887fe;
    color: #fff;
    text-align: center;
    border-radius: .32rem;
    cursor: pointer
}

.shell-appDownload .app-guide {
    width: 80%;
    margin: 1.2rem auto 0
}

.shell-appDownload .app-guide .step {
    width: 100%;
    margin-bottom: .4rem
}

.shell-appDownload .app-guide .step:last-of-type {
    margin-bottom: 0
}

.change-password {
    padding-top: .5rem
}

.change-password .login-form-bg {
    border-radius: .15rem !important
}

.top-rank-container {
    width: 100%
}

.top-rank-container .rank-banner {
    display: block;
    width: 100%
}

.error-ip,
.error-service {
    height: 100%;
    width: 100%;
    padding: .3rem;
    position: fixed
}

.error-ip {
    background: url(ip.3e16c495.jpg) no-repeat 50% fixed;
    background-size: cover
}

.error-service {
    background: url(service.027aed44.jpg) no-repeat 50% fixed;
    background-size: cover
}

.errr-logo {
    width: 2rem
}

.error-contact-us {
    background: linear-gradient(90deg, #fe6e74, #f3077f);
    color: #fff !important;
    border: none !important;
    border-radius: .5rem !important;
    position: absolute;
    bottom: .76rem;
    width: 60%;
    font-size: .3rem !important;
    left: 50%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    overflow: hidden;
    text-align: center
}

.error-icon {
    margin-right: .1rem;
    width: .3rem
}

.error-content,
.error-text {
    position: absolute;
    width: 80%;
    left: 50%;
    bottom: 20%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%)
}

.error-content {
    color: #fff;
    text-align: center
}

.error-content .title {
    font-size: .5rem;
    font-weight: 700;
    margin-bottom: .1rem
}

.error-content .ip-address {
    font-size: .3rem;
    margin-bottom: .2rem
}

.error-content .error-info {
    margin-top: .3rem
}

.error-content p {
    font-size: .3rem;
    line-height: .4rem;
    white-space: pre-wrap
}

.fb-game-iframe-container {
    width: 100vw;
    height: 100%
}

.fb-game-iframe-container .shell-spinner-loading-wrap {
    height: 100%
}

.fb-game-iframe-container iframe {
    width: 100%;
    height: 100%;
    border: none;
    padding: 0;
    margin: 0
}

.vnlist-center.vnlott-container {
    height: calc(100% - 3.2rem);
    overflow-x: hidden;
    overflow-y: auto
}

.vnlist-center.vnlott-container .vnlott-list {
    padding: 0 .15rem
}

.vnlott-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative
}

.vnlott-container .vnlott-tabs {
    height: .8rem;
    display: flex;
    align-items: center;
    position: relative
}

.vnlott-container .vnlott-tabs:after {
    position: absolute;
    left: 0;
    bottom: 0;
    content: "";
    width: 100%;
    height: .02rem;
    background-color: #1d1d34;
    box-shadow: 0 1px .02rem #343435
}

.vnlott-container .vnlott-tabs .tab-item {
    height: 100%;
    padding: 0 .3rem;
    display: flex;
    align-items: center;
    font-size: .26rem;
    color: #fff
}

.vnlott-container .vnlott-tabs .tab-item.on {
    background-color: hsla(0, 0%, 100%, .3)
}

.vnlott-container .vnlott-menu {
    display: flex;
    min-height: .4rem;
    align-items: center
}

.vnlott-container .vnlott-menu .lott-history {
    display: flex;
    align-items: center;
    height: .8rem;
    margin: 0 .1rem
}

.vnlott-container .vnlott-menu .lott-history .am-icon-clock {
    width: .4rem;
    height: .4rem
}

.vnlott-container .vnlott-menu .lott-item {
    height: .8rem;
    line-height: .8rem;
    flex: 1 1 0%;
    text-align: center;
    font-size: .26rem;
    color: #fff
}

.vnlott-container .vnlott-menu .lott-item.on {
    color: #30b5fe
}

.vnlott-container .vnlott-content {
    flex: 1 1 0%;
    overflow-y: auto;
    text-align: center
}

.vnlott-container .vnlott-list {
    display: flex;
    flex-wrap: wrap;
    color: #fff
}

.vnlott-container .vnlott-list .list-item {
    width: 33.333%;
    padding: 0 .1rem .12rem
}

.vnlott-container .vnlott-list .list-item:after {
    flex: auto;
    content: ""
}

.vnlott-container .vnlott-list .list-item .lott-img {
    width: 85%;
    margin: 0 auto;
    position: relative
}

.vnlott-container .vnlott-list .list-item .lott-img .lottime_box {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 50%;
    width: 100%;
    padding-bottom: 100%;
    overflow: hidden
}

.vnlott-container .vnlott-list .list-item .fav-icon {
    position: absolute;
    right: .1rem;
    top: .1rem;
    width: .4rem;
    height: .4rem;
    color: #ede93d;
    z-index: 11
}

.vnlott-container .vnlott-list .list-item .fav-icon .am-icon {
    display: block;
    width: 100%;
    height: 100%
}

.vnlott-container .vnlott-list .vnlott-icon {
    display: block;
    width: 100%;
    border-radius: 50%;
    background-size: 100% 100%;
    background-repeat: no-repeat
}

.vnlott-container .vnlott-list .vnlott-icon:after {
    content: "";
    display: block;
    padding-bottom: 100%
}

.vnlott-container .vnlott-list .vnlott-name {
    width: 100%;
    height: .45rem;
    line-height: .45rem;
    font-size: .24rem;
    color: #fff;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.vnlott-container .vnlott-list .lottery-time {
    width: 100%;
    position: absolute;
    left: 50%;
    bottom: 15%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    font-size: .26rem;
    color: #fff;
    background-color: rgba(0, 0, 0, .5);
    text-align: center
}

@media only screen and (max-device-width:369px) {
    .vnlott-container .vnlott-list .list-item .lott-img {
        width: 1.55rem
    }
    .vnlott-container .vnlott-list .list-item .vnlott-icon {
        width: 1.55rem;
        height: 1.55rem
    }
    .vnlott-container .vnlott-list .list-item .vnlott-name {
        height: .45rem;
        line-height: .45rem
    }
}

.game-menu {
    position: relative;
    flex-shrink: 0;
    width: 100%;
    padding: 0 .3rem
}

.game-menu.sticky {
    position: fixed;
    left: 0;
    top: 1.39rem
}

.game-menu .game-menu-scroll {
    position: relative;
    width: 100%;
    height: .93rem;
    padding: 0 .2rem;
    border-radius: .465rem;
    box-shadow: 0 .05rem .2rem 0 rgba(0, 0, 0, .1);
    background-image: linear-gradient(180deg, #5e5ea8, #3b3b89 2%, #252558)
}

.game-menu .game-menu-list {
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden
}

.game-menu .game-menu-item {
    flex-shrink: 0;
    min-width: 1.1rem;
    height: .73rem;
    padding: 0 .15rem;
    margin-right: .15rem;
    border-radius: .365rem;
    font-size: .24rem;
    color: #fff
}

.game-menu .game-menu-item .game-menu-icon {
    position: relative;
    width: 100%;
    height: 100%;
    color: #abadc8;
    background-repeat: no-repeat;
    background-position: 50%;
    background-size: cover;
    display: flex;
    align-items: center;
    justify-content: center
}

.game-menu .game-menu-item .game-menu-name {
    height: 100%;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center
}

.game-menu .game-menu-item.on {
    box-shadow: inset 0 .03rem .06rem 0 rgba(0, 0, 0, .16);
    background-color: #1c1c53;
    color: #1678ff
}

.hot-news-container {
    width: 100%;
    padding: .4rem .3rem 0
}

.hot-news-container .hot-news-content {
    position: relative;
    width: 100%;
    border-radius: .2rem;
    overflow: hidden
}

.hot-news-container .hot-news-content .news-title {
    width: 100%;
    height: 1rem;
    line-height: 1rem;
    font-size: .36rem;
    font-weight: 700;
    text-align: center;
    color: #fff;
    background-image: linear-gradient(270deg, #256bff, #3599ff)
}

.hot-news-container .hot-news-content .news-list {
    width: 100%;
    overflow: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.hot-news-container .hot-news-content .news-item {
    height: 3.9rem;
    -webkit-transform: translateZ(0);
    transform: translateZ(0)
}

.hot-news-container .hot-news-content .news-item .video-container {
    position: relative;
    width: 100%;
    height: 100%
}

.hot-news-container .hot-news-content .news-item .video-overlay.on {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    width: 100%;
    height: 100%
}

.hot-news-container .hot-news-content .news-item .js-iframe {
    width: 100%;
    height: 100%
}

.hot-news-container .hot-news-content .video-navigation {
    display: flex;
    justify-content: center;
    padding: .1rem 0
}

[data-theme=light] .hot-news-container .hot-news-content .video-navigation {
    background-color:  !important
}

[data-theme=dark] .hot-news-container .hot-news-content .video-navigation {
    background-color:  !important
}

.hot-news-container .hot-news-content .video-navigation .nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: .46rem;
    height: .46rem;
    margin: 0 .6rem;
    border-radius: 50%;
    color: #fff;
    background-color: #6d7d90
}

[data-theme=light] .hot-news-container .hot-news-content .video-navigation .nav-btn.swiper-button-disabled {
    background-color: #b2bac4 !important
}

[data-theme=dark] .hot-news-container .hot-news-content .video-navigation .nav-btn.swiper-button-disabled {
    background-color: #909ba8 !important
}

.hot-news-container .hot-news-content .video-navigation .nav-btn svg {
    display: block;
    width: .1rem;
    height: .18rem
}

.scroll-num {
    position: relative;
    width: .22rem;
    height: .4rem;
    text-align: center
}

.scroll-num .animate-list {
    width: 100%;
    font-size: .32rem;
    color: #f7cc73;
    transition: -webkit-transform 1s;
    transition: transform 1s;
    transition: transform 1s, -webkit-transform 1s
}

.scroll-num .animate-list li {
    width: 100%;
    height: .4rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.jackpot-container {
    width: 100%;
    margin-bottom: .2rem
}

.jackpot-container .jackpot-bg {
    position: relative;
    width: 100%;
    height: 1.81rem;
    padding-top: 1.1rem
}

.jackpot-container .jackpot-widget {
    width: 100%;
    display: flex
}

.jackpot-container .jackpot-box {
    width: 100%;
    padding-left: 1.2rem;
    position: relative
}

.jackpot-container .jackpot-num {
    display: flex;
    align-items: center
}

.jackpot-container .jackpot-num .symbol {
    height: .4rem;
    margin-right: .2rem;
    font-size: .32rem;
    color: #f7cc73;
    display: flex;
    align-items: center;
    justify-content: center
}

.jackpot-container .jackpot-num .num-list {
    height: .4rem;
    display: flex;
    color: #fff;
    overflow: hidden
}

.jackpot-container .jackpot-num .num-list .point {
    width: .14rem;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    font-size: .32rem;
    color: #f7cc73
}

.lottery {
    width: 100%
}

.lottery img {
    display: block;
    width: 100%;
    height: 100%
}

.lottery .lott-list {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    -webkit-column-gap: .33rem;
    column-gap: .33rem;
    row-gap: .2rem
}

.lottery .lott-list.game-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: .3rem
}

.lottery .lott-list.game-list .lott-game-item {
    width: 100%;
    height: auto
}

.lottery .lott-list.game-list .lott-game-item .lott-game-bg {
    width: 100%;
    background: none !important
}

.lottery .lott-list.game-list .lott-game-item .lott-game-bg .lott-game-icon {
    position: relative;
    width: 100%;
    padding-bottom: 100%
}

.lottery .lott-list.game-list .lott-game-item .lott-game-bg .lott-game-icon img.icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%
}

.lottery .lott-list .lott-game-item {
    width: 100%;
    height: 1.6rem;
    overflow: hidden
}

.lottery .lott-list .lott-game-bg {
    position: relative;
    height: 100%;
    padding: .2rem .148rem .05rem .18rem;
    border-radius: .12rem
}

.lottery .lott-list .lott-game-bg .fav-icon {
    position: absolute;
    right: .1rem;
    top: .1rem;
    width: .3rem;
    height: .3rem;
    color: #595959
}

.lottery .lott-list .lott-game-bg .fav-icon.on {
    color: #1678ff
}

.lottery .lott-list .lott-game-bg .fav-icon svg {
    display: block;
    width: 100%;
    height: 100%
}

.lottery .lott-list .lott-game {
    width: 100%;
    margin-top: .1rem
}

.lottery .lott-list .lott-name {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    padding-right: .5rem;
    flex: 1 1 0%
}

.lottery .lott-list .lott-name h6 {
    font-size: .24rem;
    color: #fff;
    font-weight: 700;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.lottery .lott-list .lott-game-info {
    display: flex;
    align-items: center
}

.lottery .lott-list .lott-game-info .lott-icon {
    flex-shrink: 0;
    position: relative;
    width: .6rem;
    height: .6rem;
    margin-right: .15rem;
    overflow: hidden
}

.lottery .lott-list .lott-game-info .lott-icon img {
    display: block;
    width: 100%;
    height: 100%
}

.lottery .lott-list .draw-time {
    width: .68rem;
    height: .56rem;
    display: flex;
    align-items: flex-end
}

.lottery .lott-list .win-time {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    font-size: .24rem;
    font-weight: 500
}

.lottery .lottery-time {
    line-height: 1;
    margin-top: .05rem;
    font-size: .24rem;
    color: #1678ff
}

.promo-count-down,
.promo-count-down .count-num {
    display: flex;
    align-items: center
}

.promo-count-down .count-num {
    justify-content: center;
    width: 100%;
    height: .63rem;
    border-radius: .1rem;
    font-size: .26rem;
    font-weight: 900;
    color: #fff;
    background-color: #4b4b8b
}

.promo-count-down .count-num .symbol {
    margin: 0 .1rem
}

.promo-count-down .section-count.day {
    width: 1.2rem;
    margin-right: .2rem
}

.promo-count-down .section-count.time {
    width: 2.1rem
}

.promo-count-down .section-count .unit {
    display: flex;
    justify-content: space-evenly;
    margin-top: .1rem;
    font-size: .24rem;
    font-weight: 500;
    color: #b2b2b2
}

.home-section .promo-list {
    position: relative;
    display: flex;
    flex-wrap: nowrap;
    padding-left: .3rem;
    overflow-x: auto;
    overflow-y: hidden
}

.home-section .promo-list .promo-item {
    position: relative;
    flex-shrink: 0;
    width: 6rem;
    margin-right: .2rem;
    border-radius: .1rem;
    background-color: #131723;
    overflow: hidden
}

.home-section .promo-list .promo-image {
    display: block;
    width: 100%
}

.home-section .promo-list .promo-info {
    height: 2rem;
    padding: .23rem .3rem .15rem .29rem
}

.home-section .promo-list .promo-info .item-title {
    font-size: .3rem;
    font-weight: 900;
    color: #e6d39b;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis
}

.home-section .promo-list .promo-info .promo-end-date {
    margin-top: .2rem;
    display: flex;
    justify-content: space-between
}

.home-section .promo-list .promo-info .promo-no-expiry {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.8rem;
    height: .63rem;
    border-radius: .1rem;
    font-size: .26rem;
    font-weight: 900;
    color: #fff;
    text-align: center;
    background-color: #4b4b8b
}

.home-section .promo-list .promo-info .more-btn {
    min-width: 1.69rem;
    height: .63rem;
    line-height: .63rem;
    padding: 0 .2rem;
    border-radius: .2rem;
    font-size: .24rem;
    font-weight: 900;
    color: #000;
    text-align: center;
    box-shadow: inset 0 0 .06rem 0 #ffb206;
    background-image: linear-gradient(180deg, #fff79d, #ffd800)
}

.lott-list-wrap {
    width: 100%;
    height: 100%;
    overflow: auto
}

.lott-list-wrap .lottGame-list .week-menu {
    width: 100%;
    padding: .18rem .1rem 0;
    display: flex;
    justify-content: space-between;
    align-items: center
}

.lott-list-wrap .lottGame-list .week-menu .lott-history svg {
    display: block;
    width: .34rem;
    height: .34rem
}

.lott-list-wrap .lottGame-list .week-menu .lott-item {
    font-size: .23rem;
    color: #fff
}

.lott-list-wrap .lottGame-list .week-menu .lott-item.on {
    color: #1678ff
}

.lott-list-wrap .lottGame-list .lottGame-scroll {
    padding: .2rem .18rem .4rem .2rem
}

.terms-container {
    width: 100%;
    padding: 0 .33rem;
    margin-top: .4rem
}

.terms-container .bonus-img {
    display: block;
    width: 100%
}

.terms-container .terms-conditions {
    margin-top: .25rem;
    font-size: .24rem;
    font-weight: 500;
    line-height: 1.46;
    text-align: center
}

[data-theme=light] .terms-container .terms-conditions {
    color: #fae990 !important
}

[data-theme=dark] .terms-container .terms-conditions {
    color: #fff !important
}

.terms-container .terms-conditions .terms-link {
    text-decoration: underline
}

[data-theme=light] .terms-container .terms-conditions .terms-link {
    color: #152c5b !important
}

[data-theme=dark] .terms-container .terms-conditions .terms-link {
    color: #501c70 !important
}

.shell-modal-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10001;
    overscroll-behavior: none
}

[data-theme=light] .shell-modal-mask {
    background: rgba(56, 59, 62, .6) !important
}

[data-theme=dark] .shell-modal-mask {
    background: rgba(4, 11, 38, .88) !important
}

.terms-popup {
    width: 100vw;
    height: 100vh;
    height: calc(var(--vh, 1vh)*100);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10001
}

.terms-popup .terms-popup-content {
    display: flex;
    flex-direction: column;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 10002;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    width: 6.2rem;
    height: 7.25rem;
    padding: .35rem 0;
    border-radius: .2rem
}

[data-theme=light] .terms-popup .terms-popup-content {
    background: #f1f5fc !important
}

[data-theme=dark] .terms-popup .terms-popup-content {
    background: #374057 !important
}

.terms-popup .terms-popup-content .terms-title {
    flex-shrink: 0;
    margin-bottom: .3rem;
    font-size: .32rem;
    color: #fff;
    text-align: center
}

.terms-popup .terms-popup-content .terms-content {
    flex: 1 1 0%;
    width: 100%;
    padding: 0 .35rem;
    font-size: .26rem;
    overflow-y: auto;
    overflow-x: hidden
}

[data-theme=light] .terms-popup .terms-popup-content .terms-content {
    color: #152c5b !important
}

[data-theme=dark] .terms-popup .terms-popup-content .terms-content {
    color: #fff !important
}

.terms-popup .terms-popup-content .terms-content .terms-item {
    margin-bottom: .5rem
}

.terms-popup .terms-popup-content .terms-content .terms-item h5 {
    font-size: .36rem;
    margin-bottom: .1rem;
    text-align: center
}

.terms-popup .close-terms {
    position: absolute;
    left: 50%;
    bottom: -.7rem;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    width: .45rem;
    height: .45rem;
    color: #fff
}

.twofa-tabs {
    padding: 0 .3rem .3rem;
    padding-bottom: 0;
    font-size: .28rem
}

.twofa-tabs .tab-list {
    display: flex
}

.twofa-tabs .tab-list-item {
    display: flex;
    flex-direction: column;
    list-style: none;
    margin-bottom: -1px;
    flex: 1 1 0%;
    border-bottom: .06rem solid #e3e3e3
}

.twofa-tabs .tab-list-active {
    background-color: #fff;
    border-bottom: .06rem solid #17a6ff
}

.twofa-tabs .tab-list-item-wrap {
    display: flex;
    flex-direction: column;
    align-items: center
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap {
    display: flex;
    align-items: center;
    height: .64rem;
    margin-bottom: .31rem
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap .tab-list-icon {
    width: auto;
    height: .64rem
}

.twofa-tabs .tab-list-item-wrap .tab-list-icon-wrap .tab-list-icon.email {
    height: .43rem
}

.twofa-tabs .tab-list-item-wrap .desc-text {
    margin-bottom: .14rem;
    text-align: center
}

.login-2fa-btn {
    display: flex;
    align-items: center;
    border-radius: .05rem;
    border: unset;
    padding: .2rem .72rem
}

.login-2fa-btn.loading {
    background-color: #a2c0d3;
    padding: .2rem .72rem;
    padding-left: .38rem
}

.login-2fa-btn .spinner-grow {
    display: inline-block;
    width: .26rem;
    height: .26rem;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    -webkit-animation: spinner-grow .75s linear infinite;
    animation: spinner-grow .75s linear infinite;
    margin-right: .08rem
}

@-webkit-keyframes spinner-grow {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    50% {
        opacity: 1
    }
}

.login-2fa-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100012;
    background-color: rgba(0, 0, 0, .5);
    color: #797979
}

.login-2fa-modal.show-modal .login-2fa-body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}

.login-2fa-modal.hide-modal {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.login-2fa-modal.hide-modal .login-2fa-body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}

.login-2fa-modal .login-2fa-header {
    position: relative;
    height: 1.01rem;
    line-height: 1.01rem;
    background-color: #f8f8f8;
    border-radius: .15rem .15rem 0 0;
    padding-left: 5%;
    font-size: .34rem;
    border-bottom: 1px solid #dbdbdb
}

.login-2fa-modal .login-2fa-header .close-btn {
    position: absolute;
    top: 50%;
    right: 5%;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    color: #797979;
    fill: #797979;
    width: .45rem;
    height: .45rem
}

.login-2fa-modal .login-2fa-body {
    position: relative;
    width: 6.6rem;
    height: auto;
    color: #666;
    background-color: #fff;
    box-shadow: 0 0 .2rem 0;
    border-radius: .15rem
}

.login-2fa-modal .login-2fa-body .desc {
    font-size: .26rem;
    line-height: 1.4;
    padding: .3rem .3rem .78rem
}

.login-2fa-modal .verify-form {
    padding: .41rem 0 0
}

.login-2fa-modal .verify-form .form-input-group {
    display: flex;
    margin-bottom: .31rem
}

.login-2fa-modal .verify-form .form-input-group.right-side {
    justify-content: flex-end
}

.login-2fa-modal .verify-form .form-input-group.right-side .submit-verify-btn {
    display: inline-block
}

.login-2fa-modal .verify-form .form-input-group.left-side {
    justify-content: flex-start
}

.login-2fa-modal button {
    display: flex;
    align-items: center;
    border-radius: .05rem;
    border: unset;
    padding: .2rem .72rem
}

.login-2fa-modal button.loading {
    background-color: #a2c0d3;
    padding: .2rem .72rem;
    padding-left: .38rem
}

.login-2fa-modal .submit-verify-btn {
    display: block;
    background-color: #17a6ff;
    color: #fff;
    font-size: .3rem
}

.login-2fa-modal .submit-sms-btn {
    display: block;
    color: #fff;
    font-size: .3rem
}

.login-2fa-modal .submit-sms-btn,
.login-2fa-modal .submit-sms-btn.loading {
    background-color: #a2c0d3;
    padding: .2rem .28rem
}

.login-2fa-modal .submit-sms-btn.countdown {
    min-width: 1.8rem
}

.login-2fa-modal .spinner-grow {
    display: inline-block;
    width: .26rem;
    height: .26rem;
    background-color: currentColor;
    border-radius: 50%;
    opacity: 0;
    -webkit-animation: spinner-grow .75s linear infinite;
    animation: spinner-grow .75s linear infinite;
    margin-right: .08rem
}

.login-2fa-modal input[type=text] {
    font-size: .28rem;
    height: .9rem;
    background-color: #fff;
    border-radius: .05rem;
    border: 1px solid #dbdbdb;
    color: #919191;
    transition: border-color .3s ease 0s;
    outline: none;
    -webkit-appearance: none
}

.login-2fa-modal input[type=text]:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 10rem #fff inset !important;
    -webkit-text-fill-color: #919191 !important
}

.login-2fa-modal input[type=text]::-webkit-input-placeholder {
    color: #c9c9c9
}

.login-2fa-modal input[type=text]:focus {
    background-color: #fff;
    border-radius: .05rem;
    border: 1px solid #676767
}

.login-2fa-modal input[type=text].error-tip {
    border: 1px solid #ef4743
}

.login-2fa-modal .country-code-select {
    border-radius: .05rem;
    border: 1px solid #dbdbdb;
    transition: border-color .3s ease 0s;
    flex: 1 1 0%;
    justify-content: left;
    color: #919191
}

.login-2fa-modal .country-code-select i,
.login-2fa-modal .country-code-select img {
    margin-right: .08rem
}

.login-2fa-modal .country-code-select+input {
    flex: 2 1 0%
}

@keyframes spinner-grow {
    0% {
        -webkit-transform: scale(0);
        transform: scale(0)
    }
    50% {
        opacity: 1
    }
}

.country-code-select {
    width: 1.3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: .1rem;
    color: inherit
}

.country-code-select span.country-code {
    font-weight: 400;
    font-size: .25rem
}

.country-code-select img {
    width: .4rem;
    height: .4rem;
    position: relative;
    display: inline-block
}

.country-code-select i {
    width: 0;
    height: 0;
    border: .08rem solid transparent;
    border-top: .08rem solid red;
    position: relative;
    top: .07rem
}

.popup-country-code-select {
    max-height: 6rem;
    overflow-y: scroll
}

.popup-country-code-select .am-list-item .am-list-line .am-list-content {
    display: flex;
    align-items: center;
    font-size: .28rem
}

.popup-country-code-select .header-wrap {
    position: relative;
    display: flex;
    align-items: center
}

.popup-country-code-select .header-wrap .close-btn {
    position: absolute;
    top: 50%;
    right: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    line-height: 0
}

.popup-country-code-select .am-list-header {
    height: .8rem
}

.popup-country-code-select .am-list-body {
    overflow-y: scroll;
    height: 4.6rem
}

.popup-country-code-select img {
    width: .5rem;
    height: .5rem;
    position: relative;
    margin-right: .2rem;
    display: inline-block
}

.login-choose-confirm .button-group {
    display: flex;
    flex-direction: column;
    margin-top: .3rem
}

.login-choose-confirm .button-group button {
    margin-bottom: .12rem;
    border-radius: .14rem;
    padding: .14rem 0;
    font-size: .3rem;
    color: #108ee9;
    border: .03rem solid transparent
}

.footer-home-screen {
    width: 100%;
    z-index: 99999;
    position: fixed;
    left: 0;
    bottom: .36rem !important;
    padding: 0;
    text-align: center;
    background-color: transparent;
    transition: all .5s
}

.footer-home-screen.footer-home-screen-hide {
    bottom: -4rem !important
}

.footer-home-screen.footer-home-screen-hide .model-mask {
    display: none
}

.footer-home-screen .model-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .3)
}

.footer-home-screen .icon {
    width: .44rem;
    height: .44rem;
    fill: #fff
}

.footer-home-screen .icon__share {
    width: .6rem;
    height: .6rem;
    margin: 0;
    -webkit-transform: translateY(10%);
    transform: translateY(10%)
}

.footer-home-screen .icon__close-button {
    position: absolute;
    top: .45em;
    right: .45em;
    width: .875em;
    height: .875em
}

.footer-home-screen svg {
    position: relative;
    top: .1rem;
    margin-right: .05rem
}

.footer-home-screen .add-home-screen {
    display: inline-flex;
    flex-direction: column;
    max-width: 5.9rem;
    align-items: flex-start;
    position: relative;
    background-color: hsla(0, 0%, 44%, .8);
    border-radius: .3em;
    margin: 0 auto;
    padding: .875em
}

.footer-home-screen .add-home-screen:after {
    content: "";
    position: absolute;
    bottom: -.36rem;
    left: calc(50% - .18rem);
    width: 0;
    height: 0;
    border-style: solid;
    border-width: .36rem .18rem 0;
    border-color: hsla(0, 0%, 44%, .8) transparent transparent
}

.footer-home-screen .app-icon {
    width: 1.28rem;
    height: 1.28rem;
    vertical-align: middle;
    border-radius: .8em;
    flex: 0 0 1.28rem
}

.footer-home-screen .home-screen-tips {
    display: flex;
    font-size: .24rem;
    line-height: 1.5;
    text-align: left;
    padding-left: 1em;
    color: #fff
}

.footer-home-screen .home-screen-tips .label {
    display: inline-block;
    line-height: normal;
    -webkit-transform: translateY(8%);
    transform: translateY(8%)
}

.footer-home-screen .add-app-icon {
    display: inline-block;
    width: .1rem;
    height: .1rem
}

.footer-home-screen .home-screen-tips-second {
    display: flex
}

.footer-home-screen .home-screen-tips-first .title {
    display: block;
    font-weight: 700;
    text-align: left;
    color: #fff;
    font-size: .36rem;
    padding-bottom: .2rem
}

.footer-home-screen .home-screen-tips-first .content {
    display: block;
    text-align: left;
    color: #fff;
    font-size: .24rem;
    padding-bottom: 1em;
    line-height: 1.5
}

.footer-home-screen .home-screen-tips-first h4,
.footer-home-screen span {
    line-height: 1
}

.footer-home-screen .btn-primary {
    height: auto;
    padding: .14rem;
    margin: 0 auto;
    font-size: .3rem
}

.footer-home-screen .home-screen-tips-profile {
    width: 100%
}

.download-bar-model {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001
}

.download-bar-model.hide-model {
    display: none
}

.download-bar-model .bar-header {
    display: flex;
    flex-wrap: wrap;
    border-top-left-radius: .14rem;
    border-top-right-radius: .14rem;
    background-color: #e4f3ff;
    padding-top: .2rem
}

.download-bar-model .bar-header__break {
    flex-basis: 100%;
    height: 0
}

.download-bar-model .bar-header__footer {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%
}

.download-bar-model .bar-header__btn {
    font-size: .3rem;
    color: #fff;
    background: linear-gradient(-90deg, #428ff2, #37e4f4);
    border-radius: .43rem;
    margin-bottom: .24rem;
    padding: .14rem .28rem
}

.download-bar-model .bar-header__icon {
    display: flex;
    align-items: center
}

.download-bar-model .bar-header__icon i {
    display: block;
    width: 1.83rem;
    height: 1.5rem;
    margin-left: .06rem;
    margin-right: .12rem;
    margin-bottom: .2rem;
    background: url(header-bg.19e32c82.png) no-repeat;
    background-size: 100% 100%
}

.download-bar-model .bar-header__content {
    flex: 1 0 0%
}

.download-bar-model .bar-header__title {
    font-size: .28rem;
    color: #008aff;
    margin-bottom: .08rem
}

.download-bar-model .bar-header__list {
    font-size: .26rem;
    color: #666;
    padding-bottom: .18rem;
    margin-right: .2rem;
    line-height: 1.4
}

.download-bar-model .bar-body {
    padding: 0 .35rem .6rem
}

.download-bar-model .bar-model-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .6)
}

.download-bar-model .bar-model-close {
    position: absolute;
    bottom: -1.2rem;
    left: 50%;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEwAAABMBAMAAAA1uUwYAAAAKlBMVEVHcEySkpKSkpKSkpKSkpKSkpKSkpKSkpKSkpL///+5ubm2trb29vbT09OBnB+UAAAACHRSTlMAQIXusdYnDNXMRT4AAAH0SURBVEjHpZa/TsNADMYDArpGZenYSiCxsqCOFQsZK1CljJFYOlaIgbUkba0WHuAa2BuJB+jAG5QX4G048qc525fECE9t8ul3F/s7nx0HRat9NuqHo+vexKmJtgd5RL1K0ck5GHEbVKg8QBEFEpVd17oAFs/8Q07BEpdUdQTWuCEb8+2yWdC8JFv2oF8lC10JDOGqYQh3DzVxtc+sXyebFTk+rFNBOMhlnVoZTPM1+/WycCJYU0c3lY2bZPNU5jXJojS30BhBtYXMGKCtrb62xruPHdpcae4XtS5VS/VWml3LfEOmNvs/iSGboS9YqRK3VCou37gouUmJ0z+3ZoKPwdxPgcMwGDpPYMNhGCywPQocgWmT4MOe4whMZwTLMhyFaRkpfIqjMF18cg5+cQym80utq0nfFAYhk2kUg2kZc43GURgAl70q9c4eskVT2oYt6tv2tm6UZV+6oQnxGCxeMlxEipUVIKG4iNc0Nn23r2mHw4DhptiWRTUpboFMXlqD4IboyJTWILguaiGGzzDONY+z6TPkuRlpDoY1EtIcxoYstveQOWpcuCN97lDjErZBYVOVtugHSU8VXx8t2WXU1MsHf7sohdeu9BIXjgTSAUM4rkiHH+dRNEpJBzPpmCccGnVW6Ajq/meglY7H2bB9Zxu2fwAaDcMo+xElUgAAAABJRU5ErkJggg==) no-repeat;
    background-size: 100% 100%;
    width: .76rem;
    height: .76rem;
    margin-left: -.38rem
}

.download-bar-model .bar-model-content {
    position: relative;
    top: 50%;
    -webkit-transform: translateY(-60%);
    transform: translateY(-60%);
    background-color: #fff;
    margin: 0 .5rem;
    border: 1px solid #fff;
    border-radius: .14rem
}

.download-bar-model .bar-model-content .item-link {
    display: block;
    line-height: .86rem;
    height: .86rem;
    border-radius: .43rem;
    color: #fff;
    font-size: .3rem;
    margin-top: .6rem;
    text-align: center;
    background: linear-gradient(-90deg, #fe766c, #ec1468)
}

.download-bar-model .bar-model-content .item-link.iphone-link1 {
    background: linear-gradient(-90deg, #7f55f3, #d89afd)
}

.download-bar-model .bar-model-content .item-link.iphone-link2 {
    background: linear-gradient(-90deg, #3887fe, #2bd5f0)
}

.download-bar-model .bar-model-content .item-link.main-home {
    background: linear-gradient(-90deg, #64c213, #a8eb25)
}

.download-bar-model .bar-model-content .item-link img {
    width: .55rem;
    height: .55rem;
    display: inline-block;
    vertical-align: middle;
    margin-right: .5em;
    margin-top: -.06rem
}

.app-win {
    width: 100%;
    position: fixed;
    text-align: left;
    padding: .2rem 0;
    background-color: rgba(0, 0, 0, .5);
    z-index: 1
}

.app-win-hide {
    display: none
}

.app-win.center {
    text-align: center
}

.app-win.center a {
    width: 50%
}

.app-win.close {
    display: none
}

.app-win a {
    display: inline-block;
    width: 36%;
    height: .69rem;
    line-height: .65rem;
    font-size: .28rem;
    color: #fff;
    text-align: center;
    border: 1px solid #fff;
    margin-right: .1rem;
    border-radius: .1rem;
    margin-left: .36rem
}

.app-win a svg {
    position: relative;
    top: .1rem;
    margin-right: .05rem
}

.app-win .app-close {
    float: right;
    position: relative;
    width: .4rem;
    height: .4rem;
    border-radius: 50%;
    font-size: .32rem;
    border: .02rem solid #fff;
    right: .4rem;
    top: .15rem
}

.app-win .app-close b {
    position: absolute;
    width: .02rem;
    height: .2rem;
    background: #fff;
    top: .08rem;
    left: .17rem
}

.app-win .app-close .clear-left {
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg)
}

.app-win .app-close .clear-right {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg)
}

.app-win .icon {
    width: .44rem;
    height: .44rem;
    fill: #fff
}

.app-win .icon__share {
    margin-bottom: -.45em;
    width: .6rem;
    height: .6rem
}

.app-win .icon__close-button {
    position: absolute;
    top: .45em;
    right: .45em;
    width: .875em;
    height: .875em
}

.app-win .center {
    text-align: center
}

.download-bar {
    position: fixed;
    top: 0;
    z-index: 10001;
    transition: all .5s;
    height: 1.7rem !important;
    width: 100%;
    padding-left: 1.8rem;
    padding-top: .3rem;
    color: #fff;
    background: url(downloadBar.44ab92da.png) no-repeat;
    background-size: 100% 100%
}

.download-bar.show-bar+.this-mc-header+div .banners {
    margin-top: 0
}

.download-bar.show-bar+div+form {
    background-position: 100% 1.7rem
}

.download-bar.dark {
    background: url(downloadBar-dark.45159f37.png) no-repeat;
    background-size: 100% 100%
}

.download-bar.dark .download-bar-close {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoBAMAAAB+0KVeAAAALVBMVEVHcExtbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW1tbW3y8vKEhIT////i4uKbm5tXa1iLAAAACXRSTlMA7ZiwF9uAWDelI5zmAAABBElEQVQoz2NggIA2U8mJwRkMyIAldSYYiDkgiWnOhIJJCNHCmXAgDhNjmokEFKCClsiCkyFizDNRgAFYUBNVcBJIjH0mGigACjqiC4pg6gbrZ5mJARwY2MD0rZ1gatdNEJnAwArmrVkNImevAQsGMHRClJzaCSdnzmDInAlRsxpKAME0mB9BiqAKgT6NnAlTClM4cyqD5EyY0ldQhTMnMsAcN3vNmdUwNlxw5pszL+GCkpgqJ8IsQjZzKsxJyLZPhjoexZ3ToN5E8dEMaICg+D0AGnQooZQADeS7ENP23oQEMtbowB5xWKMYa2LAnmywJjDsSRFrosWevOEZIcwBJX+gZBkAbnV94JT+2vYAAAAASUVORK5CYII=) no-repeat;
    background-size: .42rem .42rem
}

.download-bar.dark .download-bar-btn {
    background: linear-gradient(180deg, #fe303c, #f8717a)
}

.download-bar.dark .download-bar-gift {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABFBAMAAAABYV0kAAAAKlBMVEVHcExhQtVyTeWJXfaVZP1qSN+QYfhjRNhqSd9+VvByTeaJXfZ3UeqTY/yuTh26AAAAB3RSTlMAiB/fH98jiL2ZngAAAVtJREFUSMeFzvGJwyAYBfAgXeBmKB2i9PpXh7hw3B66gdINJA5QQhcI0gGEbCDucp81JGmivqcREl5+fk2DcoCN5oaRK0YCRgJGAkYCRgJGAkYCRgJGAkZCHfGgcrj64Ck1xIfXq6oQklJB7JQycvFImRGLEY8RW0Rg5WYH200pIQNSbtSg/0minW2wy7BKtnK0na1fxM56nSyiNbiInbsOKMf4vaqkSYx2aWuXQYzW9XHZ2RFhkhQxnZskNgyFjtxFhDhnaOlpObdHzKwkaXsR+36O22yR0bjt3iLjCJTjiCrsD1ZO4zOTD+S3z+UD6VGlgPQY6dfII58VogpZkFblkfsKeTzqSkRUfZbiJLPC2rtS92rlpCS9CFmusFYIKWONWtu9IEKJuPdZEPrjDe1WQngtb+SHS8HjI7nYLb4gMh6ZhpwRSYfY35KU+iSxQgiqIIRjhGOEN18w//cSqnAM+RMAAAAAAElFTkSuQmCC) no-repeat;
    background-size: 100% 100%
}

.download-bar.hide-bar {
    top: -1.7rem
}

.download-bar.hide-bar+.this-mc-header {
    top: 0 !important;
    transition: all .5s
}

.download-bar.hide-bar+.this-mc-header+div {
    transition: all .5s
}

.download-bar.hide-bar+div+form {
    padding-top: 0 !important;
    margin-top: 0 !important;
    transition: all .5s
}

.download-bar.hide-bar .download-bar-gift {
    display: none
}

.download-bar.show-bar+.this-mc-header {
    top: 1.7rem !important
}

.download-bar.show-bar+.this-mc-header+div {
    margin-top: 2.7rem !important
}

.download-bar+div+form {
    padding-top: 1.7rem !important;
    transition: all .5s
}

.download-bar .download-bar-icon {
    position: absolute;
    left: .2rem;
    top: .16rem;
    width: 1.4rem;
    height: 1.4rem;
    overflow: hidden;
    background-color: #fff;
    border-radius: .32rem
}

.download-bar .download-bar-icon img {
    width: 1.4rem;
    height: 1.4rem
}

.download-bar .download-app-name {
    font-size: .32rem
}

.download-bar .official-logo {
    color: #34bff9;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAAAmCAMAAABK8c3RAAAAUVBMVEVHcExl5ns8xeU5w+w3wvM1wPdj5X9k53xN1LxO1Lru8/hm53g0wPlN1LdW3J5c4ZA9x99I0MZDzNNl5YQ8xO9S2Kvp8/fU8eLM6/eb67CB1vS0g3C3AAAACnRSTlMAvPaQzpTXkioGx0Q6pwAAAMVJREFUSMfd1ksSgyAURFGMIAgooPz3v9CgZgW0Ayp3AadeMaEJaQm+qK4WLsjTyhUQX29jUqqkKDuKqSg1XQo/zyS7S+fJ23tAxq2IdkiRUKWdsmCHXKcsxJiIIdGYhkiwoZB9R5F9/zckBBQJ4R1k21Bk20ZC3AuIgxHnBkKOA0WOYyDEWhSxdiDkY9Evw34I9RlDsqdk9hVDqp+J8Ngp2fs2dBikNINdI4dqXXPfyMlVa/pMJaaB2PpbbYLRPoGye/h9ARTMaR1e9J4cAAAAAElFTkSuQmCC) no-repeat;
    background-size: 100% 100%;
    padding: 0 1em;
    min-width: .68rem;
    font-size: .2rem;
    display: inline-block;
    vertical-align: middle;
    height: .38rem;
    margin-left: .13rem;
    line-height: .38rem
}

.download-bar .app-star-grade {
    margin-top: .12rem;
    height: .25rem;
    font-size: .24rem;
    font-weight: 700;
    color: #fff
}

.download-bar .app-star-grade .star-icon {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAZCAMAAAAc9R5vAAAAilBMVEVHcEz/tDn/pDP/hCj/tzr/uDr/sTj/iyr/mC//oDL/hin/kS3/vDv/tDn/uDr/nzH/tjn/uTr/gif/hSj/ky3/xD7/wj7/qDT/ly//mzD/yED/gyj/li7/sDj/rTf/qTX/pjT/ojP/njL/hCj/mzD/jiz/tDn/iyv/kS3/xD7/iCr/tzr/vjz/ujvW3v2tAAAAHHRSTlMA2CDPaxDucj4JldasWTgwwpTqrvXR7/LmSd+FnMeDwQAAALhJREFUKM910VcWgyAQBVAQBazYTS+mYsz+txfEHJBA7o8678gMAIDWbALg4nOeOIOWc+76JRV1XjiCZhTa0Kpno+RbQf6WoFEMWZq8vhI/myfwihziuwHDvMgA3rsBeHDCwOuOLlQ06U42OrVnZ0s6T0v7H2qX6+tST/QO493CCvwJan2QQf1coiqgN4PuQR6GSAXRIMVoflYqqC7CIFYg5fSGVIDEV+TJm9lOiRqLlEhNwqpS9vgAHVooz6vuSbUAAAAASUVORK5CYII=) no-repeat;
    background-size: .24rem .25rem;
    height: .25rem;
    width: .24rem;
    margin-right: .08rem;
    display: inline-block
}

.download-bar .app-star-grade .grade-text {
    display: inline-block;
    vertical-align: top;
    background: linear-gradient(0deg, #ff8027, #ffc840);
    -webkit-background-clip: text;
    color: transparent
}

.download-bar .download-bar-gift {
    position: absolute;
    width: .68rem;
    height: .69rem;
    bottom: 0;
    right: 0;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEQAAABFBAMAAAABYV0kAAAALVBMVEVHcEwaEYYhGZohGZovL8MvLcEaEYYdFJAhGZkaEYkuLcIkHKEpJLQmH6cnIa1Q9nYTAAAAB3RSTlMAiB/fH99oabMBhgAAAT5JREFUSMeFz+FpwzAQBWBjskBnMJ2hCaX9E7JE6J9OIWeCCDyA8AqHBwh4AyOygTtB6hkixQhb1p3eswQyfnw6FwXKDjaKE0bOGCGMEEYII4QRwghhhDBCGCGMEEYII4QRwkimcupQZXceaMhXwiRyxSEh8iQ+NLiVQdxX2w2W5N/xd3imE5Afu0RA7OuOeWGEr7x7JFuJEcsjd5dcxSEP9+UewiIPm1fK7ylOWjlOqJIgE0aSSvk1bpMg49/4Hy+MjAmCKhyyqRx7JjHyCStVjyo80sdIwwUja6XSfFbIvtE3bq2QRjda39yzOawQLXTgJEul3NdSMBKU8kOJCYi6ignItVbSExAlNuplkosyhtnKzIg7+jfFbKNmxBX8uzHpnpWqzcYjB5PNC7lk45EWXQQmcRWItBhpi983lCfWftfWyzc+JQAAAABJRU5ErkJggg==) no-repeat;
    background-size: 100% 100%
}

@-webkit-keyframes heartBeat {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    14% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }
    28% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    42% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }
    70% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

@keyframes heartBeat {
    0% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    14% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }
    28% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
    42% {
        -webkit-transform: scale(1.3);
        transform: scale(1.3)
    }
    70% {
        -webkit-transform: scale(1);
        transform: scale(1)
    }
}

.download-bar .download-bar-gift .animate__heartBeat {
    -webkit-animation-name: heartBeat;
    animation-name: heartBeat;
    -webkit-animation-duration: 1.8s;
    animation-duration: 1.8s;
    -webkit-animation-duration: calc(var(--animate-duration) * 1.8);
    animation-duration: calc(var(--animate-duration) * 1.8);
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite
}

.download-bar .download-bar-gift .gift-icon {
    display: block;
    width: .61rem;
    height: .56rem;
    margin: 0 auto;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAD0AAAA4CAMAAAB5cQBoAAAAh1BMVEVHcEz/xRb/2kT/vxf/uRT/sxr/vBT/thr/yzD/uyr/thL/uxL/vBH/tRT/3GD/0D3/xQ3/2EL/pQ3/32j/rCL/uCn/tRL/10H/yj//uRz/4XL/xjH/vxf/sib/xBz/21z/0Er/zzr/viH/20X/0z3/yjX/1lP/5Xv/wi3/pg//qAz/xg3/vQ4lZyNhAAAAFHRSTlMA3/KSutcpOwzf92sUYt/eUuPk3z72+34AAAIpSURBVEjHzZWLcqMgFIa1EOMtm6RVo1HUkJtd+/7Pt4BcZxVTnen0N5HI4ZsfDwfiOC9o66wRXAPvst1yeBtm4eK578Isy8Jl7h7MBkHv+3AUZkJh9MPeK997bc5XrvfKWnM853dp1b7zN/EKGGbQXzztDamgzcLJR7x6bZW/A+ayge03vH2313fGwe3B+HuPVsOh73tXzC0G9Ckey3k8mn3YU8GYG1NFI+s9nn0v/GJyD44Php9f8OXsA3moACiPl+2L2fff//6vTzDj7XNFn2N693h4our2b4OuJ6YzVUO/N/bMo2+au57z49XQ80yuJ72b/RO7/3jSdT1zPRuj/zRRYQZ9Oyn69gq9v+nS6IsRmKIvhhR9N/qn6LsujS6NwAQdlLruiu6MwATddcOwjun+FGqwEZmgAYApE8aY3Buhthg6aKQDYProT6iKqipII2aad8MzjdSu7cSjQzI+OBcqFVwHMzR3TgpFcxjXdbGx0xKuCMY+eSmdi8ROK7hS3tI5sdMarGaeK3iOJkMQg5FgW+KNGIxm6IoNYTPQaQGjGW9ExJyTtBUqh2mTSFJY11vCCCk6p9mmkSSt52gOIyzgx6UaYJTWM3Qq4KLWaCxge60xmG2Hqn4IXQpEehiczq43hUmLBdzkiO0vCiOrt3QmraRbJOA0tdMCxmmn9jeh8QDbaSxhejo82NW0CrbTGqy8HwzG83RA5LoubYI/XB972RUE6m/8HzWdgjVv0Yg0AAAAAElFTkSuQmCC) no-repeat;
    background-size: 95% 95%
}

.download-bar .download-bar-btn {
    position: absolute;
    right: .58rem;
    top: .58rem;
    min-width: 1.63rem;
    padding: 0 1em;
    height: .56rem;
    border-radius: .28rem;
    line-height: .56rem;
    font-size: .26rem;
    text-align: center;
    background: linear-gradient(-90deg, #33befd, #67e875)
}

.download-bar .download-bar-close {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAMAAADyHTlpAAAAM1BMVEVHcEwic8wic8wic8wic8wic8wic8wic8wic8wic8wic8zp8ftDiNT///9Ii9XH3PNnn9z+Ul8OAAAACnRSTlMAsySbB9KHNe74XT+7bgAAAQpJREFUOMuVlVkWgyAMRUHmiMP+V1vUWhMCxbw/jlfI8AhKEdngtYspRad9sKqvYCIgRRN6oAYm3YKtT9BQ8iyMyUFHbqoOj9BVDG9Jyk5/ycL+YrAOBnJ3bh6G8t9A0xhNV7gaXkifm8IrHdsatN7mjFbrvD0LU9LHhZoXxK5lhQpm6fkZsSv574iAVupha7LUq8r/ZhlZalB36mI5CU6x9h/szkmIireqsAsnITVQ2Au6897yAI44W7tGltaZUStWx8xy5Z45q2uz3lXirK+M9dSTsYHaBVe+YotdiAnJ10ycZSprU79m7NcguzCSayi43JKRIRhEkvEmGZqSUSwZ8JJnQ/QYjZ64Dy8SN1iQ2nBoAAAAAElFTkSuQmCC) no-repeat;
    background-size: .42rem .42rem;
    position: absolute;
    right: .2rem;
    top: .2rem;
    width: .42rem;
    height: .42rem
}

.download-bar .app-full-name-wrap {
    display: flex;
    align-items: center;
    height: 2em;
    max-width: 48%
}

.download-bar .download-bar-center {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.footer__chat-icon {
    position: relative;
    max-width: .6rem
}

.footer__chat .unread-count {
    position: absolute;
    font-family: SF Pro Display, SF Pro Icons, AOS Icons, Helvetica Neue For Number, Roboto Mono, PingFang SC, Arial, sans-serif;
    font-size: .2rem;
    min-width: 2em;
    top: -.24rem;
    right: -.4rem;
    padding: 0 .5em;
    background-color: #fa5151;
    color: #fff;
    border-radius: 1em
}

.footer__chat img.chat-icon-img {
    width: 100%;
    height: auto
}

.activity-goto-btn {
    display: block;
    text-align: center;
    border-radius: .3rem;
    margin: 1em auto;
    line-height: .6rem
}

.popup_container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100011;
    background-color: rgba(0, 0, 0, .5)
}

.popup_container.show-popup .popup_body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}

.popup_container.hide-popup {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.popup_container.hide-popup .popup_body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}

.popup_container .popup_body {
    width: 80%;
    height: auto;
    padding: .1rem;
    background-color: #999;
    border-radius: .05rem
}

.popup_container ._popup_content {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: #fff
}

.popup_container .title {
    max-width: 100% !important;
    height: .8rem;
    line-height: .8rem;
    text-align: center;
    font-size: .36rem;
    font-weight: 600
}

.popup_container .close-btn {
    position: absolute;
    right: 1em;
    top: 0;
    bottom: 0;
    width: .3rem;
    height: .3rem;
    margin: auto
}

.popup_container .close-btn svg {
    fill: currentColor;
    width: 100%;
    height: 100%
}

.popup_container .text {
    padding: .2rem;
    overflow: auto;
    box-sizing: border-box;
    max-height: 60vh
}

.popup_container .text .list {
    margin-bottom: .2rem;
    border-bottom: .02rem solid #ccc
}

.popup_container .text img {
    width: 100%
}

.popup_container .text .line {
    height: .1rem
}

.popup_container .footer {
    position: absolute;
    bottom: .1rem;
    right: .08rem
}

.popup_container .footer .am-list-item .am-list-thumb:first-child {
    margin-right: .1rem
}

.popup_container .footer .am-checkbox-wrapper {
    display: flex
}

.popup_container .footer .am-checkbox,
.popup_container .footer .am-checkbox-inner {
    width: .35rem;
    height: .35rem
}

.popup_container .footer .am-list-item,
.popup_container .footer .am-list-line {
    min-height: auto
}

.popup_container .footer .am-list-line {
    padding-right: .5em
}

.popup_container .footer .am-list-item.am-list-item-active {
    background-color: transparent
}

.popup_container .footer .am-list-content {
    font-size: .24rem;
    padding: 0
}

.popup_container .footer .am-checkbox-inner:after {
    right: .08rem;
    z-index: 999;
    width: .1rem;
    height: .16rem
}

.popup_container .wysiwyg {
    padding-bottom: .08rem !important
}

#root .popup_container .header {
    position: relative;
    height: auto !important
}

.popup_container_v2 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100011;
    background-color: rgba(0, 0, 0, .7)
}

.popup_container_v2.show-popup .popup_body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}

.popup_container_v2.hide-popup {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.popup_container_v2.hide-popup .popup_body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}

.popup_container_v2 .popup_main_header {
    border-radius: .1rem .1rem 0 0
}

.popup_container_v2 .popup_main_header+.popup_content {
    border-radius: 0 0 .1rem .1rem
}

.popup_container_v2 .popup_body {
    position: relative;
    width: 74.93%;
    padding: .1rem;
    border-radius: .05rem
}

.popup_container_v2 .popup_body.hide_btn {
    height: calc(67.02% - 1.2rem)
}

.popup_container_v2 .popup_body.hide_btn .popup_content {
    height: 100%
}

.popup_container_v2 .popup_body.hide_btn .active-btn,
.popup_container_v2 .popup_body.hide_btn .disable-btn {
    top: 50%
}

.popup_container_v2 .popup_body .active-btn,
.popup_container_v2 .popup_body .disable-btn {
    position: absolute;
    top: 45%;
    width: .56rem;
    height: .56rem;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    border-radius: 50%;
    z-index: 1
}

.popup_container_v2 .popup_body .active-btn i,
.popup_container_v2 .popup_body .disable-btn i {
    display: block;
    width: 100%;
    height: 100%
}

.popup_container_v2 .popup_body .nav-left {
    left: -.2rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.popup_container_v2 .popup_body .nav-right {
    right: -.2rem;
    display: flex;
    align-items: center;
    justify-content: center
}

.popup_container_v2 .popup_body .disable-btn {
    background-color: #757575;
    box-shadow: 0 .04rem .05rem 0 hsla(0, 0%, 55%, .37)
}

.popup_container_v2 .popup_body .active-btn {
    background-image: linear-gradient(180deg, #0cd2af, #07b8c5)
}

.popup_container_v2 .popup_content {
    width: 100%;
    height: 58.02vh;
    height: calc(var(--vh, 1vh) * 58.02);
    position: relative;
    background-color: #fff;
    border-radius: .1rem;
    padding: .3rem;
    display: flex;
    flex-direction: column;
    overflow: hidden
}

.popup_container_v2 .header {
    position: relative;
    height: auto !important
}

.popup_container_v2 .close-btn {
    position: absolute;
    right: 0;
    top: 0;
    width: 1rem;
    height: 1rem;
    z-index: 10002;
    background-color: rgba(0, 0, 0, .6);
    border-radius: 0 0 0 1rem;
    overflow: hidden;
    color: #fff
}

.popup_container_v2 .close-btn svg {
    fill: currentColor;
    width: 40%;
    height: 40%;
    position: absolute;
    right: 20%;
    top: 20%
}

.popup_container_v2 .close-btn-outside {
    position: absolute;
    right: 0;
    top: -.6rem;
    width: .5rem;
    height: .5rem;
    z-index: 10002;
    border-radius: 50%;
    overflow: hidden;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center
}

.popup_container_v2 .close-btn-outside svg {
    fill: currentColor;
    width: 50%;
    height: 50%
}

.popup_container_v2 .popup_title {
    color: #007afe;
    text-align: center;
    font-size: .32rem;
    font-weight: 800;
    margin-bottom: .3rem;
    max-height: 3rem;
    line-height: 1.3;
    width: 88%;
    word-break: break-word
}

.popup_container_v2 .text {
    overflow: hidden;
    box-sizing: border-box;
    border-radius: .1rem;
    flex: auto;
    font-size: .26rem;
    line-height: 1.5;
    color: #8a8a8a
}

.popup_container_v2 .text img {
    width: 100%
}

.popup_container_v2 .popup_pagination_footer {
    margin-top: .4rem;
    width: 100%;
    display: flex;
    justify-content: space-around;
    gap: .2rem
}

.popup_container_v2 .popup_pagination_footer.hide {
    display: none
}

.popup_container_v2 .popup_pagination_footer .action {
    flex: 1 0 0%;
    min-height: .7rem;
    border-radius: .7rem;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: .3rem;
    background: linear-gradient(180deg, #00e1fa, #4aa5fb)
}

.popup_container_v2 .popup_pagination_footer .action.disable {
    background: #eaeaea;
    color: #9d9d9d
}

.popup_container_v2 .popup_pagination_footer .action .icon-right {
    position: absolute;
    right: .3rem;
    height: .25rem
}

.popup_container_v2 .popup_pagination_footer .action .icon-left {
    position: absolute;
    left: .3rem;
    height: .25rem
}

.popup_container_v2 .popup_pagination_footer .action.active-btn {
    position: relative;
    top: unset;
    margin-top: .4rem
}

.popup_container_v2 .swiper-slide {
    overflow: hidden auto
}

.popup_container_v2 .popup_logo img {
    height: .94rem
}

.popup_container_v2 .arrow-icon {
    width: 70%;
    height: 70%;
    fill: #fff
}

.popup_container_v2 .today-bar {
    margin-top: .1rem;
    margin-bottom: -.15rem
}

.popup_container_v2 .today-bar .am-list-thumb {
    margin-right: .08rem
}

.popup_container_v2 .today-bar .am-checkbox {
    -webkit-transform: scale(.8);
    transform: scale(.8)
}

.popup_container_v2 .today-bar .am-list-item,
.popup_container_v2 .today-bar .am-list-line {
    min-height: unset
}

.popup_container_v2 .today-bar .am-list-content {
    padding: 0;
    font-size: .26rem
}

.popup_container_v2 .today-bar .am-list-item {
    padding-left: 0
}

.common-spinner-loading-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100)
}

@-webkit-keyframes c-rotator {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes c-rotator {
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@-webkit-keyframes c-colors {
    0%,
    to {
        stroke: #ff5676
    }
    40% {
        stroke: #3296ff
    }
    66% {
        stroke: #30f282
    }
    80%,
    90% {
        stroke: #fefc41
    }
}

@keyframes c-colors {
    0%,
    to {
        stroke: #ff5676
    }
    40% {
        stroke: #3296ff
    }
    66% {
        stroke: #30f282
    }
    80%,
    90% {
        stroke: #fefc41
    }
}

@-webkit-keyframes c-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35
    }
    to {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124
    }
}

@keyframes c-dash {
    0% {
        stroke-dasharray: 1, 200;
        stroke-dashoffset: 0
    }
    50% {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -35
    }
    to {
        stroke-dasharray: 89, 200;
        stroke-dashoffset: -124
    }
}

.common-spinner-loading-wrap .common-spinner-loading {
    -webkit-animation: c-rotator 1.4s linear infinite;
    animation: c-rotator 1.4s linear infinite;
    width: .65rem;
    height: .65rem
}

.common-spinner-loading-wrap .common-spinner-loading .path {
    stroke-dasharray: 187;
    stroke-dashoffset: 0;
    -webkit-transform-origin: center;
    transform-origin: center;
    -webkit-animation: c-dash 1.4s ease-in-out infinite, c-colors 5.6s ease-in-out infinite;
    animation: c-dash 1.4s ease-in-out infinite, c-colors 5.6s ease-in-out infinite
}

.country-code-input {
    width: 1.3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: .1rem;
    color: inherit
}

.country-code-input span.country-code {
    font-weight: 400;
    font-size: .25rem
}

.country-code-input img {
    width: .4rem;
    height: .4rem;
    position: relative;
    display: inline-block
}

.country-code-input i {
    width: 0;
    height: 0;
    border: .08rem solid transparent;
    border-top: .08rem solid red;
    position: relative;
    top: .07rem
}

.s-country-list-container {
    max-height: 6rem;
    overflow-y: scroll
}

.s-country-list-container .am-list-item .am-list-line .am-list-content {
    display: flex;
    align-items: center;
    font-size: .28rem;
    font-weight: 700
}

.s-country-list-container .header-wrap {
    position: relative;
    display: flex;
    align-items: center
}

.s-country-list-container .header-wrap .close-btn {
    position: absolute;
    top: 50%;
    right: 0;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    line-height: 0
}

.s-country-list-container .am-list-header {
    height: .8rem
}

.s-country-list-container .am-list-body {
    overflow-y: scroll;
    height: 4.6rem
}

.s-country-list-container img {
    width: .5rem;
    height: .5rem;
    position: relative;
    margin-right: .2rem;
    display: inline-block
}

.app-upgrade-modal {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100012;
    background-color: rgba(0, 0, 0, .5)
}

.app-upgrade-modal.show-modal .app-upgrade-body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomIn;
    animation-name: zoomIn
}

.app-upgrade-modal.hide-modal {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: fadeOut;
    animation-name: fadeOut
}

.app-upgrade-modal.hide-modal .app-upgrade-body {
    -webkit-animation-duration: var(--animate-duration);
    animation-duration: var(--animate-duration);
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-name: zoomOut;
    animation-name: zoomOut
}

.app-upgrade-modal .app-upgrade-body {
    position: relative;
    width: 6rem;
    height: auto;
    background-color: #fff;
    color: #666;
    border-radius: .2rem;
    box-shadow: 0 1px .4183rem .0517rem rgba(80, 80, 80, .65)
}

.app-upgrade-modal .app-upgrade-body .close-btn {
    position: absolute;
    right: 0;
    top: -.87rem;
    color: #fff;
    z-index: 100013
}

.app-upgrade-modal .app-upgrade-body .close-btn svg {
    fill: currentColor;
    width: .55rem;
    height: .55rem
}

.app-upgrade-modal .app-upgrade-body .banner-title {
    display: flex;
    align-items: center;
    font-size: .36rem;
    font-weight: 700;
    margin-top: .48rem;
    padding-left: .34rem;
    width: 3.6rem;
    line-height: 1;
    color: var(--s-app-modal-color, #fff);
    text-shadow: 0 .02rem .1157rem rgba(0, 0, 0, .34);
    height: 1.04rem
}

.app-upgrade-modal .app-upgrade-body .app-upgrade-banner-bg {
    width: 100%;
    height: 2.15rem;
    background-color: rgb(var(--s-app-modal-bg-rgb, 183, 153, 128));
    border-radius: .2rem .2rem 0 0
}

.app-upgrade-modal .app-upgrade-body .app-upgrade-banner {
    position: absolute;
    top: -.2rem;
    width: 100%;
    height: 2.49333rem;
    background: url(banner.865114d3.png) no-repeat 100%/100%
}

.app-upgrade-modal .app-upgrade-body .desc-wrap {
    margin-top: .56rem
}

.app-upgrade-modal .app-upgrade-body .desc-wrap .desc-text {
    text-align: center;
    font-size: .32rem;
    color: #666;
    max-width: 4.2rem;
    margin: 0 auto .35rem;
    line-height: 1.2
}

.app-upgrade-modal .app-upgrade-body .desc-wrap .download-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .32rem;
    font-weight: 700;
    margin: 0 auto .48rem;
    background-color: rgb(var(--s-app-modal-bg-rgb, 183, 153, 128));
    box-shadow: 0 .04rem .0837rem .63px rgba(var(--s-app-modal-bg-rgb, 183, 153, 128), .45);
    width: 3.07rem;
    height: .7rem;
    border-radius: .4rem;
    color: var(--s-app-modal-color, #fff)
}

.spinner-brand-wrap {
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    display: none;
    z-index: 9999;
    background: rgba(0, 0, 0, .7)
}

@-webkit-keyframes spinner-rotate {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }
    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

@keyframes spinner-rotate {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0);
        -webkit-animation-timing-function: cubic-bezier(.55, .055, .675, .19);
        animation-timing-function: cubic-bezier(.55, .055, .675, .19)
    }
    50% {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
        -webkit-animation-timing-function: cubic-bezier(.215, .61, .355, 1);
        animation-timing-function: cubic-bezier(.215, .61, .355, 1)
    }
    to {
        -webkit-transform: rotate(1turn);
        transform: rotate(1turn)
    }
}

.spinner-brand-wrap .spinner-brand {
    width: 3rem;
    height: 3rem;
    position: relative
}

.spinner-brand-wrap .spinner-bg {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 0;
    background-image: var(--s-logo-loading-bg);
    background-size: 100% 100%;
    background-position-x: -.03rem;
    -webkit-animation: spinner-rotate 1s infinite;
    animation: spinner-rotate 1s infinite;
    image-rendering: pixelated;
    -webkit-animation-direction: normal;
    animation-direction: normal;
    -webkit-animation-play-state: running;
    animation-play-state: running;
    -webkit-animation-duration: 1s;
    animation-duration: 1s
}

.spinner-brand-wrap .spinner-logo {
    width: 1.2rem;
    height: 1.2rem;
    position: absolute;
    top: .9rem;
    left: 1rem;
    bottom: 0;
    right: 0;
    z-index: 5;
    background: var(--s-logo-loading-logo) no-repeat;
    background-size: cover
}

.spinner-brand-wrap.on {
    display: flex;
    justify-content: center;
    align-items: center
}

.error-restrict {
    height: 100%;
    width: 100%;
    padding: .3rem;
    position: fixed;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.error-restrict--ip {
    background: url(ip.b8142c72.jpg) no-repeat 50% fixed;
    background-size: cover
}

.error-restrict--service {
    background: url(service.b1250272.jpg) no-repeat 50% fixed;
    background-size: cover
}

.error-restrict .error-logo {
    display: block;
    width: 2rem;
    height: 3rem;
    background-image: var(--s-logo);
    background-position: top;
    background-repeat: no-repeat;
    background-size: 100% auto
}

.error-restrict .ip-address {
    font-size: .3rem;
    font-weight: 700;
    margin-bottom: .12rem;
    text-align: center
}

.error-restrict .ip-address .ip {
    font-family: SF Pro Display, SF Pro Icons, AOS Icons, Helvetica Neue For Number, Roboto Mono, PingFang SC, Arial, sans-serif;
    margin-left: .16rem
}

.error-restrict .error-footer {
    flex: 0 1 0%;
    margin: .4rem auto
}

.error-restrict .contact-us {
    background: linear-gradient(90deg, #fe6e74, #f3077f);
    color: #fff;
    border: none;
    border-radius: .5rem;
    width: auto;
    font-size: .3rem;
    padding: 0 3em;
    text-align: center;
    display: flex;
    align-items: center
}

.error-restrict .error-icon {
    display: inline-block;
    width: .3rem;
    height: .3rem;
    margin-right: .14rem;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACIAAAAiCAMAAAANmfvwAAAAP1BMVEVHcEz///////////////////////////////////////////////////////////////////////////////9KjZoYAAAAFHRSTlMAT4gjNmP63RLERpJwz34HrNYa8D9mVWgAAAEDSURBVDjLjdPZkoQgDAXQsCnLACL8/7dOAEFtxO77kKLwlCxGgCPM2HWIogx6SHrOwpvgaZr9IGpOTBVymZNQVpFyLtKKYsPl3gluQ76TfNpOjLClBqy0jDPZLoTismtyAD7kiuNC1IXkqzTJY3XlOsxI/nDaJlFqHQ8E11e9OqHSA3k+0Y9k/04IhDnZCtmm7YLhtQ040JlwtVOW6IHYJ6BIb6aA/eflEd26x9TONW1X3MNnm4qha5eayEGcHwTKFxu6nh2TjYBYB8RKj5wEF+tHdjpHsHqMC+kboueUj3dC2l1fwuKN6CIs3LJr+fmW6OEl5PaDT4mGb0S8C2CBzh/+Aw6TNeRc0QvUAAAAAElFTkSuQmCC) no-repeat;
    background-size: 100% 100%
}

.error-restrict__text {
    position: absolute;
    height: 55%;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 0 5%;
    left: 50%;
    top: 45%;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    color: #fff;
    line-height: 1.5
}

.error-restrict__text>* {
    padding: 0 5%
}

.error-restrict__text h2 {
    text-align: center;
    margin-bottom: .5em;
    font-size: .48rem;
    line-height: 1.4
}

.error-restrict__text .detail {
    flex: 1 1 0%;
    font-size: 85%;
    overflow: auto;
    -webkit-overflow-scrolling: touch
}

.error-restrict__text .detail:lang(CN),
.error-restrict__text .detail:lang(EN) {
    font-size: 90%
}

.error-restrict__text .detail:lang(EN) {
    margin-top: 2em
}

.error-restrict__text .detail p {
    text-align: center
}

.error-restrict__text p {
    margin-bottom: 1em
}

.error-restrict__text p:last-of-type {
    margin-bottom: 0
}

.marquee .marquee_content {
    -webkit-animation: marquee var(--duration) linear var(--delay) var(--iteration-count);
    animation: marquee var(--duration) linear var(--delay) var(--iteration-count);
    -webkit-animation-play-state: var(--play);
    animation-play-state: var(--play);
    -webkit-animation-delay: var(--delay);
    animation-delay: var(--delay);
    -webkit-animation-direction: var(--direction);
    animation-direction: var(--direction)
}

@-webkit-keyframes marquee {
    0% {
        -webkit-transform: translateX(67vw);
        transform: translateX(67vw)
    }
    to {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
}

@keyframes marquee {
    0% {
        -webkit-transform: translateX(67vw);
        transform: translateX(67vw)
    }
    to {
        -webkit-transform: translateX(-100%);
        transform: translateX(-100%)
    }
}

.download-bar.show-bar+.home-header+.home-container .sticky-container .sticky__fixed {
    top: calc(var(--header-height) + var(--download-bar-height))
}

.sticky-container .sticky__inner {
    position: static;
    z-index: 100
}

.sticky-container .sticky__fixed {
    position: fixed;
    top: calc(var(--header-height) + var(--download-bar-height))
}

.mini-game-container {
    position: fixed;
    top: 80%;
    left: 4%;
    width: auto;
    height: auto
}

@media (orientation:portrait) {
    .trial-game-wrap .hide-btn.landscape {
        display: none
    }
}

@media (orientation:landscape) {
    #root.trial-game-wrap .mc-navbar-blue,
    #root.trial-game-wrap .mc-navbar-blue .return_icon {
        height: .5rem !important
    }
    .trial-game-wrap .hide-btn {
        display: none
    }
    .trial-game-wrap .records {
        font-size: .14rem !important
    }
    .trial-game-wrap #mc-header.anim-show .am-icon-lg {
        width: .34rem !important;
        height: .34rem !important
    }
    .trial-game-wrap .hide-btn.landscape {
        display: block;
        z-index: 10;
        position: absolute;
        top: 0 !important;
        left: 50%;
        -webkit-transform: translate3d(-50%, 0, 0);
        transform: translate3d(-50%, 0, 0);
        width: 3.5em;
        height: .8em;
        font-size: .24rem;
        background-color: hsla(0, 0%, 44%, .9);
        border-bottom-right-radius: .2em;
        border-bottom-left-radius: .2em
    }
    .trial-game-wrap .hide-btn.landscape svg {
        display: block;
        margin: 0 auto;
        color: #fff
    }
}

.popup-game-common .am-popup {
    bottom: 0
}

.am-fade-leave+.popup-game-common,
.am-popup-mask-hidden+.popup-game-common {
    display: none
}

#root.trial-game-wrap .mc-navbar-blue.hide-no-anim {
    height: 0 !important
}

.trial-game-wrap {
    z-index: 5;
    overflow: hidden
}

.trial-game-wrap .hide-no-anim .am-navbar-left,
.trial-game-wrap .hide-no-anim .am-navbar-right {
    visibility: hidden
}

.trial-game-wrap .records {
    display: flex;
    align-items: center;
    height: 100% !important
}

.trial-game-wrap .anim-hide .return_icon {
    font-size: 0;
    display: none !important
}

.trial-game-wrap .anim-hide .records {
    display: none !important
}

.trial-game-wrap .anim-show .return_icon {
    font-size: .36rem;
    display: flex
}

.trial-game-wrap .anim-show .hide-btn {
    display: none !important
}

.trial-game-wrap .anim-show .records {
    font-size: inherit
}

.trial-game-wrap .anim-show .records svg {
    width: .3rem;
    height: .3rem
}

.trial-game-wrap .hide-btn {
    position: absolute;
    top: 0;
    width: 1.2rem;
    height: .3rem;
    text-align: center;
    background-color: hsla(0, 0%, 44%, .9);
    border-bottom-right-radius: .2em;
    border-bottom-left-radius: .2em
}

.trial-game-wrap .hide-btn svg {
    width: .3rem;
    height: .3rem;
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%)
}

.trial-game-wrap .iframe-container {
    display: flex;
    width: 100vw;
    height: 100%;
    height: calc(var(--vh, 1vh) * 100)
}

@supports (-webkit-touch-callout:none) {
    .trial-game-wrap .iframe-container {
        height: -webkit-fill-available;
        height: stretch
    }
}

.trial-game-wrap .iframe-container iframe#third-game {
    width: 100%;
    flex-grow: 1;
    -webkit-overflow-scrolling: touch;
    background: hsla(0, 0%, 44%, .9)
}

.trial-game-wrap #mc-header {
    height: 1rem
}

.trial-game-wrap #mc-header.hide-no-anim {
    height: 0 !important
}

.trial-game-wrap #mc-header.hide-no-anim .hide-btn {
    top: 0
}

.trial-game-wrap #mc-header.hide-no-anim .return_icon {
    font-size: 0;
    display: none !important
}

.trial-game-wrap #mc-header.hide-no-anim .records {
    font-size: 0
}

.trial-game-wrap #mc-header.anim-hide {
    height: 0 !important;
    transition: all .25s ease
}

.trial-game-wrap #mc-header.anim-hide .hide-btn {
    -webkit-animation: hide-btn-show .25s ease forwards;
    animation: hide-btn-show .25s ease forwards
}

.trial-game-wrap #mc-header.anim-show {
    -webkit-animation: header-show .25s ease forwards;
    animation: header-show .25s ease forwards;
    height: 1rem;
    font-size: .44rem
}

.trial-game-wrap #mc-header.anim-show .am-icon-lg {
    width: .68rem;
    height: .68rem
}

#root,
.outside-container {
    --animate-duration: 0.5s;
    --animate-delay: 0.5s;
    --animate-repeat: 1
}

@-webkit-keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
    50% {
        opacity: 1
    }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
    50% {
        opacity: 1
    }
}

@-webkit-keyframes zoomOut {
    0% {
        opacity: 1
    }
    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
    to {
        opacity: 0
    }
}

@keyframes zoomOut {
    0% {
        opacity: 1
    }
    50% {
        opacity: 0;
        -webkit-transform: scale3d(.3, .3, .3);
        transform: scale3d(.3, .3, .3)
    }
    to {
        opacity: 0
    }
}

@-webkit-keyframes fadeOut {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

@keyframes fadeOut {
    0% {
        opacity: 1
    }
    to {
        opacity: 0
    }
}

#root .wysiwyg,
.outside-container .wysiwyg {
    line-height: 1.5;
    white-space: normal !important
}

#root .wysiwyg a,
.outside-container .wysiwyg a {
    word-break: break-all
}

#root .wysiwyg *,
.outside-container .wysiwyg * {
    line-height: 1.5;
    white-space: normal !important
}

#root .wysiwyg ul,
.outside-container .wysiwyg ul {
    list-style: disc;
    padding-left: 2.5em;
    margin-top: 1em;
    margin-bottom: 1em
}

#root .wysiwyg ul li,
.outside-container .wysiwyg ul li {
    display: list-item !important;
    list-style: disc outside;
    overflow: visible;
    padding-right: 0;
    padding-left: 0;
    margin-right: 0;
    margin-left: 0
}

#root .wysiwyg ol,
.outside-container .wysiwyg ol {
    list-style: decimal;
    padding-left: 2.5em;
    margin-top: 1em;
    margin-bottom: 1em
}

#root .wysiwyg ol li,
.outside-container .wysiwyg ol li {
    display: list-item !important;
    list-style: decimal outside;
    overflow: visible;
    padding-right: 0;
    padding-left: 0;
    margin-right: 0;
    margin-left: 0
}

body.dragging,
body.error-limit,
body.popup-on,
html.dragging {
    overflow: hidden !important;
    -webkit-overflow-scrolling: auto !important
}

body.dragging::-webkit-scrollbar,
body.error-limit::-webkit-scrollbar,
body.popup-on::-webkit-scrollbar,
html.dragging::-webkit-scrollbar {
    display: none
}

.domain-modal .am-modal-button-group-h .am-modal-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 .16rem;
    font-size: .28rem;
    word-break: break-word;
    text-overflow: unset;
    white-space: normal;
    line-height: 1.2
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit
}

@-webkit-keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    6.5% {
        -webkit-transform: translateX(-.06rem) rotateY(-9deg);
        transform: translateX(-.06rem) rotateY(-9deg)
    }
    18.5% {
        -webkit-transform: translateX(.05rem) rotateY(7deg);
        transform: translateX(.05rem) rotateY(7deg)
    }
    31.5% {
        -webkit-transform: translateX(-.03rem) rotateY(-5deg);
        transform: translateX(-.03rem) rotateY(-5deg)
    }
    43.5% {
        -webkit-transform: translateX(.02rem) rotateY(3deg);
        transform: translateX(.02rem) rotateY(3deg)
    }
    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

@keyframes headShake {
    0% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
    6.5% {
        -webkit-transform: translateX(-.06rem) rotateY(-9deg);
        transform: translateX(-.06rem) rotateY(-9deg)
    }
    18.5% {
        -webkit-transform: translateX(.05rem) rotateY(7deg);
        transform: translateX(.05rem) rotateY(7deg)
    }
    31.5% {
        -webkit-transform: translateX(-.03rem) rotateY(-5deg);
        transform: translateX(-.03rem) rotateY(-5deg)
    }
    43.5% {
        -webkit-transform: translateX(.02rem) rotateY(3deg);
        transform: translateX(.02rem) rotateY(3deg)
    }
    50% {
        -webkit-transform: translateX(0);
        transform: translateX(0)
    }
}

.headShake {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-name: headShake;
    animation-name: headShake
}

.shine:after {
    content: "";
    position: absolute;
    top: -50%;
    left: -60%;
    width: 20%;
    height: 200%;
    -webkit-transform: rotate(35deg);
    transform: rotate(35deg);
    background: linear-gradient(90deg, hsla(0, 0%, 100%, .13) 0, hsla(0, 0%, 100%, .13) 77%, hsla(0, 0%, 100%, .5) 92%, hsla(0, 0%, 100%, 0));
    -webkit-animation: animate-shine 4s ease 0s infinite;
    animation: animate-shine 4s ease 0s infinite
}

@-webkit-keyframes animate-shine {
    80% {
        left: -60%;
        opacity: 0
    }
    to {
        left: 130%;
        opacity: 1
    }
}

@keyframes animate-shine {
    80% {
        left: -60%;
        opacity: 0
    }
    to {
        left: 130%;
        opacity: 1
    }
}

@-webkit-keyframes infinite-tada {
    78% {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
    80%,
    82% {
        -webkit-transform: scale3d(.9, .9, .9) rotate(-3deg);
        transform: scale3d(.9, .9, .9) rotate(-3deg)
    }
    86%,
    90%,
    94%,
    98% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate(3deg)
    }
    88%,
    92%,
    96% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg)
    }
    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@keyframes infinite-tada {
    78% {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
    80%,
    82% {
        -webkit-transform: scale3d(.9, .9, .9) rotate(-3deg);
        transform: scale3d(.9, .9, .9) rotate(-3deg)
    }
    86%,
    90%,
    94%,
    98% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate(3deg)
    }
    88%,
    92%,
    96% {
        -webkit-transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
        transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg)
    }
    to {
        -webkit-transform: scaleX(1);
        transform: scaleX(1)
    }
}

@supports(padding-bottom:constant(safe-area-inset-bottom)) {
    :root {
        --safe-area-inset-bottom: constant(safe-area-inset-bottom);
        --safe-area-bottom: .37rem;
    }
}

@supports(padding-bottom:env(safe-area-inset-bottom)) {
    :root {
        --safe-area-inset-bottom: env(safe-area-inset-bottom);
        --safe-area-bottom: .37rem;
    }
}

:root {
    --s-app-modal-bg-rgb: 73, 147, 253;
    --s-app-modal-color: #fff;
}

a,
abbr,
acronym,
address,
applet,
article,
aside,
audio,
b,
big,
blockquote,
body,
canvas,
caption,
center,
cite,
code,
dd,
del,
details,
dfn,
div,
dl,
dt,
em,
embed,
fieldset,
figcaption,
figure,
footer,
form,
h1,
h2,
h3,
h4,
h5,
h6,
header,
hgroup,
html,
i,
iframe,
img,
ins,
kbd,
label,
legend,
li,
mark,
menu,
nav,
object,
ol,
output,
p,
pre,
q,
ruby,
s,
samp,
section,
small,
span,
strike,
strong,
sub,
summary,
sup,
table,
tbody,
td,
tfoot,
th,
thead,
time,
tr,
tt,
u,
ul,
var,
video {
    margin: 0;
    padding: 0;
    border: 0;
    vertical-align: baseline
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
    display: block
}

html body {
    -webkit-overflow-scrolling: touch
}

html body,
html body button,
html body input,
html body optgroup,
html body select,

html body input::-webkit-input-placeholder {
    font-family: SF Pro Text, SF Pro Icons, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif
}

html body input:-moz-placeholder,
html body input::-moz-placeholder {
    font-family: SF Pro Text, SF Pro Icons, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif
}

html body input:-ms-input-placeholder {
    font-family: SF Pro Text, SF Pro Icons, Helvetica Neue, Helvetica, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif
}

ol,
ul {
    list-style: none
}

blockquote,
q {
    quotes: none
}

blockquote:after,
blockquote:before,
q:after,
q:before {
    content: "";
    content: none
}

table {
    border-collapse: collapse;
    border-spacing: 0
}

.pointer {
    cursor: pointer
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box
}

.mc-home-test-link {
    display: inline-block;
    font-size: .4rem;
    width: 100%;
    padding: .3rem .4rem;
    background: #6495ed;
    color: #fff;
    text-align: center
}

.mc-flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.mc-flex-start {
    -webkit-box-pack: start;
    -webkit-justify-content: flex-start;
    -ms-flex-pack: start;
    justify-content: flex-start
}

.mc-flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -webkit-flex-flow: row wrap;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap
}

#mc-animate-container {
    transition: opacity .8s, -webkit-transform .5s;
    transition: transform .5s, opacity .8s;
    transition: transform .5s, opacity .8s, -webkit-transform .5s
}

.fade-enter {
    opacity: 0;
    z-index: 1
}

.fade-enter.fade-enter-active {
    opacity: 1;
    transition: opacity 50ms ease-in
}

.fade-exit.fade-exit-active {
    opacity: 0
}

.this-home-bg {
    width: 100%;
    height: 100%;
    background: url(bg.23bbdfbc.jpg) no-repeat;
    background-size: 100% 100%
}

#root .mc_app_box {
    width: 100%;
    overflow: hidden
}

.mc_app_box .mc_app_list {
    width: 100%;
    height: 100%;
    position: relative;
    -webkit-overflow-scrolling: touch
}

.mc_app_list .home_nav a {
    color: #7ab9e4 !important
}

.am-modal-mask {
    background-color: rgba(0, 0, 0, .6) !important
}

.am-modal-button-group-h .am-modal-button {
    color: #009cff
}

.skype-chat .lwc-chat-frame {
    width: 80% !important;
    max-height: 80% !important
}

.skype-button.bubble a {
    display: block;
    opacity: 0 !important;
    background-color: transparent;
    width: .7rem !important;
    height: .7rem !important;
    border-radius: .4rem;
    z-index: 88 !important
}

.skype-button.bubble a .lwc-button-icon {
    display: none !important
}

body.change-position {
    position: fixed
}

.am-toast.am-toast-mask {
    z-index: 100000000000
}

.number-shell {
    font-family: SF Pro Display, SF Pro Icons, AOS Icons, Helvetica Neue For Number, Roboto Mono, PingFang SC, Arial, sans-serif
}

.monospace-shell {
    font-family: Helvetica Neue, Roboto Mono, Avenir, sans-serif
}

.app-win a .icon {
    fill: currentColor
}

.none-exit {
    opacity: 0 !important;
    display: none !important;
    position: absolute !important;
    right: -400rem !important
}

@-webkit-keyframes placeHolderShimmer {
    0% {
        background-position: -4.68rem 0
    }
    to {
        background-position: 4.68rem 0
    }
}

.loading-example {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    animation-iteration-count: infinite;
    -webkit-animation-name: placeHolderShimmer;
    animation-name: placeHolderShimmer;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    background: #d8d8d8 linear-gradient(90deg, #d8d8d8 0, #bdbdbd 20%, #d8d8d8 40%, #d8d8d8) no-repeat;
    background-size: 100% 100%
}

.input-holder {
    position: fixed;
    left: -100rem;
    top: -100rem
}

.login_wrap,
.register_wrap {
    padding: 0 0 .5rem
}

.login_wrap .form-banner,
.register_wrap .form-banner {
    min-height: 2.2rem
}

.login_wrap .form-banner .default-banner,
.register_wrap .form-banner .default-banner {
    display: block;
    width: 100%
}

.login_wrap .form_title,
.register_wrap .form_title {
    margin: .42rem 0;
    font-size: .5rem;
    font-weight: 700;
    color: #fff;
    text-align: center
}

.login_wrap .form_banner .page-banner,
.register_wrap .form_banner .page-banner {
    margin-top: .42rem
}

.login_wrap .already_account,
.register_wrap .already_account {
    text-align: center;
    white-space: pre-wrap;
    margin-top: .5rem;
    font-size: .28rem;
    font-weight: 500;
    color: #fff
}

.login_wrap .already_account .login_now,
.register_wrap .already_account .login_now {
    margin-left: .1rem;
    color: #fdd82d
}

.login-form-bg,
.register-form-bg {
    width: 100%
}

.form_wrap {
    width: 6.56rem;
    /*min-height: 6.28rem;*/
    background: rgba(1,1,1,0);
    border-radius: 0.2rem;
    border: 0.02px solid #979797;
    box-shadow: inset 0 0 8px rgb(255 255 255 / 80%);
    /*width: 100%;*/
    padding: 0.3rem .51rem;
    margin : 0 auto;
}

.form_wrap .panel-default {
    min-height: .2rem;
    padding: .05rem 0 .1rem;
    text-align: left
}

.form_wrap .panel-default strong {
    display: block;
    color: #ff6969;
    font-size: .23rem;
    font-weight: 400;
    line-height: 1.4;
    text-shadow: 1px 0 rgba(0, 0, 0, .4);
    padding-left: .08rem
}

.form_wrap .panel-default strong.on {
    display: block
}

.form_wrap .method_select {
    height: .73rem;
    margin: .45rem 0 .38rem;
    display: flex
}

.form_wrap .method_select .select-wrap {
    display: flex;
    /*border-radius: .365rem;*/
    /*background-color: #212937*/
}
.form_wrap .method_select .select-wrap > div:first-child {
    border-right : 1px solid #464545;;
}
.form_wrap .method_select .select-wrap .method_toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: .2rem;
    font-weight: 600;
    font-size: 0.26rem;
    text-align: center;
    color: #575656;
    padding: 0.01rem .33rem;
    position: relative;
}

.form_wrap .method_select .select-wrap .method_toggle.on {
    /*border-radius: .365rem;*/
    /*background-color: #503dd4;*/
    color: #fff
}

.form_wrap .no-method-select {
    height: .38rem
}

.form_wrap .form_inputs {
    flex: 1 1 0%
}

.form_wrap .checkbox-item {
    padding: 0 .3rem;
    margin-top: .2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: .3rem;
    color: #fff
}

.form_wrap .checkbox-item .am-list-item {
    padding-left: 0;
    background-color: transparent
}

.form_wrap .checkbox-item .am-list-item .am-list-thumb {
    margin-right: .1rem
}

.form_wrap .checkbox-item .am-checkbox {
    width: .32rem;
    height: .32rem
}

.form_wrap .checkbox-item .am-checkbox .am-checkbox-input {
    width: 2rem;
    height: .32rem;
    padding-left: 0
}

.form_wrap .checkbox-item .am-checkbox .am-checkbox-inner {
    width: .32rem;
    height: .32rem;
    border-color: #fff;
    background-color: none
}

.form_wrap .checkbox-item .am-checkbox .am-checkbox-inner:after {
    top: 0;
    right: .06rem;
    width: .1rem;
    height: .18rem
}

.form_wrap .checkbox-item .am-checkbox.am-checkbox-checked .am-checkbox-inner {
    background-color: #fff
}

.form_wrap .checkbox-item .am-checkbox.am-checkbox-checked .am-checkbox-inner:after {
    border-color: rgba(0, 0, 0, .8)
}

.form_wrap .checkbox-item .am-list-content span {
    font-size: .3rem;
    color: #fff
}

.form_wrap .outter_form .fixed_list,
.form_wrap .outter_form .form-group {
    position: relative;
    z-index: 1
}

.form_wrap .outter_form .getCode {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: .4rem;
    top: .24rem;
    font-size: .33rem;
    font-weight: 500;
    text-align: center;
    color: #1678ff
}

.form_wrap .outter_form .form_label {
    margin-bottom: .2rem;
    line-height: .6;
    font-size: .28rem
}

.form_wrap .outter_form .input_icon {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: .37rem;
    top: .25rem;
    width: .24rem;
    height: .24rem;
    color: #FFF
}

.form_wrap .outter_form .input_icon svg {
    width: 100%;
    height: 100%
}

.form_wrap .outter_form .input_icon+input {
    padding: 0 .22rem 0 .97rem
}

.form_wrap .outter_form input {
    width: 100%;
    height: .68rem;
    line-height: normal;
    padding: 0 .22rem;
    font-size: .3rem;
    font-weight: 600;
    color: #fff;
    background: #2C2B29;
    border-radius: 0.1rem;
    border: none
}

.form_wrap .outter_form input::-webkit-input-placeholder {
    color: #4b5566;
    font-weight: 600;
    font-size: .3rem
}

.form_wrap .outter_form input::placeholder {
    color: #4b5566;
    font-weight: 600;
    font-size: .3rem
}

.form_wrap .outter_form .input_required {
    position: absolute;
    left: -.2rem;
    top: .3rem;
    font-size: .3rem;
    color: #ff6969
}

.form_wrap .outter_form .right-icon {
    position: absolute;
    right: .3rem;
    top: .13rem;
    width: .45rem;
    height: .45rem;
    margin-left: .2rem;
    background-repeat: no-repeat;
    background-position: 50%;
    color: #FFF;
    display: flex;
    align-items: center;
    justify-content: center
}

.form_wrap .outter_form .right-icon .am-icon {
    width: .267rem;
    height: .17rem
}

.form_wrap .outter_form .remove-btn .am-icon {
    width: .221rem;
    height: .221rem
}

.form_wrap .outter_form .catchat_pic {
    position: absolute;
    right: .3rem;
    top: .1rem;
    width: auto;
    height: .6rem
}

.form_wrap .outter_form .checkbox-container {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: .2rem
}

.form_wrap .outter_form .checkbox-container+.submit-btn {
    margin-top: .45rem
}

.form_wrap .outter_form .submit-btn {
    width: 100%;
    height: .88rem;
    margin-top: .3rem;
    font-weight: 600;
    font-size: 0.26rem;
    color: #F38F34;
    font-weight: 600;
    width: 4.95rem;
    height: 0.58rem;
    background: rgba(255,182,39,0);
    border-radius: 0.29rem;
    border: 0.02px solid rgba(255, 182, 39, 1);
    margin-left : .28rem;
}

.form_wrap .outter_form .submit-btn.register-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    border: .02rem solid #1678ff;
    color: #1678ff
}

.form_wrap .outter_form .submit-btn:disabled {
    background: #888
}

.form_wrap .form-menu {
    justify-content: center;
    text-align: center;
    margin-top: .6rem
}

.form_wrap .form-menu,
.form_wrap .form-menu .menu_links {
    display: flex;
    align-items: center
}

.form_wrap .form-menu .menu-link {
    text-align: center;
    font-size: .24rem;
    font-weight: 900;
    color: #1678ff;
    text-decoration: underline
}

.form_wrap .form-menu .menu-line {
    width: 1px;
    height: .44rem;
    margin: 0 .39rem;
    background: #707070
}

.form_logo {
    position: relative;
    height: 1rem;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: .28rem
}

.form_logo img {
    position: absolute;
    left: .6rem;
    top: 0;
    height: 1rem
}

.country-code-input {
    justify-content: space-evenly;
    position: absolute;
    left: .15rem;
    top: 0;
    width: 1.9rem;
    height: .9rem;
    color: #4b5566;
    padding: 0 .16rem;
    border-radius: .06rem
}

.country-code-input img {
    display: none
}

.country-code-input .country-code {
    font-size: .3rem !important;
    font-weight: 600 !important;
    position: relative;
    left: -.25rem
}

.country-code-input i {
    border-top: .08rem solid #5f5f7d;
    left: .8rem
}

.country-code-input+.input_icon+input.form-mobileNum {
    padding: 0 .22rem 0 2rem
}

.s-country-list-container .am-list-header {
    color: #b4b4b4
}

.s-country-list-container .popup-list {
    box-shadow: 0 -.03rem .1rem 0 rgba(0, 0, 0, .42);
    background-color: #44371a
}

.s-country-list-container .am-list-body {
    border-top: 1px solid #5339a7
}

.s-country-list-container .am-list-item .am-list-line:after {
    border-bottom: 1px solid hsla(0, 0%, 100%, .5) !important
}

.social-media-login {
    margin-top: .55rem;
    text-align: center
}

.social-media-login .tips {
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    justify-content: center;
    margin-bottom: .3rem
}

.social-media-login .tips span {
    padding: 0 .4rem;
    font-size: .3rem;
    font-weight: 500;
    color: #93a6ca
}

.social-media-login .item-group {
    display: flex;
    flex-direction: row;
    justify-content: center;
    gap: .272rem
}

.social-media-login .item {
    display: flex;
    align-items: center;
    border-radius: .43rem;
    font-size: .29rem;
    font-weight: 500;
    color: #fff
}

.social-media-login .item.facebook svg,
.social-media-login .item.google svg {
    display: block;
    width: .818rem;
    height: .818rem;
    border-radius: .2rem
}

.hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none
}

.hide-scrollbar::-webkit-scrollbar {
    display: none
}

.svg-gradient {
    position: absolute;
    width: 0;
    height: 0
}

.s-country-list-container .am-list-header,
.s-country-list-container .header-wrap .close-btn {
    color: #fff
}

[data-theme=light] .s-country-list-container .popup-list {
    background: #957bf1 !important
}

[data-theme=dark] .s-country-list-container .popup-list {
    background: #181f2b !important
}

.s-country-list-container .am-list-body {
    background: none;
    border-top: 1px solid #fff
}

.s-country-list-container .am-list-item {
    background: none
}

.s-country-list-container .am-list-item .am-list-line:after {
    border-bottom: 1px solid #fff
}

.s-country-list-container .am-list-item .am-list-line .am-list-content {
    color: #fff
}

._container_box {
    /*width: 100vw*/
    min-height : 100vh;
    background: #F9F9F9;
}

._container_box.on {
    height: 100vh;
    height: calc(var(--vh, 1vh)*100);
    overflow: hidden
}

._container_box .lazy-load-image-background {
    display: block !important;
    width: 100%
}

._container_box .img-loading {
    display: block;
    width: 100%
}

.collapse-accordion {
    border: none
}

.collapse-accordion .am-accordion-header {
    background: none !important
}

.collapse-accordion .am-accordion-header:after {
    display: none !important
}

.collapse-accordion .am-accordion-header .arrow {
    display: none !important
}

.collapse-accordion .am-accordion-content {
    background: none !important
}

.collapse-accordion .am-accordion-content-box:after {
    display: none !important
}