<template>
<!--  <pcApp  v-if="isPc"/>-->
<!--  <mobileApp v-else/>-->
  <mobileApp/>
</template>

<script>
// import pcApp from "./modules/pc/App.vue";
import mobileApp from "./modules/mobile/App.vue";

export default {
  name: "App",
  components: {
    // pcApp,
    mobileApp
  },
  data() {
    return {
      isPc: true
    }
  },
  beforeMount() {
    localStorage.setItem("hisLang", 'vn');
    if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
      this.isPc = false
    }
  },
  /*methods: {
    loadCSS(href) {
      let cssLink = document.createElement("link");
      cssLink.rel = "stylesheet";
      cssLink.type = "text/css";
      cssLink.href = href;
      document.getElementsByTagName("head")[0].appendChild(cssLink);
    },
    loadImage(src) {
      let img = document.createElement("img");
      img.src = src;
      document.body.appendChild(img);
    },
    loadJS(src) {
      let script = document.createElement("script");
      script.type = "text/javascript";
      script.src = src;
      document.body.appendChild(script);
    }
  }*/

};
</script>

<style>

:root {
  --theme-rem-unit: 100;
  --theme-header-height: 0.8rem;
  --theme-scroll-bar-bg-color: var(--theme-color-line);
  --theme-filter-active-color: #1678FF;
  --theme-main-bg-color: #FFFFFF;
  --theme-main-bg-color-hex: 255,255,255;
  --theme-bg-color: #F8F8F8;
  --theme-table-bg-color: #FFFFFF;
  --theme-disabled-bg-color: #999;
  --theme-countTo-bg: #f6faff;
  --theme-ant-message-bg: rgba(0, 0, 0, 0.8);
  --theme-bg-shadow: rgba(0, 0, 0, 0.06);
  --theme-load-bg-color: #E3E3E399;
  --theme-home-bg-color: 255,255,255;
  //--theme-primary-color: #1678FF;
  --theme-primary-color: #FFB627;
  --theme-primay-color-hex: 255, 182, 39;
  --theme-primary-font-color: #FFFFFF;
  --theme-disabled-font-color: #fff;
  --theme-ant-message-font-color: #fff;
  --theme-aside-icon-color-0: #B4B6BF;
  --theme-aside-icon-color-1: #909199;
  --theme-aside-icon-color-2: #666;
  --theme-aside-font-color: #fff;
  --theme-aside-active-font-color: #fff;
  --theme-aside-active-bg-color-0: #F6FAFF;
  --theme-aside-active-bg-color-1: #F3F8FF;
  --theme-aside-box-shadow: 0 0.02rem 0.04rem 0 rgba(114, 159, 255, 0.15);
  --theme-aside-no-active-box-shadow: #729FFF1F;
  --theme-jackpot-bg-color-0: #fff;
  --theme-jackpot-bg-color-1: #edf3fd;
  --theme-jackpot-num-bg-color-0: #c9d2df;
  --theme-jackpot-num-bg-color-1: #f5f8fe;
  --theme-jackpot-num-bg-color-2: #c9d2df;
  --theme-jackpot-num-bg-shadow-color-0: #fff6;
  --theme-jackpot-num-bg-shadow-color-1: #fff6;
  --theme-jackpot-title-color: #999999;
  --theme-game-lobby-border: #FFFFFF;
  --theme-search-icon-color: #1678FF;
  --theme-btm-def-color: #666666;
  --theme-btm-bg-color: #FFFFFF;
  --theme-mine-icon-color: #1678FF;
  --theme-hx-color: #000000d9;
  --theme-text-color-darken: #333333;
  --theme-text-color: #666666;
  --theme-text-color-lighten: #999999;
  --theme-text-color-lighten-withshadow: #999;
  --theme-text-color-placeholder: #CCCCCC;
  --theme-text-color-task-btn: #fff;
  --theme-text-color-process-text: #fff;
  --theme-text-color-activities-text: #fff;
  --theme-text-color-side-tabs: #999;
  --theme-alt-text-color: #FFFFFF;
  --theme-text-person-color: #FFFFFF;
  --theme-color-line: #E3E3E3;
  --theme-color-line-hex: 227,227,227;
  --theme-active-bg-color: #F8F8F8;
  --theme-active-gou-color: #1678FF;
  --theme-cardBg-color: rgba(7, 112, 255, 0.8);
  --theme-cardShadow-mask-color: rgba(254, 255, 255, 0.9);
  --theme-cardShadow-font-color: #fff;
  --theme-progress-line-color: #ccc;
  --theme-text-color-darken-hex: 51,51,51;
  --theme-secondary-color-success: #04BE02;
  --theme-secondary-color-success-linear-gradient: #18c516;
  --theme-secondary-color-success-text: #fff;
  --theme-secondary-color-error: #EA4E3D;
  --theme-secondary-color-error-linear-gradient: #f35a4a;
  --theme-secondary-color-finance: #FFAA09;
  --theme-secondary-color-finance-hex: 255,170,9;
  --theme-secondary-color-finance-linear-gradient: #ffb325;
  --theme-secondary-color-tabs-start-gradient: #fff;
  --theme-secondary-color-tabs-end-gradient: #f2f3f7;
  --theme-secondary-color-tabs-active-start-gradien: #46a1ff;
  --theme-secondary-color-tabs-active-end-gradient: #1678ff;
  --theme-secondary-color-tabs-active-border: transparent;
  --theme-secondary-color-help-text-color: #FFFFFF;
  --theme-label-secondary-color-finance: #FFAA09;
  --theme-event-luck-wheel-silver: #1672f2;
  --theme-event-luck-wheel-gold: #ffaa09;
  --theme-event-luck-wheel-diamond: #b751ff;
  --theme-user-card-top-bg-color: #3C8DFF;
  --theme-user-card-bg-color: #3187ff;
  --theme-user-card-bottom-bg-color: #1B7AFF;
  --theme-vip-progress-bg-color: #1678FF;
  --theme-top-nav-bg: #FFFFFF;
  --theme-side-menu-bg-color: #FFFFFF;
  --theme-side-menu-btn-color: #F8F8F8;
  --theme-side-menu-btn-color-hex: 248,248,248;
  --theme-side-menu-btn-hover: #FFFFFF;
  --theme-side-footer-bg-color: #FFFFFF;
  --theme-tabbar-bg-color: #111923;
  --theme-home-bg: #FFFFFF;
  --theme-side-bg-color: #0f212e;
  --theme-left-nav-text-color: #999999;
  --theme-left-nav-text-active-color: #FFFFFF;
  --theme-side-popver-text-color: #fff;
  --theme-game-card-ddt_bg: #E3E3E3;
  --theme-game-card-ddt_icon: #FFFFFF;
  --theme-alt-primary: #1678FF;
  --theme-alt-neutral-1: #666666;
  --theme-alt-neutral-2: #999999;
  --theme-alt-border: #666666;
  --theme-profile_icon_1: #FFAA09;
  --theme-profile_icon_2: #04BE02;
  --theme-profile_icon_3: #1678FF;
  --theme-profile_icon_4: #EA4E3D;
  --theme-web_filter_gou: #FFFFFF;
  --theme-max-width: 100%;
  --vh: 9.32px;
  --theme-ant-primary-color-0: #1678ff;
  --theme-ant-primary-color-1: #2d86ff;
  --theme-ant-primary-color-2: #4593ff;
  --theme-ant-primary-color-3: #5ca1ff;
  --theme-ant-primary-color-4: #73aeff;
  --theme-ant-primary-color-5: #8bbcff;
  --theme-ant-primary-color-6: #a2c9ff;
  --theme-ant-primary-color-7: #b9d7ff;
  --theme-ant-primary-color-8: #d0e4ff;
  --theme-ant-primary-color-9: #E6F4FF;
  --theme-ant-primary-color-10: #BAE0FF;
  --theme-ant-primary-color-11: #91CAFF;
  --theme-ant-primary-color-12: #69B1FF;
  --theme-ant-primary-color-13: #4096FF;
  --theme-ant-primary-color-14: #1678FF;
  --theme-ant-primary-color-15: #0958D9;
  --theme-ant-primary-color-16: #003EB3;
  --theme-ant-primary-color-17: #002C8C;
  --theme-ant-primary-color-18: #001D66;
  --theme-ant-primary-color-19: 22, 120, 255;
}
html {
  --bs-body-font-size: 0rem !important;
}


.br_loader_child, .br_loader_root {
  display: none
}

.br_loader_child.active, .br_loader_root.active {
  display: block;
  z-index: 1000;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0
}

.br_loader_root.active {
  position: fixed
}

.br_loader_child.active {
  position: absolute
}

.br_loader_main {
  position: relative;
  width: 100%;
  height: 100%
}

.br_loader_bg {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, .8)
}

.br_loader_content {
  width: 60px;
  height: 60px;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -30px
}

.van-button--warning {
  color: #fff;
  background-color: #FFB627 !important;
  border: 1px solid #FFB627 !important;
}
.ant-modal-wrap, .ant-modal-mask, .van-overlay, .modal-backdrop {
  left: auto !important;
  right: auto !important;
  width: var(--theme-max-width) !important;
}
.ad-bg {
  pointer-events: none;
}
.ad-wrap {
  height        : 9.1rem !important;
}
</style>
