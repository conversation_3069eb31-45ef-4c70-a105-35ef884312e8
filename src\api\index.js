import axios from "axios";
import { Package, Message } from "@/api/protocol";
import Protobuf from "@/api/compiled";
import store from "@/store";
import i18n from "@/lang";
import Vue from "vue";
import router from "@/router";

export const ROUTE_CAPTCHA = "auth.logon.captcha";
export const ROUTE_LOGON_TOKEN = "auth.logon.logontoken";
export const ROUTE_LOGON_USERNAME = "auth.logon.logonusername";
export const ROUTE_REGISTER_USERNAME = "auth.logon.registerusername";
export const ROUTE_LOGON_PASSWD = "auth.logon.passwd";
export const ROUTE_LOGON_ICON = "auth.logon.icon";
export const ROUTE_LOGON_OAUTHDETAILS = "auth.logon.oauthdetails";
export const ROUTE_LOGON_OAUTHBIND = "auth.logon.oauthbind";
export const ROUTE_LOGON_ACCOUNTMESSAGE = "auth.logon.accountmessage";

export const ROUTE_LOGON_REGISTERUSERNAMECAPTCHA = "auth.logon.registerusernamecaptcha";
export const ROUTE_LOGON_LOGONUSERNAMECAPTCHA = "auth.logon.logonusernamecaptcha";
export const ROUTE_LOGON_LOGONPHONECAPTCHA = "auth.logon.logonphonecaptcha";
export const ROUTE_LOGON_PASSWDCAPTCHA = "auth.logon.passwdcaptcha";
export const ROUTE_LOGON_BINDPHONECAPTCHA = "auth.logon.bindphonecaptcha";
export const ROUTE_LOGON_SMSCAPTCHA = "auth.logon.smscaptcha";
export const ROUTE_LOGON_OAUTHBINDCAPTCHA = "auth.logon.oauthbindcaptcha";

// 找回密码
export const ROUTE_LOGON_RETRIEVEPASSWDCAPTCHA = "auth.logon.retrievepasswdcaptcha";
export const ROUTE_LOGON_RETRIEVEVERIFYCAPTCHA = "auth.logon.retrieveverifycaptcha";
export const ROUTE_LOGON_RETRIEVERESETCAPTCHA = "auth.logon.retrieveresetcaptcha";
export const ROUTE_LOGON_RETRIEVEPHONECAPTCHA = "auth.logon.retrievephonecaptcha";

export const ROUTE_LOGON_SMS = "auth.logon.sms";
export const ROUTE_LOGON_BINDPHONE = "auth.logon.bindphone";

export const ROUTE_PLATFORM_LOGONGAME = "platform.platform.logongame";
export const ROUTE_PLATFORM_LOGON = "platform.platform.logonplatform";
export const ROUTE_PLATFORM_STOREFAVORITE = "platform.platform.storefavorite";
export const ROUTE_PLATFORM_FAVORITE = "platform.platform.favorite";
export const ROUTE_PLATFORM_LATEST = "platform.platform.latest";
export const ROUTE_PLATFORM_BALANCE = "platform.platform.balance";
export const ROUTE_PLATFORM_GAMES = "platform.platform.games";
export const ROUTE_PLATFORM_HOT = "platform.platform.hot";
export const ROUTE_PLATFORM_HELPDETAILS = "platform.platform.helpdetails";
export const ROUTE_PLATFORM_NOTICEDETAILS = "platform.platform.noticedetails";
export const ROUTE_PLATFORM_BANNERETAILS = "platform.platform.bannerdetails";
export const ROUTE_PLATFORM_TRANSACTIONPASSWD = "platform.platform.transactionpasswd";
export const ROUTE_PLATFORM_TRANSFEROUTALL = "platform.platform.transferoutall";
export const ROUTE_PLATFORM_WITHDRAWAL = "platform.platform.withdrawal";
export const ROUTE_PLATFORM_ADDWALLET = "platform.platform.addwallet";
export const ROUTE_PLATFORM_WITHDRAWALDETAILS = "platform.platform.withdrawaldetails";
export const ROUTE_PLATFORM_ACCOUNTINFO = "platform.platform.accountinfo";
export const ROUTE_PLATFORM_PLATFORMGAMES = "platform.platform.platformgames";
// 代理
export const ROUTE_RECORDER_QUERY_QUERYAGENTOVERVIEW = "recorder.query.queryagentoverview";
export const ROUTE_RECORDER_QUERY_QUERYAGENTRECORDS = "recorder.query.queryagentrecords";
export const ROUTE_RECORDER_QUERY_QUERYAGENTINCOMES = "recorder.query.queryagentincomes";
export const ROUTE_PLATFORM_AGENTSPREADINFO = "platform.platform.agentspreadawardinfo";
export const ROUTE_PLATFORM_GETAGENTSPREAD = "platform.platform.getagentspreadaward";
export const ROUTE_PLATFORM_GETAGENTDRAW = "platform.platform.getagentdraw";
export const ROUTE_RECORDER_QUERY_QUERYAGENTDRAWLOG = "recorder.query.queryagentdrawlog";
// 活动 - 签到
export const ROUTE_PLATFORM_DAILYCHECKINDETAIL = "platform.platform.dailycheckindetail";
export const ROUTE_PLATFORM_DAILYCHECKIN = "platform.platform.dailycheckin";
export const ROUTE_RECORDER_QUERY_QUERYDAILYCHECKIN = "recorder.query.querydailycheckin";
// 活动 - 转盘
export const ROUTE_PLATFORM_LUCKYWHEELDETAIL = "platform.platform.luckywheeldetail";
export const ROUTE_PLATFORM_LUCKYWHEELSTART = "platform.platform.luckywheelstart";
export const ROUTE_RECORDER_QUERY_QUERYLUCKYWHEEL = "recorder.query.queryluckywheel";
// 活动 - 每日登录领取红包
export const ROUTE_PLATFORM_DAILYLOGONREDPACKETDETAIL = "platform.platform.dailylogonredpacketdetail";
export const ROUTE_PLATFORM_DAILYLOGONREDPACKET = "platform.platform.dailylogonredpacket";
export const ROUTE_RECORDER_QUERY_QUERYDAILYLOGONREDPACKET = "recorder.query.querydailylogonredpacket";
// 活动 - 红包雨
export const ROUTE_PLATFORM_REDPACKETRAINDETAIL = "platform.platform.redpacketraindetail";
export const ROUTE_PLATFORM_REDPACKETRAIN = "platform.platform.redpacketrain";
export const ROUTE_RECORDER_QUERY_QUERYREDPACKETRAIN = "recorder.query.queryredpacketrain";
// 活动 - VIP福利
export const ROUTE_PLATFORM_VIPWELFAREDETAIL = "platform.platform.vipwelfaredetail";
export const ROUTE_PLATFORM_VIPWELFARE = "platform.platform.vipwelfare";
export const ROUTE_RECORDER_QUERY_QUERYVIPWELFARE = "recorder.query.queryvipwelfare";
// 活动 - 首充奖励
export const ROUTE_PLATFORM_FIRSTCHARGEREWARDDETAIL = "platform.platform.firstchargerewarddetail";
export const ROUTE_PLATFORM_FIRSTCHARGEREWARD = "platform.platform.firstchargereward";
export const ROUTE_RECORDER_QUERY_QUERYFIRSTCHARGEREWARD = "recorder.query.queryfirstchargereward";
// 活动 - 每日投注奖励
export const ROUTE_PLATFORM_DAILYBETREWARDDETAIL = "platform.platform.dailybetrewarddetail";
export const ROUTE_PLATFORM_DAILYBETREWARD = "platform.platform.dailybetreward";
export const ROUTE_RECORDER_QUERY_QUERYDAILYBETREWARD = "recorder.query.querydailybetreward";
// 活动 - 每周投注奖励
export const ROUTE_PLATFORM_weeklybetrewarddetail = "platform.platform.weeklybetrewarddetail";
export const ROUTE_PLATFORM_weeklybetreward = "platform.platform.weeklybetreward";
export const ROUTE_RECORDER_QUERY_queryweeklybetreward = "recorder.query.queryweeklybetreward";
// 活动 - 每日赢家
export const ROUTE_PLATFORM_DAILYWINNERREWARDDETAIL = "platform.platform.dailywinnerrewarddetail";
export const ROUTE_PLATFORM_DAILYWINNERREWARD = "platform.platform.dailywinnerreward";
export const ROUTE_RECORDER_QUERY_QUERYDAILYWINNERREWARD = "recorder.query.querydailywinnerreward";
// 活动 - 每日救济金
export const ROUTE_PLATFORM_DAILYRELIEFFUNDDETAIL = "platform.platform.dailyrelieffunddetail";
export const ROUTE_PLATFORM_DAILYRELIEFFUND = "platform.platform.dailyrelieffund";
export const ROUTE_RECORDER_QUERY_QUERYDAILYRELIEFFUND = "recorder.query.querydailyrelieffund";
// 活动 - 每月会员日
export const ROUTE_PLATFORM_MONTHLYREWARDDETAIL = "platform.platform.monthlyrewarddetail";
export const ROUTE_PLATFORM_MONTHLYREWARD = "platform.platform.monthlyreward";
export const ROUTE_RECORDER_QUERY_QUERYMONTHLYREWARD = "recorder.query.querymonthlyreward";
// 活动 - 周六狂欢
export const ROUTE_PLATFORM_SATURDAYCARNIVALDETAIL = "platform.platform.saturdaycarnivaldetail";
export const ROUTE_PLATFORM_SATURDAYCARNIVALREWARD = "platform.platform.saturdaycarnivalreward";
export const ROUTE_RECORDER_QUERY_QUERYSATURDAYCARNIVAL = "recorder.query.querysaturdaycarnival";
// 活动 - 天天返利
export const ROUTE_PLATFORM_DAILYREBATEDETAIL = "platform.platform.dailyrebatedetail";
export const ROUTE_PLATFORM_DAILYREBATE = "platform.platform.dailyrebate";
export const ROUTE_RECORDER_QUERY_QUERYDAILYREBATE = "recorder.query.querydailyrebate";
// 活动 - 推广得现金
export const ROUTE_PLATFORM_EARNCASHBYPROMOTEDETAIL = "platform.platform.earncashbypromotedetail";
export const ROUTE_PLATFORM_EARNCASHBYPROMOTESTARTWHEEL = "platform.platform.earncashbypromotestartwheel";
export const ROUTE_PLATFORM_EARNCASHBYPROMOTEWITHDRAWAL = "platform.platform.earncashbypromotewithdrawal";
export const ROUTE_RECORDER_QUERY_QUERYEARNCASHWITHDRAWAL = "recorder.query.queryearncashwithdrawal";

export const ROUTE_RECORDER_QUERY_ORDERS = "recorder.query.queryorders";
export const ROUTE_RECORDER_QUERY_TRANSFER = "recorder.query.querytransfer";
export const ROUTE_RECORDER_QUERY_PAYMENTS = "recorder.query.querypayments";
export const ROUTE_RECORDER_QUERY_WITHDRAWS = "recorder.query.querywithdraws";
export const ROUTE_RECORDER_QUERY_BONUS = "recorder.query.querybonus";
export const ROUTE_RECORDER_QUERY_ACCOUNTPROFITANDLOSS = "recorder.query.queryaccountprofitandloss";

export const ROUTE_PAYMENT_CHARGE = "payment.charge.charge";
export const ROUTE_PAYMENT_PAYMENTS = "payment.charge.payments";

export const ROUTE_RECORDER_QUERY_QUERYADEVENT = "recorder.query.queryadevent";

const protos = {
  [ROUTE_CAPTCHA]: ["CaptchaReq", "CaptchaResp"],
  [ROUTE_LOGON_TOKEN]: ["LogonReqToken", "LogonResp"],
  [ROUTE_LOGON_USERNAME]: ["LogonReqUserName", "LogonResp"],
  [ROUTE_REGISTER_USERNAME]: ["RegisterUsername", "RegisterResp"],
  [ROUTE_LOGON_PASSWD]: ["PassWdReq", "PassWdResp"],
  [ROUTE_LOGON_ICON]: ["IconReq", "IconResp"],
  [ROUTE_LOGON_OAUTHDETAILS]: ["OAuthDetailsReq", "OAuthDetailsResp"],
  [ROUTE_LOGON_OAUTHBIND]: ["OAuthBindReq", "OAuthBindResp"],
  [ROUTE_LOGON_ACCOUNTMESSAGE]: ["AccountMessageReq", "AccountMessageResp"],

  [ROUTE_LOGON_REGISTERUSERNAMECAPTCHA]: ["RegisterUsernameCaptcha", "RegisterResp"],
  [ROUTE_LOGON_LOGONUSERNAMECAPTCHA]: ["LogonReqUserNameCaptcha", "LogonResp"],
  [ROUTE_LOGON_LOGONPHONECAPTCHA]: ["LogonReqPhoneCaptcha", "LogonResp"],
  [ROUTE_LOGON_PASSWDCAPTCHA]: ["PassWdCaptchaReq", "PassWdResp"],
  [ROUTE_LOGON_BINDPHONECAPTCHA]: ["BindPhoneCaptchaReq", "BindPhoneResp"],
  [ROUTE_LOGON_SMSCAPTCHA]: ["SMSCaptchaReq", "SMSResp"],
  [ROUTE_LOGON_OAUTHBINDCAPTCHA]: ["OAuthBindCaptchaReq", "OAuthBindResp"],

  [ROUTE_LOGON_RETRIEVEPASSWDCAPTCHA]: ["RetrievePasswdCaptchaReq", "RetrievePasswdResp"],
  [ROUTE_LOGON_RETRIEVEVERIFYCAPTCHA]: ["RetrieveVerifyCaptchaReq", "RetrieveVerifyResp"],
  [ROUTE_LOGON_RETRIEVERESETCAPTCHA]: ["RetrieveResetCaptchaReq", "RetrieveResetResp"],
  [ROUTE_LOGON_RETRIEVEPHONECAPTCHA]: ["RetrievePhoneCaptchaReq", "RetrievePhoneResp"],

  [ROUTE_LOGON_SMS]: ["SMSReq", "SMSResp"],
  [ROUTE_LOGON_BINDPHONE]: ["BindPhoneReq", "BindPhoneResp"],

  [ROUTE_PLATFORM_LOGON]: ["LogonPlatformReq", "LogonPlatformResp"],
  [ROUTE_PLATFORM_GAMES]: ["GamesReq", "GamesResp"],
  [ROUTE_PLATFORM_FAVORITE]: ["FavoriteReq", "FavoriteResp"],
  [ROUTE_PLATFORM_STOREFAVORITE]: ["StoreFavoriteReq", "StoreFavoriteResp"],
  [ROUTE_PLATFORM_LATEST]: ["LatestReq", "LatestResp"],
  [ROUTE_PLATFORM_HOT]: ["HotGamesReq", "HotGamesResp"],
  [ROUTE_PLATFORM_LOGONGAME]: ["LogonGameReq", "LogonGameResp"],
  [ROUTE_PLATFORM_BALANCE]: ["BalanceReq", "BalanceResp"],
  [ROUTE_PLATFORM_HELPDETAILS]: ["HelpDetailsReq", "HelpDetailsResp"],
  [ROUTE_PLATFORM_NOTICEDETAILS]: ["NoticeDetailsReq", "NoticeDetailsResp"],
  [ROUTE_PLATFORM_BANNERETAILS]: ["BannerDetailsReq", "BannerDetailsResp"],
  [ROUTE_PLATFORM_TRANSACTIONPASSWD]: ["TransactionPasswdReq", "TransactionPasswdResp"],
  [ROUTE_PLATFORM_TRANSFEROUTALL]: ["TransferOutAllReq", "TransferOutAllResp"],
  [ROUTE_PLATFORM_WITHDRAWAL]: ["WithdrawalReq", "WithdrawalResp"],
  [ROUTE_PLATFORM_ADDWALLET]: ["AddWithdrawalWalletReq", "AddWithdrawalWalletResp"],
  [ROUTE_PLATFORM_WITHDRAWALDETAILS]: ["WithdrawalDetailsReq", "WithdrawalDetailsResp"],
  [ROUTE_PLATFORM_ACCOUNTINFO]: ["AccountInfoReq", "AccountInfoResp"],
  [ROUTE_PLATFORM_PLATFORMGAMES]: ["PlatformGamesReq", "PlatformGamesResp"],

  [ROUTE_RECORDER_QUERY_QUERYAGENTOVERVIEW]: ["AgentOverviewReq", "AgentOverviewResp"],
  [ROUTE_RECORDER_QUERY_QUERYAGENTRECORDS]: ["AgentRecordsReq", "AgentRecordsResp"],
  [ROUTE_RECORDER_QUERY_QUERYAGENTINCOMES]: ["AgentIncomesReq", "AgentIncomesResp"],
  [ROUTE_PLATFORM_AGENTSPREADINFO]: ["AgentSpreadAwardReq", "AgentSpreadAwardResp"],
  [ROUTE_PLATFORM_GETAGENTSPREAD]: ["AgentSpreadAwardReq", "AgentSpreadAwardGetResp"],
  [ROUTE_PLATFORM_GETAGENTDRAW]: ["AgentDrawReq", "AgentDrawResp"],
  [ROUTE_RECORDER_QUERY_QUERYAGENTDRAWLOG]: ["AgentDrawLogReq", "AgentDrawLogResp"],

  [ROUTE_PLATFORM_DAILYCHECKINDETAIL]: ["DailyCheckInDetailsReq","DailyCheckInDetailResp"],
  [ROUTE_PLATFORM_DAILYCHECKIN]: ["DailyCheckInReq","DailyCheckInResp"],
  [ROUTE_RECORDER_QUERY_QUERYDAILYCHECKIN]: ["QueryDailyCheckInReq","QueryDailyCheckInResp"],

  [ROUTE_PLATFORM_LUCKYWHEELDETAIL]: ["LuckyWheelDetailsReq","LuckyWheelDetailsResp"],
  [ROUTE_PLATFORM_LUCKYWHEELSTART]: ["LuckyWheelReq","LuckyWheelResp"],
  [ROUTE_RECORDER_QUERY_QUERYLUCKYWHEEL]: ["QueryLuckyWheelReq","QueryLuckyWheelResp"],

  [ROUTE_PLATFORM_DAILYLOGONREDPACKETDETAIL]: ["DailyLogonRedPacketDetailsReq","DailyLogonRedPacketDetailsResp"],
  [ROUTE_PLATFORM_DAILYLOGONREDPACKET]: ["DailyLogonRedPacketReq","DailyLogonRedPacketResp"],
  [ROUTE_RECORDER_QUERY_QUERYDAILYLOGONREDPACKET]: ["QueryDailyLogonRedPacketReq","QueryDailyLogonRedPacketResp"],

  [ROUTE_PLATFORM_REDPACKETRAINDETAIL]: ["RedPacketRainDetailsReq","RedPacketRainDetailsResp"],
  [ROUTE_PLATFORM_REDPACKETRAIN]: ["RedPacketRainReq","RedPacketRainResp"],
  [ROUTE_RECORDER_QUERY_QUERYREDPACKETRAIN]: ["QueryRedPacketRainReq","QueryRedPacketRainResp"],

  [ROUTE_PLATFORM_VIPWELFAREDETAIL]: ["VipWelfareDetailsReq","VipWelfareDetailsResp"],
  [ROUTE_PLATFORM_VIPWELFARE]: ["VipWelfareReq","VipWelfareResp"],
  [ROUTE_RECORDER_QUERY_QUERYVIPWELFARE]: ["QueryVipWelfareReq","QueryVipWelfareResp"],

  [ROUTE_PLATFORM_FIRSTCHARGEREWARDDETAIL]: ["FirstChargeRewardDetailsReq","FirstChargeRewardDetailsResp"],
  [ROUTE_PLATFORM_FIRSTCHARGEREWARD]: ["FirstChargeRewardReq","FirstChargeRewardResp"],
  [ROUTE_RECORDER_QUERY_QUERYFIRSTCHARGEREWARD]: ["QueryFirstChargeRewardReq","QueryFirstChargeRewardResp"],

  [ROUTE_PLATFORM_DAILYBETREWARDDETAIL]: ["DailyBetRewardDetailsReq","DailyBetRewardDetailsResp"],
  [ROUTE_PLATFORM_DAILYBETREWARD]: ["DailyBetRewardReq","DailyBetRewardResp"],
  [ROUTE_RECORDER_QUERY_QUERYDAILYBETREWARD]: ["QueryDailyBetRewardReq","QueryDailyBetRewardResp"],

  [ROUTE_PLATFORM_weeklybetrewarddetail]: ["WeeklyBetRewardDetailsReq","WeeklyBetRewardDetailsResp"],
  [ROUTE_PLATFORM_weeklybetreward]: ["WeeklyBetRewardReq","WeeklyBetRewardResp"],
  [ROUTE_RECORDER_QUERY_queryweeklybetreward]: ["QueryWeeklyBetRewardReq","QueryWeeklyBetRewardResp"],

  [ROUTE_PLATFORM_DAILYWINNERREWARDDETAIL]: ["DailyWinnerRewardDetailsReq","DailyWinnerRewardDetailsResp"],
  [ROUTE_PLATFORM_DAILYWINNERREWARD]: ["DailyWinnerRewardReq","DailyWinnerRewardResp"],
  [ROUTE_RECORDER_QUERY_QUERYDAILYWINNERREWARD]: ["QueryDailyWinnerRewardReq","QueryDailyWinnerRewardResp"],

  [ROUTE_PLATFORM_DAILYRELIEFFUNDDETAIL]: ["DailyReliefFundDetailReq","DailyReliefFundDetailResp"],
  [ROUTE_PLATFORM_DAILYRELIEFFUND]: ["DailyReliefFundReq","DailyReliefFundResp"],
  [ROUTE_RECORDER_QUERY_QUERYDAILYRELIEFFUND]: ["QueryDailyReliefFundReq","QueryDailyReliefFundResp"],

  [ROUTE_PLATFORM_MONTHLYREWARDDETAIL]: ["MonthlyRewardDetailReq","MonthlyRewardDetailResp"],
  [ROUTE_PLATFORM_MONTHLYREWARD]: ["MonthlyRewardReq","MonthlyRewardResp"],
  [ROUTE_RECORDER_QUERY_QUERYMONTHLYREWARD]: ["QueryMonthlyRewardReq","QueryMonthlyRewardResp"],

  [ROUTE_PLATFORM_SATURDAYCARNIVALDETAIL]: ["SaturdayCarnivalDetailReq","SaturdayCarnivalDetailResp"],
  [ROUTE_PLATFORM_SATURDAYCARNIVALREWARD]: ["SaturdayCarnivalReq","SaturdayCarnivalResp"],
  [ROUTE_RECORDER_QUERY_QUERYSATURDAYCARNIVAL]: ["QuerySaturdayCarnivalReq","QuerySaturdayCarnivalResp"],

  [ROUTE_PLATFORM_DAILYREBATEDETAIL]: ["DailyRebateDetailReq","DailyRebateDetailResp"],
  [ROUTE_PLATFORM_DAILYREBATE]: ["DailyRebateReq","DailyRebateResp"],
  [ROUTE_RECORDER_QUERY_QUERYDAILYREBATE]: ["QueryDailyRebateLogsReq","QueryDailyRebateLogsResp"],

  [ROUTE_PLATFORM_EARNCASHBYPROMOTEDETAIL]: ["EarnCashByPromoteDetailReq","EarnCashByPromoteDetailResp"],
  [ROUTE_PLATFORM_EARNCASHBYPROMOTESTARTWHEEL]: ["EarnCashByPromoteStartWheelReq","EarnCashByPromoteStartWheelResp"],
  [ROUTE_PLATFORM_EARNCASHBYPROMOTEWITHDRAWAL]: ["EarnCashByPromoteWithdrawalReq","EarnCashByPromoteWithdrawalResp"],
  [ROUTE_RECORDER_QUERY_QUERYEARNCASHWITHDRAWAL]: ["QueryEarnCashByPromoteWithdrawalLogReq","QueryEarnCashByPromoteWithdrawalLogResp"],

  [ROUTE_RECORDER_QUERY_ORDERS]: ["RecordsReq", "RecordsResp"],
  [ROUTE_RECORDER_QUERY_TRANSFER]: ["TransferReq", "TransferResp"],
  [ROUTE_RECORDER_QUERY_PAYMENTS]: ["DepositReq", "DepositResp"],
  [ROUTE_RECORDER_QUERY_WITHDRAWS]: ["QueryWithdrawalsReq", "QueryWithdrawalsResp"],
  [ROUTE_RECORDER_QUERY_BONUS]: ["QueryBonusLogReq", "QueryBonusLogResp"],
  [ROUTE_RECORDER_QUERY_ACCOUNTPROFITANDLOSS]: ["QueryAccountProfitAndLossReq", "QueryAccountProfitAndLossResp"],

  [ROUTE_PAYMENT_CHARGE]: ["ChargeReq", "ChargeResp"],
  [ROUTE_PAYMENT_PAYMENTS]: ["PaymentsReq", "PaymentsResp"],

  [ROUTE_RECORDER_QUERY_QUERYADEVENT]: ["QueryAdEventReq", "QueryAdEventResp"],
};

const dict = {
  "auth.logon.captcha":          1,
  "auth.logon.logontoken":       2,
  "auth.logon.logonusername":    3,
  "auth.logon.registerusername": 4,
  "auth.logon.passwd":           5,
  "auth.logon.icon":             6,
  "auth.logon.oauthdetails":     7,
  "auth.logon.oauthbind":        8,
  "auth.logon.accountmessage":   9,
  "auth.logon.sms":              12,
  "auth.logon.bindphone":        13,

  "auth.logon.registerusernamecaptcha": 150,
  "auth.logon.logonusernamecaptcha":    151,
  "auth.logon.passwdcaptcha":           152,
  "auth.logon.bindphonecaptcha":        153,
  "auth.logon.smscaptcha":              154,
  "auth.logon.oauthbindcaptcha":        155,
  "auth.logon.retrievepasswdcaptcha":   156,
  "auth.logon.retrieveverifycaptcha":   157,
  "auth.logon.retrieveresetcaptcha":    158,
  "auth.logon.retrievephonecaptcha":    159,
  "auth.logon.logonphonecaptcha":       160,

  "payment.charge.charge":   10,
  "payment.charge.payments": 11,

  "recorder.query.queryorders":              15,
  "recorder.query.querytransfer":            16,
  "recorder.query.querypayments":            17,
  "recorder.query.querywithdraws":           18,
  "recorder.query.queryluckywheel":          19,
  "recorder.query.querydailylogonredpacket": 20,
  "recorder.query.queryredpacketrain":       21,
  "recorder.query.queryvipwelfare":          22,
  "recorder.query.querydailycheckin":        23,
  "recorder.query.queryfirstchargereward":   24,
  "recorder.query.querydailybetreward":      25,
  "recorder.query.queryagentoverview":       26,
  "recorder.query.queryagentrecords":        27,
  "recorder.query.queryagentincomes":        28,
  "recorder.query.queryagentdrawlog":        29,
  "recorder.query.queryweeklybetreward":     100,
  "recorder.query.querydailyrelieffund":     102,
  "recorder.query.querymonthlyreward":       103,
  "recorder.query.querysaturdaycarnival":    104,
  "recorder.query.querybonus":               105,
  "recorder.query.queryaccountprofitandloss": 106,
  "recorder.query.querydailyrebate":          107,
  "recorder.query.queryadevent":              108,
  "recorder.query.queryearncashwithdrawal":   109,

  "platform.platform.logongame":     30,
  "platform.platform.logonplatform": 31,
  "platform.platform.storefavorite": 32,
  "platform.platform.favorite":      33,
  "platform.platform.latest":        34,
  "platform.platform.balance":       35,
  "platform.platform.games":         36,
  "platform.platform.hot":           37,
  "platform.platform.helpdetails":   38,
  "platform.platform.bannerdetails": 39,

  "platform.platform.transactionpasswd": 40,
  "platform.platform.transferoutall":    41,
  "platform.platform.withdrawal":        42,
  "platform.platform.addwallet":         43,
  "platform.platform.withdrawaldetails": 44,
  "platform.platform.accountinfo":       45,

  "platform.platform.agentspreadawardinfo": 53,
  "platform.platform.getagentspreadaward":  54,
  "platform.platform.getagentdraw":         55,
  "platform.platform.platformgames":        56,

  "platform.platform.activityswitchdetails":     60,
  "platform.platform.dailycheckindetail":        61,
  "platform.platform.dailycheckin":              62,
  "platform.platform.luckywheeldetail":          63,
  "platform.platform.luckywheelstart":           64,
  "platform.platform.dailylogonredpacketdetail": 65,
  "platform.platform.dailylogonredpacket":       66,
  "platform.platform.redpacketraindetail":       67,
  "platform.platform.redpacketrain":             68,
  "platform.platform.vipwelfaredetail":          69,
  "platform.platform.vipwelfare":                70,
  "platform.platform.firstchargerewarddetail":   71,
  "platform.platform.firstchargereward":         72,
  "platform.platform.dailybetrewarddetail":      73,
  "platform.platform.dailybetreward":            74,
  "platform.platform.weeklybetrewarddetail":     75,
  "platform.platform.weeklybetreward":           76,
  "platform.platform.dailywinnerrewarddetail":   77,
  "platform.platform.dailywinnerreward":         78,
  "platform.platform.dailyrelieffunddetail":     79,
  "platform.platform.dailyrelieffund":           80,
  "platform.platform.monthlyrewarddetail":       81,
  "platform.platform.monthlyreward":             82,
  "platform.platform.saturdaycarnivaldetail":    83,
  "platform.platform.saturdaycarnivalreward":    84,
  "platform.platform.dailyrebatedetail":         85,
  "platform.platform.dailyrebate":               86,
  "platform.platform.earncashbypromotedetail":     87,
  "platform.platform.earncashbypromotestartwheel": 88,
  "platform.platform.earncashbypromotewithdrawal": 89,
}

let reqId = 0;

export const protoApi = function (route, msg = {}) {

  // 域名配置
  const chars = 'cdefghijklmnopqrstuvwxyz0123456789';
  const preHost = `-a-b-a-`;//AaBb2345!

  // 设置初始域名
    if (!store.state.preHost) {
      const init = (preHost) => {
        let maxLen = chars.length;
        while (~preHost.indexOf('-')) {
          let rand = (Math.random() * 10 + 1) | 0;
          let str = '';
          for (let i = 0; i < rand; i++) {
            let idx = Math.random() * maxLen | 0;
            str += chars[idx];
          }
          preHost = preHost.replace('-', str);
        }
        store.commit("setPreHost", preHost)
      }
      init(preHost);
    }
  let baseURLs = [
    `https://${store.state.preHost}.tdjjkkb.com/request`,
    `https://${store.state.preHost}.tdjjkkb.com/message`,
  ]
  if (process.env.VUE_APP_ENV === "development") {
    baseURLs = [
      `http://************:8888/request`,
      `http://************:8888/message`,
    ]
    // baseURLs = [
    //   `http://************:8888/request`,
    //   `http://************:8888/message`,
    // ]
    // baseURLs = [
    //   `http://*************:8888/request`,
    //   `http://*************:8888/message`,
    // ]
  }
  //   banner
  if (![
    ROUTE_CAPTCHA,
    ROUTE_LOGON_USERNAME,
    ROUTE_LOGON_REGISTERUSERNAMECAPTCHA,
    ROUTE_LOGON_LOGONUSERNAMECAPTCHA,
    ROUTE_LOGON_LOGONPHONECAPTCHA,
    ROUTE_REGISTER_USERNAME,
    ROUTE_PLATFORM_LOGON,
    ROUTE_PLATFORM_GAMES,
    ROUTE_PLATFORM_PLATFORMGAMES,
    ROUTE_PLATFORM_HOT,
    ROUTE_PLATFORM_HELPDETAILS,
    ROUTE_PLATFORM_NOTICEDETAILS,
    ROUTE_PLATFORM_BANNERETAILS,
    ROUTE_LOGON_RETRIEVEPASSWDCAPTCHA,
    ROUTE_LOGON_RETRIEVEVERIFYCAPTCHA,
    ROUTE_LOGON_RETRIEVERESETCAPTCHA,
    ROUTE_LOGON_RETRIEVEPHONECAPTCHA,
    ROUTE_LOGON_SMSCAPTCHA,
  ].includes(route) && !msg.token) {
    return Promise.reject();
  }
  if ([
    ROUTE_PLATFORM_WITHDRAWAL
  ].includes(route) && !msg.amount) {
    if (store.state.webType > 1) {
      window.$toast.fail(i18n.t("The amount is err"));
    } else {
      window.cover.init({
        html: i18n.t("The amount is err"),
        icon: "error",
        timeOut: 2000,
      });
    }
    return Promise.reject();
  }
  iconsole(">>>", route, msg);
  let baseURL = baseURLs[0]
  if ([
    ROUTE_LOGON_TOKEN,
    ROUTE_LOGON_USERNAME,
    ROUTE_LOGON_REGISTERUSERNAMECAPTCHA,
    ROUTE_LOGON_LOGONUSERNAMECAPTCHA,
    ROUTE_LOGON_LOGONPHONECAPTCHA,
    ROUTE_PLATFORM_LOGON,
    ROUTE_RECORDER_QUERY_QUERYADEVENT,
    ROUTE_PLATFORM_BALANCE,
    ROUTE_REGISTER_USERNAME
  ].includes(route) && baseURL[1]) {
    baseURL = baseURLs[1]
  }
  if (
    [
      ROUTE_LOGON_TOKEN,
      ROUTE_LOGON_USERNAME,
      ROUTE_LOGON_REGISTERUSERNAMECAPTCHA,
      ROUTE_LOGON_LOGONUSERNAMECAPTCHA,
      ROUTE_LOGON_LOGONPHONECAPTCHA,
      ROUTE_REGISTER_USERNAME,
      ROUTE_PLATFORM_LOGON,
      ROUTE_PLATFORM_FAVORITE,
      ROUTE_PLATFORM_LATEST,
      ROUTE_PLATFORM_GAMES,
      ROUTE_PLATFORM_PLATFORMGAMES,
      ROUTE_PLATFORM_HOT,
      ROUTE_PLATFORM_HELPDETAILS,
      ROUTE_PLATFORM_BANNERETAILS,
    ].includes(route) && store.state.webType === 1
  ) {
    window.cover.loader(500);
  }
  if (
      [
        ROUTE_LOGON_USERNAME,
        ROUTE_LOGON_REGISTERUSERNAMECAPTCHA,
        ROUTE_LOGON_LOGONUSERNAMECAPTCHA,
        ROUTE_LOGON_LOGONPHONECAPTCHA,
        ROUTE_REGISTER_USERNAME,
        ROUTE_PLATFORM_PLATFORMGAMES,
        ROUTE_PLATFORM_LOGONGAME,
      ].includes(route) && store.state.webType === 2
  ) {
    window.$toast.loading({
      message: i18n.t('a6.common.tips.loadingTip'),
      forbidClick: true,
      duration: 6000
    });
  }
  const instance = axios.create({
    baseURL: baseURL,
    transformRequest: [
      function (data) {
        let compressRoute = 0;
        let to = route
        if (dict[route]) {
          to = dict[route];
          compressRoute = 1;
        }
        return Package.encode(
          Package.TYPE_DATA,
          Message.encode(
            reqId,
            Message.TYPE_REQUEST,
              compressRoute,
              to,
            Protobuf.requests[protos[route][0]].encode(data).finish()
          )
        );
      },
    ],
    headers: { "Content-Type": "application/octet-stream" },
    method: "POST",
    responseType: "arraybuffer",
  });

  instance.interceptors.request.use(
    function (config) {
      return config;
    },
    function (error) {
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    function (response) {
      let data;
      if (response.headers["content-type"] === "application/octet-stream") {
        data = Protobuf.requests[protos[route][1]].decode(
          new Uint8Array(response.data)
        );
      } else {
        data = JSON.parse(
          String.fromCharCode.apply(null, new Uint8Array(response.data))
        );
      }
      iconsole("<<<", route, data);
      // 2xx 范围内的状态码都会触发该函数。
      if (data.code !== 200) {
        let msg = i18n.t(data.code);
        switch (data.code) {
          case 506:
            store.commit("setLogout");
            store.commit("initConfig");
            if (store.state.webType > 1) {
              setTimeout(()=> {
                router.push("/m/login").catch(()=>{})
              }, 2000)
            } else {
              setTimeout(()=> {
                Vue.prototype.$modal.show('loginPopupModal')
              }, 2000)
            }
            break;
          case 516:
            return Promise.reject(data);
        }

        if (route !== ROUTE_RECORDER_QUERY_QUERYADEVENT) {
          if (store.state.webType > 1) {
            window.$toast.fail(msg);
          } else {
            window.cover.init({
              html: msg,
              icon: "error",
              timeOut: 2000,
            });
          }
        }

        return Promise.reject(data);
      }
      if (store.state.webType === 2) {
        setTimeout(()=>{
          window.$toast.clear()
        }, 800)
      }
      return Promise.resolve(data);
    },
    function (error) {
      iconsole("===err");
      iconsole(error);
      return Promise.reject(error);
    }
  );

  return instance({
    data: msg,
  });
};
