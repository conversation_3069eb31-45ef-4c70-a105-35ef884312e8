/***** Protocol *****/
let PKG_HEAD_BYTES = 4;
let MSG_FLAG_BYTES = 1;
let MSG_ROUTE_CODE_BYTES = 2;

let MSG_ID_MAX_BYTES = 5;
let MSG_ROUTE_LEN_BYTES = 1;

let MSG_ROUTE_CODE_MAX = 0xffff;

let MSG_COMPRESS_ROUTE_MASK = 0x1;
let MSG_TYPE_MASK = 0x7;

let Protocol = {
  /**
   * pomele client encode
   * id message id;
   * route message route
   * msg message body
   * socketio current support string
   */
  strencode: function (str) {
    let byteArray = new Uint8Array(str.length * 3);
    let offset = 0;
    for (let i = 0; i < str.length; i++) {
      let charCode = str.charCodeAt(i);
      let codes = null;
      if (charCode <= 0x7f) {
        codes = [charCode];
      } else if (charCode <= 0x7ff) {
        codes = [0xc0 | (charCode >> 6), 0x80 | (charCode & 0x3f)];
      } else {
        codes = [
          0xe0 | (charCode >> 12),
          0x80 | ((charCode & 0xfc0) >> 6),
          0x80 | (charCode & 0x3f),
        ];
      }
      for (let j = 0; j < codes.length; j++) {
        byteArray[offset] = codes[j];
        ++offset;
      }
    }
    let _buffer = new Uint8Array(offset);
    copyArray(_buffer, 0, byteArray, 0, offset);
    return _buffer;
  },

  /**
   * client decode
   * msg String data
   * return Message Object
   */
  strdecode: function (buffer) {
    let bytes = new Uint8Array(buffer);
    let array = [];
    let offset = 0;
    let charCode = 0;
    let end = bytes.length;
    while (offset < end) {
      if (bytes[offset] < 128) {
        charCode = bytes[offset];
        offset += 1;
      } else if (bytes[offset] < 224) {
        charCode = ((bytes[offset] & 0x3f) << 6) + (bytes[offset + 1] & 0x3f);
        offset += 2;
      } else {
        charCode =
          ((bytes[offset] & 0x0f) << 12) +
          ((bytes[offset + 1] & 0x3f) << 6) +
          (bytes[offset + 2] & 0x3f);
        offset += 3;
      }
      array.push(charCode);
    }
    return String.fromCharCode.apply(null, array);
  },
};

export const Package = {
  TYPE_HANDSHAKE: 1,
  TYPE_HANDSHAKE_ACK: 2,
  TYPE_HEARTBEAT: 3,
  TYPE_DATA: 4,
  TYPE_KICK: 5,

  /**
   * Package protocol encode.
   *
   * Pomelo package format:
   * +------+-------------+------------------+
   * | type | body length |       body       |
   * +------+-------------+------------------+
   *
   * Head: 4bytes
   *   0: package type,
   *      1 - handshake,
   *      2 - handshake ack,
   *      3 - heartbeat,
   *      4 - data
   *      5 - kick
   *   1 - 3: big-endian body length
   * Body: body length bytes
   *
   * @param  {Number}    type   package type
   * @param  {Uint8Array} body   body content in bytes
   * @return {Uint8Array}        new byte array that contains encode result
   */
  encode: function (type, body) {
    let length = body ? body.length : 0;
    let buffer = new Uint8Array(PKG_HEAD_BYTES + length);
    let index = 0;
    buffer[index++] = type & 0xff;
    buffer[index++] = (length >> 16) & 0xff;
    buffer[index++] = (length >> 8) & 0xff;
    buffer[index++] = length & 0xff;
    if (body) {
      copyArray(buffer, index, body, 0, length);
    }
    return buffer;
  },

  /**
   * Package protocol decode.
   * See encode for package format.
   *
   * @param  {Uint8Array} buffer byte array containing package content
   * @return {Object}           {type: package type, buffer: body byte array}
   */
  decode: function (buffer) {
    let bytes = new Uint8Array(buffer);
    let type = bytes[0];
    let index = 1;
    let length =
      ((bytes[index++] << 16) | (bytes[index++] << 8) | bytes[index++]) >>> 0;
    let body = length ? new Uint8Array(length) : null;
    copyArray(body, 0, bytes, PKG_HEAD_BYTES, length);
    return { type: type, body: body };
  },
};
export const Message = {
  TYPE_REQUEST: 0,
  TYPE_NOTIFY: 1,
  TYPE_RESPONSE: 2,
  TYPE_PUSH: 3,

  /**
   * Message protocol encode.
   *
   * @param  {Number} id            message id
   * @param  {Number} type          message type
   * @param  {Number} compressRoute whether compress route
   * @param  {Number|String} route  route code or route string
   * @param  {Buffer} msg           message body bytes
   * @return {Buffer}               encode result
   */
  encode: function (id, type, compressRoute, route, msg) {
    // caculate message max length
    let idBytes = msgHasId(type) ? caculateMsgIdBytes(id) : 0;
    let msgLen = MSG_FLAG_BYTES + idBytes;

    if (msgHasRoute(type)) {
      if (compressRoute) {
        if (typeof route !== "number") {
          throw new Error("error flag for number route!");
        }
        msgLen += MSG_ROUTE_CODE_BYTES;
      } else {
        msgLen += MSG_ROUTE_LEN_BYTES;
        if (route) {
          route = Protocol.strencode(route);
          if (route.length > 255) {
            throw new Error("route maxlength is overflow");
          }
          msgLen += route.length;
        }
      }
    }

    if (msg) {
      msgLen += msg.length;
    }

    let buffer = new Uint8Array(msgLen);
    let offset = 0;

    // add flag
    offset = encodeMsgFlag(type, compressRoute, buffer, offset);

    // add message id
    if (msgHasId(type)) {
      offset = encodeMsgId(id, idBytes, buffer, offset);
    }

    // add route
    if (msgHasRoute(type)) {
      offset = encodeMsgRoute(compressRoute, route, buffer, offset);
    }

    // add body
    if (msg) {
      offset = encodeMsgBody(msg, buffer, offset);
    }

    return buffer;
  },

  /**
   * Message protocol decode.
   *
   * @param  {Buffer|Uint8Array} buffer message bytes
   * @return {Object}            message object
   */
  decode: function (buffer) {
    let bytes = new Uint8Array(buffer);
    let bytesLen = bytes.length || bytes.byteLength;
    let offset = 0;
    let id = 0;
    let route = null;

    // parse flag
    let flag = bytes[offset++];
    let compressRoute = flag & MSG_COMPRESS_ROUTE_MASK;
    let type = (flag >> 1) & MSG_TYPE_MASK;

    // parse id
    if (msgHasId(type)) {
      id = 0;
      for (let i = offset; ; i++) {
        let b = bytes[i];
        id += (b & 0x7f) << (7 * (i - offset));
        if (b < 128) {
          offset = i + 1;
          break;
        }
      }
    }

    // parse route
    if (msgHasRoute(type)) {
      if (compressRoute) {
        route = (bytes[offset++] << 8) | bytes[offset++];
      } else {
        let routeLen = bytes[offset++];
        if (routeLen) {
          route = new Uint8Array(routeLen);
          copyArray(route, 0, bytes, offset, routeLen);
          route = Protocol.strdecode(route);
        } else {
          route = "";
        }
        offset += routeLen;
      }
    }

    // parse body
    let bodyLen = bytesLen - offset;
    let body = new Uint8Array(bodyLen);

    copyArray(body, 0, bytes, offset, bodyLen);

    return {
      id: id,
      type: type,
      compressRoute: compressRoute,
      route: route,
      body: body,
    };
  },
};

let copyArray = function (dest, doffset, src, soffset, length) {
  if ("function" === typeof src.copy) {
    // Buffer
    src.copy(dest, doffset, soffset, soffset + length);
  } else {
    // Uint8Array
    for (let index = 0; index < length; index++) {
      dest[doffset++] = src[soffset++];
    }
  }
};

let msgHasId = function (type) {
  return type === Message.TYPE_REQUEST || type === Message.TYPE_RESPONSE;
};

let msgHasRoute = function (type) {
  return (
    type === Message.TYPE_REQUEST ||
    type === Message.TYPE_NOTIFY ||
    type === Message.TYPE_PUSH
  );
};

let caculateMsgIdBytes = function (id) {
  let len = 0;
  do {
    len += 1;
    id >>= 7;
  } while (id > 0);
  return len;
};

let encodeMsgFlag = function (type, compressRoute, buffer, offset) {
  if (
    type !== Message.TYPE_REQUEST &&
    type !== Message.TYPE_NOTIFY &&
    type !== Message.TYPE_RESPONSE &&
    type !== Message.TYPE_PUSH
  ) {
    throw new Error("unkonw message type: " + type);
  }

  buffer[offset] = (type << 1) | (compressRoute ? 1 : 0);

  return offset + MSG_FLAG_BYTES;
};

let encodeMsgId = function (id, idBytes, buffer, offset) {
  let index = offset;
  do {
    let n = id & 0x7f;
    id >>= 7;
    buffer[index++] = id > 0 ? n | 0x80 : n;
  } while (id > 0);

  return offset + idBytes;
};

let encodeMsgRoute = function (compressRoute, route, buffer, offset) {
  if (compressRoute) {
    if (route > MSG_ROUTE_CODE_MAX) {
      throw new Error("route number is overflow");
    }

    buffer[offset++] = (route >> 8) & 0xff;
    buffer[offset++] = route & 0xff;
  } else {
    if (route) {
      buffer[offset++] = route.length & 0xff;
      copyArray(buffer, offset, route, 0, route.length);
      offset += route.length;
    } else {
      buffer[offset++] = 0;
    }
  }

  return offset;
};

let encodeMsgBody = function (msg, buffer, offset) {
  copyArray(buffer, offset, msg, 0, msg.length);
  return offset + msg.length;
};
