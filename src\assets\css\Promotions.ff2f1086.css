.promo-details {
  width: 800px;
  padding-bottom: 10px;
  color: #333;
  background: #fff;
  box-shadow: 0 2px 8px 0 hsla(0, 0%, 78.4%, 0.5);
}
.promo-details .modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-clip: padding-box;
  border-radius: 8px;
  outline: 0;
}
.promo-details .modal-header {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0.8rem;
  font-size: 32px;
  color: #fff;
  background: #d2b79c;
}
.promo-details .modal-body {
  flex: 1 1 auto;
  padding: 1rem;
}
.promo-details .modal-body iframe,
.promo-details .modal-body img {
  max-width: 100%;
}
.modal-body::-webkit-scrollbar {
  width: 4px;
  height: 1px;
}
.modal-body::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.4);
  background: #e8e8e8;
}
.modal-body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.4);
  border-radius: 10px;
  background: #fff;
}
.promo-details .modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}
.promo-details .modal-footer {
  width: 100%;
  text-align: center;
  display: flex;
  justify-content: flex-end;
  padding: 5px 20px 0 0;
}
.promo-details .btn {
  cursor: pointer;
  display: block;
  min-width: 100px;
  line-height: 32px;
  text-align: center;
  border-radius: 2px;
  margin-top: 10px;
  font-weight: 700;
  color: #fff;
  background: #d2b79c;
}
.promo-details .btn:hover {
  opacity: 0.7;
}
.promo-container .no-data {
  width: 100%;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.promo-container .no-data img {
  display: block;
  width: 200px;
}
.promo-container .no-data p {
  margin-top: 10px;
  font-size: 14px;
  color: #fff;
  text-align: center;
}
.promo-detail-container {
  padding-bottom: 40px;
  padding-top: 53px;
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 1085px;
}
.promo-detail-container .detail-img {
  width: 100%;
  border-radius: 10px;
}
.promo-detail-container .detail-title {
  width: 100%;
  font-size: 30px;
  font-weight: 800;
  color: #f5df4b;
  margin-top: 31px;
}
.promo-detail-container .vhtml {
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  color: #ebebeb;
}
.promo-detail-container .detail-footer {
  width: 100%;
}
.promo-detail-container .detail-footer .promo-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  transition: 0.2s;
  cursor: pointer;
  min-width: 90px;
  height: 39px;
  padding: 0 24px;
  border-radius: 7px;
  background-color: #da394f;
  font-size: 16px;
  color: #fff;
  font-size: 22px;
  font-weight: 700;
  height: 55px;
  border-radius: 10px;
  padding: 0 40px;
  margin-top: 40px;
}
.promo-detail-container .detail-footer .promo-btn:hover {
  opacity: 0.8;
}
.promo-back {
  cursor: pointer;
  border-radius: 10px;
  font-size: 25px;
  color: #5c677a;
  background-color: #222a38;
  width: 255px;
  height: 68px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 29px;
  margin-bottom: 10px;
}
.promo-back .svg-icon {
  width: 29px;
  height: 26px;
}
.zoom-in-top-enter-active,
.zoom-in-top-leave-active {
  opacity: 1;
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
  transition: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    -webkit-transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    -webkit-transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  -webkit-transform-origin: center top;
  transform-origin: center top;
}
.zoom-in-top-enter,
.zoom-in-top-leave-active {
  opacity: 0;
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
}
.zoom-in-bottom-enter-active,
.zoom-in-bottom-leave-active {
  opacity: 1;
  -webkit-transform: scaleY(1);
  transform: scaleY(1);
  transition: opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    -webkit-transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    -webkit-transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
}
.zoom-in-bottom-enter,
.zoom-in-bottom-leave-active {
  opacity: 0;
  -webkit-transform: scaleY(0);
  transform: scaleY(0);
}
.promo-container {
  min-height: calc(100vh - 63px);
  padding-bottom: 40px;
}
.promo-container.dark {
  background-color: #000;
}
.promo-container.promoDetail {
  max-width: 1920px;
}
.promo-container .promo-bg {
  margin: 0 auto;
  max-width: 1085px;
}
.promo-container .promo-content {
  position: relative;
}
.promo-container .promo-content .promo-menu-list {
  gap: 20px;
  padding: 38px 0 49px 0;
}
.promo-container .promo-content .promo-menu-list .swiper-wrapper {
  display: flex;
}
.promo-container .promo-content .promo-menu-list .swiper-wrapper .swiper-slide {
  width: auto;
}
.promo-container .promo-content .promo-menu-list .promo-menu-item {
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  transition: 0.2s;
  cursor: pointer;
  color: #5c677a;
  font-size: 16px;
  width: -webkit-min-content;
  width: min-content;
  min-width: 126px;
  height: 48.8px;
  border-radius: 14px;
  background-color: #181f2b;
  position: relative;
  padding: 0 15px;
}
.promo-container .promo-content .promo-menu-list .promo-menu-item.all {
  margin-right: 15px;
}
.promo-container .promo-content .promo-menu-list .promo-menu-item .promo-name {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 10px;
}
.promo-container .promo-content .promo-menu-list .promo-menu-item.active,
.promo-container .promo-content .promo-menu-list .promo-menu-item:hover {
  background: url(../img/promo-bg.84c5ba16.png) no-repeat 50% / cover;
  color: #fff;
}
.promo-container .promo-content .active-promo-box {
  position: relative;
}
.promo-container .promo-content .active-promo-list {
  display: grid;
  grid-template-columns: repeat(3, 348px);
  gap: 30px 20px;
}
.promo-container .promo-content .active-promo-list .active-promo-item {
  width: 100%;
  height: 217px;
  display: flex;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  transition: 0.2s;
  cursor: pointer;
  flex-direction: column;
  gap: 20px;
  border-radius: 17px;
  position: relative;
  overflow: hidden;
}
.promo-container .promo-content .active-promo-list .active-promo-item:hover {
  opacity: 0.8;
}
.promo-container .promo-content .active-promo-list .active-item-img {
  position: relative;
  width: 100%;
  height: 100%;
}
.promo-container .promo-content .active-promo-list .active-item-img img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.promo-container .promo-content .active-promo-list .active-item-info {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: rgba(53, 27, 108, 0.8);
  height: 45px;
  display: flex;
  align-items: center;
}
.promo-container
  .promo-content
  .active-promo-list
  .active-item-info
  .item-title {
  display: block;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  line-height: 1.2;
  width: 100%;
  font-size: 18px;
  color: #fff;
  flex: 1;
  margin-left: 14px;
}
@supports (-webkit-line-clamp: 2) {
  .promo-container
    .promo-content
    .active-promo-list
    .active-item-info
    .item-title {
    display: -webkit-box !important;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    white-space: normal;
  }
}
.promo-container .promo-content .active-promo-list .active-item-info .more-btn {
  min-width: 78.4px;
  height: 31.2px;
  padding: 0 5px;
  border-radius: 20px;
  border: 1px solid #f8afaf;
  background-image: linear-gradient(
    180deg,
    #ff4545 0,
    #c30000 52%,
    #cb0000 80%,
    #ff4f4f
  );
  margin-right: 9px;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}
