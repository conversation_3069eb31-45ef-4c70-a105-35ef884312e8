<template>
  <transition name="fade">
  <div v-if="fabShow" class="LIj05jKUpegPgDeMG5EV" @click ="onBtnClicked" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"  :style="{'width':itemWidth+'px','height':itemHeight+'px','left':left+'px','top':top+'px', transition: transition}">
    <i class="" style="display: inline-flex; justify-content: center; align-items: center;">
      <svg width="25px" height="25px" fill="currentColor" aria-hidden="true" focusable="false">
        <use xlink:href="#game_2_style_4_zk--svgSprite:all"></use>
        <linearGradient id="id-780bb3ab-554a-4bab-8710-4af234f97f4c" x1="0.5" x2="0.5" y2="1"></linearGradient>
      </svg>
    </i>
    <span style="font-size: 12px">{{$t('backHome')}} </span>
  </div>
  </transition>
</template>


<script>
export default {
  name: "FloatImgBtn",
  props:{
    itemWidth:{
      type:Number,
      default:60
    },
    itemHeight:{
      type:Number,
      default:60
    },
    gapWidth:{
      type:Number,
      default:10
    },

    coefficientHeight:{
      type:Number,
      default:0.8
    }
  },
  mounted(){
    this.initPosition();
    window.addEventListener("orientationchange", this.initPosition);
  },
  beforeDestroy() {
    // window.matchMedia("(orientation: portrait)").matches
    window.removeEventListener("orientationchange", this.initPosition);
  },
  methods:{
    touchstart(e){
      e.stopPropagation();
      this.transition = 'none';
    },
    touchmove(e){
      e.preventDefault();
      e.stopPropagation();
      if (e.targetTouches.length === 1) {
        let touch = event.targetTouches[0];
        this.left = touch.clientX - this.itemWidth/2;
        this.top = touch.clientY - this.itemHeight/2;
      }
    },
    touchend(e){
      e.stopPropagation();
      this.transition = 'all 0.3s';
      if(this.left>this.clientWidth/2){
        this.left = this.clientWidth - this.itemWidth - this.gapWidth;
      }else{
        this.left = this.gapWidth;
      }
      if(this.top<=36)
      {
        this.top = this.gapWidth
      }
      else{
        let bottom=this.clientHeight-this.itemHeight-this.gapWidth
        if(this.top>=bottom)
        {
          this.top=bottom
        }

      }
    },
    initPosition() {
      this.fabShow = false
      setTimeout(()=>{
        this.fabShow = true
        const isPortrait = window.orientation === 0 || window.orientation === 180;
        this.clientWidth = isPortrait ? Math.min(window.screen.availWidth, window.screen.availHeight) : Math.max(window.screen.availWidth, window.screen.availHeight);
        this.clientHeight = isPortrait ? Math.max(window.screen.availWidth, window.screen.availHeight) : Math.min(window.screen.availWidth, window.screen.availHeight);
        this.left = this.clientWidth - this.itemWidth - this.gapWidth;
        this.top = 10;
      }, 500)

    },
    onBtnClicked(){
      if (this.$route.path === '/m/seamless/go') {
        this.$router.back()
      } else {
        this.$router.replace('/').catch(()=>{})
      }
    },

  },
  data(){
    return{
      transition: "",
      currentTop:0,
      clientWidth:0,
      clientHeight:0,
      left:0,
      top:0,
      fabShow: false,
    }
  }
}
</script>



<style lang="scss" scoped>

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active in <2.1.8 */ {
  opacity: 0;
}
.LIj05jKUpegPgDeMG5EV {
  -ms-flex-align: center;
  -ms-flex-pack: center;
  -webkit-user-drag: none;
  align-items: center;
  background-color: rgba(0,0,0,.6);
  border: .015rem solid hsla(0,0%,100%,.65);
  border-radius: 50%;
  -webkit-box-shadow: 0 .03rem .06rem 0 rgba(0,0,0,.16);
  box-shadow: 0 .03rem .06rem 0 rgba(0,0,0,.16);
  color: #fff;
  cursor: pointer;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  height: .95rem;
  justify-content: center;
  left: .03rem;
  position: fixed;
  top: .03rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: .95rem;
  z-index: 1998
}

.LIj05jKUpegPgDeMG5EV>i {
  font-size: .4rem
}

.LIj05jKUpegPgDeMG5EV>span {
  font-size: .2rem
}

html[data-device-nodesktop="1"] .LIj05jKUpegPgDeMG5EV {
  height: 1.1rem;
  width: 1.1rem
}

html[data-device-nodesktop="1"] .LIj05jKUpegPgDeMG5EV>i {
  font-size: .44rem
}

html[data-device-nodesktop="1"] .LIj05jKUpegPgDeMG5EV>span {
  font-size: .24rem;
  margin-top: .03rem
}

@media screen and (orientation: landscape) {
  html[data-device-os=android] .LIj05jKUpegPgDeMG5EV,html[data-device-os=ios] .LIj05jKUpegPgDeMG5EV {
    height:1.4rem;
    width: 1.4rem
  }

  html[data-device-os=android] .LIj05jKUpegPgDeMG5EV>i,html[data-device-os=ios] .LIj05jKUpegPgDeMG5EV>i {
    font-size: .58rem
  }

  html[data-device-os=android] .LIj05jKUpegPgDeMG5EV>span,html[data-device-os=ios] .LIj05jKUpegPgDeMG5EV>span {
    font-size: .32rem;
    margin-top: .04rem
  }
}
</style>