<script>
import {
  ROUTE_LOGON_RETRIEVEPASSWDCAPTCHA,
  ROUTE_LOGON_RETRIEVEVERIFYCAPTCHA,
  ROUTE_LOGON_RETRIEVERESETCAPTCHA,
  ROUTE_LOGON_RETRIEVEPHONECAPTCHA, ROUTE_LOGON_SMSCAPTCHA
} from "@/api";
import {actionCaptcha} from "@/mixins/actionCaptcha";
import {MD5} from "crypto-js";
import {check} from "@/mixins/check";
import Sms from '@/modules/mobile/components/Sms.vue'
import {regex} from '@/mixins/regex'
// gjscvip 56378
export default {
  components: {Sms},
  mixins: [actionCaptcha, check, regex],
  data() {

    return {
      smsBtnNum: 1,
      hasSend: 0,
      strength: 0,
      levels: ["", "QZ0QekXF9duW7ClyMSdQ", "i7RHg3ioXzOBB19tWHGD", "cTD_ssvnROtzVyQfdPAV", "GaV7MsO6XnDPxdXsTB7R"],
      countdown: 0,
      loading: false,
      phoneHide: '',
      isInputPhone: false,
      step: 0,
      phoneDigits: ["*", "*", "*", "*", "*"],
      RetrievePasswdResp: {
        // code: 200,
        // phone: "0084880000001",
        token: "",
      },
      RetrievePhoneResp: {
        code: 0,
        token: "",
      },
      SMSResp: {
        code: 0,
      },
      RetrieveVerifyResp: {
        code: 0,
        token: ""
      },
      formValues: {},
      form2Values: {
        password: '',
        passwordConfirm: '',
      },
      form: this.$form.createForm(this, {name: 'coordinated'}),
      form2: this.$form.createForm(this, {name: 'form2'}),
    };
  },
  computed: {
    phoneSegment() {
      let segment = [];
      if (this.RetrievePasswdResp.phone.startsWith(this.$store.state.phonePreApi)) {
        segment[0] = this.$store.state.phonePreShow + ' ';
        segment = segment.concat(this.RetrievePasswdResp.phone.substring(this.$store.state.phonePreApi.length).split('*****'));
      }
      return segment
    },
    phoneHideLength() {
      return Array.from(this.phoneHide).length
    }
  },
  mounted() {
    // this.initActionCaptcha(this.RetrievePasswdCaptcha);
    this.initActionCaptcha(this.RetrievePhoneCaptcha, "RetrievePhoneCaptcha");
    // this.initActionCaptcha(this.submitSmsCaptcha, 2);
    this.initActionCaptcha(this.RetrieveVerifyCaptcha, "RetrieveVerifyCaptcha");
    this.initActionCaptcha(this.RetrieveResetCaptcha, "RetrieveResetCaptcha");
    this.setInterval();
  },
  methods: {
    onSmsFocus(btnNum) {
      this.hasSend = btnNum
      this.$nextTick(()=>{
        this.$refs.smsCode.focus();
      })
    },
    onSmsBtn(btnNum) {
      this.smsBtnNum=btnNum;
      if (btnNum === 1) {
        this.form.setFieldsValue({
          'phone_code': this.$store.getters.tempAccount
        })
      } else {
        this.form.setFieldsValue({
          'phone_code': ""
        })
      }
    },
    setInterval() {
      this.countdown = Math.round(
          (this.$store.state.smsExpiresTime - Date.now()) / 1000
      );
      if (this.countdown) {
        this.timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown -= 1;
          } else {
            clearInterval(this.timer);
          }
        }, 1000);
      }
    },
    validatePasswords(rule, value, callback) {
      if (value && value !== this.form2.getFieldValue('password')) {
        callback(new Error('Senha incorreta, digite novamente!'));
      } else {
        callback();
      }
    },
    checkPasswordStrength() {
      let e = this.form2.getFieldValue('password');
      if (!e) {
        this.strength = 0;
        return
      }
      let n = e.length >= 12,
          r = /[a-z]/.test(e),
          i = /[A-Z]/.test(e),
          o = /\d/.test(e),
          a = /[^a-zA-Z0-9]/.test(e),
          u = 0,
          s = [r, i, o, a, n].filter(function (e) {
            return e;
          });
      this.strength = s.length > 1 ? s.length - 1 : 1;
    },
    closeForgotForm() {
      this.step = 0;
      this.phoneHide = '';
      this.isInputPhone = false;
      this.RetrievePasswdResp = {
        code: 0,
        phone: "",
        token: "",
      };
      this.RetrievePhoneResp = {
        code: 0,
      };
      this.SMSResp = {
        code: 0,
      };
      this.RetrieveVerifyResp = {
        code: 0,
        token: ""
      };
      this.formValues = {};
      this.$store.commit('setShowForgotModal', false)
    },
    handleSubmit(e) {
      e.preventDefault();
      let that = this;
      this.form.validateFields((err, values) => {
        if (!err) {
          that.formValues = values
          that.showActionCaptcha('RetrievePhoneCaptcha')
        }
      });
    },
    handleSubmitForm2(e) {
      e.preventDefault();
      let that = this;
      this.form2.validateFields((err, values) => {
        if (!err) {
          that.form2Values = values
          that.showActionCaptcha('RetrieveResetCaptcha')
        }
      });
    },
    handleVerify(e) {
      e.preventDefault();
      let that = this;
      this.form.validateFields((err, values) => {
        if (!err) {
          that.formValues = values
          that.showActionCaptcha('RetrieveVerifyCaptcha')
        }
      });
    },
    RetrievePasswdCaptcha() {
      this.loading = true;
      this.$protoApi(ROUTE_LOGON_RETRIEVEPASSWDCAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        username: this.formValues.username,

        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
          .then((res) => {
            if (!res.phone) {
              res.code = 624
            }
            this.RetrievePasswdResp = res
          })
          .catch((res) => {
            this.RetrievePasswdResp = res
          })
          .finally(()=>{
            this.loading = false;
          });
    },
    RetrievePhoneCaptcha() {
      this.$protoApi(ROUTE_LOGON_RETRIEVEPHONECAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        // token: this.RetrievePasswdResp.token,
        // phone: this.RetrievePasswdResp.phone.replace('*****', this.phoneHide),
        phone: this.$store.state.phonePreApi + this.formValues.phone,

        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
          .then((res) => {
            this.RetrievePhoneResp = res
            this.RetrievePasswdResp.phone = this.$store.state.phonePreApi + this.formValues.phone
          })
          .catch((res) => {
            this.RetrievePhoneResp = res
            if (res.code !== 200) {
              this.phoneHide = ""
            }
          });
    },
    submitSmsCaptcha() {
      this.loading = true;
      this.$protoApi(ROUTE_LOGON_SMSCAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        phone: this.RetrievePasswdResp.phone.replace('*****', this.phoneHide),
        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
          .then((res) => {
            this.$refs.smsCode.focus();
            this.SMSResp = res;
            this.$store.commit("setSmsExpiresTime", Date.now() + 120 * 1000);
            setTimeout(() => {
              this.setInterval();
            });
          })
          .catch((res) => {
            this.SMSResp = res
          })
          .finally(()=>{
            this.loading = false;
          });
    },
    RetrieveVerifyCaptcha() {
      this.loading = true;
      this.$protoApi(ROUTE_LOGON_RETRIEVEVERIFYCAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.RetrievePhoneResp.token,
        code: this.formValues.phone_code,

        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
          .then((res) => {
            this.RetrieveVerifyResp = res
            this.step = 1
          })
          .catch((res) => {
            if (this.smsBtnNum !== 1) {
              this.form.setFieldsValue({
                'phone_code': ""
              })
            }
          })
          .finally(()=>{
            this.loading = false;
          });
    },
    clickServer() {
      if (this.$store.state.webType === 1) {
        this.getService()
      } else {
        this.goUrl(this.$store.state.configs.customer_web)
      }
    },
    RetrieveResetCaptcha() {
      this.loading = true;
      this.$protoApi(ROUTE_LOGON_RETRIEVERESETCAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,

        token: this.RetrieveVerifyResp.token,
        passwd: MD5(this.form2Values.password).toString(),

        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
          .then((res) => {
            this.step = 2
          })
          .catch((res) => {
          })
          .finally(()=>{
            this.loading = false;
          });
    },
  },
};
</script>
<template>
  <a-modal :visible="true" :title="$t('a6.modal.forgotPassword.title')" :keyboard="false" :maskClosable="false" class="my-modal-base dVcrVAhaF7F3FMS1CHgd" width="6.9rem" :centered="true" :closable="false" :footer="null">
    <section class="FHKvk_2Z7RDcwcOAVg1J">
      <div class="df15kCedcxS_03LfokDh">
        <a-steps :current="step">
          <a-step :title="$t('a6.modal.forgotPassword.step1')" />
          <a-step :title="$t('a6.modal.forgotPassword.step2')"/>
          <a-step :title="$t('a6.modal.forgotPassword.step3')"/>
        </a-steps>
      </div>
      <div class="a1efIfU7vchFIGInfRhe">
        <div class="N0CRgeEAXgmO0jQ2Xj8I PtX1zjfdX8hxyKSlmLhw">
          <div class="my-scrollbar my-scrollbar-hover v6hju3cXblAJtrJ1Bivd" style="width: 100%; height: 100%;">
            <div class="my-scrollbar-wrap my-scrollbar-wrap-y">
              <div class="my-scrollbar-content">
                <div class="mJqaBUwdO54uyMJbHOjM">
                  <a-form v-if="step === 0" :form="form" @submit="handleSubmit" :hideRequiredMark="true">
                    <a-form-item class="base-form-item-username gD0G2pAAJZZTJ1n0KQXv" data-validate-field="username" data-show-prefix="true">
                      <a-input :disabled="RetrievePhoneResp.code === 200"
                          @focus="RetrievePhoneResp = {}"
                          v-decorator="['phone', { rules: [{ required: true, message: this.$t('bind_tip_inputPhone')},{pattern: patternPhone, message: this.$t('bind_tip_inputPhone')}] }]"
                          ref="userNameInput" :placeholder="$t('ENTERPHONE.TIP_ENTER')" type="text" autocomplete="off">

                        <template #prefix>
                          {{$store.state.phonePreShow}}
                        </template>
                      </a-input>
                      <div class="ant-form-explain" v-if="RetrievePhoneResp.code && RetrievePhoneResp.code !== 200">
                        <div class="nsN53pBXlfBIKOxH2i_f HWQ7_CMnMe_0uIl_Xi6h bsw6_G2Y0FVAaB4DTsMb">
                          <i data-id="sprite_main_comm_icon_tip1" aria-hidden="true" focusable="false" class="IJaOtRyPA3gLzK7GQQBD" style="display: inline-block; position: relative; width: 0.18rem; height: 0.18rem; background-image: url(img/a6icons.png); background-position: -13.509rem -4.59rem; background-size: 13.689rem 13.419rem;"></i>
                          <div>
                            <div @click="clickServer" v-html="$t('a6.common.supports.notBindVaild')"></div>
                          </div>
                        </div>
                      </div>
                    </a-form-item>
                    <div class="yYbEaiQsUnq_HZAMZbUY" v-if="RetrievePhoneResp.code === 200">
<!--                      <div class="GagpXsPoU9Yu9MdHy9Qf"></div>-->
                      <div isforgetpassword="true">
<!--                        <div class="gTEpck2xQfCnW9FTDRU8">
                          <div class="rUGZYqxcffwTGfGByptS toggle">
                            <span class="cMyzhDdA3Uxu9lN8BQMn">{{$t('a6.common.components.auth.phone.title')}}</span></div>
&lt;!&ndash;                          <div class="ThtX6xB6yB9dokLkd0hI">&ndash;&gt;
&lt;!&ndash;                            <span class="nH80Fc9iIn93I8qjDHiL">{{$t('phone_hide')}}</span><span class="HpzJrGLeDr77zMiUjRUV">{{ phoneSegment[0] + ' ' + phoneSegment[1] + '*****' + phoneSegment[2] }}</span>&ndash;&gt;
&lt;!&ndash;                          </div>&ndash;&gt;
                        </div>-->
<!--                        <div class="ant-row ant-form-item ant-form-item-with-help base-form-item-phone gD0G2pAAJZZTJ1n0KQXv P9Xh3_vUfvg3HKFNFJhj" data-validate-field="phone">
                          <div class="ant-col ant-form-item-control-wrapper">
                            <div class="ant-form-item-control has-success">
                              <span class="ant-form-item-children"><div>

                              <section class="ObXUdqvr2O_T59WnYfIP">
                                <div class="AOy9_spDwFfaJ1KyoXaO">
                                  <span class="rS7A0MEjX5QYCuc8DBRj">{{ phoneSegment[0] }}</span>{{ phoneSegment[1] }}</div>
                                <div class="JOQ3xKe3o2PATB3sIlSl">
                                  <section @click="$refs.myInput.focus()" :class="['PFr_a_F2Hp5B4yxj3xvh', isInputPhone ? 'JWExyL7wP1w73iOHOxyc' : 'Gj0Iih8M0bKElM2QGtHO', RetrievePhoneResp.code === 200 ? 'X7dUlEkj1ycuuUbQ_2Vf': '']">
                                    <input ref="myInput" native-type="button" v-model="phoneHide" @focus="isInputPhone = true" @blur="phoneInputBlur" type="tel" maxlength="5" :class="['k54PxY4rjAorQBZf12Ab', isInputPhone ? 'WeJIDzHw_dbFN6u7T59A' : '']">
                                    <ul class="zNd0y3FNYltxAiKhPbAy pwd-wrap" :class="{disabled: RetrievePhoneResp.code === 200}">
                                      <li v-for="(item, index) in phoneDigits" :key="index" :class="[isInputPhone && phoneHideLength === index ? 'pwd-input-item-active qywCxukpVNuYyPfI4XVq' : '', phoneHideLength > index ? 'pwd-input-item-fill mzfnT1F3zRDTZOXCiEGs': '']" class="pwd-input-item ">
                                        <span class="number-input__text">{{ phoneHide.substring(index, index + 1) }}</span>
                                      </li>
                                    </ul>
                                  </section>
                                </div>
                                <div class="AOy9_spDwFfaJ1KyoXaO">{{ phoneSegment[2] }}</div>
                              </section>
                            </div>
                            </span>
                              <div class="ant-form-explain" v-if="RetrievePhoneResp.code !== 200">
                                <div class="zjaom39px2jNU41fx0dl">
                                  <div class="gGyEuvIkHiI2Ofp5bWpL">
                                    <i><i data-id="sprite_main_comm_icon_pay_4" aria-hidden="true" focusable="false" class="before-mock" style="display: inline-block; position: relative; width: 0.24rem; height: 0.24rem; background-image: url(img/a6icons.png); background-position: -1.91314rem -3.35314rem; background-size: 5.21486rem 5.112rem;"></i></i><span>{{ $t('a6.common.components.auth.phone.requiredNew',{length: 5})}}</span>
                                  </div>
                                </div>
                              </div>
                              <div class="ant-form-explain" v-else>
                                <div class="zjaom39px2jNU41fx0dl">
                                  <div class="gGyEuvIkHiI2Ofp5bWpL hGcydUqhSrpSgTJ6vtGA">
                                    <i><i data-id="sprite_main_comm_icon_pay_1" aria-hidden="true" focusable="false" class="before-mock" style="display: inline-block; position: relative; width: 0.24rem; height: 0.24rem; background-image: url(img/a6icons.png); background-position: -2.81143rem -3.10629rem; background-size: 5.21486rem 5.112rem;"></i></i><span>{{$t('a6.common.components.auth.phone.success')}}</span>
                                  </div>
                                </div>
                              </div>

                            </div>
                          </div>
                        </div>-->
                        <sms v-if="RetrievePhoneResp.code === 200" :phone="RetrievePasswdResp.phone" @smsBtn="onSmsBtn" @smsFocus="onSmsFocus"/>
                        <a-form-item class="base-form-item-phone_code gD0G2pAAJZZTJ1n0KQXv" :colon="false" v-show="hasSend === smsBtnNum">
                          <template #label>
                            <div>
                              <i class="anticon">
                                <i class="" style="display: inline-flex; justify-content: center; align-items: center;">
                                  <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
                                    <use xlink:href="#input_icon_yz--svgSprite:all"></use>
                                    <linearGradient id="id-9d4ce851-07e0-4e42-8c23-b4f224288a9f" x1="0.5" x2="0.5" y2="1"></linearGradient>
                                  </svg>
                                </i> </i> <span>{{$t('a6.modal.login.form.smsCode.label')}}</span>
                            </div>
                          </template>
                          <a-input allowClear v-decorator="['phone_code', { rules: [{ required: true, message: $t('a6.modal.login.form.smsCode.required')}]}]" class="yUCly6epdXnWW54zhUvY verification__codeInput" ref="smsCode" :placeholder="$t('a6.modal.login.placeholder.verificationCode3')" type="number" autocomplete="off">
<!--                            <template #suffix>
                              <a-button :loading="loading" :disabled="RetrievePhoneResp.code !== 200 || countdown > 0" @click="!(countdown > 0) && showActionCaptcha(2)" type="link" class="gDjlVPiPc_pVvkYClVBg verification__smsCode">
                                <span class="qqhDfh0Yw8XfaLbPnS1v">{{ countdown > 0 ? countdown : (!loading ? $t('a6.modal.login.form.smsCode.send'): '')}}</span>
                              </a-button>
                            </template>-->
                          </a-input>
                        </a-form-item>
                        <!--                        <div class="XtaynleEvyiBlpc5sWgE">
                                                  <button type="button" class="ant-btn ant-btn-link QLZXF52okNyv0DPjxARk">
                                                    <span>Outros Métodos</span>
                                                  </button>
                                                </div>-->
                      </div>
                      <div class="v-portal" style="display: none;"></div>
                    </div>


                    <a-button native-type="button" :loading="loading" v-if="RetrievePasswdResp.phone" type="primary" @click="handleVerify" class="epU4bDH9dqeX5Hd83a4F bPtNPwi8tx8YqjiZIeL_" :block="true">
                      {{ $t('a6.modal.passModal.next') }}
                    </a-button>
                    <a-button :loading="loading" v-else type="primary" html-type="submit" class="epU4bDH9dqeX5Hd83a4F bPtNPwi8tx8YqjiZIeL_" :block="true">
                      {{ $t('a6.modal.passModal.next') }}
                    </a-button>
                  </a-form>
                  <a-form v-if="step === 1" :form="form2" :hideRequiredMark="true">
<!--                    data-validate-field="password" data-show-prefix="true"-->
                    <a-form-item :class="['base-form-item-password', 'gD0G2pAAJZZTJ1n0KQXv']">
                      <a-input-password v-decorator="['password', { rules: [{ required: true}]}]" @input="checkPasswordStrength" :placeholder="$t('a6.center.security.loginPwd.newPwd.placeholder2')" type="text" autocomplete="new-password" allowClear>
                        <template #prefix>
                          <div>
                            <i class="" style="display: inline-flex; justify-content: center; align-items: center;">
                              <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
                                <use xlink:href="#input_icon_mm--svgSprite:all"></use><linearGradient id="id-d660a567-e7fe-44e5-9252-4912f1d1b36f" x1="0.5" x2="0.5" y2="1">

                              </linearGradient></svg></i>
                          </div>
                        </template>
                        <template #suffix>
                          <i aria-label="icon: eye-invisible" tabindex="-1" class="anticon anticon-eye-invisible ant-input-password-icon">
                            <i class="" style="display: inline-flex; justify-content: center; align-items: center;">
                              <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
                                <use xlink:href="#comm_icon_hide--svgSprite:all"></use>
                                <linearGradient id="id-e6ac219d-d2a1-4500-a6f7-0a7b91388fdb" x1="0.5" x2="0.5" y2="1"></linearGradient>
                              </svg></i></i>
                        </template>
                      </a-input-password>
                    </a-form-item>
                    <div class="ptEr7ybENfgNXBPMnsRb">
                      <div class="rBX8yUXrZ3DzhJ4ZvZUD">
                        <span>{{$t('a6.common.formRules.psswdstrength')}}</span>
                        <span class="VYe1C86O9HyF7KaCqIUg" v-for="item in 4" :key="item" :class="strength >= item ? levels[strength] : ''"></span>
                      </div>
                    </div>
<!--                    data-validate-field="passwordConfirm"-->
                    <a-form-item :class="['base-form-item-passwordConfirm', 'gD0G2pAAJZZTJ1n0KQXv']">
                      <a-input-password v-decorator="['passwordConfirm', { rules: [{ validator: validatePasswords, message: $t('a6.modal.register.notMatch')  }]}]" :placeholder="$t('a6.center.security.loginPwd.confirmNewPwd.placeholder')" type="text" autocomplete="new-password" allowClear>
                        <template #prefix>
                          <div>
                            <i class="" style="display: inline-flex; justify-content: center; align-items: center;">
                              <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
                                <use xlink:href="#input_icon_mm--svgSprite:all"></use><linearGradient id="id-d660a567-e7fe-44e5-9252-4912f1d1b36f" x1="0.5" x2="0.5" y2="1">

                              </linearGradient></svg></i>
                          </div>
                        </template>
                        <template #suffix>
                          <i aria-label="icon: eye-invisible" tabindex="-1" class="anticon anticon-eye-invisible ant-input-password-icon">
                            <i class="" style="display: inline-flex; justify-content: center; align-items: center;">
                              <svg width="1em" height="1em" fill="currentColor" aria-hidden="true" focusable="false">
                                <use xlink:href="#comm_icon_hide--svgSprite:all"></use>
                                <linearGradient id="id-e6ac219d-d2a1-4500-a6f7-0a7b91388fdb" x1="0.5" x2="0.5" y2="1"></linearGradient>
                              </svg></i></i>
                        </template>
                      </a-input-password>
                    </a-form-item>
<!--                    <div class="yYbEaiQsUnq_HZAMZbUY">
                      <div class="GagpXsPoU9Yu9MdHy9Qf"></div>
                      <div isforgetpassword="true">
                        <div class="gTEpck2xQfCnW9FTDRU8">
                          <div class="rUGZYqxcffwTGfGByptS toggle">
                            <span class="cMyzhDdA3Uxu9lN8BQMn">Verificar Senha de Saque</span></div>
                        </div>
                        <div class="ant-row ant-form-item base-form-item-withdraw_pass gD0G2pAAJZZTJ1n0KQXv" data-validate-field="withdraw_pass" data-show-prefix="true">
                          <div class="ant-col ant-form-item-control-wrapper">
                            <div class="ant-form-item-control"><span class="ant-form-item-children"><section class="PFr_a_F2Hp5B4yxj3xvh"><input type="number" maxlength="6" class="k54PxY4rjAorQBZf12Ab"><ul class="zNd0y3FNYltxAiKhPbAy pwd-wrap"><li class="pwd-input-item pwd-input-item-active qywCxukpVNuYyPfI4XVq"><i></i></li><li class="pwd-input-item"><i></i></li><li class="pwd-input-item"><i></i></li><li class="pwd-input-item"><i></i></li><li class="pwd-input-item"><i></i></li><li class="pwd-input-item"><i></i></li></ul></section></span>&lt;!&ndash;&ndash;&gt;
                            </div>
                          </div>
                        </div>
                        <div class="XtaynleEvyiBlpc5sWgE">
                          <button type="button" class="ant-btn ant-btn-link QLZXF52okNyv0DPjxARk">
                            <span>Outros Métodos</span></button>
                        </div>
                      </div>
                      <div class="v-portal" style="display: none;"></div>
                    </div>-->
                  </a-form>
                  <div v-if="step === 2" style="display: flex;flex-direction: column; justify-content: center; align-items: center; margin-top: .3rem;">
                    <van-icon name="checked" color="#31ba31" size="1.3rem"/>
                    <span>{{$t('label_success')}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="vue-portal-target">
          <a-button :loading="loading" type="primary" v-if="step === 1" @click="handleSubmitForm2" class="epU4bDH9dqeX5Hd83a4F" :block="true">
            <span>{{$t('ok')}}</span>
          </a-button>
          <button v-if="step === 2" @click="closeForgotForm" type="button" class="ant-btn ant-btn-primary ant-btn-block epU4bDH9dqeX5Hd83a4F" ant-click-animating-without-extra-node="false">
            <span>{{$t('login')}}</span>
          </button>
        </div>
      </div>
    </section>
    <div style="position: absolute; bottom: -1rem; left: 44%" @click="closeForgotForm">
      <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
    </div>
<!--    <div class="QzRd3YqTnYxG8Latq8fW closeIcon" @click="closeForgotForm">-->
<!--      <i data-id="sprite_main_img_close_s2" aria-hidden="true" focusable="false" class="" style="display: inline-block; position: relative; width: 0.5984rem; height: 0.5984rem; background-image: url(img/a6icons.png); background-position: -5.1136rem -4.1616rem; background-size: 10.3428rem 10.1388rem;"></i>-->
<!--    </div>-->
  </a-modal>

</template>