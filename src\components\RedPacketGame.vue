<template>
  <div>
    <div class="game-area" ref="gameArea">
      <!-- 精灵角色 -->
      <div
          :class="['player', { dragging: dragState.isDragging }]"
          ref="player"
          :style="{ left: playerPosition + 'rem' }"
          @mousedown="handleMouseDown"
          @touchstart="handleTouchStart"
          @selectstart.prevent
          @dragstart.prevent
      >
        <img src="/img/redGame/sprite.png" alt=""/>
      </div>

      <!-- 红包 -->
      <div
          v-for="packet in redPackets"
          :key="packet.id"
          :data-id="packet.id"
          class="red-packet"
          :style="{
          left: packet.x + 'rem',
          top: packet.y + 'rem',
          display: packet.show ? 'unset' : 'none',
          animationDuration: packet.fallDuration + 'ms'
        }"
      >
        <img v-if="res.award_type === 1" src="/img/redGame/icon2.png" alt=""/>
        <img v-else src="/img/redGame/icon1.png" alt=""/>
      </div>

      <!-- 游戏说明 -->
      <div v-if="!gameRunning && !gameOver" class="instructions">
        <p>Nhấp vào "Bắt đầu" là có thể nhận lì xì đỉnh cao. Nhấn và giữ Thần Tài rồi trượt sang trái và phải, nhận càng nhiều, bạn sẽ càng may mắn.</p>
        <button @click="startGame" @touchstart.prevent="startGame">Bắt đầu</button>
      </div>

      <!-- 游戏结束界面 -->
      <div v-if="gameOver" class="game-over">
        <img style="height: 1.3rem;width: 1.3rem" v-if="res.award_type === 1" src="/img/redGame/icon2.png" alt=""/>
        <img style="height: 1.3rem;width: 1.3rem" v-else src="/img/redGame/icon1.png" alt=""/>
        <p>Tổng số tiền nhận được: {{ $options.filters['formatGold2'](res.award_amount, 3) }} VND</p>
        <button @click="restartGame" @touchstart.prevent="restartGame">Xác nhận</button>
      </div>

      <!-- 倒计时显示 -->
      <div  v-if="isCountingDown" class="countdown">
        <h2 style="color: #f32e2e">{{ gameStartCountdown }}</h2>
      </div>

      <!-- 收集效果 -->
      <div
          v-for="effect in collectEffects"
          :key="effect.id"
          class="collect-effect"
          :style="{ left: effect.x + 'rem', top: effect.y + 'rem' }"
      >
        <img src="/img/activity/wheel/Rewards_4.png" class="effect-image" alt=""/>
        <!--        +{{ effect.points }}-->
      </div>
    </div>
  </div>
</template>

<script>
import {TDTC_ROURE} from '@/api/tdtc'

export default {
  name: 'RedPacketGame',
  data() {
    return {
      res:            {
        award_type:               1, //类型:int32,奖励类型(1:锦鲤红包,其他就是其他)
        award_amount:             0, //类型:int64,奖励金额
        remaining_lottery_count:  0, //类型:int32,剩余抽奖次数
        redpacket_time_countdown: 0, //类型:int32,红包雨倒计时
      },
      gameRunning:    false,
      gameOver:       false,
      playerPosition: 0,
      redPackets:     [],
      collectEffects: [],

      // 游戏配置
      gameConfig: {
        gameWidth:    0,
        gameHeight:   0,
        playerWidth:  1.6, // rem单位
        playerHeight: 1.6, // rem单位
        gameSpeed:    1,
        remToPx:      100 // 1rem = 100px的基准值
      },

      // 控制状态
      keys: {
        left:  false,
        right: false
      },

      // 拖拽状态
      dragState: {
        isDragging:     false,
        dragOffset:     0,
        gameAreaRect:   null,
        lastUpdateTime: 0
      },

      // 游戏循环ID
      gameLoopId:      null,
      spawnTimeoutId:  null,
      packetIdCounter: 0,
      effectIdCounter: 0,

      // 游戏状态
      gameStartCountdown:      0,
      isCountingDown:          false,
      initialPacketsGenerated: false
    }
  },

  methods: {
    get44() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.SRPR_LOTTERY)
          .then((res) => {
            Object.assign(this.res, res)
          })
          .catch(() => {
          })
    },
    // 初始化rem适配
    initRemAdaptation() {
      const setRem = () => {
        const html = document.documentElement
        const width = html.clientWidth
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)

        console.log('setRem called, width:', width, 'isMobile:', isMobile)

        if (isMobile) {
          // 移动端：以750px设计稿为基准，保持原有的7.5倍关系
          const fontSize = width / 7.5
          this.gameConfig.remToPx = fontSize
          console.log('Mobile fontSize:', fontSize)
        } else {
          // PC端：固定最大宽度
          const maxWidth = 499
          const fontSize = maxWidth / 7.5
          this.gameConfig.remToPx = fontSize
          console.log('PC fontSize:', fontSize)
        }
      }

      setRem()
      window.addEventListener('resize', setRem)
      window.addEventListener('pageshow', setRem)
    },

    // 初始化游戏
    async initGame() {
      await this.$nextTick()

      // 等待一小段时间确保rem适配完成
      setTimeout(() => {
        console.log('initGame called')
        console.log('gameArea ref:', this.$refs.gameArea)
        console.log('remToPx:', this.gameConfig.remToPx)

        if (this.$refs.gameArea && this.gameConfig.remToPx > 0) {
          // 获取游戏区域尺寸（转换为rem）
          const rect = this.$refs.gameArea.getBoundingClientRect()
          console.log('gameArea rect:', rect)

          this.gameConfig.gameWidth = rect.width / this.gameConfig.remToPx
          this.gameConfig.gameHeight = rect.height / this.gameConfig.remToPx
          this.playerPosition = this.gameConfig.gameWidth / 2 - this.gameConfig.playerWidth / 2

          console.log('Calculated game size (rem):', this.gameConfig.gameWidth, this.gameConfig.gameHeight)
          console.log('Player position:', this.playerPosition)

          // 设置精灵样式
          if (this.$refs.player) {
            this.$refs.player.style.cursor = 'grab'
            this.$refs.player.style.userSelect = 'none'
          }
        } else {
          console.error('gameArea ref not found or remToPx not set!')
        }
      }, 100)
    },

    // 键盘事件处理
    handleKeyDown(e) {
      switch (e.code) {
        case 'ArrowLeft':
        case 'KeyA':
          this.keys.left = true
          e.preventDefault()
          break
        case 'ArrowRight':
        case 'KeyD':
          this.keys.right = true
          e.preventDefault()
          break
      }
    },

    handleKeyUp(e) {
      switch (e.code) {
        case 'ArrowLeft':
        case 'KeyA':
          this.keys.left = false
          break
        case 'ArrowRight':
        case 'KeyD':
          this.keys.right = false
          break
      }
    },

    // 精灵触摸开始事件（类似鼠标拖拽）
    handleTouchStart(e) {
      e.preventDefault()
      e.stopPropagation() // 防止事件冒泡

      if (!e.touches || e.touches.length === 0) {
        return
      }

      this.dragState.isDragging = true

      const touch = e.touches[0]
      const rect = this.$refs.gameArea.getBoundingClientRect()
      const touchX = (touch.clientX - rect.left) / this.gameConfig.remToPx

      // 计算触摸点相对于精灵中心的偏移
      this.dragState.dragOffset = touchX - (this.playerPosition + this.gameConfig.playerWidth / 2)

      // 缓存游戏区域的边界信息，避免重复计算
      this.dragState.gameAreaRect = rect
    },

    // 触摸移动事件
    handleTouchMove(e) {
      if (!this.dragState.isDragging) return

      e.preventDefault()
      e.stopPropagation()

      if (!e.touches || e.touches.length === 0) {
        return
      }

      const touch = e.touches[0]
      // 使用缓存的边界信息，避免重复计算getBoundingClientRect
      const rect = this.dragState.gameAreaRect || this.$refs.gameArea.getBoundingClientRect()
      const touchX = (touch.clientX - rect.left) / this.gameConfig.remToPx

      // 计算新的精灵位置（考虑拖拽偏移）
      let newPosition = touchX - this.dragState.dragOffset - this.gameConfig.playerWidth / 2

      // 限制精灵在游戏区域内
      const minPos = 0
      const maxPos = this.gameConfig.gameWidth - this.gameConfig.playerWidth
      newPosition = Math.max(minPos, Math.min(newPosition, maxPos))

      // 使用节流优化，避免过于频繁的更新
      this.updatePlayerPositionThrottled(newPosition)
    },

    // 触摸结束事件
    handleTouchEnd() {
      if (!this.dragState.isDragging) return

      this.dragState.isDragging = false
      this.dragState.gameAreaRect = null // 清理缓存
    },

    // 优化的位置更新方法（节流）
    updatePlayerPositionThrottled(newPosition) {
      const now = performance.now()
      const timeDiff = now - this.dragState.lastUpdateTime

      // 只有位置真正改变且距离上次更新超过8ms时才更新（约120fps）
      if (Math.abs(this.playerPosition - newPosition) > 0.001 && timeDiff >= 8) {
        this.playerPosition = newPosition
        this.dragState.lastUpdateTime = now
      } else if (Math.abs(this.playerPosition - newPosition) > 0.01) {
        // 如果位置变化较大，立即更新
        this.playerPosition = newPosition
        this.dragState.lastUpdateTime = now
      }
    },

    // 鼠标拖拽事件处理
    handleMouseDown(e) {
      e.preventDefault()
      e.stopPropagation()
      this.dragState.isDragging = true

      const rect = this.$refs.gameArea.getBoundingClientRect()
      const mouseX = (e.clientX - rect.left) / this.gameConfig.remToPx

      // 计算鼠标点击位置相对于精灵中心的偏移
      this.dragState.dragOffset = mouseX - (this.playerPosition + this.gameConfig.playerWidth / 2)

      // 缓存游戏区域的边界信息
      this.dragState.gameAreaRect = rect

      document.body.style.userSelect = 'none'
    },

    handleMouseMove(e) {
      if (!this.dragState.isDragging) return

      e.preventDefault()
      e.stopPropagation()

      // 使用缓存的边界信息
      const rect = this.dragState.gameAreaRect || this.$refs.gameArea.getBoundingClientRect()
      const mouseX = (e.clientX - rect.left) / this.gameConfig.remToPx

      // 计算新的精灵位置（考虑拖拽偏移）
      let newPosition = mouseX - this.dragState.dragOffset - this.gameConfig.playerWidth / 2

      // 限制精灵在游戏区域内
      const minPos = 0
      const maxPos = this.gameConfig.gameWidth - this.gameConfig.playerWidth
      newPosition = Math.max(minPos, Math.min(newPosition, maxPos))

      // 使用节流优化，避免过于频繁的更新
      this.updatePlayerPositionThrottled(newPosition)
    },

    handleMouseUp() {
      if (!this.dragState.isDragging) return

      this.dragState.isDragging = false
      this.dragState.gameAreaRect = null // 清理缓存
      document.body.style.userSelect = ''
    },

    // 开始游戏
    startGame() {
      this.get44();

      // 重新计算游戏区域尺寸，确保准确
      if (this.$refs.gameArea && this.gameConfig.remToPx > 0) {
        const rect = this.$refs.gameArea.getBoundingClientRect()
        this.gameConfig.gameWidth = rect.width / this.gameConfig.remToPx
        this.gameConfig.gameHeight = rect.height / this.gameConfig.remToPx
        this.playerPosition = this.gameConfig.gameWidth / 2 - this.gameConfig.playerWidth / 2

        console.log('Updated game size (rem):', this.gameConfig.gameWidth, this.gameConfig.gameHeight)
        console.log('Updated player position:', this.playerPosition)
      }

      this.gameRunning = true
      this.gameOver = false
      this.gameConfig.gameSpeed = 1
      this.redPackets.splice(0)
      this.collectEffects.splice(0)
      this.initialPacketsGenerated = false
      this.packetIdCounter = 0
      this.effectIdCounter = 0

      // 生成初始红包并开始倒计时
      this.generateInitialPackets()
      this.gameLoop() // 启动游戏循环，但红包在倒计时期间不会移动
      this.startCountdown()
    },

    // 开始倒计时
    startCountdown() {
      this.isCountingDown = true
      this.gameStartCountdown = 3

      const countdownInterval = setInterval(() => {
        this.gameStartCountdown--

        if (this.gameStartCountdown <= 0) {
          clearInterval(countdownInterval)
          this.isCountingDown = false
          this.startPacketsFalling()
        }
      }, 1000)
    },

    // 开始红包下落
    startPacketsFalling() {
      // 让所有静态红包开始下落
      this.redPackets.forEach(packet => {
        if (packet.isStatic) {
          const fallDuration = Math.max(3000 - this.gameConfig.gameSpeed * 100, 1500)
          packet.speed = this.gameConfig.gameHeight / (fallDuration / 16.67)
          packet.fallDuration = fallDuration
          packet.isStatic = false

          // 红包掉落完成后结束
          setTimeout(() => {
            const index = this.redPackets.findIndex(p => p.show)
            if (index !== -1) {
              this.endGame()
            }
          }, fallDuration)
        }
      })
    },

    // 重新开始游戏
    restartGame() {
      this.playerPosition = this.gameConfig.gameWidth / 2 - this.gameConfig.playerWidth / 2
      this.$emit("close")
      // this.startGame()
    },

    // 游戏主循环
    gameLoop() {
      if (!this.gameRunning) return

      this.updatePlayer()
      this.updateRedPackets()
      this.checkCollisions()

      this.gameLoopId = requestAnimationFrame(this.gameLoop)
    },

    // 更新玩家位置
    updatePlayer() {
      // 如果正在拖拽，则不使用键盘控制
      if (this.dragState.isDragging) {
        return
      }

      const moveSpeed = 0.05 // rem单位的移动速度

      if (this.keys.left && this.playerPosition > 0) {
        this.playerPosition -= moveSpeed
      }
      if (this.keys.right && this.playerPosition < this.gameConfig.gameWidth - this.gameConfig.playerWidth) {
        this.playerPosition += moveSpeed
      }
    },

    // 生成初始红包（游戏开始时）
    generateInitialPackets() {
      // 生成3-5个随机位置的红包
      // const packetCount = 5 + Math.floor(Math.random() * 3) // 3-5个红包
      const packetCount = 20 // 3-5个红包

      for (let i = 0; i < packetCount; i++) {
        const x = Math.random() * (this.gameConfig.gameWidth - 1)
        const y = Math.random() * (this.gameConfig.gameHeight * 0.6) // 在上半部分随机分布

        const packet = {
          show: true,
          id: this.packetIdCounter++,
          x: x,
          y: y,
          speed: 0, // 初始时不移动
          fallDuration: 0,
          isStatic: true // 标记为静态红包
        }

        this.redPackets.push(packet)
      }

      // 确保有一个红包在精灵上方很近的位置
      const guaranteedPacket = {
        show: true,
        id: this.packetIdCounter++,
        x: this.playerPosition + (this.gameConfig.playerWidth - 1) / 2, // 精灵中心位置
        y: this.gameConfig.gameHeight - 3, // 距离精灵很近的位置
        speed: 0,
        fallDuration: 0,
        isStatic: true
      }

      this.redPackets.push(guaranteedPacket)
      this.initialPacketsGenerated = true
    },

    // 更新红包位置
    updateRedPackets() {
      this.redPackets.forEach(packet => {
        if (!packet.isStatic) {
          packet.y += packet.speed
        }
      })
    },

    // 检查碰撞
    checkCollisions() {
      for (let i = this.redPackets.length - 1; i >= 0; i--) {
        const packet = this.redPackets[i]
        if (!packet.show) continue
        const packetRect = {
          x:      packet.x,
          y:      packet.y,
          width:  1, // rem单位
          height: 1 // rem单位
        }

        const playerRect = {
          x:      this.playerPosition,
          y:      this.gameConfig.gameHeight - 1, // rem单位
          width:  this.gameConfig.playerWidth,
          height: this.gameConfig.playerHeight
        }

        if (this.isColliding(packetRect, playerRect)) {
          this.collectRedPacket(packet, i)
        }
      }
    },

    // 碰撞检测
    isColliding(rect1, rect2) {
      return rect1.x < rect2.x + rect2.width &&
          rect1.x + rect1.width > rect2.x &&
          rect1.y < rect2.y + rect2.height &&
          rect1.y + rect1.height > rect2.y
    },

    // 收集红包
    collectRedPacket(packet, index) {
      if (this.redPackets[index].isStatic) return
      this.redPackets[index].isStatic = true
      this.redPackets[index].show = false
      this.gameConfig.gameSpeed += 0.1

      // 添加收集效果
      this.showCollectEffect(packet.x, packet.y)
    },

    // 显示收集效果
    showCollectEffect(x, y) {
      const effect = {
        id:     "effect" + this.effectIdCounter++,
        x:      x,
        y:      y,
        points: 10
      }

      this.collectEffects.push(effect)
    },

    // 结束游戏
    endGame(flag) {
      this.gameRunning = false
      this.gameOver = true

      if (this.gameLoopId) {
        cancelAnimationFrame(this.gameLoopId)
      }
      if (this.spawnTimeoutId) {
        clearTimeout(this.spawnTimeoutId)
      }

      this.redPackets.splice(0)
    }
  },

  mounted() {
    // 初始化rem适配
    this.initRemAdaptation()

    // 初始化游戏
    this.initGame()

    // 添加事件监听器
    document.addEventListener('keydown', this.handleKeyDown)
    document.addEventListener('keyup', this.handleKeyUp)
    document.addEventListener('mousemove', this.handleMouseMove)
    document.addEventListener('mouseup', this.handleMouseUp)

    // 添加全局触摸事件监听器（用于拖拽）
    document.addEventListener('touchmove', this.handleTouchMove)
    document.addEventListener('touchend', this.handleTouchEnd)
  },

  beforeUnmount() {
    // 清理事件监听器
    document.removeEventListener('keydown', this.handleKeyDown)
    document.removeEventListener('keyup', this.handleKeyUp)
    document.removeEventListener('mousemove', this.handleMouseMove)
    document.removeEventListener('mouseup', this.handleMouseUp)
    document.removeEventListener('touchmove', this.handleTouchMove)
    document.removeEventListener('touchend', this.handleTouchEnd)

    if (this.gameLoopId) {
      cancelAnimationFrame(this.gameLoopId)
    }
    if (this.spawnTimeoutId) {
      clearTimeout(this.spawnTimeoutId)
    }
  }
}
</script>

<style scoped>
.game-area {
  position      : relative;
  height        : 9.28rem;
  width         : 7rem;
  background    : linear-gradient(to bottom, #940B0E 0%, #F1BD75 100%);
  border        : 0.05rem solid #ffc107;
  border-radius : 0.2rem;
  overflow      : hidden;
}

.player {
  position        : absolute;
  bottom          : 0.2rem;
  z-index         : 10;
  transition      : left 0.1s ease;
  filter          : drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  width           : 1.6rem;
  height          : 1.6rem;
  display         : flex;
  align-items     : center;
  justify-content : center;
  cursor          : grab;
  user-select     : none;
  touch-action    : none; /* 防止移动端滚动 */
}

.red-packet img,
.player img {
  width          : 100%;
  height         : 100%;
  object-fit     : contain;
  pointer-events : none; /* 防止图片本身响应事件 */
}

/* 拖拽时禁用过渡效果，提高响应速度 */
.player.dragging {
  transition : none !important;
  cursor     : grabbing;
  transform  : scale(1.05); /* 拖拽时稍微放大 */
}

.player:active {
  cursor : grabbing;
}

.red-packet {
  position        : absolute;
  z-index         : 5;
  animation       : fall linear;
  filter          : drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
  width           : 1rem;
  height          : 1rem;
  display         : flex;
  align-items     : center;
  justify-content : center;
}

@keyframes fall {
  from {
    top : -1.2rem;
  }
  to {
    top : 100%;
  }
}

.instructions, .game-over {
  position      : absolute;
  top           : 50%;
  left          : 50%;
  width         : 6rem;
  transform     : translate(-50%, -50%);
  background    : rgba(255, 255, 255, 0.95);
  padding-top   : 0.2rem;
  border-radius : 0.15rem;
  text-align    : center;
  box-shadow    : 0 0.1rem 0.2rem rgba(0, 0, 0, 0.2);
  z-index       : 20;
}

.countdown {
  position      : absolute;
  top           : 50%;
  left          : 50%;
  transform     : translate(-50%, -50%);
  padding-top   : 0.2rem;
  border-radius : 0.15rem;
  text-align    : center;
  z-index       : 19;
}
.countdown h2 {
  font-weight: bold;
  font-size: 3em;
  margin: 0.1rem 0;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.game-over p {
  font-family : SFU Machine;
  font-weight : 400;
  font-size   : 0.3rem;
  color       : #964109;
}

.instructions p {
  margin-bottom : 0.2rem;
  color         : #495057;
  font-size     : .26rem;
}

.instructions h2, .game-over h2 {
  color         : #d63384;
  margin-bottom : 0.2rem;
  font-size     : 2em;
}

.collect-effect {
  position       : absolute;
  color          : #ff6b6b;
  font-weight    : bold;
  font-size      : 1.5em;
  z-index        : 15;
  pointer-events : none;
  animation      : fadeUp 1s ease-out forwards;
  filter         : drop-shadow(1px 1px 2px rgba(0, 0, 0, 0.3));
}

.collect-effect .effect-image {
  width      : .8rem;
  height     : .8rem;
  object-fit : contain;
}

@keyframes fadeUp {
  0% {
    opacity   : 1;
    transform : translateY(0);
  }
  100% {
    opacity   : 0;
    transform : translateY(-0.3rem);
  }
}

button {
  background    : linear-gradient(45deg, #ff6b6b, #feca57);
  color         : white;
  border        : none;
  border-radius : 0.3rem;
  cursor        : pointer;
  transition    : transform 0.2s ease;
  font-weight   : bold;
  box-shadow    : 0 0.04rem 0.08rem rgba(0, 0, 0, 0.2);
  font-size     : .3rem;
  min-height    : 0.68rem;
  min-width     : 2rem;
  touch-action  : manipulation; /* 防止移动端双击缩放 */
  margin        : .2rem 0;
}

button:hover {
  transform  : translateY(-0.02rem);
  box-shadow : 0 0.06rem 0.12rem rgba(0, 0, 0, 0.3);
}

button:active {
  transform  : translateY(0);
  background : linear-gradient(45deg, #e55a5a, #e6b84f);
}
</style>
