import Vue from "vue";
import VueI18n from "vue-i18n";
import { merge } from 'lodash';

Vue.use(VueI18n); //n["b"].use(o["a"]);

const locales = require.context(
    "./locales",
    true,
    /[A-Za-z0-9-_,\s]+\.js$/i
);
const messages = {};
// 'en', 'pt',
    ['vn'].forEach((lang)=>{
  let content = {}
  locales.keys().forEach((file) => {
    if (file.indexOf('./'+lang) === 0) {
      content = merge(content, locales(file).default);
    }
  })
  messages[lang] = content;
})

const i18n = new VueI18n({
  locale: localStorage.getItem("hisLang") || "vn",
  fallbackLocale: "vn",
  messages: messages,
  silentTranslationWarn: true
});
export default i18n;
