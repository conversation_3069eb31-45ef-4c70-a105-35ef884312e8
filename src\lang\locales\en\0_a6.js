export default {
    a6: {
        theme40_dz:              {
            userInfo: {
                sayHi:    "Hello",
                login:    "Login",
                register: "Register",
            },
            footer:   {
                license:         "License",
                protect:         "Protection",
                followUs:        "Follow us",
                playResponsibly: "Play responsibly",
            },
            jackpot:  {
                400051:  "Money Coming",
                2000048: "Double luck",
                2000068: "Fortune Mouse",
                2000098: "Fortune OX",
                2000126: "Fortune Tiger",
                2000135: "Big Bounty Battle",
                2001007: "Fortune Rabbit",
                2001027: "Fortune Dragon",
                winTxt:
                         'Just won<span class="reward">{reward}</span></br><p class="txt">in<span class="place">{place}</span></p>',
            },
        },
        theme39_dz:              {gameTip: "The game is under maintenance"},
        theme34_dz:              {
            promote: "Free Money\nRefer & Earn",
            events:  "Promotions and\nBonuses",
        },
        theme27_dz:              {
            home:   {
                promoteTitle: "Earn one million per month",
                popular:      "Popular",
                total:        "All",
                distancial:   "Remaining",
                recebido:     "Received",
                totalAmount:  "Total amount",
                invite:       "Invite",
                player:       "Player",
                open:         "Open",
                justWin:      "Just won",
                jackpotTitle: "Play for free, the award is waiting for you",
                downloadTips: "Fast and stable, enjoy at your fingertips",
            },
            header: {inviteFriends: "Invite friends"},
        },
        theme19_dz:              {search: "Search"},
        theme33_dz:              {
            search:                 "Search",
            discount:               "Offers",
            jackpot:                "JACKPOT",
            helpCenter:             "Help",
            popular:                "Popular",
            pgTop:                  "PG Slots",
            jiliLucky:              "JILI Slots",
            ppSlot:                 "PP Slots",
            feature:                "JDB Slots",
            featuredGames:          "Featured Games",
            feast:                  "Entertainment feast",
            galore:                 "Entertainment feast",
            downloadTitle:          "Best Mobile Gambling App",
            downloadContent:
                                    "Download the mobile application to experience the most thrilling slot games, fishing, card games, live casino, sports, and blockchain at your fingertips. 5D screen allows you to enjoy exciting gaming experiences anytime, anywhere.",
            googleText:             "Get it on Google Play",
            appText:                "Get it on the App store",
            centerWallet:           "Wallet",
            withdrawalManagement33: "Withdraw",
            accountDetails33:       "Statement",
            bettingRecord33:        "Betting\nrecord",
            security33:             "Security\ncenter",
            feedback33:             "Reward Feedback",
            messageCenter33:        "Message\nCenter",
        },
        theme14_dz:              {
            home: {
                downloadAppTxt:
                         "Download the APP now to have a better and safer gaming experience on your phone. The app supports all categories: games, fishing, casinos, and card games.",
                footer:  {
                    license:         "",
                    protect:         "",
                    followUs:        "",
                    playResponsibly: "",
                    tgLink:          "https://t.me/aabr88",
                    youtubeLink:     "https://www.youtube.com/",
                    fbLink:          "https://www.facebook.com/aabrcom",
                },
                welcome: "Welcome",
                h5:      "<p>Direct Access</p><h1>AAbr.com</h1>",
            },
        },
        theme12_dz:              {
            header:   {tool_tips_hot: "Hot"},
            platform: {start_game: "Start game"},
            download: {
                h1:      "Download application",
                h3:      "Log in to pick up one -click click <span> 7 Real </span>",
                ios:     "Download iOS",
                android: "Download Android",
            },
            card:     "Cards",
            real:     "Live",
            events:   "Event",
            popular:  "Hot",
            discount: "Offers",
        },
        theme15_dz:              {
            download:  {
                h1:          "Best Mobile Betting App",
                h3:          "The industry's best mobile gambling App",
                p:           "Enjoy it anytime, anywhere! Here, you'll find the most extensive collection of slot, card, fishing, live, sports, esports, and lottery games across the entire web.",
                app:         "Download APP",
                downloading: "Downloading",
                nodeNeed:    "No need to download",
                domin:       "best777.com",
            },
            more:      "More",
            header:    {
                servider: "Line Detection",
                suporte:  "Customer service",
            },
            headerNav: {
                menu0:           "Hot",
                promote:         "Agent",
                download:        "APP Download",
                collected:       "Reward",
                collectedRecord: "History",
            },
            asideList: {
                game:            "Game",
                club:            "Club",
                discount:        "Offers",
                customerService: "Customer Service Center",
                checkLine:       "Switch line",
                aboutUs:         "",
            },
            tabLable:  {
                Ajuda:    "Help Center",
                Sugestao: "Reward Feedback",
                FAQ:      "FAQ",
                Sobre:    "About",
            },
            bran:      {
                cli:          {
                    label:  "GLI International Certification",
                    label1: "Completely random algorithm",
                },
                Comite:       {
                    label:  "Supervisory board",
                    label1: "UKGC"
                },
                Filipinas:    {
                    label:  "Philippines (PAGCOR)",
                    label1: "Supervision of gambling license",
                },
                Certificacao: {
                    label:  "Admission certification",
                    label1: "Malta (MGA)",
                },
            },
            footerText:
                       "Best777 is the world's most famous online casino operating company, providing exciting and entertaining activities with live dealers, including slots, table games, electronic games, fishing, lotteries, sports, etc. The group is authorized and regulated by the government of Curacao and operates under license number 8048/JAZ (Antillephone). The group has passed all compliance audits and has obtained legal authorization to operate all chance games and gambling.",
        },
        theme13_dz:              {
            header: {
                week:          {
                    day0: "Monday",
                    day1: "Tuesday",
                    day2: "Wednesday",
                    day3: "Thursday",
                    day4: "Friday",
                    day5: "Saturday",
                    day6: "Sunday",
                },
                onlineService: "Hello, I want ...",
                welcome:       "Welcome!",
                balance:       "Balance",
            },
            home:   {
                partInfo: {
                    safeTitle:    "PRESTIGIOUS BRAND",
                    safeTxt:
                                  "A market-leading reputable brand, with billions in deposits and withdrawals, no worries.",
                    varietyTitle: "Diverse Products",
                    varietyTxt:
                                  "Slot machines, cards, fishing. Multiple products for you to choose.",
                    topupTitle:   "Security",
                    topupTxt:
                                  "Diverse payment methods, ensuring safety and information security.",
                    fastTitle:    "Quick Transactions",
                    fastTxt:
                                  "Remittance is processed immediately upon receipt of funds.",
                    playNow:      "Play Now",
                },
                winner:   "BECOME A WINNER",
                downloadAppTxt:
                          "Download the APP now for a better and safer gaming experience on your mobile phone.",
                h5:       "<p>Visit</p><h1>win55.com</h1>",
            },
        },
        error:                   {
            lostTo404:        "Oh no, that is not supposed to happen",
            backToHome:       "Back to Home",
            demoAccountTips:  "Streamer account have no permission",
            networkTimeout:
                              "Network timeout, please try again later. If the request fails continuously, please contact customer service for assistance",
            networkTimeout2:  "Network timeout, please",
            networkTimeout3:  "Timeout please try again",
            networkError:
                              "Network is down, please check your internet connection",
            upgraded:         "The current page has timed out",
            upgradedMsg:
                              "We have upgraded! For your better experience, please update to the latest version!",
            upgradedBtn:      "Upgrade immediately",
            guide2Reload:
                              "You've been inactive for a while, please refresh the page!",
            channelErrorText:
                              "Please access using the generated channel or referral link, otherwise, linking won't be successful!",
            loadingError:     "Timeout, please {retry} or {reload} page!",
            updatePage:
                              "The content of the current page has expired, please refresh the page and try again!",
            update:           "Refresh now",
            otherClientLogin: {
                content:
                              "<b>This account is currently playing the game on the ({deviceModel}) device.</b> Currently no operations are available. You can choose to exit the game; <b>If the device is not used by you, you can click to forcefully delete the device and promptly change Password. </b>",
                deleteDevice: "Delete device and change password",
                logoutGame:   "Quit Game",
                deleteDeviceError:
                              "Forced deletion of the device failed, please try again or contact customer service.",
                logoutGameError:
                              "The third-party game failed to exit. Please try again or contact customer service.",
            },
            friendlyReminder: "Reminder",
        },
        common:                  {
            number:        {
                1: "<span>1</span>",
                2: "<span>2</span>",
                3: "<span>3</span>",
                4: "<span>4</span>",
                5: "<span>5</span>",
                6: "<span>6</span>",
                7: "<span>7</span>",
            },
            formRules:     {
                required:            "{requiredItem} cannot be empty",
                requiredWithoutItem: "Cannot be empty",
                username:            "{range} bit character, support English/numbers",
                userpass:
                                     "{range} characters, at least two of English/numbers/symbols",
                loginUserpass:       "{range} digits, English/digits/symbols",
                userpassSpecial:
                                     "Symbols are not supported. Please enter symbols from !@#$%^&*_=+-,./?()",
                usercpf:             "Please enter CPF",
                paypass:             "{length}-digit number",
                numberRequired:      "{type} must be pure digits",
                itemRequired:        "You have essential information yet to fill in",
                withdrawalPass:
                                     "Please enter {length} characters, supporting numbers/letters/English symbols",
                atLeast:             "　Characters, must include at least　",
                digit:               "Digits",
                lowercase:           "Lowercase",
                uppercase:           "Uppercase",
                num:                 "Numbers",
                special:             "Symbols",
                psswdstrength:       "Strength",
            },
            tips:          {
                paste:              "Paste",
                copy:               "Copied successfully",
                copyFail:           "Copy failed",
                return:             "Back",
                next:               "Next",
                cancel:             "Cancel",
                confirm:            "Confirm",
                skip:               "Skip",
                validating:         "Detecting...",
                loadingTip:         "Loading, please wait...",
                customerNotEnabled: "Online customer service is unavailable",
                downloadAppPageNotEnabled:
                                    "The app download function is unavailable",
                month:              "Month",
            },
            supports:      {
                simple: "<span>Contact Us</span>",
                notBind:
                        "Account abnormal. Please <span> contact Customer Service</span>",
                accountException:
                        "Account abnormal. Please <span>contact the Customer Service</span>",
                forgotPwd:
                        "Forgot Password? <span>Contact Customer Service</span>",
                forgotSecrecyQuestion:
                        "Forgot Security Question? <span>Contact Customer Service</span>",
                authUnavailable:
                        "The above method is not available? <span> Contact Us</span>",
                notBindVaild:
                        "Unlinked verification items, please <span>contact customer service</span>",
                accountError:
                        "Account does not exist, please <span> contact customer service</span>",
            },
            pagination:    {
                prev:   "Previous page",
                next:   "Next page",
                total:  "Total of {0} entries",
                goto:   "Go",
                page:   "Page",
                select: "{0} entries/page",
            },
            components:    {
                auth:       {
                    contactUS:    "Support",
                    othersVerify: "Other Verification Methods",
                    ga:           {
                        title:     "Google Verification",
                        label:     "Google Authenticator Code",
                        inputTips: "Please enter Google Authenticator code",
                    },
                    email:        {
                        inputTips:   "Enter Email",
                        current:     "Current Linked Email",
                        title:       "Email Verification",
                        label:       "Email",
                        success:     "Email entered correctly",
                        error:       "Email error",
                        formatError: "Email format error, please re-enter",
                        required:
                                     "For the security of your account, please fill in the complete email in the middle",
                        requiredNew:
                                     "Please provide a complete email to receive the verification code",
                    },
                    phone:        {
                        current:        "Linked phone",
                        title:          "Mobile Verification",
                        label:          "Mobile Phone",
                        success:        "Phone Number Entered Correctly",
                        error:          "Incorrect phone number, please re-enter",
                        required:
                                        "For your account security, please fill in the complete {length} digits in the middle of the phone number",
                        requiredNew:
                                        "Please provide a complete phone number to receive the verification code (i.e., enter the middle {length} digits)",
                        requiredOfActivity:
                                        "Please enter a complete {x}-digit mobile number",
                        sendSuccess:    "Verification code obtained successfully!",
                        toBindPhone:    "You haven't linked your mobile number yet,",
                        toBindPhoneBtn: "Link",
                    },
                    withdrawal:   {
                        title:  "Withdrawal Password Verification",
                        label:  "Verify Withdrawal Password",
                        forgot: "Forgot password?",
                    },
                    question:     {
                        name:      "Security",
                        title:     "Security Verification",
                        label:     "Security Question",
                        label2:    "Answer",
                        inputTips: "Enter Security Answer",
                    },
                },
                geetest:    {
                    clickToValid: "Click the button to verify",
                    success:      "Verification successful",
                    error:
                                  "Failed to obtain verification, please refresh the page and try again",
                },
                datePicker: {
                    error: "Please enter the correct date",
                    YYYY:  "Year",
                    MM:    "Month",
                    DD:    "Day",
                },
            },
            upload:        {
                error: {
                    fileSize:    "File size not supported",
                    fileSize2MB: "Only PNG, JPG format, not exceeding 2MB",
                    fileType:    "File type not supported",
                    fileNull:    "File not found, please re-upload",
                },
                text:  "Upload the file",
            },
            deviceContext: {
                betterExperience: "To ensure a good gameplay experience",
                rotateToPortrait:
                                  "Please operate your mobile/tablet in portrait mode",
            },
            loading:       {
                click:  "Clicked",
                retry:  "Retry",
                reload: "Re-enter"
            },
            date:          {
                today:     "Today",
                yesterday: "Yesterday"
            },
        },
        saveShortcut:            {
            btn:                    "<span>Add</span> to the main screen first",
            guideModalTitle:        "Add to main screen",
            guideModalTitleAndriod: "Add to Home Screen",
            guideModalStep1:
                                    '1. Click the "More" icon, then click "Add to Home Screen"',
            guideModalStep2:        '2. Click "Add," then select "Add"',
            label1:                 "Share",
            label2:                 "Find on the webpage",
            label3:                 "Desktop site",
            label4:                 "Add to Quick Notes",
            label5:                 "Find on page",
            label6:                 "Bookmark",
            label7:
                                    "A shortcut will be added to your home screen for quick access to this website",
            cancle:                 "Cancel",
            add:                    "Add",
            useH5:                  "Continue to use",
            click:                  "Click",
            openmenu:               "Open menu bar",
            select:                 "Select",
            addHome:                "Add to home screen",
        },
        rightBar:                {
            reward:                        "Claim",
            backHome:                      "Home",
            backTop:                       "Top",
            downloadTitleFromClickGame:
                                           "In order to improve your user experience, you must download the native APP to play the game!",
            downloadApp:                   "Download APP",
            downloadAppTitle:              "Download APP",
            downloadAndroidApp:            "Android",
            downloadIosApp:                "iOS",
            onlineCustomer:                "Online  Service 24/7",
            scanQR:                        "Scan QR code",
            downloadAppSpeed:              "Speed Version",
            downloadAppNormal:             "Normal Version",
            downloadAppComplete:           "Complete Version",
            downloadDevice:                "Download mobile client",
            redPocketEnd:                  "Finished",
            noDownApp:                     "Download address not yet configured~",
            downloadAppTips:
                                           "Download and use the app to participate in more activities and enjoy more discounts!",
            useH5:                         "Continue using H5",
            titleFromChannel:              "Important reminder",
            channelTip1:
                                           "You must download and use the APP to play the game!",
            channelTip2:
                                           "At the same time, the system will give you additional",
            channelTip3:                   "Bonus!",
            channelTip4:
                                           "You must download and use the APP to play the game!",
            saveShortcutMessage:           "Install now",
            installingSaveShortcutMessage: "Installing",
            installedSaveShortcutMessage:
                                           "Installation completed! Open desktop shortcut",
            channelTip5:                   "(Only for first-time deposited members)",
            depositAmount:                 "Your recharge of <b>{0}</b> has arrived!",
            gameExperience:                "For a better gaming experience,",
            downloadAndInstall:
                                           "Please download and install the latest APP, and then use the account:",
            getAward:                      "Log in and play on the APP!",
            getAward1:                     "At the same time, the system will send you free",
            comma:                         ",",
            contactService:                "If you have any other questions, please",
            contactService1:               "Advisory Service",
            confirmApp:                    "Please look for the following APP icon and name:",
            installGuild:                  "Installation tutorial:",
            installDetail1ByiOS:
                                           "1. Select the application you want to install",
            installDetail2ByiOS:           "2. Click the install button",
            installDetail3ByiOS:           "3. Return to the desktop and open settings",
            installDetail4ByiOS:           "-General-VPN and device management",
            installDetail5ByiOS:
                                           "4. Find the {0} application and click Set Trust",
            installDetail1ByAndroid:
                                           "1. Select the application you want to install",
            installDetail2ByAndroid:
                                           "2. Click the Download or Install Now button",
            installDetail3ByAndroid:
                                           "3. Return to the desktop and select {0} to open",
            installDetail4ByAndroid:
                                           "This application has received safety certification from Google Play, please feel free to install it.",
            startInstall:                  "Install now",
        },
        tabbar:                  {
            home:       "Home",
            menu:       "Menu",
            event:      "Event",
            service:    "Support",
            download:   "APP Download",
            discount:   "Offers",
            recharge:   "Deposit",
            withDraw:   "Withdraw",
            register:   "Register",
            login:      "Login",
            mine:       "Profile",
            balance_dz: "Balance",
        },
        dz_5159:                 {tarbar: {service: ""}},
        mine:                    {
            vip:      {
                tips: {
                    deposit: "amount for deposit ",
                    bet:     "Coding"
                }
            },
            share:    {
                tips:                             "Promote",
                yesterdayMembers:                 "The number of people yesterday (valid)",
                yesterdayTotalDirectBets:         "Direct performance yesterday",
                yesterdayTotalDirectCommissionse: "Commission not claimed",
            },
            itemList: {
                netWorkStatus:    "Network Status",
                selectLanguage:   "Language",
                switchMode:       "Switch Mode",
                currentMode:      "Current mode {mode}",
                versionUpdate:    "New version update",
                versionContent:   "Version content",
                gotVersionUpdate: "Discovery a new version",
                club:             "Club",
                music:            "Music",
            },
        },
        header:                  {
            account:          "Account",
            messages:         "Messages",
            withdraw:         "Withdraw",
            recharge:         "Deposit",
            personalCenter:   "Profile",
            menu:             "Menu",
            loginout:         "Log Out",
            onlineService_dz: "",
            welcome_dz:       "",
            menus:            {
                messageCenter:             "Message Center",
                accountDetails:            "Statement",
                bettingRecord:             "Bet Records",
                personalStatement:         "Reports",
                personalStatementCyanBlue: "Report",
                withdrawalManagement:      "Withdraw",
                promote:                   "Agent {content}",
                defaultPromoteAdvertise:   "Monthly income of millions",
                settings:                  "Profile",
                security:                  "Security Center",
                music:                     "Music",
                feedback:                  "Reward Feedback",
                about:                     "About",
                logout:                    "Quit",
            },
            logoutTips:       "Do you want to log out from the current account?",
            logoutOkText:     "Confirm exit",
            logoutCancelText: "Cancel",
            moneyInGame:      "In-game use",
            officeDomain:     "Official domain",
        },
        balance:                 {
            current:   "Current balance",
            text1:     "If the balance is lost, you can click",
            find:      "Retrieve the balance",
            award:     "Reward",
            text2:     "Bet again",
            text3:
                       "You can transfer it out and bring it into the game or withdraw cash.",
            checkRule: "View rules",
        },
        modal:                   {
            noData:            "No data",
            notOpen:           "Coming Soon",
            verify_user_password_text:
                               "Here is your current account password. Please don't forget it!",
            importantReminder: "important hint",
            reset_withdraw_password:
                               "Please change the withdrawal password before making deposits/withdrawals",
            force:             {
                understood:                "Got it",
                main_not_show_again_today: "Don't show again today",
                serviceUpgrade:            "Stop service and upgrade",
            },
            title:             {error: "Important reminder"},
            forceUpdate:       {
                content:
                        "Your current version is too low, you need to upgrade to the latest version before you can operate it.",
                okText: "Upgrade immediately",
            },
            search:            {placeholder: "Search"},
            login:             {
                label:         "Login",
                otherWayLogin: "Other login methods",
                type:          {
                    casino: "Casino Login",
                    club:   "Club Login"
                },
                googleInitError:
                               "The login method is currently under maintenance, please try again later or contact customer service.",
                passwordHint:
                               "Your current password is weak, please change the password as soon as possible.",
                form:          {
                    smsCode:       {
                        label:       "Verification Code",
                        placeholder: "Email verification code",
                        counting:    "Resend in {seconds}s",
                        send:        "Get Verification Code",
                        required:    "Verification code cannot be empty",
                        getCode:     "Please acquire the SMS verification code first",
                        emailCode:   "Please get the email verification code first",
                    },
                    phone:         {
                        label:       "Phone Number",
                        placeholder: "Phone number",
                        required:    "Incorrect phone number, please re-enter!",
                        viError:
                                     "Numbers starting with 0 do not require the 0, for example, **********, just enter *********",
                    },
                    username:      {
                        label:       "Member account",
                        labelShort:  "Account",
                        placeholder: "Enter Member Account",
                    },
                    realName:      {
                        label:       "Real Name",
                        labelShort:  "Name",
                        placeholder: "Please enter your real name",
                        realNameTips:
                                     "Support the input letters, text, space and special symbols’'·",
                        realNameTips2:
                                     "Support letters/text/spaces and special symbols’'·",
                        realNameTips3:
                                     "Support letters/spaces and special symbols’'·",
                    },
                    userpass:      {label: "Password"},
                    stakeusername: {placeholder: "Enter Member Account"},
                    stakepassword: {placeholder: "Please enter the password"},
                },
                placeholder:   {
                    platformId:        "Member account",
                    platformId3:
                                       "Please enter {minmax} character to support English/numbers",
                    passwd:            "Please enter password",
                    passwd3:
                                       "Please enter the {minMax} bit, at least the symbols in English/numbers/symbols",
                    passwdConfirm:
                                       "Please confirm the password again, it should be the same as the password!",
                    passwdConfirm3:
                                       "Please enter the {minMax} bit, at least the symbols in English/numbers/symbols",
                    mobilePhoneNumber: "Please enter phone number",
                    verificationCode:
                                       "Please enter the mobile phone verification code",
                    verificationCode3: "Enter verification code",
                    email:             "Please enter the mailbox account",
                    emailCode:         "Please enter the email verification code",
                    emailCode3:        "Enter verification code",
                    realName:
                                       "Fill in the true name and make it convenient for later withdrawal!",
                    realName31:        "Please enter your real name",
                    realName32:
                                       "Please enter your real name, only support uppercase English",
                    cpfCode:
                                       "Fill in the CPF and make it convenient for later withdrawal!",
                    currency:          "Please select currency",
                    inviterId:         "Please enter your referral code",
                    inviterId3:        "Please enter the referral code",
                },
                tabs:          {
                    accountLogin:    "Login",
                    accountRegister: "Account Register",
                    phoneLogin:      "Phone Login",
                    phoneRegister:   "Phone Register",
                    phoneSubTitle:
                                     "This field is for registration or login, not linking!",
                    emailLogin:      "Email Login",
                    emailRegister:   "Email Register",
                    emailSubTitle:
                                     "This field is for registration or login, not linking!",
                },
                operate:       {
                    rememberPwd:     "Remember Password",
                    forgotPwd:       "Forgot Password?",
                    registerAccount: "Register now",
                    smsLogin:        "SMS login",
                    accountLogin:    "Login",
                    tryItFree:       "Play Demo",
                    registerGive:    "Register +",
                    bindingGive:     "Linking +",
                    accountRegister: "Account Sign up",
                    modify:          "Modify",
                    clickLogin:      "Login",
                },
            },
            currency:          {
                chinese:     "Chinese",
                english:     "English",
                USD:         "USD",
                CNY:         "CNY",
                HKD:         "HK$",
                THB:         "THB",
                VND:         "VND",
                IDR:         "IDR",
                INR:         "INR",
                AUD:         "AUD",
                GBP:         "GBP",
                KRW:         "KRW",
                JPY:         "JPY",
                BRL:         "BRL",
                MXN:         "MXN",
                EUR:         "EUR",
                RUB:         "RUB",
                MMK:         "MMK",
                AED:         "Dirham",
                PHP:         "PHP",
                KHR:         "Cambodian Riel",
                USDT:        "USDT（For USD exchange）",
                USDT7_1:     "USDT 1:7 (For CNY exchange)",
                USDC:        "USDC (For USD exchange)",
                USDC7_1:     "USDC 1:7 (For CNY exchange)",
                BTC:         "BTC",
                BTC100000_1: "BTC 1:100000  (For CNY exchange)",
                BTC10000_1:  "BTC 1:10000 (For USD exchange)",
                ETH:         "ETH",
                ETH10000_1:  "ETH 1:10000 (For CNY exchange)",
                ETH1000_1:   "ETH 1:1000 (For USD exchange)",
                KPW:         "KPW",
                PRK:         "PKR",
                NPR:         "NPR",
                BDT:         "BDT",
                MUR:         "MUR",
                NGN:         "Naira",
                TRX:         "Tron",
                TRY:         "Turkish lira",
                MAD:         "Moroccan Dirham",
            },
            register:          {
                readed:                 "I have read",
                label:                  "Register",
                thirdLabel:             "Account Setup",
                thirdAccountLabel:      "{thirdWay} Account",
                imageVerificationCode:  "Verification Code",
                successTips:            "Registered successfully! Let's play a game now~",
                successRegister:        "Registraten Successful",
                available:              "Account available",
                existed:                "Account existed",
                notMatch:               "Passwords do not match, please re-enter!",
                passwdConfirm:          "Confirm Password",
                inviterAccount:         "Ignore if no invitation code",
                selectCountry:          "Please select currency",
                realNameError:          "Only upper-case English letters are supported",
                cpfError:
                                        "Support input of 11-digit numbers, special characters supported: .-",
                selectCountryError:
                                        "The currency determines third-party games and payment and cannot be changed!",
                selectCurrencyTips:
                                        "The currency of the invitation code and your currency must be consistent, otherwise, the binding will fail",
                submitCurrencyError:
                                        "Current linked currency is inconsistent with the invitation code. Please fill in again",
                invitetAccountRequired: "Please enter the referral code",
                saveAccountPwd:         "Save Image",
                modifyNow:              "Modify now",
                confirm:                "Register",
                rangeNumber:            "{range} digits",
                editAccountTips:
                                        "Important: The Account can only be edited this once.",
                agreementRequiredTips:
                                        "Please check the User Agreement box first",
                userAgreement:
                                        'I am over 18 years old and have read and agree to the <span data-user-agreement="true">《User Agreement》<span/>',
                inviterAccount1:        "",
                inviterCode:            "Invitation Code",
                close:                  "Close",
                accountInfo:            "Account information",
                successCallback:        "Congratulations on successful registration!",
                saveAccountInfo:
                                        "Here are your account details and password. Please keep them safe to prevent any login issues.",
                saveAccountTips:
                                        "Do not share them with others to avoid any losses!",
            },
            forgotPassword:    {
                title:        "Forgot Password",
                step1:        "Identity Verification",
                step2:        "Change Password",
                step3:        "Done",
                newPassword:  "New Password",
                backHome:     "Back to Home",
                loginNow:     "Login Now",
                resetSuccess: "Your password has been modified successfully",
                systembusy:   "System busy, please try again later",
            },
            task:              {
                reward:                  {
                    8:       "First Deposit to receive {reward}",
                    11:      "Download to claim {reward}",
                    17:      "The first deposit to receive {reward}",
                    default: "Claim {reward}",
                },
                amount:                  "Bonus",
                activity:                "Active level",
                title:                   "Mission",
                activityRate:            "Current Active Level",
                activityBoxTip:
                                         "Contains a reward of {reward} bonus <br/>audit <span>{x}</span> required in order to Withdrawal",
                activityBoxReceiveTip:
                                         "The {reward} bonus can be withdrawn only after <span>{x}</span> times of turnover are satisfied",
                reactiveTime:            "Active level Reset Time: ",
                reactiveTime1:           "{h}:{m}:{s}",
                reactiveTime2:           "{d}day(s) {h}:{m}:{s}",
                newbieBenefit:           "Newplayer Bonus",
                daily:                   "Daily Mission",
                weekly:                  "Weekly Mission",
                threeDays:               "Three-day Missions",
                arcane:                  "Mystery Mission",
                collectAll:              "Claim All",
                applyAll:                "One-click application",
                receiveDiscount:         "Apply For Offers",
                receiveRecord:           "Application Record",
                receiveDiscountRequired: "Enter the answer",
                receiveDiscountSuccess:  "Application submitted successfully!",
                collect:                 "Claim",
                processing:              "Ongoing",
                forward:                 "Go",
                applyAward:              "Apply for rewards",
                collected:               "Claimed",
                waitReview:              "Pending Review",
                applied:                 "Applied",
                distributed:             "Distributed",
                noStart:                 "Not Started",
                expired:                 "Expired",
                cancel:                  "Cancelled",
                invalid:                 "Not activated yet",
                dayCount:                "DAY {x}",
                finishTreeDay:
                                         "Complete missions for 3 consecutive days to claim the final reward!",
                threeDaysTips:
                                         "After claiming, obtain <br /><span>{x}</span> bonus",
                threeDaysSuccessTips:    "<span>{x}</span> Bonus claimed",
                taskUnstart:             "Mission has not open yet",
                describe:                {
                    0:         "Long-term mission",
                    1:         "Long-term mission (reset daily at 00:00)",
                    2:         "Long-term mission (reset at 00:00 every Monday)",
                    3:         "Long-term mission (updated daily at 00:00)",
                    time:      "Mission time",
                    condition: "Mission conditions",
                    content:   "Mission content",
                    rule:      "Rules",
                },
                rulesDes:
                                         "The reward for this mission requires {times} valid bets for withdrawal",
                receiveSussessTip:       "Claimed successfully!",
                thisTimeNotShow:         "Do not show again this session",
                noMorePoptoday:          "Don't show again for today",
                neverPrompt:             "Never remind (check at the Mission Center)",
                neverPop:                "Never remind",
                rewardWaitApprove:       "Reward will be credited after audit",
                saveUrl:                 {
                    title:           "Save URL to Desktop Tutorial",
                    computer:        "PC end",
                    savingURLToGo:
                                     '1. Click the "Go" button for the Save URL mission',
                    clickTheMore:
                                     '2. Click the "{x}" icon in the top right corner of the browser',
                    createShortcuts: "Then click > More tools > Create shortcut",
                    clickToCreate:   "3. Click to create",
                    returnToTheDesktop:
                                     "4. Return to the desktop, click on the created desktop shortcut, open the website, and claim the reward",
                    chrome:          "Mobile Phone Google Browser",
                    clickMoreIcon:   '2. Click the "{x}" icon',
                    clickAdd:        'Then click "Add to home screen"',
                    selectAdd:       '3. Click Add, then select "Add"',
                    safari:          "Mobile Phone Safari Browser",
                    clickEnjoy:      "2. Click the share button at the bottom",
                    selectToAdd:     '3. Select "Add to home screen"',
                },
                timeNotArrived:          {
                    expiryTime: "{x} expired",
                    tomorrow:   "The reward can only be claimed the next day",
                    weekWhole:
                                "This mission reward can only be claimed next week",
                    monthWhole:
                                "This mission reward can only be claimed next month",
                    weekNext:   "Available to claim next week",
                    monthNext:  "Available to claim next month",
                },
                only:                    "Only limited to:",
                onlyGame:                "Only limited to the games",
                onlyRecharge:            "Only limited to deposit",
                contact:                 "Contact Information",
                close:                   "Close",
                rechargeAtLeastOnce:
                                         "You need to complete 1 recharge to receive it!",
            },
            event:             {
                payAfter:       "Summary of recharge activities",
                moreActivities: "More Events",
            },
            smartPay:          {
                edit:             "Modify",
                closeButtonTips:  "Close after {s}s",
                balance:          "Current balance",
                recommendAmount:  "Recommended deposit amount",
                recommendChannel: "Recommended payment channel",
                give:             "Bonus",
                payedAmount:      "Expected to arrive",
            },
            pay:               {
                retry:                        "Click to retry",
                bobiBalance:                  "WAVES Balance",
                buyBobi:                      "Purchase WAVES",
                title:                        "Deposit",
                title2:                       "Deposit",
                onceTips:                     "Click to switch, enter the amount/deposit amount",
                artificial:                   "Manual Customer Service",
                record:                       "Deposit Records",
                accountTotal:                 "Balance",
                payBtn:                       "Deposit Now",
                bindBtn:                      "Link Now",
                openNow:                      "Activate Now",
                openBobi:                     "Please activate WAVES wallet now",
                expand:                       "Expand",
                required:                     "Required ",
                putAway:                      "Fold",
                payMoney:                     "Deposit amount",
                giveTip:                      "Bonus event explanation",
                giveInfo:
                                              "Deposit according to the recommended amount to receive the corresponding bonus. The bonus amount stacks with the deposit bonus.",
                payScore:                     "Deposit amount",
                mark:                         "Reminder",
                placeholder:                  "Min {a}, Max {b}",
                suffix:                       "Last number {no}",
                lastNo:                       "**** {no}",
                firstNo:                      "{no} ****",
                channelLimit:                 "Lim. ({a}-{b})",
                give:                         "{num}",
                give2:                        "Bonus {num}%",
                give3:                        "+{num}%",
                copy:                         "Copy",
                chatPay:                      "Chat Deposit",
                accountNo:                    "Account: {no}",
                noBindUrlError:
                                              "Configuraten message error, please contact customer service!",
                artificialError:              "Deposit Customer Service configuration error",
                silverMerchantError:
                                              "Merchant deposit address configuration error",
                realName:                     "Payer's Real Name",
                realNameRequired:             "Real name cannot be empty",
                cpfRemark:
                                              "(Please enter correctly: the CPF of the payment account for this order, to credit normally)",
                cpfPlaceholder:               "Please enter CPF number",
                realNameRemark:
                                              "(Please provide the correct legal name associated with the payment account for the order to be credited successfully)",
                realNamePlaceholder:          "Please enter payer's real name",
                payEmpty1:                    "There is currently no payment method for you",
                payEmpty2:                    "Learn more",
                realNameFreezePlaceholder:
                                              "Please enter your real name, cannot be changed",
                walletAddress:                "Payer's wallet address",
                walletAddressRequired:
                                              "The payer's wallet address cannot be empty",
                walletAddressRemark:
                                              "(Please enter correct payer's wallet address for successful transaction)",
                walletAddressPlaceholder:
                                              "Please enter the payer's wallet address",
                personalInfo:                 "Payer verification information",
                personalInfoRemark:
                                              "(Please correctly fill in: the payer's information, so it will be credited to the account normally)",
                personalPhoneInfoPlaceholder:
                                              "Please enter the payer's phone number",
                personalEmailInfoPlaceholder: "Please enter the payer's email",
                personalPhoneInfoRequired:    "Phone number cannot be empty",
                personalEmailInfoRequired:    "Email cannot be empty",
                personalEmailInfoError:       "Incorrect email format input",
                walletRecommendMoneyNone:
                                              "No withdrawal form is available temporarily. Please select again",
                walletRecommendMoneyNone1:    "No big amount",
                walletRecommendMoneyNone2:    "No middle amount",
                walletRecommendMoneyNone3:    "No small amount",
                bindQianNengWalletSuccess:    "Link successful",
                qianNengTitle:                "Qian Neng Wallet balance",
                toBuyQianNeng:                "Purchase Balance",
                noWallet:                     "Each has a bonus between <span>01 and 3.3%</span>",
                errorMoneyMsg1:
                                              "The current wallet balance is insufficient, please make a purchase first or select another payment method!",
                errorMoneyMsg2:
                                              "The current USDT balance is insufficient, please select another payment method!",
                errorMoneyMsg3:
                                              "The current arbitrage balance is insufficient, please select another payment method!",
                notBindQianNeng:
                                              "You have not yet registered or linked a Qian Neng wallet",
                qianNengBindTip:
                                              "(Please fill in the correct information: real name and mobile number to link normally)",
                download:                     "NO Wallet APP",
                noWalletID:                   "Wallet account number",
                max:                          "Max",
                about:                        "About",
                exchangeRate:                 "Exchange rate",
                phonePlaceholder:             "Please enter phone number",
                walletTooltips:               "Reward {num}%",
                notBindUPAYWallet:
                                              "Automatically create a UPAY account with one click",
                bindUPAYBtn:                  "Use immediately",
                UPAYBalance:                  "UPAY balance",
                buyUPAYBtn:                   "Buy U Coins",
                bindUPAYSuccess:              "Registration and linking successful",
                total:                        "Total Deposit",
                rate:                         "Exchange rate",
                fee:                          "Fee",
                feeRate:                      "Processing Fee Rate",
                realFee:                      "Actual Processing Fee",
                deduction:                    "Reduction",
                realScore:                    "Actual Up Amount",
                feeErrorToast:
                                              "Withdrawal amount must be greater than the transaction fee ({x})",
                gameWalletBalance:            "No wallet balance",
                usdtBalance:                  "USDT balance",
                brickBalance:                 "NO arbitrage balance",
                notBindNoWallet:              "Already have an account, can log in and bind",
                notSettingPayPassword:
                                              "First time using? Just set the payment password",
                setting:                      "Configure Now",
                notBindBobiWallet:
                                              "You have not yet registered or linked a WAVES wallet",
                notBindGoubaoWallet:          "You have not linked the game wallet yet",
                goubaoBalance:                "Game wallet balance",
                goubaoStep2:
                                              "Step 2: Click to get and fill in the SMS verification code",
                goubaoStep1:
                                              "Step 1: Use the game wallet to scan this QR code for account linking:",
                goubaoQrCode:                 "Game wallet QR code",
                payPwdRequired:               "Payment password cannot be empty",
                goubaoPayPwdPlayceholder:
                                              "Please enter game wallet payment password",
                qrcodeExpired:                "The QR code has expired, please refresh",
                qrcodeTime:                   "QR code valid time",
                bindNoWalletBtnText:          "Register or Link Immediately",
                balanceInsufficientText:
                                              "Please choose to buy now or transfer directly.\n",
                balanceInsufficientText2:
                                              "The current <b>No wallet balance is insufficient</b>. Do you want to buy now?",
                thinkAgain:                   "Let me think",
                balanceCancelText:            "Buy Now",
                offlineTransferText:          "Transfer directly",
                rechargeSuccessful:           "Deposit Successful",
                refreshSuccessful:            "Refresh successful",
                refreshFailed:                "Refresh failed",
                allCurrency:                  "All currencies",
                combination:                  {
                    mobile:                  "Phone Number",
                    walletAddress:           "Wallet address",
                    email:                   "Email",
                    name:                    "Real Name",
                    cardNo:                  "Card Number",
                    cardNoRequired:          "The card number cannot be empty",
                    cardNoPlaceholder:       "Please enter the card number",
                    cardPass:                "Serial Number",
                    cardPassRequired:        "The serial number cannot be empty",
                    cardPassPlaceholder:     "Please enter the serial number",
                    networkMerch:            "Provider",
                    networkMerchRequired:    "The provider cannot be empty",
                    networkMerchPlaceholder: "Please select the provider",
                    onlineBank:              "Select Bank",
                    onlineBankRequired:      "Bank cannot be empty",
                    onlineBankPlaceholder:   "Please select a bank",
                },
                table:                        {
                    time:    "Time",
                    method:  "Deposit Method",
                    amount:  "Amount",
                    status:  "Status",
                    action:  "Action",
                    details: "Details",
                },
                details:                      {
                    title:                     "Deposit Details",
                    name:                      "Bank Account Name",
                    no:                        "Bank Account",
                    bankName:                  "Bank Name",
                    qrCode:                    "Payment QR",
                    channel:                   "Deposit channels",
                    method:                    "Deposit Method",
                    transaction:               "Transaction Type",
                    createTime:                "Creation Time",
                    payedTime:                 "Transaction Time",
                    transferCertificate:       "Transfer voucher",
                    orderNo:                   "Order Number",
                    tripartiteWallet:          "Third-party wallet",
                    checkWallet:               "Check Third-party wallet",
                    frontRemark:               "Order Remarks",
                    senderName:                "Transferor's name",
                    placeHolder:               "Please enter the transferor's name",
                    senderName2:               "Payer's Real Name",
                    placeHolder2:              "Please enter payer's real name",
                    transferRemk:              "Transfer Remarks",
                    transferRemkTip:
                                               "(Please input transfer remarks for a quick balance update)",
                    transferRemkToast:         "Please enter transfer remarks",
                    transferRemarkZFBPayTip:   "(Optional)",
                    transferRemarkZFBPayPlaceholder:
                                               "Please enter transfer remarks, max. 255 words",
                    transferRemkPlaceholder:
                                               "Please enter the order number/token/TxID for deposit confirmation",
                    UTR:                       "UTR",
                    UTRPlaceholder:            "Please enter the UTR number",
                    UTRToast:                  "Please enter last {num} digits of UTR",
                    UTRPlaceholderLast4:       "Please enter the 4 digits after UTR",
                    UTRPlaceholderLast6:       "Please enter last 6 digits after UTR",
                    UTRRemark:
                                               "(Please enter the correct UTR number for quick updates to your limit)",
                    UTRRemarkLast4:
                                               "(Please enter the 4 digits after UTR, you can update the amount quickly)",
                    UTRRemarkLast6:
                                               "(Please enter last 6 digits of UTR, you can quickly update the limit for you)",
                    uploadTransferCertificate: "Upload transfer voucher",
                    uploadTransferCertificateRemark1:
                                               "Click the button on the left to upload the proof",
                    uploadTransferCertificateRemark2:
                                               "Image size cannot exceed 5MB",
                    uploadTransferCertificateRemark3:
                                               "Cannot be modified after uploading",
                    uploadTransferCertificateRemark4:
                                               "Click the re-upload button to upload the voucher image",
                    uploadTransferCertificateRequired:
                                               "Please upload transfer voucher",
                    cancelOrderContent1:
                                               "Are you sure you want to cancel the order?",
                    cancelOrderContent2:
                                               "After cancellation, recharge can be initiated again",
                    mark:                      "* For timely entry, please input correct payer’s name",
                    confirm:                   "I Have Transferred",
                    confirmRemark:
                                               "Submitted after {seconds}seconds. Come to transfer)",
                    auditBtn:                  "Under Review",
                    toPayBtn:                  "Continue to Deposit",
                    cancel:                    "Cancel an order",
                    usdtText:                  "USDT",
                    usdtType:                  "Currency",
                    proto:
                                               "Mainnet Protocol (the network channel, very important)",
                    address:                   "Payment Address",
                    usdtRemark1:
                                               "1. Please ensure the correct amount after deducting the withdrawal fee (the correct received amount) to complete the transaction; otherwise, the deposit won't be successful.",
                    usdtRemark2:
                                               "2. Confirm that the mainnet protocol you are transferring/depositing matches the mainnet protocol you have selected. Otherwise, the deposit/transfer won't be successful or will be refunded.",
                    usdtRemark3:
                                               "3. Only supports USDT. Please do not transfer other currencies to this address.",
                    cpf:                       "Recipient CPF",
                    cnpj:                      "Recipient CNPJ",
                    accountType:               "Account type",
                    noWalletRemark:
                                               "You still have pending orders. Please go to the wallet to confirm",
                    goToWallet:                "Go to wallet",
                    downloadQRCode:            "Save",
                    transferRemark:            "Transaction remark",
                    countdownText1:            "Please",
                    countdownText2:            "complete the deposit",
                },
                toast:                        {
                    moneyIncorrect: "Please enter the correct amount",
                    payFailed:      "Deposit failed. Please contact customer service",
                    payZFBError:
                                    "Alipay app type, please contact customer service to modify the channel type",
                    chargeMoneyQiannengError:
                                    "The deposit amount cannot exceed the Qian Neng balance",
                    chargeMoneyBobiError:
                                    "The deposit amount cannot exceed the WAVES balance",
                    chargeMoneyUPAYError:
                                    "The deposit amount cannot exceed the UPAY balance",
                },
                backHome:                     "Return to Lobby",
                payDomainErrorToast:
                                              "Please contact customer service to configure domain name",
            },
            payQRCode:         {
                tips1:
                                 "Please open your payment app and scan or copy and paste the QR code below to complete your purchase;",
                tips2:
                                 "This QR code can only be paid once. If you need to pay again, please go back and recharge;",
                tips3:
                                 "After the payment is successful, you can return to the game lobby and wait for points to be added!",
                effectiveTime:   "Effective time",
                copyQRCode:      "Copy QR code",
                orderStatus:     "Order Status",
                createdTime:     "Creation Time",
                orderNo:         "Order Number",
                qrCodeUrl:       "QR code address",
                merchantOrderNo: "Merchant order number",
                timeoutTips:     "The QR code has expired",
                timeoutTips2:
                                 "Please return to the lobby to place an order again. The current page will be closed in 10 seconds.",
                successTips:     "Successfully received",
                successTips2:
                                 "The system has automatically assigned you a score",
                successTips3:
                                 "The current page will be closed after 10 seconds",
                chose:           "Select",
                or:              "Or",
                close:           "Close now",
                checkOrder:      "check order",
                status:          {
                    wait:    "To be paid",
                    timeout: "Timed out",
                    success: "Payment successful",
                },
            },
            passModal:         {
                defaultTitle: "Enter PIN",
                withdraw:     "Withdrawal Password",
                remark:
                              "For your account safety, please enter the withdrawal password",
                forget:       "Forgot password?",
                ok:           "Confirm",
                next:         "Next",
            },
            forceChangePwd:    {
                title:       "Change Password",
                tips:        "This is your first login or your password has been reset. For account security, please change your password.",
                successTips: "Password modified successfully",
            },
            forceBindPhone:    {
                title:       "Link Mobile Number Upon Login",
                tips:        "Note: You can access the game after mobile verification",
                successTips: "Mobile phone linked successfully",
            },
            loginRegister:     {
                quickVerifySuccess:       "Successfully verified quickly!",
                quickLogin:               "Quick login",
                bindRegister:             "Binding registration",
                isPhone:                  "This is the mobile phone number",
                isAccount:                "This is the account",
                useSmsVerifyLogin:        "SMS Login",
                passwdLogin:              "Password Login",
                usePasswdLogin:           "Log in with password",
                usePasswdRegister:        "Register with password",
                passwdRegister:           "Password registration",
                useCaptchaLogin:          "Log in with verification code",
                captchaLogin:             "Verification Code Login",
                useCaptchaRegister:       "Register with verification code",
                captchaRegister:          "Verification code registration",
                emailCodeLogin:           "Email verification code login",
                smsCodeLogin:             "SMS verification code login",
                account:                  "Username",
                phoneNumber:              "Phone Number",
                email:                    "Email",
                input:                    "Please enter",
                support:                  "Support",
                onlySupport:              "Only supports",
                register:                 "Register",
                login:                    "Login",
                memberLogin:              "Member Login",
                memberRegister:           "Sign Up",
                nowEdit:                  "Modify now",
                cancelEdit:               "Cancel modification",
                confirmEdit:              "Confirm modification",
                editAccount:              "Modify account",
                saveImageAndRegister:     "Save the picture and register",
                rangeNumber:              "{range} digits",
                noteTitleAccountPassword: "Account and password prompts",
                noteTitleAccount:         "Account prompt",
                editTitleAccountPassword: "Change account and password",
                editTitleAccount:         "Modify account",
                rememberAcount:           "Remember me",
                rememberAcountPassword:   "Remember account password",
                registerSuccessTitle:
                                          "Congratulations on successful registration!",
                registerSuccessInfoAccountPassword:
                                          "The system has automatically generated the following account number and password for you. You can copy and save the text or pictures.",
                registerSuccessInfoAccount:
                                          "The system has automatically generated the following account for you. You can copy and save text or pictures.",
                registerSuccessSecurity:
                                          "And do not disclose it to others to avoid causing losses!",
                emailErrorMessage:        "Email format error, please re-enter",
                phoneErrorMessage:
                                          "Incorrect phone number format, please try again",
                phoneErrorMessage1:
                                          "If your mobile phone number starts with 0, you do not need to enter 0. Please re-enter it.",
                phoneErrorMessage2:
                                          "Area code {segment} supports {range} mobile phone numbers, please re-enter",
                accountErrorMessage:
                                          "Account format error,{range} characters, supports English/numbers",
                accountErrorMessage1:
                                          "The account does not support {range} pure digits, please re-enter",
                inputEffective:           "Please enter a valid",
                verifyAccountPasswordEqual:
                                          "The account number and password cannot be consistent",
                tipsAccountPasswordEqual:
                                          "The current password is the same as the account, please change it as soon as possible",
                unableCaptcha:            "Can't receive verification code?",
                usernameRegisterd:
                                          "Member account already exists, please re-enter",
                requiredRealName:         "Please enter your real name",
                copy:                     "One-click copy of account password",
                toSave:                   "Remember to save it!",
                contactCustomer:          "Customer Service",
            },
        },
        finance:                 {
            noUrlErrorTip:
                      "Download link configuration is incorrect. Please contact customer service for assistance!",
            getInfoError:
                      "We have not yet recieved the member status, please try again later",
            withdraw: {
                title:                     "Withdraw",
                tabs:                      {
                    withdraw:       "Apply for withdrawal",
                    withdrawRecord: "Withdrawal Records",
                    heaven_record:  "Betting tasks",
                    account:        "Receiving account",
                },
                maintain:                  {
                    closeTitle: "The withdrawal channel is closed",
                    closeTime:  "Close Time",
                    customer:   "Support",
                },
                checkDetails:              "Details",
                query:                     "Withdrawal query",
                schedule:                  "Withdrawal Progress",
                orderSell:                 "Sell ​​orders",
                total:                     "Balance",
                withdrawable:              "Withdrawable",
                yuebao:                    "Interest",
                remark:                    "Required bet amount of {num} to withdraw",
                betRequired:               "Required bet amount of",
                toWithdraw:                "to withdraw",
                btn1:                      "Regular Withdrawal",
                btn2:                      "No wallet withdrawal",
                btn3:                      "Crypto Withdrawal",
                account:                   "Withdrawal Account",
                addAccount:                "Add withdrawal account",
                accountPlaceholder:        "Please select the withdrawal account",
                accountRequired:           "Please add the Withdrawal account first",
                withdrawAmount:            "Withdrawal Amount",
                pleaseEnterWithdrawAmount: "Enter the withdrawal amount",
                withdrawAmountPlaceholder: "Min {a}, Max {b}",
                withdrawNoAccountPlaceholder:
                                           "Please select a withdrawal account first",
                selectAccountPlaceholder:
                                           "Please add a withdrawal account before withdrawing",
                all:                       "All",
                noRemark:                  "Each has a subsidy between <span>1 and 2.1%</span>",
                download:                  "NO Wallet APP",
                withdrawPass:              "Withdrawal Password",
                ok:                        "Confirm",
                give:                      "Subsidy {number}",
                give2:                     "Get allowances {number}%",
                yearRate:                  "Annualized{number}%",
                exchangeRate:              "Settlement exchange rate$1:{chart}{rate}",
                rate:                      "Exchange rate",
                bankLessNo:                "(****{no})",
                bankLess2No:               "****{no}",
                alipayTransfer:            "Alipay Transfer",
                wechatTransfer:            "WeChat Transfer",
                alipayQR:                  "Alipay QR code Scan",
                wechatQR:                  "WeChat QR code Scan",
                crypto:                    "Cryptocurrency",
                withdrawSuccess:
                                           "Withdrawal under review, please check account balance",
                show:                      "Show",
                hidden:                    "Hide",
                noAuditText:               "List of games that are not audited",
                noAuditListText:
                                           "List of games that are not included in auditing",
                noAuditSearchPlaceholder:
                                           "Please enter the name of the gaming platform or the game",
                noAuditExplain:
                                           "Explanation: The betting turnover of the following platforms or games does not count towards the audit amount; it is automatically excluded.",
                noAuditTip:
                                           "Reminder: If you have any questions, feel free to ask",
                freeNotice:                "No handling fee this time",
                remainingCount:            "({count} times left today)",
                freeNotice2:
                                           "This handling fee≈{num1}(estimated arrival{num2})",
                remainingValidBet:
                                           "You also need to effectively bet {num} to withdraw money!",
                verifyAmount:              "Please enter the correct amount",
                seeRules:                  "See rules for details",
                reward:                    "Reward",
                claim:                     "Claim",
                deposit:                   "Deposit",
                withdrawal:                "Withdraw",
                immediatelyWithdrawal:     "Transfer out immediately",
                earnInterest:              "Earn interest",
                placeBet:                  "Game Betting",
                goToBetting:               "Go to bet",
                viewTasks:                 "View tasks",
                forceWithdraw:             "Forced withdrawal",
                withdrawConfirmation:      "Confirm withdrawal",
                currentFee:                "This handling fee",
                currentReward:             "Current rewards",
                rebetForWithdrawal:
                                           "Bet {num} again to transfer all and withdraw money",
                canWithdraw:               "Can be transferred out",
                withdraw:                  "Withdraw",
                congratulations:
                                           "congratulations! All rewards can now be transferred out!",
                hasUncompletedWithdrawalOrder:
                                           "During the forced withdrawal application, the reward wallet has been frozen!",
                effectiveBet:              "Valid bets required",
                multiple:                  "Times",
                source:                    "Source",
                systemTask:                "System tasks",
                transferSuccess:           "Successfully transferred to your balance",
                bubbleText:                {
                    viewRules: "View rules",
                    bubbleText1:
                               "Forcing a withdrawal will lose the {num} reward",
                    bubbleText2:
                               "Required bet amount of {num} to withdraw,after that, it will be automatically upgraded to the mode where the principal can be directly withdrawn",
                    bubbleText3:
                               "There are currently rewards {num} that can be transferred out and then withdrawn.",
                },
                popUp:                     {
                    viewBettingTasks:  "View betting tasks",
                    importantReminder: {
                        importantReminder: "Important reminder",
                        paragraph1:        "According to relevant rules",
                        paragraph2:
                                           "You currently still need to bet {num} to withdraw money!",
                        paragraph3:        "There are currently {num1} rewards,",
                        paragraph4:
                                           ", if you force a withdrawal, you will not only lose the {num1} reward directly, but you may also be charged a handling fee. The handling fee rules are as follows:",
                        paragraph5:
                                           "After each recharge, you need to make a valid bet before you can withdraw money!",
                        paragraph6:        "Just bet {num2} to transfer out",
                    },
                    feeRules:          {
                        feeRules:      "Handling fee rules",
                        vipPrivileges: "VIP Privilege",
                        period:        ".",
                        paragraph1:    "Handling fee description:",
                        paragraph2:
                                       "In order to prevent malicious money laundering, applying for withdrawal needs to comply with the betting task rules or pay a small amount of third-party handling fees.",
                        paragraph3:    "Charging standards:",
                        paragraph4:
                                       "The handling fee is {num} of the amount of each withdrawal, and the minimum is 1. If it is less than 1, no handling fee will be charged.",
                        paragraph5:    "VIP can waive the handling fee:",
                        paragraph6:
                                       "You are currently a VIP {num1}, and you can enjoy {num2} times of free handling every day",
                        paragraph7:
                                       "The handling fee is a fixed fee of {num} for each withdrawal, and the minimum fee is 1/less than 1, no handling fee will be charged.",
                        seeDetails:    ", see details",
                    },
                },
                table:                     {
                    time:    "Time",
                    method:  "Withdrawal Method",
                    amount:  "Amount",
                    status:  "Status",
                    type:    "Type",
                    action:  "Action",
                    details: "Details",
                    total:   "Total Withdrawal",
                    heaven:  "Remaning audit",
                },
                details:                   {
                    title:        "Withdrawal Details",
                    method:       "Withdrawal Method",
                    createTime:   "Creation Time",
                    orderNo:      "Order Number",
                    transaction:  "Transaction Type",
                    address:      "Withdrawal Address",
                    net:          "Agreement",
                    usdtMethod:   "Withdrawal Method",
                    completeTime: "Complete Time",
                    usdt:         "Withdraw",
                    usdtText:     "USDT",
                    noWalletRemark:
                                  "You still have pending orders. Please go to the wallet to confirm",
                    goToWallet:   "Go to wallet",
                    cancelReason: "Cancellation reason",
                    rejectReason: "Reason for Rejection",
                    remark:       "Remark",
                },
                auditRecordDetails:        {
                    title:            "Betting task details",
                    type:             "Source type",
                    status:           "Task status",
                    amount:           "Get/Add points",
                    range:            "Require bet multiplier",
                    total:            "Requires audit",
                    needAudit:        "Required bet amount of",
                    audited:          "Already valid bet",
                    limitPlatform:    "Specified gaming platform",
                    limitGame:        "Game(s) Limited",
                    time:             "Creation Time",
                    activeName:       "Event Name",
                    taskName:         "mission name",
                    completedTime:    "Completion time",
                    calculationRules: "Calculation Rules",
                    sourceName:       "Source name",
                },
                accountReceivable:         {
                    bank:       "Withdrawal Account",
                    alipay:     "Alipay",
                    wechat:     "WeChat",
                    addDefault: "Set as Default",
                    disable:    "Disabled",
                    disabled:   "Deactivated",
                    qr:         "QR Scan",
                    crypto:     "Cryptocurrency",
                    transfer:   "Transfer",
                    add:        "Add",
                    cryptoName: "Cryptocurrency",
                    pix:        "PIX",
                    upi:        "UPI",
                    wallet:     "Third-party wallet",
                },
                add:                       {
                    account:                    "Account",
                    addBank:                    "Add Bank account",
                    addWechatDef:               "Add WeChat",
                    addWechat:                  "Add WeChat  Transfer",
                    addWechatQR:                "Add WeChat QR Scan",
                    addAlipayDef:               "Add Alipay",
                    addAlipay:                  "Add Alipay Transfer",
                    addAlipayQR:                "Add Alipay QR Scan",
                    bankNo:                     "Bank Card Number",
                    bankNoRemark:
                                                "(Please verify the card number carefully, or it will not be credited to the account)",
                    bankNoPlaceholder:          "Please enter bank account number",
                    bank:                       "Bank",
                    bankPlaceholder:            "Please select card issuing bank",
                    bankDetail:                 "Need to input the branch information!",
                    bankDetailPlaceholder:      "Enter the bank branch location",
                    branchBank:                 "Bank branch",
                    branchBankPlaceholder:      "Enter a 4-digit bank branch number",
                    bankDetailIFSC:             "IFSC cannot be empty",
                    remark:
                                                "Please carefully check the name and card number, otherwise it will not be credited",
                    remark2:
                                                "Please verify the account carefully, or it cannot be credited",
                    remark3:
                                                "Please upload the real-name receipt code, or it cannot be credited",
                    remark4:
                                                "Only the files in png, jpg, etc. are supported, with the size not exceeding 2M",
                    realName:                   "Real Name　",
                    realNamePlaceholder:        "Please enter name",
                    realNameUpperCaseRemark:
                                                "(Can only fill in uppercase English letters!)",
                    realNameRemark:
                                                "(Please verify the real name carefully, or it will not be credited to the account)",
                    alipayNo:                   "The Alipay account cannot be empty",
                    alipayNoPlaceholder:        "Phone Number or Email",
                    alipayNoRemark:
                                                "Alipay account is invalid, must be a mobile phone or email",
                    wechatNo:                   "WeChat account cannot be empty",
                    wechatNoPlaceholder:        "Enter WeChat Account",
                    wechatQR:                   "WeChat Payment QR Code",
                    alipayQR:                   "Alipay Payment QR Code",
                    transfer:                   "Account transfer",
                    receiveQR:                  "Receipt code",
                    ok:                         "Confirm",
                    nextStep:                   "Next",
                    retry:                      "Reupload",
                    setDefaultSuccess:          "Set successfully",
                    noWalletNobind:             "Not yet Linked",
                    nowalletId:                 "Wallet Account:",
                    noWalletBalance:            "NO wallet balance:",
                    delSuccess:                 "Deleted successfully",
                    addSuccess:                 "Added successfully",
                    alreadyBindCPF:             "The account is already linked",
                    addWalletAccount:           "Add a third party wallet account",
                    addCryptoAccount:           "Add crypto account",
                    addCrypto:                  "Add Crypto ",
                    addWallet:                  "Add a third party wallet",
                    addWalletAddress:           "Add the wallet address",
                    wallet:                     "Wallet",
                    cionType:                   "Currency",
                    protocol:                   "Agreement",
                    address:                    "Address cannot be empty",
                    walletAddressPlaceholder:   "Please enter wallet address",
                    addressLabel:
                                                "Please confirm that the currency, protocol and address are consistent, or else the amount will not be credited",
                    addressPlaceholder:
                                                "Please enter the Cryptocurrency address. ",
                    addressRemark:
                                                "Please confirm that the currency, protocol and address are consistent, or else the amount will not be credited",
                    cryptoRemark:
                                                "Tip: USDT only supports withdrawal addresses using the TRC20 protocol.",
                    addPIX:                     "Add PIX",
                    addPIXAccount:              "Add PIX Account",
                    accountType:                "Account type",
                    PIXAccount:                 "PIX Account",
                    PIXAccountPlaceholder:      "Please enter PIX account",
                    CNPJAccountPlaceholder:     "Please enter CNPJ number",
                    CPF:                        "CPF",
                    CPFPlaceholder:             "Please enter the 11-digit CPF number",
                    addUPI:                     "Add UPI",
                    addPayMaya:                 "Add PayMaya",
                    addGrabPay:                 "Add GrabPay",
                    PayMayaAccount:             "PayMaya Account",
                    GrabPayAccount:             "GrabPay Account",
                    PayMayaFieldPlaceholder:
                                                "Enter your PAYMAYA account starting with 0",
                    GrabPayFieldPlaceholder:    "Please enter your GrabPay Account",
                    UPIAccount:                 "UPI Account",
                    UPIAccountPlaceholder:      "Enter UPI account",
                    addMOMO:                    "Add MOMO",
                    MOMOAccount:                "MOMO Account Number",
                    MOMOAccountPlaceholder:
                                                "Please enter the MOMO account number",
                    addZALO:                    "Add ZALO",
                    ZALOAccount:                "ZALO Account Number",
                    ZALOAccountPlaceholder:
                                                "Please enter the ZALO account number",
                    addHuiW:                    "Add Huione",
                    huiWAccount:                "Huione Account Number　",
                    huiWAccountPlaceholder:
                                                "Please enter the account number of Huiwang wallet, beautiful number/ID/mobile phone number",
                    remark5:
                                                "Please check carefully, otherwise it will not be credited.",
                    fileNull:                   "Please upload the payment code",
                    addGCASH:                   "Add GCASH",
                    addCLABE:                   "Add CLABE",
                    addRocket:                  "Add Rocket",
                    addBKash:                   "Add BKash",
                    addSureCash:                "Add SureCash",
                    addNagad:                   "Add Nagad",
                    addUPAY:                    "Add UPAY",
                    addOVO:                     "Add OVO",
                    addDANA:                    "Add DANA",
                    addLINKAJA:                 "Add LINKAJA",
                    addGOPAY:                   "Add GOPAY",
                    addDOKU:                    "Add DOKU",
                    addGoTymeBank:              "Add GoTyme Bank",
                    addPaga:                    "Add Paga",
                    addFlutterwave:             "Add Flutterwave",
                    addPaystack:                "Add Paystack",
                    addInterswitch:             "Add Interswitch",
                    addOpay:                    "Add Opay",
                    addCellulant:               "Add Cellulant",
                    addKongapay:                "Add Kongapay",
                    addLINEPay:                 "Add LINEPay",
                    addApplePay:                "Add LINEPay",
                    gcashField:                 "GCASH account",
                    CLABEField:                 "CLABE Account",
                    CLABEValidate:
                                                "Your account is incorrect, are you sure to proceed with submission?",
                    RocketField:                "Rocket account",
                    bKashField:                 "bKash account",
                    SureCashField:              "SureCash account",
                    NagadField:                 "Nagad account",
                    UPAYField:                  "UPAY account",
                    OVOField:                   "OVO account",
                    DANAField:                  "DANA account",
                    LINKAJAField:               "LINKAJA account",
                    GOPAYField:                 "GOPAY account",
                    DOKUField:                  "DOKU account",
                    GoTymeBankField:            "GoTyme Bank account",
                    PagaField:                  "Paga Account",
                    FlutterwaveField:           "Flutterwave Account",
                    PaystackField:              "Paystack Account",
                    InterswitchField:           "Interswitch account",
                    OpayField:                  "Opay account",
                    CellulantField:             "Cellulant account",
                    KongapayField:              "Kongapay account",
                    LINEPayField:               "LINEPay account",
                    ApplePayField:              "LINEPay account",
                    RocketFieldPlaceholder:     "Enter Rocket account",
                    bKashFieldPlaceholder:      "Enter bKash account",
                    SureCashFieldPlaceholder:   "Enter SureCash account",
                    NagadFieldPlaceholder:      "Enter Nagad account",
                    UPAYFieldPlaceholder:       "Enter UPAY account",
                    OVOFieldPlaceholder:        "Enter OVO account",
                    DANAFieldPlaceholder:       "Enter DANA account",
                    DANARemark:                 "Please enter a phone number starting with 0",
                    LINKAJAFieldPlaceholder:    "Enter LINKAJA account",
                    GOPAYFieldPlaceholder:      "Enter GOPAY account",
                    DOKUFieldPlaceholder:       "Enter DOKU account",
                    PagaFieldPlaceholder:       "Please enter Paga account",
                    FlutterwaveFieldPlaceholder:
                                                "Please enter Flutterwave account",
                    PaystackFieldPlaceholder:   "Please enter Paystack account",
                    InterswitchFieldPlaceholder:
                                                "Please enter the Interswitch account",
                    OpayFieldPlaceholder:       "Please enter the Opay account",
                    CellulantFieldPlaceholder:
                                                "Please enter the Cellulant account",
                    KongapayFieldPlaceholder:   "Enter Kongapay account",
                    LINEPayFieldPlaceholder:    "Enter LINEPay account",
                    ApplePayFieldPlaceholder:   "Enter the LINEPay account",
                    gcashFieldPlaceholder:      "Enter GCASH account starting with 0",
                    GoTymeBankFieldPlaceholder: "",
                    inputLengthExcessed:        "Enter a space to continue typing",
                    inputMaxLengthExcessed:
                                                "Up to {leng} characters are supported for continuous input. If it exceeds {leng} characters, please use spaces to separate them",
                    addAccount:                 "Add {name}",
                    accountName:                "{name} account",
                    accountPlaceholder:         "Please enter {name} account number",
                    IBANCode:                   "IBAN code",
                    IBANCodePlaceholder:        "Please enter a 26-digit IBAN code",
                    IBANCodeVerify:
                                                "Must start with TR and have a total of 26 characters",
                    companyNamePlaceholder:     "Please enter the company name",
                    companyName:                "Company name",
                    accountFormatError:         "Account format error",
                    digitOnly:                  "Only support pure numbers",
                },
                warning:                   {
                    needRecord:
                                         "Audit is incompleted, withdrawal is not available",
                    withdrawAmountMinError:
                                         "Your Withdrawal amount must be greater than or equal to {a}",
                    withdrawAmountMaxError:
                                         "Your Withdrawal amount must be less than or equal to {a}",
                    cpf:                 "It must be 11 -bit pure numbers",
                    email:               "Please enter a valid email format",
                    evp:                 "Enter 36 characters",
                    disableAccount:      "The withdrawal account has been deactivated",
                    phone:               "Please enter {range} digits",
                    gcashPhone:
                                         "Please enter the 11-digit number starting with 0",
                    cpfUnanimousPix:     "CPF type PIX account must match CPF",
                    pixPhoneNoStartWithZero:
                                         "The beginning is not 0 and must be 11 digits of pure numbers",
                    pureDigital:         "Must be {range} digits of pure numbers",
                    pixNonEmpty:         "You have an uncompleted PIX account!",
                    cpfNonEmpty:         "You have an uncompleted CPF!",
                    companyNameNonEmpty: "Please enter the company name!",
                    bankNoNonEmpty:      "Need to enter bank account number!",
                    bankNonEmpty:        "Please choose a bank!",
                    branchBankNonEmpty:
                                         "Need to enter a 4-digit bank branch number!",
                    realNameNonEmpty:    "Please enter your real name!",
                    bankIFSC:            "Please enter the correct IFSC code",
                    evpVerify:           "Please enter the correct EVP format",
                },
                withdrawText:              {
                    alipay:         "Withdraw to Alipay",
                    wechat:         "Withdraw to WeChat",
                    bank:           "Withdrawing to a bank card",
                    crypto:         "Withdrawing to digital currency",
                    wallet:         "Withdraw to third-party wallet",
                    huiW:           "Withdraw to Huiwang",
                    NoWallet:       "Request to NO wallet",
                    withdrawCashTo: "Withdraw to {to}",
                },
            },
            center:   {
                wallet: {
                    title:                  "Retrieve the balance",
                    balance:                "Total Account Balance",
                    recycle:                "One click to retrieve",
                    searchPlatform:         "Platform Search",
                    refreshGoldSuccessText: "All balances have been retrieved",
                    refreshGoldFailText:
                                            "There is currently no balance to withdraw",
                    prompt:
                                            "Only integer balance will be pulled back to main wallet",
                    updateError:            "Fail",
                    pending:                "Loading",
                    findBackTips:
                                            "You can only retrieve the integer multiple of the balance (that is, no decimal point)",
                },
            },
        },
        aside:                   {
            music:   {
                modalConfigTitle:   "Music",
                modalLibraryTitle:  "List",
                xhbf:               "Cycle",
                sj:                 "Shuffle",
                dqxh:               "Repeat",
                qk:                 "List",
                tabSys:             "System Music",
                tabMy:              "My music",
                toastMessage:       "Last song, which cannot be deleted",
                toastDeleteTitle:   "Reminder",
                toastDeleteMessage: "Delete?",
                toastCancel:        "Cancel",
                toastOk:            "Confirm",
                noMoreMusic:        "No songs to play currently",
                noMoreNewMusic:     "No latest songs available",
                downloaded:         "Downloaded",
                downloadError:      "Music resource download failed!",
            },
            jackpot: {
                title:      "JACKPOT",
                record:     "Records",
                modelTitle: "Jackpot Records",
                table:      {
                    time:         "Time",
                    nickname:     "Player Nickname",
                    gameName:     "Winning Game",
                    winningPrize: "Winnings",
                },
            },
            ping:    {
                network:            "Network",
                networkLine:        "Line",
                currentNetworkLine: "Current Server",
                unavailable:        "The line is not available",
                selectLine:         "Select Server",
                checkLine:          "Line Detection",
            },
        },
        gameMenu:                {
            all:              "All",
            home:             "Home",
            loadingRecent:    "Recent",
            loadingCollect:   "Favorite",
            loadingDemo:      "Demo",
            menu0:            "Hot",
            menu1:            "Cards",
            menu2:            "Fishing",
            menu3:            "Slot",
            menu4:            "Live",
            menu5:            "Sports",
            menu6:            "Cockfight",
            menu7:            "E-Sports",
            menu8:            "Lottery",
            menu9:            "Club",
            menu10:           "Friends",
            menu11:           "Blockchain",
            menu20:           "Demo",
            allmenu1:         "All Cards",
            allmenu2:         "All Fishing",
            allmenu3:         "All Slot",
            allmenu11:        "All Blockchains",
            allmenu20:        "Demo",
            menuClub:         "Club",
            menuFriend:       "Friends",
            tipsClub:         " Can only be used on the APP side!",
            toDownloadApp:    "Download Now",
            NotToDownloadApp: "Download Later",
        },
        stakeSystem:             {
            discount:      "Offer Center",
            fastShare:     "Quick Share",
            onlineSupport: "Online support",
            support:       "Support Center",
        },
        system:                  {
            activity:     "Event",
            task:         "Mission",
            rebate:       "Rebate",
            award:        "Rewards",
            vip:          "VIP",
            promote:      "Agent",
            yuebao:       "Interest",
            service:      "Support Center",
            service2:     "Support",
            report:       "Report",
            more:         "More",
            vip2:         "Superior Club",
            vip3:         "Royal Benefit",
            interest:     "Interest",
            rechargeFund: "Provident Fund",
        },
        game:                    {
            nav:                     {
                fastNews:             "",
                clickLogin:           "Login",
                signUp:               "Register now",
                or:                   "Or",
                loginBalance:         "View balance after logging in",
                Interest:             "Interest",
                yuebao:               "Interest",
                vip:                  "VIP",
                promote:              "Agent",
                active:               "Event",
                task:                 "Mission",
                backwater:            "Rebate",
                prizeFeedback:        "Reward Feedback",
                security:             "Security Center",
                settings:             "Profile",
                accountDetails:       "Statement",
                bettingRecord:        "Bet Records",
                personalStatement:    "Report",
                award:                "Reward",
                withdrawalManagement: "Withdraw",
                network:              "Network Status",
                language:             "Language",
                wallet:               "Wallet",
                audit:                "Betting tasks",
                facebook:             "Facebook",
                google:               "Google",
                line:                 "Line",
                wechat:               "WeChat",
                qq:                   "QQ",
                fastNewsLabel:        "News",
                discount:             "Event",
                waitingCollect:       "Reward",
                receiveRecord:        "History",
                inviteFriends:        "Invite friends",
            },
            home:                    "Lobby",
            lobby:                   "Hall",
            recent:                  "Recent",
            collect:                 "My Favorite",
            demo:                    "Play Demo",
            liveLine:                "Channel",
            openDemoGameTip:
                                     "You are entering a trial experience; withdrawals are not available after making a profit",
            demoGameToRegTip:
                                     "Time waits for no one, sign up now, no need to wait to win big prizes! Opportunity knocks only once!",
            all:                     "All",
            hot:                     "Hot",
            allHot:                  "Hot",
            searchResults:           "Search",
            hotCommend:              "Featured",
            menue:                   "Games",
            egame:                   "E-Sports",
            loadingAll:              "All",
            empty:                   "No games available",
            maintain:                "Under Maint",
            gameMaintainText:        "Game is under maintenance",
            platformClose:
                                     "The gaming platform has been closed. Please try again later.",
            fetchingPrize:           "Balance are loading, please try again later.",
            goldInadequate:
                                     "Your balance is insufficient {gold}, Please deposit before playing the game.",
            quotaRatioPrompt:
                                     "Your current balance is {balance}, which will be brought in according to the ratio of {proportion}. After being brought in, the displayed amount will be: {amount}",
            enterGame:               "Enter Game",
            scrollDown:              "Scroll down",
            loadingInfo:
                                     "Currently displaying {count} {type} games out of {total}",
            loadingHot:              "Hot",
            loadingRecent:           "Recent",
            loadingCollect:          "Favorites",
            loadingDemo:             "Demo",
            loadingMore:             "Load More",
            loadinged:               "Loading",
            searchHistory:           "History",
            searchGame:              "Search",
            search:                  "Search",
            searchName:              "Please enter league name",
            deleteAll:               "Delete All",
            recommend:               "BEST",
            takeOutScore:            "Update Score",
            gameContinueTips:        "You have unfinished games; please continue",
            confirmExitGame:
                                     "To ensure the synchronization of your funds, please make sure to return to the lobby after the game ends",
            returnLobby:             "Return to lobby",
            enterCurrenctGame:       "Enter the current game",
            continueGame:            "Continue playing",
            continueGameTips:
                                     "The last game is in progress. Do you want to continue the game?",
            nopermission:            "This account has no game permissions",
            needBindBankCard:
                                     "For a better game experience, you need to link your withdrawal method first",
            needFirsstRecharge:
                                     "For your game experience, you need to complete the first recharge first",
            needDownloadApp:
                                     "Download and use the app to continue playing games!",
            otherLiveList:           "Other live event broadcasts",
            liveEnded:               "The current live broadcast has ended",
            noLiveForNow:            "No live events available",
            olympicGameLiveWillStart:
                                     "The Olympics have started, click me to switch",
            europeanSuperLeagueLiveWillStart:
                                     "The European Super League has started, click me to switch",
            olympicGameLive:         "Olympics Live",
            europeanSuperLeagueLive: "European Super League Live",
            highLight:               "Highlights Replay",
            noHighLight:
                                     "No highlight replay available, please return to watch the live broadcast",
            drag:                    "DRAG",
            liveError:
                                     "The current connection is unstable. Please switch to other lines",
            refresh:                 "Refresh",
            fullScreen:              "Full Screen",
            update:                  "UPDATE",
            new:                     "NEW",
            suggest:                 "BEST",
            exitFullScreen:          "Exit Full Screen",
            crownGold:               {
                title:          "Crown Sports",
                desc:           "HOT top events, enjoy the event storm",
                imgTitle1:      "Stephen Curry",
                imgDesc1:       "Exclusive Image Ambassador for Asian Brands",
                imgTitle2:      "Argentina National Team:",
                imgDesc2:       "Official Exclusive Partner for Asia",
                imgTitle3:      "FC Barcelona",
                imgTitle4:      "Manchester United Football Club",
                bodyLeft1:      "Women's World Cup",
                bodyDesc1:      "Official Betting Platform",
                bodyLeft2:      "Basketball World Cup",
                bodyLeft3:      "Asian Cup",
                bodyLeft4:      "European Cup",
                bodyLeft5:      "Copa America",
                bodyLeft6:      "Paris Olympics",
                bodyRightTitle: "Exciting Events: At Your Fingertips",
                bodyRightDesc:
                                "Industry's Highest Odds Offered, thousands of sporting events daily, covering sports and leagues worldwide. Provides a variety of betting options including handicap, over/under, half/full-time, correct score, odd/even goals, parlay betting, and live streaming, allowing you to easily seize the best betting opportunities!",
                football:       "Football",
                basketball:     "Basketball",
                tennis:         "Tennis",
                snuock:         "Snooker",
                volleyball:     "Volleyball",
                f1:             "Racing",
                baseball:       "Baseball",
                football2:      "Rugby",
                copyRight:      "Galaxy Group. All rights reserved.",
                logosBjl:       "Baccarat",
                logosLp:        "Roulette",
                logosSb:        "Sic Bo",
                logosLh:        "Dragon Tiger",
                logosNn:        "Bull Bull",
                logosZjh:       "Win Three Cards",
                logosSg:        "San Gong",
                logosSsc:       "Timely",
                logosLhc:       "Mark Six Lottery",
                logosSyxw:      "11 Choose 5",
                logosBjsc:      "Beijing car",
                logosXync:      "Lucky farm",
                logosJssc:      "Speed ​​racing",
                legend:         "League of Legends",
                kingHonor:      "King Glory",
                stoneLegend:    "Hearthstone",
                watchPioneer:   "Overwatch",
                logosGjzl:      "FIFA",
                crownVIP:       "VIP Royal Benefit",
                gamingDesc:
                                "Full coverage of the world's top events, providing professional odds and match result data. Offering over 100 esports events daily, 50+ new esports games, and 10+ live esports matches with the best odds in the industry. The stunning visual interface and efficient user experience make it easy for you to get started.",
                lotteryDesc:
                                "With years of focus on the lottery gaming industry, we pioneered blockchain lottery and offer numerous exclusive innovative gaming methods. Our user-friendly gaming interface includes official lotteries like the 24/7 official lottery, 11/5, PK10, PC egg, and official Six-Lottery, allowing you to play to your heart's content without leaving your home!",
                porkerDesc:
                                "Offering a comprehensive and diverse range of card games, including Zhuang Niu Niu, Baccarat, Frying Golden Flowers, Landlords, Two Mahjong, Dragon and Tiger Fighting, etc., Play against friends and other players in real battles, allowing you to easily enjoy card games!",
                realDesc:
                                "The most creative and game-oriented platform within the industry provides professional technical support, top-notch stable equipment, a team of hundreds of professionally trained dealers, and a simulated casino environment. All to ensure that players can fully enjoy the game and immersive atmosphere!\n",
                sportsDesc:
                                "With diverse gameplay and top-quality experiences, we cover a wide range of sports events and a hundred ways to play in a single match. The operatens are simple, catering to the experience of both new and veteran users. Special events allow you to experience passion through unique gameplay features like special bets, 15-minute handicaps, over/under bets, single wins, and more, ensuring you enjoy the thrill at all times.\n\n",
            },
            galaxyGold:              {
                searchTip:      "Enter the game name",
                backTop:        "Back to top",
                elecGame:       "Electronic Games",
                elecGameDesc:
                                "A classic slot machine platform with tens of thousands of slot games, allowing you to play freely. Upholding the spirit of craftsmanship, we continuously innovate to provide players with the ultimate gaming experience. The easily accessible multi-million cumulative jackpot is ready to be triggered by you in an instant!",
                seeMore:        "View More",
                elecTips:       "Embark on the top-tier entertainment here",
                porkerFishTips: "Experience the dazzling galaxy in perfection",
                porker:         "Card Games",
                porkerTips:
                                "Banker Bull Bull or Dragon Tiger, choose from various card games and showcase how broad-minded you are. Competitive entertainment that challenges your mind where you can play competitive matches with friends – those games can change lives!",
                fishing:        "Fishing Games",
                fishingTips:
                                "Fishing games are coming in full force, with many popular platforms, daily gifts, the God of Wealth Fishing, Thousand Cannon Fishing - Fish ponds are overflowing, waiting for you to play!\r\n",
                game4Tips:      "Every experience is a form of enjoyment",
                realPerson:     "Live Casino",
                electric:       "E-Sports",
                lottery:        "Lottery Games",
                sports:         "Sports Games",
                realPersonTips:
                                "The most creative and entertaining platform in the industry, professional technical support, top-notch stable equipment, hundreds of professionally trained dealers, and a simulated casino environment that allows players to enjoy the fun.",
                electricTips:
                                "Explore a diverse selection of over a hundred lottery games tailored for your enjoyment. We are dedicated to crafting a top-tier entertainment environment for players, ensuring a secure and delightful gaming experience, all while maintaining a commitment to fair and equitable lottery outcomes.",
                lotteryTips:
                                "The most professional esports platform in Asia, designed exclusively for enthusiasts of various esports games. It offers the highest quality visual interface and an efficient betting experience, bringing together the latest and hottest events from across the web for you to choose from.",
                sportsTips:
                                "Offering the industry's highest odds, covering the most popular events worldwide, diverse predictions, and animated and video livestreams, making it easy for you to experience entertainment betting and find joy in the world of sports.",
                appTitle:       "Galaxy Group Official App",
                appSubTitle:    "A moment and thought, enjoying the time",
                appDesc:
                                "The mobile betting platform caters to players worldwide, offering nearly ten thousand slot games, slot machines, baccarat, lottery, and sports betting. It provides online deposits and withdrawals with one-click operations. By employing 3D real-time calculations to create lifelike scenes combined with 3D imagery, it forms a comprehensive cross-system entertainment platform. Integrated synchronized accounts and data transmission allow for uninterrupted enjoyment anytime, anywhere. With every detail meticulously designed, entertainment is at your fingertips, effortlessly accessible, and wealth is just a click away.",
                downloadTip:    "Scan the QR code on your mobile to download",
                downloadH5:     "Access without download",
                aboutTitle:     "Indulge in exceptional treatment and experience",
                aboutP1:
                                'Galaxy Entertainment Group ("Galaxy" or the "Group") is a globally renowned resort, hotel, and gaming enterprise. It is listed on the Hong Kong Stock Exchange and is a constituent of the Hang Seng Index.',
                aboutP2:
                                'As one of the three operators in Macau to have been granted a gambling contract, Galaxy has consistently gained recognition for its pioneering, innovative, and award-winning projects, products, and services. Coupled with the service philosophy of "Defying Limits, Connecting Asia," the Group\'s performance remains exemplary and leads the Macau market.',
                professional:   "Most Professional",
                professionalTips:
                                "The world's most comprehensive gaming platform, with a professional team ensuring your journey, offers you a wide range of electronic games for your enjoyment. Choose from various entertainment options like cards, fishing, and more, allowing you a perfect gaming experience.",
                safety:         "Most Secure",
                safetyTips:
                                "Exclusively developed, employing 128-bit encryption technology and stringent security management systems, customer funds are comprehensively safeguarded, allowing you to fully enjoy entertainment, event betting, and more, all without any worries.",
                reliable:       "Most Reliable",
                reliableTips:
                                "The platform utilizes a cutting-edge, independently developed financial processing system that achieves lightning-fast deposits, withdrawals, and transfers. Our exclusive network optimization technology ensures a top-notch gaming experience by minimizing network latency to the maximum extent.",
                convenient:     "Most Convenient",
                convenientTips:
                                "Leading the market with our exceptional technologies, we have independently developed a complete set of terminal applications. You can enjoy seamless access through Web and H5 interfaces, and there's even the added convenience of iOS and Android native apps, allowing you to engage in entertainment and betting anytime, anywhere.",
                activities:     "Offers",
                vip:            "VIP Exclusive Benefits",
                appDownload:    "APP Download",
                exCenter:       "Exclusive Center",
                newerHelp:      "Beginner's help",
                service:        "Customer service",
                monitor:        "Regulatory Authorities",
                aboutUs:        "About Us",
                onlineService:  "Online Support",
                rewardFeedback: "Reward Feedback",
                privacy:        "Privacy Policy",
                aomen:          "Authorized by the Macau Government",
                feilvbin:       "Authorized by the Philippine government",
                jianpuzhai:     "Authorized by the Cambodian government",
                gesida:         "Authorized by the Costa Rican government",
                latestGame:     "Newest Games",
                latest:         "Newest",
                elecGameTips:   "Big Jackpot - Ready to Burst at Any Moment",
                allGame:        "All Games",
                hotGame:        "Hot Games",
                collectGame:    "Game Collection",
                fishingDesc:    "Full of Big Wins - Bursts with One Tap",
                porkerDesc:     "Thousands Online - Intense PK",
                sportsDesc:     "Top-Level Events  Enjoy the thrill",
                lotteryDesc:    "Hot Lotteries  Unlimited choices",
                electricDesc:   "Comprehensive  Coverage of Top Events",
                realDesc:       "Attractive Dealers  Live dealing",
                gamingTitle:
                                "A vast selection of hot games for unlimited enjoyment",
                legend:         "League of Legends",
                kingHonor:      "Honor of Kings",
                eatChicken:     "PlayerUnknown's Battlegrounds",
                stoneLegend:    "Hearthstone",
                starWar:        "StarCraft II",
                watchPioneer:   "Overwatch",
                legendMatch:    "League of Legends Championship Series",
                dota2Match:     "DOTA 2 International Tournament",
                csgoMatch:      "CS:GO Official Partnerships",
                lotteryLhc:     "Mark Six Lottery",
                lotteryCqssc:   "Chongqing Shishi Lottery",
                lotteryPk:      "PK10",
                lotteryKlsf:    "Happy 10",
                lotterySyxw:    "11 Choose 5",
                lotteryPls:     "3D Arrangement",
                lotteryKs:      "Fast 3",
                lotteryJssb:    "Jiangsu Sic Bo",
                lotteryFcsd:    "Fujian Lottery 3D",
                lotteryTitle:   "A wealth of hot games for unlimited enjoyment",
                onshowing:      "Being displayed",
                loadingUnit:    "Items",
                loadingContent: "Middle",
                loadMore:       "Load More",
            },
            hk:                      {
                receber_dz:      "Claim",
                convidar_dz:     "Invited {0} players",
                total_dz:        "Total: {0}",
                distancia_dz:    "Remaining: {0}",
                recebido_dz:     "Received: {0}",
                montante_dz:     "Lump Sum: {0}",
                hotTest_dz:      "HOTTEST",
                play_dz:         "Multi—play",
                playTipTitle_dz: "World's leading",
                pplayTipContent_dz:
                                 "{0} platform,\na variety of gameplay,\\ndaily gifts Yan",
            },
            playStep:                "Steps {n}",
            createAccount:           "Register Account",
            topUpNow:                "Deposit Now",
            topUpNowBtn:             "Deposit Now",
            withdrawNow:             "Withdraw Now",
            startPlay:               "Start playing!",
            playNow:                 "Play Now",
            mostPopular:             "Most popular",
            palyFishing:
                                     "A cutting-edge global fishing platform offering diverse gameplay options and daily rewards.",
            palySlot:
                                     "By combining the elements of luck and skillful spinning, this game delivers both fun and fascination to players.",
            palyLive:
                                     "The live games section serves as a hub for gambling and betting activities.",
        },
        selfoperatedGames:       {
            sports:        {
                soccer:                       "Football",
                basketball:                   "Basketball",
                football:                     "Football",
                iceHockey:                    "Ice hockey",
                tennis:                       "Tennis",
                volleyball:                   "Volleyball",
                billiards:                    "Snooker",
                baseball:                     "Baseball",
                badminton:                    "Badminton",
                golf:                         "Golf",
                motorsports:                  "Racing",
                swimming:                     "Swim",
                politics:                     "Politics",
                water:                        "Water polo",
                diving:                       "Diving",
                boxing:                       "Boxing",
                tableTennis:                  "Pingpong",
                handball:                     "Handball",
                darts:                        "Darts",
                rugby:                        "Rugby",
                judo:                         "Judo",
                ESports:                      "eSports",
                beachVolleyball:              "Beach Volleyball",
                finance:                      "Finance",
                lotto:                        "Lotto",
                unauthorized:                 "No permission",
                streamingTemporarilyClosed:   "Video temporarily closed",
                fetchStreamingFail:
                                              "Failed to get video, please try again later",
                systemIsUnderMaintenance:     "System under maintenance",
                stakeProblem:
                                              "The bet amount exceeds the maximum or less than the minimum value",
                max:                          "Max",
                priceClosed:                  "Handicap temporarily closed",
                searchResult:                 "Search",
                pointChanged:                 "Ball header has been updated",
                internalServerError:          "Server encountered an unexpected error",
                invalidParameterInput:
                                              "Parameter input is invalid or not supported",
                transactionIdNotFound:
                                              "Corresponding bet order number not found",
                cannotCashout:                "This bet slip can not be withdrawed",
                sellBackFail:                 "End in advance failed",
                eventClosedOrInvalidMarketID: "Match Closed",
                parlaySameEvent:              "",
                singleWalletFailed:
                                              "Under maintenance, please try again later!",
                parlayComboMaxPayoutOverLimit:
                                              "This parlay combination has reached the payout limit",
                parlayOddsLowerThanMinOdds:
                                              "The odds are less than the minimum limit of the string pass",
                overMaxPayoutPerEvent:
                                              "Single event exceeds the maximum payout amount",
                parlayNoComboAvailable:
                                              "Reduce the number of parlays as it exceeds the maximum allowed",
                parlayMaxBetLessThanMinBet:
                                              "Exceeds the maximum payout; please reduce the parlay quantity",
                eventClosed:                  "Match Closed",
                underParlayCount:
                                              "Below the minimum number of match for parlay",
                insufficientBalance:          "Insufficient balance",
                oddsChanged:                  "",
                marketClosed:                 "Handicap has been closed",
                overMaxBet:
                                              "The number of bets in a single event exceeds the maximum value",
                duplicatedBet:                "Duplicate bet",
                disabledParlayEvent:          "Match prohibits parlay",
                disabledParlayByNum:
                                              "The minimum number of cross-links has not been reached, and bets cannot be placed.",
                loadMore:                     "Click to load more",
                noMore:                       "No more",
                handicap:                     "Handicap",
                bigsmall:                     "Over/Under",
                winlosee:                     "wins loss",
                winloseequal:                 "wins draw lose",
                betting:                      "Bets",
                betTime:                      "Bet Time",
                inputMoney:                   "Please enter the principal",
                allMoney:                     "All balance",
                passLevel:                    "Parlay",
                parlayBetSimple:              "Parlay",
                canGet:                       "Potential Winnings",
                all:                          "All",
                wgSports:                     "WG Sports",
                rollingCount:                 "Rolling ball {count}",
                todayCount:                   "Today {count}",
                morningCount:                 "Early market {count}",
                passLevelCount:               "Parlay {count}",
                championCount:                "Champion {count}",
                focusOnCount:                 "Follow {count}",
                home:                         "Home",
                away:                         "Away",
                more:                         "More",
                small:                        "Small",
                big:                          "Big",
                homewin:                      "Home win",
                awaywin:                      "Away win",
                win:                          "win",
                foreven:                      "draw",
                lose:                         "lose",
                balance:                      "Balance",
                limitAmount:                  "Please enter stake (limit {minBet}~{maxBet})",
                onlyInteger:                  "Bet amount can only be an integer greater than 0",
                bettingAmount:                "Bet Amount",
                winLose:                      "Win/Loss",
                betted:                       "Bet",
                canGetMost:                   "Max. Winnings",
                cashout:                      "Early settlement",
                cashouting:                   "Early settlement processing",
                cashoutTips:
                                              "This bet will be settled immediately, and the final result related to this bet will not affect the amount returned to your account. The refund amount includes the stake",
                dealSellback:                 "Refund in progress",
                dealSellbackWaiting:          "Refund in progress",
                dealSellbackAccept:           "Return accepted",
                dealSellbackReject:           "Return rejected",
                newOffer:                     "Provide new withdrawal information",
                totalStake:                   "Total bet",
                sellBack:                     "Refund",
                autoReceiveNewPirce:          "Auto accept latest odds",
                autoReceiveHighPirce:         "Auto accept highest odds",
                notChangePirce:               "Never accept odd changes",
                parlayBet:                    "Parlay Bet",
                league:                       "Leagues",
                time:                         "Time",
                europe:                       "European Handicap",
                hongkong:                     "HongKong Handicap",
                noEventsYet:                  "No event",
                routerBack:                   "Back",
                clearAll:                     "Clear all",
                filterLeague:                 "Screen",
                searchEvent:                  "Search",
                hotSearch:                    "Hot searches",
                noLeague:                     "Sorry, no relevant events",
                showMoreBet:                  "Expand more stringing methods",
                showLessBet:                  "Close more parlay options",
                totalBet:                     "Total Bets",
                cancle:                       "Cancel",
                selectAll:                    "All",
                cancleAll:                    "Deselect all",
                confirm:                      "Confirm",
                checkLeague:                  "Please select event",
                betSuccess:                   "Bet Successful",
                betFalid:                     "Bet Failed",
                parlayTip:                    "Maximum parlay count",
                parlayTip2:
                                              "There is a duplicate match. We've switched to a new match for you.",
                disableParlay:                "Current match cannot be parlayed",
                addParlaySuccess:             "Successfully added to parlay!",
                clearBetType:                 "Delete the string success!",
                marketParlayError:
                                              "Match exception, please select the match again",
                seeBetRecord:                 "View records",
                stakeRecord:                  "Bet Records",
                continueBetting:              "Continue betting",
                betRecord:                    "Bet record",
                backToHome:                   "Back to homepage",
                sports:                       "Sports",
                settled:                      "Settled",
                unSettled:                    "Unsettled",
                settledAndNot:                "All Records",
                betAmount:                    "Bet amount",
                transId:                      "Order number",
                betContent:                   "Bet content",
                markets:                      "Handicap",
                bettingStatus:                "Status",
                winOrLose:                    "Lose/win (excluding principal)",
                tranId:                       "Order number",
                copy:                         "Copy",
                screenShot:                   "Screenshot",
                share:                        "Share",
                screenShotFalid:              "Screenshot failed, please try again",
                acceptTikets:                 "Bet Successful",
                betConfirming:                "Confirmation pending",
                void:                         "Void",
                refund:                       "Refund",
                reject:                       "Cancelled",
                draw:                         "Draw",
                halfWon:                      "Semi-win",
                halfLose:                     "Semi-lost",
                noData:                       "No screenshot data",
                PointDisappear:               "The match has been removed",
                OddsChanged:                  "The odds have been updated",
                OddsAdjust:                   "Odds adjustment in progress",
                OddsError:                    "Odds Error",
                GameStatusChanged:            "Game status change",
                GameScoreChanged:             "Game scores change",
                GamePointChanged:             "Game match change",
                sortPlaceholder:              "Sort",
                oddPlaceholder:               "Odd",
                processing:                   "",
                firstHalf:                    "First half",
                secondHalf:                   "Second half",
                halftimeBreak:                "Halftime",
                accessTitle:                  "Restricted access",
                accessContent:
                                              "The region where your IP is located is not within our service range. Please change your IP address to another country. We apologize for any inconvenience this may cause you.",
                liveStreaming:                "Live video",
                viewData:                     "View data",
                yellowCard:                   "Yellow card",
                redCard:                      "Red card",
            },
            country:       {
                China:     "China",
                Russia:    "Russia",
                Argentina: "Argentina",
                Ukraine:   "Ukraine",
                Mexico:    "Mexico",
            },
            markets:       {
                special:                  "Special bet",
                handicap:                 "Handicap",
                handicapUp:               "First half - Handicap",
                handicapDown:             "In second half, Handicap",
                bigsmall:                 "Over/Under",
                bigsmallUp:               "First Half - Over/Under",
                bigsmallDown:             "Second hald, Over/Under",
                winDrawLose:              "Result",
                winDrawLoseUp:            "Result - First half",
                winDrawLoseDown:          "Result - Second half",
                score:                    "Correct Score",
                scoreUp:                  "First Half-Bodan",
                scoreDown:                "Second half - wave courage",
                HalfAndFullCorrectScore:  "First half - full time excitement",
                HalfAndFullCorrectScore1: "First half - full time excitement",
                AOSToOther:               "other",
                singleDouble:             "Odd/Even",
                singleDoubleUp:           "The first half-single/dual disk",
                singleDoubleDown:         "The second half-single/dual disk",
                doubleChance:             "Double Chance",
                doubleChanceUp:           "First Half Double Chance",
                doubleChanceDown:         "Second Half Double Chance",
                totalGoal:                "Total Score",
                totalGoalUp:              "First Half Total Goals",
                rightTotalGoal:           "Exact Total Goals",
                rightHomeTotalGoal:       "Exact Home Team Total Goals",
                rightAwayTotalGoal:       "Exact Away Team Total Goals",
                rightTotalGoalUp:         "Exact First Half Total Goals",
                rightTotalGoalDown:       "Exact Second Half Total Goals",
                zeroLose:                 "Clean Sheet",
                zeroLoseWinner:           "Clean Sheet Winner",
                halfAll:                  "Half/Full Time",
                winLose:                  "Win/Loss",
            },
            live:          {
                notSupportFull:  "Browser does not support full-screen function",
                notSupportInPicture:
                                 "Browser does not support picture-in-picture function",
                pleasePlayFirst: "Please click to play first",
            },
            eventStatus:   {
                prepare:   "Not start",
                running:   "In progress",
                close:     "Closed",
                postponed: "Postponed",
                deleted:   "Cancelled",
            },
            inputKeyBoard: {
                integerWarning: "Please use an integer for the betting amount",
            },
        },
        notice:                  {
            announcement:    "News",
            notice:          "Notification",
            marquee:         "Marquee",
            readed:          "Read",
            unread:          "Unread",
            empty:           "No Messages",
            winPrize:        "Bonus",
            receivedSuccess: "Claimed successfully!",
            receive:         "Claim",
            received:        "Claimed",
            receviePrize:    "Accept Bonus",
            all:             "All",
            edit:            "Edit",
            allRead:         "All Read",
            delete:          "Delete",
            cancle:          "Cancel",
            complete:        "Complete",
            importantNotice: "Important reminder",
            deleteTip:       "Are you sure you want to clear all notifications?",
            readTip:         "All marks read successfully",
        },
        center:                  {
            components: {
                categorySelect: {
                    allCategory:         "All Types",
                    newAllCategory:      "Account change categories",
                    walletTypeCategory:  "All wallets",
                    allPlatformCategory: "All Platforms",
                    alGameCategory:      "All Games",
                    allDealCategory:     "Subcategory details",
                },
                dateRadio:      {
                    today:          "Today",
                    yesterday:      "Yesterday",
                    lastTowDay:     "In the last 2 days",
                    withIn2Days:    "Within 2 days",
                    lastSevenDay:   "Last 7 Days",
                    withIn7Days:    "Within 7 days",
                    lastFifteenDay: "Last 15 Days",
                    lastThirtyDay:  "Last 30 Days",
                    lastSixtyDay:   "Near 60 days",
                    thisWeek:       "This Week",
                    lastWeek:       "Last Week",
                    thisMonth:      "This Month",
                    lastMonth:      "Last Month",
                    lastTwoMonth:   "Last 2 months",
                    lastThreeMonth: "Last 3 months",
                    all:            "All",
                    undo:           "Awaiting bets",
                    pending:        "Ongoing",
                    done:           "Completed",
                    abandoned:      "Abandoned",
                    custom:         "",
                    customize:      "Custom",
                },
            },
            report:     {
                title:             "Report",
                tabs:              {
                    personalStatement: "Report",
                    bettingRecord:     "Bet Records",
                    accountDetails:    "Statement",
                },
                table:             {
                    time:              "Time",
                    type:              "Type",
                    platform:          "Platform",
                    game:              "Games",
                    betting:           "Valid Bets",
                    betSlip:           "Bet amount",
                    win:               "Win/Loss",
                    no:                "Bet ID",
                    accountChangeType: "Transaction Type",
                    typeDetails:       "Type Details",
                    amount:            "Amount",
                },
                statistics:        {
                    validBetTotal: "Total Valid Bets",
                    betSlip:       "Cumulative bet amount",
                    profitTotal:   "Total W/L",
                    rechargeTotal: "Total Deposit",
                    reflectTotal:  "Total Withdrawal",
                    discount:      "Total",
                },
                bettingRecord:     {
                    canneled:  "Cancelled",
                    unsettled: "Not settled",
                    settled:   "Settled",
                    all:       "All Status",
                },
                personalStatement: {
                    winOrLose: "No. of Bet /<br />Bet amt. /<br />W/L",
                },
            },
            security:   {
                title:               "Security Center",
                notBind:             "Unlinked",
                none:                "Unset",
                done:                "Already Set",
                status:              "Current Status",
                close:               "Off",
                isClosed:            "Enable after verification",
                open:                "Enabled",
                isOpened:            "Disable after verification",
                loginLable:          "Login Password",
                valid:               "Verify Login Password",
                validOldPwd:         "Verify old login password",
                verifyCode:          "Enter verification code",
                passwordvalidation:  "Password verification",
                twoStepVerification: "Two-step Verification",
                forceBindAnd:        "and",
                forceBindTitle:
                                     "For your account safety, you need to link {bindItem}",
                forceBindWidthdrawPass:
                                     "You need to set the withdrawal password before applying for withdrawal",
                forceBindItemTitle:
                                     "For the security of your account, you need to link at least {length} of {bindItem}",
                bindOtherPrompt:
                                     "For the security of your account, please bind other security items first",
                noModificationPhone:
                                     "Phone number currently does not support modification",
                bindOther:           "Bind other safety items",
                LargeBalancesBindTitle:
                                     "Due to the substantial assets in your account, it is necessary to link two-step verification to ensure the security of funds. Click OK to proceed with the mandatory linking process",
                phone:               {
                    tab:              "Mobile Phone",
                    forceBind:        "Link your mobile phone to enjoy games",
                    phonePlaceholder: "Please enter phone number",
                    smsPlaceholder:
                                      "Please enter the mobile phone verification code",
                    update:           {
                        title:   "Change Phone",
                        tips:    "You can change your phone after verifying the login password",
                        success: "Phone modification successful",
                    },
                    bind:             {
                        title:   "Link Mobile No.",
                        tips:    "You can link the phone after verifying the login password",
                        success: "Phone linking successful",
                    },
                },
                cpf:                 {
                    tab:   "CPF",
                    label: "CPF",
                    placeholder:
                           "After setting, it cannot be modified. Please verify your CPF carefully",
                    tips:  "Enter up to 11 digits",
                    bind:  {
                        title:   "Set CPF",
                        tips:    "CPF can be set after verifying the login password",
                        success: "CPF set successfully",
                    },
                },
                email:               {
                    tab:       "Email",
                    forceBind: "Link email to enjoy games",
                    auth:      {
                        placeholder:     "Please link your email",
                        codePlaceholder: "Please enter the email verification code",
                    },
                    update:    {
                        title:   "Modify Email",
                        tips:    "Email can be modified after verifying the login password",
                        success: "Email updated successfully",
                    },
                    bind:      {
                        title:   "Link Email",
                        tips:    "Email can be linked after verifying the login password",
                        success: "Email linked successfully",
                    },
                },
                ga:                  {
                    tab:       "Google Authenticator",
                    open:      "Enable Google Authenticator",
                    close:     "Disable Google Authenticator",
                    forceBind: "Link Google Authenticator to enjoy games",
                    update:    {
                        title:        "Modify Google Authenticator",
                        openTips:
                                      "Google Authenticator can be enabled after verifying the login password",
                        closeTips:
                                      "Google Authenticator can be disabled after verifying the login password",
                        openSuccess:  "Google Authenticator enabled successfully",
                        closeSuccess: "Google Authenticator disabled successfully",
                    },
                    bind:      {
                        title:   "Link Google Authenticator",
                        tips:    "Google Authenticator can be linked after verifying the login password",
                        success: "Google Authenticator set successfully",
                    },
                    steps:     {
                        1: {
                            title: "Step 1: Download Google Authenticator",
                            content:
                                   'Search "Google Authenticator" in each app store to download the app',
                        },
                        2: {
                            title:
                                     "Step 2: Open the Authenticator App and add a QR code",
                            content: "Unable to scan, please enter the key",
                        },
                        3: {
                            title:
                                "Step 3: Enter the six-digit number in the Authenticator App to verify",
                        },
                    },
                },
                loginPwd:            {
                    tab:           "Login Password",
                    title:         "Change Password",
                    placeholder:   "Please enter password",
                    newPwd:        {
                        label:        "New Password",
                        placeholder:
                                      "6-16 characters, containing two of the following: English letter/number/symbol",
                        placeholder2: "Please enter a new password",
                    },
                    confirmNewPwd: {
                        label:       "Confirm Password",
                        placeholder: "Confirm the new password",
                    },
                    update:        {
                        tips:    "Modify the login password after verifying the login password",
                        success: "Successful password change",
                    },
                },
                withdrawalPwd:       {
                    tab:                   "Withdrawal Password",
                    label:                 "Withdrawal Password",
                    confirmPwd:            "Confirm new Withdrawal Password",
                    oldWithdrawalPwd:      "Original withdrawal password",
                    newWithdrawalPwd:      "New Withdrawal Password",
                    withdrawalPwdNonEmpty: "Withdrawal password cannot be empty",
                    confirmNewPwd:         "Confirm New Withdrawal Password",
                    update:                {
                        title:   "Reset Withdrawal Password",
                        tips:    "The withdrawal password can be modified after verifying the login password",
                        success: "Withdrawal password modified successfully",
                    },
                    bind:                  {
                        topTips:
                                   "For the safety of your funds, you need to set a withdrawal password first!",
                        title:     "Set Up Withdrawal Password",
                        tips:      "After verifying the login password, you can set the Withdrawal Password",
                        success:   "Withdrawal password set successfully",
                        topRemark: "Please set the withdrawal password first",
                        remark:
                                   " Attention: The withdrawal password protects your funds and is extremely important. Keep it to yourself to prevent any financial loss ",
                    },
                    sameOrContinuousTip:   "Can't be 6 same or continuous numbers",
                },
                question:            {
                    tab:           "Security Question",
                    label:         "Security Question",
                    update:        {
                        title:   "Change Security Question",
                        tips:    "Security Question can be modified after verifying the login password",
                        success: "Security Question changed success",
                    },
                    bind:          {
                        title:     "Set Up Security Question",
                        tips:      "Security Question can be set after verifying the login password",
                        success:   "Security Question set successfully",
                        topRemark: "Please set up a security question first",
                    },
                    answerConfirm: "Confirm Answer",
                    answerConfirmPlaceholder:
                                   "Please re-enter the security answer",
                    notMatch:      "The answers don't match, please re-enter!",
                },
                thirdWay:            {
                    tab:     "Third-party linking",
                    tips:    "The third-party can be linked after verifying the login password",
                    success: "Third-party linked successfully",
                    login:   "Link Third-Party Login",
                },
                securityConfigExpired:
                                     "Security settings have been updated, the page is refreshing",
                WithdrawAccount:     {title: "Withdrawal Account"},
            },
            setting:    {
                avatar:              "Avatar",
                setting:             "Set Up",
                account:             "Member account",
                nick:                "Nickname",
                wechat:              "WeChat",
                whatsapp:            "WhatsApp",
                facebook:            "Facebook",
                telegram:            "Telegram",
                zalo:                "Zalo",
                line:                "LINE\n",
                twitter:             "Twitter",
                name:                "Name",
                enterbirthday:       "Birthday (cannot be modified once set)",
                registerDate:        "Sign up date",
                nickPlaceholder:     "Please enter a nickname",
                wechatPlaceholder:   "Please enter WeChat ID",
                whatsAppPlaceholder: "Please enter WhatsApp ID",
                facebookPlaceholder: "Please enter Facebook ID",
                telegramPlaceholder: "Please enter Telegram ID",
                zaloPlaceholder:     "Please enter your Zalo number",
                linePlaceholder:     "Please enter Line",
                twitterPlaceholder:  "Please enter Twitter account",
                realNamePlaceholder: "Please link the Withdrawal account first",
                phonePlaceholder:    "Please link your mobile phone",
                ok:                  "Confirm",
                birthdayPlaceholder: "Birthday (cannot be modified once set)",
                goBackConfirm:       "Confirm to return?",
                remark:
                                     "The current modifications have not been saved. After return, all changes will not be applied",
                cancel:              "Cancel",
                man:                 "Male",
                woman:               "Female",
                updateAvatar:        "Change Avatar",
                error1:              "Exceeded characters, please re-enter.",
                error2:
                                     "WeChat ID must start with a letter or underscore and can consist of 6-20 characters using letters, numbers, underscores, hyphens, or their combinations",
                error3:              "Please enter 6-40 digits",
                error4:              "Up to 40 characters, please re-enter",
                error5:              "Enter up to 40 characters",
                error6:              "Enter up to 5-40 characters",
                error7:              "Please input 4-40 letters/numbers",
                error8:              "Please enter 4-40 alphanumeric/symbol characters",
                error9:
                                     "Only letters, numbers, and special characters @.-\\\\ are allowed for input",
                error10:
                                     "Supports a maximum of 60 English letters/numbers/characters for input",
                error11:             "Please enter 3-50 bits of English/numbers/characters",
                success:             "Successfully updated personal information",
                save:                "Save",
                noChangeToast:       "You have not made any edits",
                goBind:              "Link",
                goRecharge:          "Deposit",
                selectDate:          "Select date",
            },
            deviceInfo: {
                title:            "Log into device",
                userAccount:      "Account",
                currentDevice:    "Current device",
                historyDevice:    "Other devices",
                application:      "App",
                version:          "Version",
                deviceType:       "Equipment type",
                os:               "System",
                networkType:      "Network Type",
                ip:               "IP area",
                loginTime:        "Login time",
                logoutTime:       "Automatically log out",
                idleTime:         "idle time",
                autoLogout:       "Automatic exit duration",
                twoWeek:          "2 weeks",
                oneMonth:         "1 month",
                threeMonth:       "3 months",
                sixMonth:         "6 months",
                oneYear:          "1 year",
                saveImg:          "Save Image",
                deleteTipTitle:   "",
                deleteTipContent: "Delete this login device?",
                okBtnText:        "",
                deviceImg:        "",
                setSucceed:       "",
            },
        },
        footer:                  {
            quickJump:       {
                recreationCity:  "Casino",
                active:          "Event",
                task:            "Mission",
                backwater:       "Rebate",
                award:           "Reward",
                vip:             "VIP",
                paidToPromote:   "Agent",
                game:            "Games",
                chessAndCards:   "Cards",
                fishing:         "Fishing",
                electronic:      "Slot",
                Cockfight:       "Cockfight",
                person:          "Live",
                sports:          "Sports",
                lottery:         "Lottery",
                support:         "Support",
                onlineSupport:   "Online Support",
                helpCenter:      "Help Center",
                prizeFeedback:   "Reward Feedback",
                quickLogin:      "Quick Login",
                bindFacebook:    "Link Facebook",
                bindGoogle:      "Link Google",
                bindLine:        "Link Line",
                boundFacebook:   "Facebook linked",
                boundGoogle:     "Google linked",
                boundLine:       "Line already Linked",
                aboutUs:         "Introduction",
                aboutUsSub:      "About Us",
                privacyPolicy:   "Privacy Policy",
                other:           "Other",
                withdraw:        "Withdrawal Questions",
                recharge:        "Deposit Questions",
                language:        "Language",
                eSports:         "E-Sports",
                BlockchainGames: "Blockchain",
                providentFund:   "Provident\nFund",
            },
            cyanBlue:        {
                daily:            "Agent",
                dailyContent:
                                  "Join the program for free collaborators and capture to earn commissions",
                introduceFriends: "Introduce friends",
                introduceFriendsContent:
                                  "Refer a friend and join often instantly",
                vip:              "VIP",
                vipContent:       "Upgrade VIP to enjoy benefits and often exclusive",
                support:          "Support",
                supportContent:
                                  "Customer support service ready to serve you at any time",
                moreInformation:  "More information",
                followUs:         "Follow us",
                licensor:         "Licensor",
                supplier:         "Vendor",
                gameLicecense:    "Game License",
                certificate:      "Certificate",
                paymentMethod:    "Payment method",
            },
            footerContactUs: "Contact Us",
            copyright:       "@Copyright 2002-2022",
            companyInfo:
                             "****Group is one of the world's most renowned online gambling operators, offering a thrilling and entertaining range of games, including live casino, chess, slot games, fishing, lottery, and sports betting. It is authorized and regulated by the government of Curaçao and operates under license number **** issued to ****. It has passed all compliance checks and holds legal authorization to conduct all gaming operations involving opportunities and betting.",
        },
        cashback:                {
            title:        "Rebate",
            successTips:
                          "Claimed successful. Continue playing to get more rewards!~",
            rewardSource: "",
            subInfo:      {
                validBets:             "Valid Bets",
                amount:                "Rebateable",
                canReceive:            "Collectable",
                collectAll:            "Claim All",
                allValidBets:          "Total Valid Bets",
                allAmounts:            "Total Rebate",
                washCodeAll:           "One-click Rebate",
                washableCodesToday:    "Today Valid Bet",
                washableCodesTomorrow: "Tomorrow Valid Bet",
                accumulated:           "Accumulated",
                totalCodesWash:        "Total Rebate",
                todayCollection:       "Available for claiming today:",
                tomorrowCollection:    "Available for claiming tomorrow:",
                washableCode:          "Can claim <span>{x}</span>",
                settingDay:            "Automatically credited daily at <span>{x}</span>",
                settingWeek:           "<span>{x}</span> Automatically arrive",
                settingMonth:
                                       "Automatically credited monthly on day {d} at <span>{x}</span>",
            },
            popup:        {
                dayRebate:      "Daily rebate",
                weekRebate:     "Weekly rebate",
                moonRebate:     "Monthly rebate",
                pickUpManually: "Manual claim",
                systemDispatch: "System distribution",
                upToTip1:
                                "Automatically credited due to failure to claim reward by {x}",
                upToTip2:       "Forfeited due to failure to claim reward by {x}",
                upToTip3:       "Rewards are automatically credited by {x}",
            },
            tab:          {cashback: "{name} Rebate"},
            pageTitle:    {
                cards:        "Card Games Rebate",
                fishHunt:     "Fishing Rebate",
                electronic:   "Slot Rebate",
                sports:       "Sports Rebate",
                lottery:      "Lottery Rebate",
                rates:        "Rebate rate",
                records:      "Rebate Records",
                vipLevel:     "VIP Level",
                pickUpRecord: "History",
            },
            table:        {
                platform:         "Platform",
                downRates:        "Lower level rate",
                canReceiveAmount: "Available",
                upgradeVip:       "Upgrade to VIP {v} to receive {r}%",
                betAgain:         "Bet {b} to receive {r}%",
                amount:           "Rebate Bonus",
                selectCategory:   "Select type",
                selectPlatform:   "Select platform",
                category:         "Type",
                time:             "Time",
                operator:         "Action",
                detail:           "Details",
                current:          "Current",
            },
            detail:       "Rebate Details",
        },
        event:                   {
            title:                 "Event",
            titleGalaxyGold:       "Offers",
            galaxyGoldPropaganda1: "Enjoy life to the fullest",
            galaxyGoldPropaganda2: "",
            allActivities:         "All events",
            tabLabel:              "{x} event",
            rules:                 "Rules",
            content:               "Event Details",
            rulesDes:
                                   "This event's rewards require {times} valid bets for withdrawal",
            condition:             "Event Requirement",
            conditionDeposit:      "Designated Deposit",
            conditionPlatform:     "Designated platform",
            time:                  "Event time",
            reset:                 "Reset daily at 00:00",
            resetWeek:             "Reset at 00:00 every Monday",
            resetMonth:            "Reset at 00:00 on the 1st of each month",
            rulesOnlyDes:
                                   "The event bonus can be claimed on {device} and withdrawn only after {times} times of turnover are satisfied",
            rulesOnlyDesNoTimes:
                                   "This event reward needs to be claimed on {device}",
            onlyAndroid:           "Android APP",
            onlyIos:               "iOS APP",
            onlyApp:               "APP",
            onlyH5:                "Mobile webpage",
            onlyWeb:               "PC webpage",
            receiveTimes:          "Remaining application times",
            receiveBtn:            "Apply Now",
            questionPlaceholder:   "Enter the answer",
            unconditional:         "No conditions",
            cumulativereCharge:    "Total Deposit",
            cumulativeCoding:      "Wager",
            firstIndex:            "I.",
            secondIndex:           "II.",
            thirdIndex:            "III.",
            fourIndex:             "IV.",
            participate:           "Join",
            verifyTips:            "Reward will be credited after audit",
            pendingApply:          "Pending application",
            tipsText1:             "Back to the water, it has not been opened yet",
            tipsText2:             "VIP not available",
            tipsText3:             "Interest not available",
            expiredTip:            "Expired on {0}",
            valid:                 {
                hasH5:  "Login",
                hasApp: "Download and log in",
                h5:     "Mobile webpage",
                reward: "Claim {reward}",
            },
            validRule:             {
                limit_1:
                    "The event bonus can be claimed at iOS APP, and withdrawn only after {times} times of turnover are satisfied",
                limit_2:
                    "The event bonus can be claimed at Android APP, and withdrawn only after {times} times of turnover are satisfied",
                limit_3:
                    "The event bonus can be claimed on mobile webpage, and withdrawn only after {times} times of turnover are satisfied",
                limit_4:
                    "The event bonus can be claimed at PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_2:
                    "The event bonus can be claimed at APP, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_3:
                    "The event bonus can be claimed at iOS APP and mobile webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_4:
                    "The event bonus can be claimed at iOS APP and PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_2_3:
                    "The event bonus can be claimed at Android APP and mobile webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_2_4:
                    "The event bonus can be claimed at Android APP and PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_3_4:
                    "The event bonus can be claimed at mobile webpage and PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_2_3:
                    "The event bonus can be claimed at APP and mobile webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_2_4:
                    "The event bonus can be claimed at APP and PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_3_4:
                    "The event bonus can be claimed at iOS APP, mobile webpage and PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_2_3_4:
                    "The event bonus can be claimed at Android APP, mobile webpage and PC webpage, and withdrawn only after {times} times of the turnover are satisfied",
                limit_1_2_3_4:
                    "The event bonus can be withdrawn only after {times} times of the turnover are satisfied",
                limitNoTimes_1:
                    "Rewards for this event can be claimed via iOS APP",
                limitNoTimes_2:
                    "Rewards for this event can be claimed via Adndroid APP",
                limitNoTimes_3:
                    "Rewards for this event can be claimed via mobile webpage.",
                limitNoTimes_4:
                    "Rewards for this event can be claimed via a computer webpage",
                limitNoTimes_1_2:
                    "Rewards for this event can be claimed via APP",
                limitNoTimes_1_3:
                    "Rewards for this event can be claimed via iOS APP and mobile webpage",
                limitNoTimes_1_4:
                    "Rewards for this event can be claimed via iOS APP and desktop webpage",
                limitNoTimes_2_3:
                    "Rewards for this event can be claimed via Android APP and mobile webpage",
                limitNoTimes_2_4:
                    "Rewards for this event can be claimed via Android APP and computer webpage",
                limitNoTimes_3_4:
                    "Rewards for this event can be claimed via mobile wepage and computer webpage",
                limitNoTimes_1_2_3:
                    "Rewards for this event can be claimed via APP and mobile wepage",
                limitNoTimes_1_2_4:
                    "Rewards for this event can be claimed via APP and computer wepage",
                limitNoTimes_1_3_4:
                    "Rewards for this event can be claimed via iOS APP, mobile webpage and computer wepage",
                limitNoTimes_2_3_4:
                    "Rewards for this event can be claimed via Android APP, mobile webpage and computer wepage",
            },
            validTips:             {
                limit_1:   "Download and log in to the iOS app to claim reward",
                limit_2:   "Download and log in to the Android app to claim",
                limit_3:   "Log in to the mobile web page to claim",
                limit_4:   "Log in to the desktop web page to claim",
                limit_1_2: "Download and log in to the app to claim",
                limit_1_3:
                           "Download and log in to the iOS app, mobile web page, and desktop web page to claim",
                limit_1_4:
                           "Download and log in to the iOS app, desktop web page to claim",
                limit_2_3:
                           "Download and log in to the Android app, mobile web page to claim",
                limit_2_4:
                           "Download and log in to the Android app, desktop web page to claim",
                limit_3_4:
                           "Log in to the mobile web page, desktop web page to claim",
                limit_1_2_3:
                           "Download and log in to the app, mobile web page to claim",
                limit_1_2_4:
                           "Download and log in to the app, desktop web page to claim",
                limit_1_3_4:
                           "Download and log in to the iOS app, mobile webpage, and desktop webpage to claim",
                limit_2_3_4:
                           "Download and log in to Android, mobile web page, and desktop web page to claim",
            },
            validDay:              {
                monday:        "Every Monday",
                tuesday:       "Every Tuesday",
                wednesday:     "Every Wednesday",
                thursday:      "Every Thursday",
                friday:        "Every Friday",
                saturday:      "Every Saturday",
                sunday:        "Every Sunday",
                daily:         "Daily Limit",
                day:           "Daily",
                renPocketTime: "Distribution Time",
                reset:         "Reset",
                template:      "{day} {startTime} - {endTime}",
            },
            eventTypes:            {
                deposit:    "Deposit Event",
                chips:      "Bet Event",
                characters: "Words Collection Event",
                sign:       "Check-In Event",
                redPocket:  "Red Envelope Event",
                rescue:     "Rescue Bonus Event",
                invest:     "Investment Event",
                luckyWheel: "Fortune Wheel Event",
                customize:  "Custom Event",
                proxy:      "Agent event",
                promote:    "Promotion Event",
            },
            listedRewards:         {
                perTime:            "Each time",
                perMonth:           "Monthly",
                perDay:             "Daily",
                perWeek:            "Weekly",
                upperLimit:         "(Up to {x})",
                noRewards:          "Currently no available rewards to claim",
                firstRecharge:      "First Deposit",
                weeklyPay:          "Single deposit per week",
                weeklyTotalPay:     "Accumulated deposit  per week",
                perMonthPay:        "Single deposit per month",
                perMonthTotalPay:   "Accumulated deposit per month",
                perMonthReceive:    "Receive on {time} every month",
                weeklyReceive:      "Receive on {time}",
                receiveTimeDay:
                                    "Only claim from {startTime} to {endTime} daily",
                canReceiveTip:
                                    'This reward can only be claimed on {day}; expired rewards will be forfeited!"',
                canReceiveTip1:     "This reward can be claimed after {day}",
                canReceiveTip2:
                                    "This reward can be claimed daily from {startTime} to {endTime}",
                canReceiveTip3:
                                    "This reward can be claimed on {week} {startTime}-{endTime}",
                canReceiveTip4:
                                    "This reward can be claimed on the {day}th day of each month {startTime}-{endTime}",
                source:             "Source",
                toBeCollectedToday: "Reward today",
                collectTomorrow:
                                    "Can receive tomorrow <span> (remember to check) </span>",
                receivedToday:      "Automatically credited today",
                rewardSource:       "Reward source",
                rewardName:         "Reward name",
                rewardTime:         "Reward time",
                rewardexplain:      "Rewards",
                distributed:        "",
                validBet:           "Total valid bets",
                ratio:              "Ratio",
                award:              "Reward type",
                arrivalTime:        "Arrival time",
                availableTime:      "Available time",
                validityTime:       "Validity period",
                status:             "Status",
                remark:             "Remark",
                pending:            "Reward",
                received:           "Claimed",
                dispatched:         "Distributed",
                expiredDispatched:  "Expired dispatch",
                bonus:              "Bonus",
                ruleTabe:           {
                    singleRechargeAmount: "Single deposit amount",
                    awardAmount:          "Reward Amount",
                    activityDescription:  "Event introduction",
                    singleRecharge:
                                          "A single deposit of {x} entitles you to receive {y}",
                    single:               "Single deposit",
                    rewardRatio:          "Reward ratio",
                    awarMaxLimit:         "Reward limit",
                },
            },
            redPocket:             {
                todaySinglePay:         "Daily single deposit",
                todayPay:               "Daily total deposit",
                todaySingleBet:         "Daily wagering",
                todayBet:               "Daily total bet",
                redPocketAmount:        "Red envelope amount",
                grabPocketAwarMaxLimit: "Red envelope limit",
                payTip:                 "Daily deposit {x}, already deposited {y}",
                grabTip1:
                                        "Total limit: {x} times, this round's limit: {y} times (remaining: {z} times)",
                grabTip2:
                                        "This round's maximum redemptions: {x} times (remaining: {y} times)",
                betTip:                 "Wagered {x} required today, already wagered {y}",
                sendRedPockTip:
                                        "Red envelope limit: {x} times (remaining: {y} times)",
                times:                  "{x} times",
                closeTip:
                                        "After closing, the rewards for this round will no longer pop up. <br />When voluntarily giving up the reward.",
                open:                   "ON",
                grab:                   "GRAB",
                send:                   "Send",
                congrats:               "CONGRATULATIONS",
                message:                "The reward has been distributed to your wallet",
                pinReset:               "Clear",
                pinDelete:              "Delete",
                pinText:                "Code",
                errorTips:              "You've entered the wrong code",
            },
            rescue:                {
                yesterdayData:  "Yesterday's losses",
                yesterdayData1: "Last week losses",
                yesterdayData2: "Last month losses",
                todayData:      "Today's Rescue Bonus",
                todayData1:     "This Week's Support Bonus",
                todayData2:     "This Month's Support Bonus",
                columns:        {
                    amount: {label: "Loss amount"},
                    reward: {
                        label:          "Extra Rewards({unit})",
                        labelNoPercent: "Extra Rewards",
                    },
                },
            },
            proxy:                 {
                yesterdayCommission: "Yesterday's commissions",
                todayCommission:     "Today's commissions",
                todayReward:         "Today's bonus",
                dayPay:              {
                    0: "Yesterday subordinate deposit",
                    1: "Daily subordinate deposit",
                },
                day:                 {
                    0: "Yesterday's commissions",
                    1: "Daily commission",
                    2: "Today's reward",
                },
                weekPay:             {
                    0: "Last week subordinate deposit",
                    1: "Weekly subordinate deposit",
                },
                week:                {
                    0: "Last week's commission",
                    1: "Weekly commission",
                    2: "This week's reward",
                },
                monthPay:            {
                    0: "Last month subordinate deposit",
                    1: "Monthly subordinate deposit",
                },
                month:               {
                    0: "Last month's commission",
                    1: "Monthly commission",
                    2: "This month's reward",
                },
                otherReward:         "Extra Rewards",
            },
            invest:                {
                todayData:        "Today's Rebate",
                daysReceived:     "Days claimed",
                investmentAmount: "invest amount",
                accumulated:      "Total",
                now:              "Invest Now",
                receive:          "Claim",
                receiveSuccess:   "Claimed successfully!",
                applySuccess:     "Application successful!",
                received:         "Claimed",
                confirm:          "Confirm",
                title:            "Investment",
                balance:          "Account Balance",
                amount:           "invest amount",
                investSuccess:    "Investment successful",
                investLimitTips:  "Insufficient balance",
                receiveItem:
                                  "Invest {range}, receive an additional {give} bonus",
                tips:             "The invested amount will be deducted from the account, and the reward can be claimed starting the next day after investment",
                columns:          {
                    investmentAmount: {label: "Invest amount"},
                    dayRewardRate:    {label: "Daily Claim"},
                    rewardDays:       {
                        label: "Days for Rebate",
                        value: "{days}d"
                    },
                    totalRewardRate:  {label: "Total Bonus"},
                },
            },
            collectWords:          {
                beingSynthesized:  "Synthesizing",
                synthesized:       "Combination successful",
                totalAmount:       "Collect words to share Profit",
                reveal:            "Reward",
                collected:         "I've completed {x} sets",
                collectedStrong:   "I've completed <span>{x}</span> sets",
                tips:              "Complete missions daily to get drawing chances",
                extract:           "DRAW",
                extractAllWord:    "One-click draw",
                extractValidTimes: "Remaining {x} times",
                button:            "MERGE",
                ruleButtons:       {
                    unstarted:  "Not Started",
                    forward:    "Go",
                    processing: "Ongoing",
                    complete:   "Completed",
                },
                syntheticSuccess:  "Successful Synthesis",
                syntheticFailed:   "Synthesis Failed",
                ruleTimes:         "Completed {x}/{y} times",
                congrats:          "Congratulations!  ",
                resultSummary:
                                   "{x} people have collected {y} sets, each set gets <span>{z}</span>",
                resultCollect:     "Claim",
                resultCollected:   "Claimed",
                result:            "{x}×{y} sets: <span>{z}</span>",
                resultUnFinish:
                                   "You haven't completed the event this time. Keep it up!",
            },
            sign:                  {
                continued:  "Consecutive checked-in: <span>{x}</span>days",
                grand:      "Accumulative check-in <span>{x}</span> days",
                topupNeed:  "Required deposit <span>{x}<span>/{y}</span></span>",
                collected:  "Claimed <strong>{x}</strong>",
                betNeed:    "Required bets <span>{x}<span>/{y}</span></span>",
                signIn:     "Claim",
                hasSignIn:  "Claimed",
                hasReceive: "Claimed",
                dayCount:   "DAY {x}",
                extraPrize: "Extra Rewards",
                successText:
                            "Congratulations! You have received a bonus of {x}",
                extraPrizeSuccessText:
                            "Congratulations! You have received additional bonuses {x}",
                noSignIn:   "Can't sign in at the moment",
            },
            luckyWheel:            {
                tabs:        {
                    silver:            "Silver Wheel",
                    gold:              "Gold Wheel",
                    diamond:           "Diamond Wheel",
                    luckyValue:        "Lucky point",
                    turntableTask:     "Mission require",
                    getAnnouncement:   "Reward Announcement",
                    myRecord:          "My Records",
                    noData:            "No Records",
                    drawNow:           "Draw",
                    currentLuckyValue: "Current lucky point",
                    details:           "Details",
                },
                splicingGet: "{user} won at {zhuanpan}",
                effectiveBetTips:
                             "You still need to wager {x} to get {n} lucky Points",
                betDetails:  {
                    betTotal:            "Total wagers during the event period",
                    currentRulesLabel:   "Current rules",
                    currentRules:        "Every {b} wagered gains {n} Lucky Points",
                    luckyPointsUsed:     "Used lucky point",
                    luckyValueAvailable: "Available lucky point",
                    expireIntegral:      "Expired lucky point",
                    countingMethodTitle: "Lucky point calculation method:",
                },
                toast:       {
                    notEnough:      "Your lucky point is not enough",
                    receiveSuccess: "Claimed successfully",
                    depositBalance: "Deposited into the balance",
                },
                table:       {
                    time:             "Time",
                    dayTask:          "Daily Mission",
                    weekTask:         "Weekly Mission",
                    luckyValue:       "Lucky point",
                    canGetluckyValue: "Can get lucky point",
                },
            },
            promote:               {
                currentValid:                 "Currently referred by <span>{x}</span> people",
                currentRequire:
                                              "(Deposit requirement <span>{x}</span>, Bet requirement <span>{y}</span>)",
                currentRecharge:              "(Deposit requirements<span>{x}</span>)",
                currentBet:                   "(Betting requirements <span>{y}</span>)",
                copySuccess:                  "Copied successfully!",
                copyFailed:                   "Copy failed, please copy manually",
                people:                       "{x} people",
                promoteUrl:                   "Referral link",
                userName:                     "Sub. accounts",
                createTime:                   "Registration time",
                isItEffective:                "Is it valid",
                yes:                          "Yes",
                no:                           "No",
                validConditions:              "Valid conditions",
                details:                      "Details",
                all:                          "All",
                isEffective:                  "Is it valid?",
                efficient:                    "Valid",
                invalid:                      "Invalid",
                accountFirstDeposit:          "Account First Deposit",
                cumulativeRecharge:           "Total Deposit",
                cumulativeCoding:             "Wager",
                cumulativeRechargeDays:       "Total deposits",
                cumulativeRechargeTimes:      "Total days of deposit",
                registerIPRestrictions:       "Repeated registration IP",
                registerDeviceRestrictions:   "Repeated registration devices",
                downloadAPPLogin:             "Download the app and log in",
                promotionDetails:             "Referral details",
                effectivePromotionNumber:     "Valid number of referrals",
                award:                        "Reward",
                effectivePromotionNumberQuestion:
                                              "What is a valid promotional number? (Meet all of the below requirements)",
                effectivePromotionNumberArbitrarily:
                                              "What is a valid promotional number? (Meets any of the following)",
                effectivePromotionNumberTip0:
                                              "Subordinates need to download app and log in",
                effectivePromotionNumberTip1: "Subordinate's first deposit",
                effectivePromotionNumberTip2: "The subordinate's total deposit",
                effectivePromotionNumberTip3: "Subordinate's total turnover",
                effectivePromotionNumberTip4:
                                              "The subordinate's total number of deposits",
                effectivePromotionNumberTip5:
                                              "The subordinate's accumulative deposits",
                mySubordinate:                "My subordinates",
                numberPeople:                 "Players",
                effectiveNumberOfPeople:      "{x} Valid Players",
                yesterdayEfficient:           "{x} valid people yesterday",
                receive:                      "Claim",
                quickShare:                   "Quick Share",
                shareNow:                     "Share Now",
                more:                         "More",
                shareTo:                      "Share To",
                orAbove:                      "Or more",
                timesOrMore:                  "times or above",
                copyLink:                     "Copy Link",
                save:                         "Save",
                cancel:                       "Cancel",
                cumulativeRewards:            "Cumulative rewards",
                cumulativeRewardsTip:
                                              "Automatical distribution in the next day",
                effectivePromotionYesterday:  "Yesterday valid promotion",
                dayOrMore:                    "Days or more",
                updateTime:                   "Update time",
                effectiveSubordinate:         "Valid subordinates {x} people",
                yesterdaysEffectivePromoter:
                                              "Valid promotion from yesterday {x} people",
                receiveTimeEveryDaily:
                                              "This reward can only be claimed daily from {startTime} to {endTime}",
                receiveTimeNextDay:
                                              "This reward can only be claimed on the next day from {startTime} to {endTime}",
                receiveTimeWeekly:
                                              "This reward can only be claimed every Monday from {startTime} to {endTime}",
                receiveTimeMonthly:
                                              "This reward can only be claimed on the 1st day of each month from {startTime} to {endTime}",
            },
            bet:                   {
                endToBeAwarded:    "Activity ended, awaiting results",
                endAndAwarded:
                                   "The reward has been distributed, you can go to the claim record to view it",
                myIntegral:        "My points",
                slimageBonusBonus: "Share the total bonus",
                award:             "Reward",
                winWin:            "Points win/loss",
                record:            "Records",
                time:              "Time",
                result:            "Results",
                quizProject:       "Betting events",
                bettingBelow:      "Bet {x} to win ({y})",
                bettingOneBelow:   "Bet {x} to win",
                bettingDraw:       "Bet {x}",
                toBeAwarded:       "To be opened",
                winning:           "Won",
                unintendedPrize:   "Lost",
                guessRecord:       "Betting records",
                allDisplayed:      "All displayed",
                totalIntegrals:    "total points",
                noGame:            "No Games available",
                day:               "Receive {x} points daily upon logging in to the event",
                charge:
                                   "Daily recharge can get corresponding points according to 1:{x}",
                coding:
                                   "You can get corresponding points by typing 1:{x} every day",
                congratulation:    "Congratulations on winning!",
                notAwarded:        "If you didn’t win, please try again!",
                item:              {
                    betting:                   "In betting process",
                    bettingEnd:                "Wait for reward",
                    win:                       "Congratulations!",
                    failed:                    "Failed",
                    opened:                    "Reward",
                    renew:                     "Continue betting",
                    noGame:                    "No Games available",
                    bet:                       "Bet",
                    seal:                      "Closed",
                    youDontNote:               "You haven't placed a bet",
                    integral:                  "Points",
                    toBeDetermined:            "To be determined",
                    odds:                      "Odd",
                    draw:                      "Tied",
                    winOut:                    "Win",
                    all:                       "All",
                    youHaveBet:                "You have bet <span>{x}</span> points",
                    betTeam:                   "You select {x} to win",
                    betDraw:                   "You choose Tied",
                    pleaseSelectTheOdds:       "Please select the odds",
                    pleaseEnterTheBetPoint:    "Enter betting points",
                    notBeLower:                "Bet points cannot be lower than {x}",
                    notBeHigher:               "Bet points cannot be higher than {x}",
                    onlySupports:              "The bet points only support {x}-{y}",
                    notBeHigherThanYours:      "Your points are insufficient",
                    withdrawAmountPlaceholder: "Min {a}, Max {b}",
                    deadlineForBetting:        "Betting deadline:",
                    onlyParticipate:           "Need {a} {b} to participate",
                    yourIntegrals:             "Your points",
                    dailyGift:                 "Daily gift",
                    betBouns:                  "Bet bonus",
                    rechargeGift:              "Recharge gift",
                    guessWin:                  "Guess the prize",
                    guessBetting:              "Betting",
                },
            },
            luckyBet:              {
                awardOrders:    "Today's winning wager",
                notAwarded:     "No winning wager today",
                receive:        "Claim",
                received:       "Claimed",
                reward:         "Reward",
                orderNo:        "Winning ticket number:",
                totalLimit:     "Total limit of {x} times'",
                todayLimit:     "Max. {y} times claimed today ({z} times left)",
                orderEnd:       "Bet number last digits",
                orderContinue:
                                "Consecutive numbers at any position of the wager",
                orderRandom:    "Any position of the wager includes",
                amountTimes:    "Reward multiple",
                betNumTimes:    "{x} times of the bet amount",
                fixedAmount:    "Fixed reward",
                amountLimit:    "Reward limit",
                validBet:       "Daily valid bets",
                validCharge:    "Daily deposit",
                dailyLimit:     "Daily claim limit",
                times:          "Time(s)",
                piece:          "One",
                rewardDescription:
                                "Reward amount = Bet amount of that bet slip x Bonus multiplier, without exceeding the limit",
                receiveSuccess: "Claimed successfully!",
                noLimit:        "Unlimited",
            },
            ranking:               {
                ranking:                 "Presence",
                loadingMore:             "Load all",
                settling:                "Settlement in progress",
                rewardAmount:            "Reward Amount",
                accumulatedRecharge1:    "Player/Cumulative\nrecharge",
                accumulatedRecharge2:    "Player/single\nrecharge",
                accumulatedRecharge3:    "Player/Cumulative\nbetting",
                todayRanking1_1:         "Today's recharge ranking",
                yesterdayRankingList1_1: "Yesterday's recharge ranking",
                todayRanking2_1:         "Ranking this week",
                yesterdayRankingList2_1: "Last week's recharge ranking",
                todayRanking3_1:         "Ranking of recharge this month",
                yesterdayRankingList3_1: "Last month's recharge ranking",
                todayRanking1_2:         "Today's single ranking",
                yesterdayRankingList1_2: "Yesterday's\nsingle ranking",
                todayRanking2_2:         "Single ranking this week",
                yesterdayRankingList2_2: "Last week's single ranking",
                todayRanking3_2:         "This month's single ranking",
                yesterdayRankingList3_2: "Last month's single ranking",
                todayRanking1_3:         "Today's bet ranking",
                yesterdayRankingList1_3: "Betting ranking yesterday",
                todayRanking2_3:         "Betting rankings this week",
                yesterdayRankingList2_3: "Last week's bet ranking",
                todayRanking3_3:         "Betting rankings this month",
                yesterdayRankingList3_3: "Last month's bet ranking",
                yesterdayRanking1:       "Your ranking yesterday",
                yesterdayRanking2:       "You rank last week",
                yesterdayRanking3:       "You rank last month",
                maxRow:                  "Show up to 100 player data",
                getBonus:                "Win bonus",
                myself:                  "Myself",
                currentRanking:          "Your current ranking",
                todayBonus:              "Expected bonus",
                refresh:                 "Refresh",
                rankingNum:              "Rank {0}",
                everyOneRewardAmount:    "/Players",
                notOnList:               "Not Ranked",
            },
            discountCode:          {
                discountCode:           "New Player Bonus",
                firstDepositCount:      "First deposit amount",
                discountCodeSms:        "Verify SMS to receive prize money",
                placeholder:            "Please enter the redemption code",
                confirmTheExchange:     "Claim Bonus",
                payBtn:                 "Deposit Now",
                success:                "Congratulations, you've received a bonus of {x}",
                confirm:                "Confirm",
                close:                  "Close",
                needRecharge:           "Required to deposit {x} to withdraw",
                tipsLable:              "Bonus Amount:",
                tips3:                  "{x}% of the First Deposit Amount, up to {y}",
                activityNotStarted:     "Event not started",
                activityHasEnded:       "Event has ended",
                activityIDInvalid:      "Invalid event ID",
                notPhoneGold:           "Event ID is not a phone number bonus",
                areaCodeIncorrect:      "Incorrect area code",
                phoneNumberNothingness: "Invalid phone number",
                phoneNumberBeUsed:      "The phone number has already been used.",
                congratulations:
                                        "Congratulations! You've completed the SMS verification",
                completedRegistration:
                                        "Congratulations, you have successfully completed account registration",
                activityDissatisfy:
                                        "You do not meet the activity requirements, participation is limited to invited users only.",
                notEligibleEvent:       "You do not meet the event requirements",
                alreadyWon:             "Received Bonuses",
                verifyMobile:           "Verify Your Mobile Number to Claim Bonus",
                congratulationsReCode:  "Congratulations, exchange successful!",
                tipsText:
                                        "Reminder: The max. profit limit of this bonus is {x}. After profit is made, you need to deposit {y} before you can withdraw it!",
                notProfitLimit:         "Unlimited",
                tipsProfitUpperLimit:
                                        "The max. profit limit for this bonus is {x}",
                tipstipsNeedRechargeAfterProfit:
                                        "After profit, a deposit of {y} is required to withdraw!",
                distributed:            "Distributed",
                getRedeemCode:          "Get the exchange code",
            },
            cutOnce:               {
                lottery:             "Draw",
                drawsNumber:         "Remaining {x} times",
                myRecord:            "My records",
                winningAnnouncement: "Winning announcement",
                rule:                "Rules",
                bonus:               "Prize<span>{x}-{y}</span>",
                receive:             "Claim",
                progressText1:       "{x}% completed, {n}% remaining",
                progressText2:       "{x}% Completed",
                resetcountdown:      "Reset after {time}",
                updateTime:          "Update timing: {time}",
                manualUpdate:        "Manual update",
                detail:              "Details",
                forward:             "Go",
                lookover:            "View",
                more:                "More",
                userAccount:         "Member account",
                createTime:          "Registration time",
                detailInfo:          "Details",
                announcement:
                                     "Successfully claimed <span>{prize}</span> on <span>{time}</span>",
                firstAward:          "Directly won<span>{prize}</span> in the lottery",
                secondAward:
                                     "Won<span>{prize}</span> in the lottery (need to complete the mission to claim)",
                finishTask:          "Complete Mission",
                getAward:            "Successfully claimed<span>{prize}</span>",
                tips:                "Accumulated winnings ≥ {x} to claim, currently accumulated <span>{y}</span>",
                cardTitle:
                                     "Complete the following missions to receive<span>{prize}</span>",
                detailTitle1:        "How to get rewards?",
                detailTitle2:        "Valid subordinate conditions",
                detailTitle3:        "My Subordinates",
                detailTitle4:        "Quick Share",
                taskText1:           "Valid promoted subordinates ≥ {x} people",
                taskText2:           "Valid-bet subordinates ≥ {x} people",
                taskText3:           "First-deposit Subordinates ≥ {x}",
                taskText4:           "Subordinate deposit number ≥ {x}",
                taskText5:           "Subordinate total deposit amount ≥ {x}",
                taskText6:           "Subordinate total withdrawal amount ≥ {x}",
                taskText7:           "Subordinate withdrawal number ≥ {x}",
                taskText8:           "Download and log in to APP",
                taskText9:           "My single recharge ≥{x}",
                taskText10:          "My single bet ≥{x}",
                taskText11:          "My accumulated recharge ≥{x}",
                taskText12:          "My accumulated bets are ≥ {x}",
                text1:
                                     "Valid promoted subordinates ≥ {x} people, receive the remaining bonus<span>{prize}</span>",
                text2:
                                     "Valid-bet subordinates ≥ {x} people, receive the remaining bonus<span>{prize}</span>",
                text3:
                                     "First-deposit subordinates ≥ {x}, receive the remaining bonus<span>{prize}</span>",
                text4:
                                     "Subordinate deposit number ≥ {x}, receive the remaining bonus<span>{prize}</span>",
                text5:
                                     "Subordinate total deposit amount ≥ {x}, receive the remaining bonus<span>{prize}</span>",
                text6:
                                     "Subordinate total withdrawal amount ≥ {x}, receive the remaining bonus<span>{prize}</span>",
                text7:
                                     "Subordinate withdrawal number ≥ {x}, receive the remaining bonus<span>{prize}</span>",
                text8:
                                     "Lower level bet ≥ {x}, you can get the remaining bonus <span>{prize}</span>",
                processText:         "Valid subordinates {x} people, {y} remaining",
                detailText1:
                                     "The successful login of the subordinates, it means that it is effective",
                detailText2:         "Subordinates have bets, which means valid",
                detailText3:
                                     "The completion of the subordinates' first charging indicates that it is effective",
                detailText4:         "Successfully log in during the cycle",
                withdrawals:         "Number of withdrawals",
                withdrawValue:       "Withdrawal Amount",
                rechargeValue:       "Deposit amount",
                rechargeCount:       "Number of recharges",
                awardText:
                                     "Congratulations, You have won a bonus of <span>{x}</span>",
                awardText1:          "Bonus <span>{x}</span>",
                myLink:              "My link",
                change:              "Change",
                progressTextInvitedNumber:
                                     "Effectively invite {x} people, but also poor {n} people",
                progressTextDaMaNumber:
                                     "Effective downline {x} people, still lacking {n} people",
                progressTextFirstRechargeNumber:
                                     "Effective subordinates have {x} first deposits, and there are still {n} people left.",
                progressTextRechargeCount:
                                     "Effective lower -level recharge number {x} times, still poor {n} times",
                progressTextTotalRechargeValue:
                                     "Effective lower -level accumulated recharge {x}, and poor {n}",
                progressTextTotalWithdrawValue:
                                     "Effective subordinates cumulative withdrawal {x}, still poor {n}",
                progressTextWithdrawCount:
                                     "Effective subordinate withdrawal {x} times, still poor {n} times",
                progressTextTotalDaMaValue:
                                     "The effective subordinates have accumulated {x} bets, and there is still {n} left.",
                detailInfoText1:     "Betting during the cycle",
                detailInfoText2:     "Complete the first recharge within the cycle",
                detailInfoText3:
                                     "Cumulative recharge {n} times during the cycle",
                detailInfoText4:     "Cumulative recharge during the cycle {x}",
                detailInfoText5:     "Cumulative withdrawal during the cycle {x}",
                detailInfoText6:
                                     "Cumulative withdrawal during the cycle {n} times",
                detailInfoText7:     "Accumulated bets during the period {n}",
                secondConditionInvitedNumber:
                                     "The progress of inviting friends is 100.00%, and you can get the remaining <span>{prize}</span>",
                completedValue:      "{x}% Completed",
                cardInvitedNumber:   "Effectively promoted {x} people below",
            },
            newCutOnce:            {
                firstAward:               "{0}th draw, got {1}",
                firstAward1:              "The {0}th draw did not win, please try again!",
                secondAward:
                                          "Added {0} people as effective promoters, and the number of lucky draws +1",
                finishTask:               "Congratulations, you have won {0} in total.",
                getAward:                 "Successfully claimed bonus {0}",
                lotterytip:               "Number of draws remaining: 0",
                demoModeTip:              "Streamer account have no permission",
                notStartProgress:         "Win ≥{totalAward} to claim",
                nextTaskRechargeCountTip: "Subordinate recharge ≥ {0}",
                nextTaskRechargeCountTip1:
                                          "Promoting subordinates to successfully log in is considered valid.",
                progressingpProgress:     "Remaining {undoneAmount}",
                cardTipsTitle:
                                          "Promote {0} subordinates to get {1} draw chances",
                awardText1:               "Won {0} (maximum {1})",
                awardText2:               "Won {0}",
                tip1:                     "You have drawn prizes, and the current cumulative prize is {0}",
                gotoRecord:               "View Records",
                cardTipsSubText:
                                          "Require subordinates to recharge ≥{amount}, currently valid for {people}",
                cardTipsSubText1:
                                          "Successful login of the promoted subordinates is considered valid, and the current {people} people are valid.",
                shareText:                "Share",
            },
        },
        records:                 {
            totalReceive:       "Total claimable",
            rewardAmount:       "Reward Amount",
            reward:             "Reward",
            activeId:           "ID",
            rewardName:         "Reward name",
            rewardTime:         "Reward time",
            amount:             "Amount",
            activity:           "Active level",
            remark:             "Rewards",
            activeTypeName:     "Source",
            receiveRecords:     "Application Record",
            checkStatus:        "Status",
            underReview:        "Under review",
            passed:             "Approved",
            rejected:           "Rejected",
            arriveAccount:      "Processing",
            progress:           "Processing, will be credited to the account later",
            cancelled:          "Cancelled",
            operate:            "Action",
            detail:             "Details",
            info:               "Application information",
            rewardAmount2:      "Reward Amount",
            administratorReply: "Admin reply",
            noReply:            "No reply yet",
            arrivalTime:        "Arrival time",
            platformRewardDetail:
                                "Accumulative valid bet {betCode}, rate {rate}%, reward {gold}",
        },
        cpf:                     {
            CPFTitle:            "Deposit provident fund",
            accumulHistory:      "Total records",
            betHistory:          "Cumulative betting requirements",
            rulesDesc:           "Rules",
            totalRechargeAmount: "Total deposit",
            recharge:            "Deposit",
            give:                "Bonus",
            maxLimitFundAmount:  "Bonus cap",
            maxLimitAddTimes:    "Times capped",
            noCap:               "not to be capped",
            cpfName:             "Provident\nFund",
            withdraw:            "Withdraw",
            hasWithdraw:         "Already taken out",
            hasSend:             "Distributed",
            hasCancel:           "Cancelled",
            currentCPF:          "Current provident fund",
            firtRechargeCPF:     "Free provident fund {num} for each recharge",
            maxLimitText1:
                                 "The maximum number of gifts is {num1} and the maximum amount is {num2}",
            maxLimitText2:
                                 "The number of gifts is capped {num} times, and the cap amount is not capped.",
            maxLimitText3:
                                 "There is no cap on the number of gifts, and the cap amount is {num}",
            maxLimitText4:
                                 "There is no cap on the number of gifts and no cap on the amount.",
            needRecharge:        "Required bet amount of",
            finishRecharge:      "Completed betting requirements",
            rechargeCount:       "Time(s)",
            maxAmount:           "Cap amount　",
            enableWithdraw:      "Available for withdrawal",
            details:             "Details",
            time:                "Time",
            rechargeAmount:      "Deposit amount",
            addFundRate:         "Bonus ratio",
            addFundAmount:       "Bonus amount",
            totalAmount:         "Total",
            addBetCodeRate:      "Bet times",
            addBetCodeAmount:    "New betting\nrequirements",
            afterReset:          "Reset after {x}",
        },
        promote:                 {
            title:                      "Agent",
            unitPeople:                 "",
            empty:                      "none",
            myPromote:                  "Referral Link",
            myData:                     "My Data",
            promoteShare:               "Promotion Sharing",
            myCommission:               "Commission",
            myPerformance:              "Performance",
            returnCommissionProportion: "Commission Rate ",
            tutorial:                   "Agent Network",
            videoTutorial:              "Video Tutorial",
            directAccountOpening:       "Register Subordinates",
            agencyTransfer:             "Agent Transaction",
            conversionRecord:           "Records",
            pickUpRecord:               "History",
            online:                     "Online",
            offline:                    "Offline",
            yes:                        "Yes",
            times:                      "Time(s)",
            no:                         "No",
            date:                       "Date",
            startTime:                  "Start Date",
            endTime:                    "End Date",
            cancel:                     "Cancel",
            confirm:                    "Confirm",
            memberID:                   "Member ID",
            searchMemberID:             "Member ID",
            memberAccount:              "Member Account",
            searchMemberAccount:        "Member Account",
            memberDetail:               "Subordinate details",
            subordinate:                "Subordinates of {id}",
            userStatusType:             {
                normal:                  "Normal",
                freezeManually:          "Manually Frozen",
                abnormalFreezing:        "Abnormally Frozen",
                prohibitionOfReceivingDiscounts:
                                         "Prohibited from receiving benefits",
                blacklist:               "Blacklist",
                disabled:                "Deactivated",
                withdrawalProhibited:    "Withdrawal Prohibited",
                forbiddenToEnterTheGame: "Game entry prohibited",
                returnThePrincipal:      "Discourage Principal Refund",
            },
            myDataPage:                 {
                coreData:          "Core data",
                dataPreview:       "Data overview",
                newDirectMembers:  "Add direct members",
                firstChargeUsers:  "Number of first deposit players",
                rechargeUsers:     "Number of deposits",
                rechargeAmount:    "Deposit amount",
                performance:       "Performance",
                commission:        "Commission",
                myTeam:            "My team",
                totalUsers:        "Total number of people",
                directUsers:       "Direct subordinates",
                otherUsers:        "Other subordinates",
                totalPerformance:  "Total Perf.",
                directPerformance: "Sub's Perf.",
                otherPerformance:  "Others' Perf.",
                totalCommission:   "Total Commission",
                directCommission:  "Direct Commission",
                otherCommission:   "Other Commission",
            },
            directBetting:              {
                memberID:               "Member ID",
                searchMemberID:         "Enter Member ID",
                orders:                 "Number of bets",
                validBet:               "Valid Bets",
                profit:                 "Total W/L",
                statisticalDate:        "Statistical date",
                details:                "Details",
                directBettingDetail:    "Betting Details",
                betTime:                "Bet time",
                platform:               "Game platform",
                category:               "Game type",
                name:                   "Game name",
                memberProfit:           "Win/Loss",
                validOrderAmount:       "Valid Bets",
                profitAmount:           "Win/Loss amount",
                totoalValidOrder:       "Total number of bets",
                directValidBet:         "Direct valid bets",
                directProfit:           "Direct wins/losses",
                otherValidBet:          "Other valid bets",
                otherProfit:            "Other wins/losses",
                totoalValidOrderAmount: "Total valid bets",
                totoalProfit:           "Total W/L",
            },
            directData:                 {
                memberID:                "Member ID",
                searchMemberID:          "Enter Member ID",
                registrTime:             "Registration time",
                registrDate:             "Sign up date",
                loginDate:               "Login date",
                lastLogin:               "Last login",
                account:                 "Account",
                firstDeposit:            "First Deposit",
                isFirstDeposit:          "First deposit or not?",
                status:                  "Status",
                times:                   "Online login and counts",
                now:                     "Current",
                subordinate:             "His subordinates",
                totalRegisterPerson:     "Number of registrations",
                totalDepositPerson:      "Number of deposits",
                totalFirstDepositPerson: "Number of first deposit players",
                totalDepositAmount:      "Deposit amount",
                totalValidBet:           "Valid Bets",
            },
            common:                     {
                all:          "All Types",
                chess:        "Cards",
                fish:         "Fishing",
                street:       "Arcade",
                sports:       "Sports",
                real:         "Live",
                gameing:      "Slot",
                lottery:      "Lottery",
                other:        "Others",
                cockfighting: "Cockfight",
                game:         "Games",
                blockchain:   "Blockchain",
                gaming:       "E-Sports",
            },
            commission:                 {
                parentAgentID:          "Superior ID",
                memberID:               "Member ID",
                searchMemberID:         "Enter Member ID",
                addTime:                "Joining time",
                settleTime:             "Settlement time",
                settleDay:              "Settlement date",
                type:                   "Type",
                performance:            "Performance",
                lowerLevelPeople:       "Subordinate",
                count:                  "Commission",
                details:                "Details",
                contributionCommission: "Contribution Commission",
                account:                "Account",
                commissionReceived:     "Commission Claimed",
                searchAccount:          "Account",
                commissionDetail:       "Commission Details",
                contributebetUsername:  "Contributing Account",
                contributePerformance:  "Performance",
                totalPerformance:       "Total Perf.",
                totalCount:             "Total Commission",
                directly:               "Direct",
                lowerLevel:             "Subordinates",
                parentAgent:            "Superior",
                deposit:                "Total Deposits",
                netProfix:              "Net Profit",
                unReached:              "Not Yet Qualified",
                directPerformance:      "Sub's Perf.",
                directCommission:       "Direct Commission",
                otherAchievements:      "Others' Perf.",
                otherCommissions:       "Other Commission",
                validmembers:           "Contributors",
                memberProfitLoss:       "Member profit and loss",
                discount:               "Claim discount",
            },
            promote:                    {
                searchMemberID:      "Enter Member ID",
                account:             "Account",
                agentMode:           "Agent mode",
                infinite:            "Unlimited Level Difference",
                firstLevel:          "Level 1 agent",
                netProfit:           "Level 1 net profit",
                threeNetProfit:      "Level 3 net profit",
                national:            "National agent",
                settleDurationDay:   "Daily settlement",
                settleDurationWeek:  "Weekly",
                settleDurationMonth: "Monthly",
                memberId:            "My ID",
                mySuperior:          "Superior ID",
                myParent:            "My superiors",
                directNum:           "Direct subordinates",
                inviteCode:          "Invitation code",
                available:           "Collectable",
                getCommission:       "Claim Commission",
                commission:          "Commission",
                teamData:            "All Data",
                directBetting:       "Subordinate Wagers",
                directData:          "Subordinate Stats",
                directFinance:       "Subordinate Finance",
                directReceive:       "Subordinate Claims",
                totalDeposit:        "Total deposit",
                totalDepositCount:   "Total first-deposit players",
                totalDepositDirect:  "Subordinate Deposits",
                totalDepositOthers:  "Other Deposit",
                totalValidBet:       "Total valid bets",
                totalNetProfit:      "Total W/L",
                betTimes:            "Total number of bets",
                firstDeposit:        "Number of first deposit players",
                firstDepositDirect:  "Direct first deposit ",
                firstDepositOthers:  "Other first deposit",
                more:                "More",
                totalCommission:     "Total Commission",
                availableCommission: "Commission Collected",
                shareSite:           "Referral information",
                totalNumberOfTeams:  "Total",
                directMember:        "Direct Subordinates",
                totalNetProfix:      "Total net earnings",
                directNetProfix:     "Direct net earnings",
                otherNetProfix:      "Other net earnings",
                directValidNum:      "Valid direct subordinates",
                otherValidNum:       "Other valid subordinate",
                otherMembers:        "Others",
                totalPerformance:    "Total Perf.",
                directPerformance:   "Sub's Perf.",
                directCommission:    "Direct Commission",
                otherPerformance:    "Others' Perf.",
                otherCommission:     "Other Commission",
                lastCommission:      "Last Commission",
                validMember:         "Valid players",
                performance:         "Performance",
                clickSave:           "SAVE",
                myLink:              "My link",
                myLinkChange:        "Change",
                copy:                "Copy",
                proxyLevel:          "Agent Tier",
                receiveSussessTip:   "Commission claimed successfully",
                noCommonssionTip:    "You currently have no commission to claim.",
                proxyLevelTable:     {
                    proxyLevel: "Agent Tier",
                    promotionConditions:
                                "Promotion conditions (promote to get commissions)",
                },
                publicityIntruduction:
                                     "World's first 2K HD Card Game\nScan to download",
                downloadSuccessTips: "The picture has been saved successfully",
                downloadImageName:   "Share",
                shareText:           "{account} promotional link",
                notSupportShare:
                                     "This browser does not support the sharing feature",
            },
            directFinance:              {
                memberID:           "Member ID",
                searchMemberID:     "Enter Member ID",
                deposit:            "Deposit",
                withdrawal:         "Withdraw",
                difference:         "Difference",
                balance:            "Balance",
                totalDeposit:       "Total deposit",
                totalWithdrawal:    "Total withdrawal",
                totalNetDiff:       "Total Deposit-Withdrawal Difference",
                depositCount:       "Number of deposits",
                firstDepositPerson: "Number of first deposit players",
                withdrawPerson:     "Withdrawal times",
                regCount:           "Number of registrations",
                timesCount:         "Times",
                statisticalDate:    "Statistical Date",
                rechargeAmount:     "Deposit amount",
                withdrawalAmount:   "Withdrawal Amount",
                netDiff:            "Deposit-Withdrawal Difference",
                currentBalance:     "Current Balance",
            },
            directReceive:              {
                searchMemberID:          "Enter Member ID",
                statisticalDate:         "Statistical Date",
                totalClaimed:            "Total Claimed",
                activityClaimed:         "Event Claimed",
                taskClaimed:             "Mission Claimed",
                cashbackClaimed:         "Rebate Claimed",
                vipClaimed:              "VIP Claimed",
                agentCommission:         "Agent Commission",
                balanceTreasureInterest: "Interest Earnings",
            },
            pickUpRecordRoot:           {
                pickUpTime:         "Collection time",
                contributors:       "Contributors",
                amount:             "Amount",
                commissionReceived: "Commission Collected",
            },
            transferRecord:             {
                transferTime:        "Transfer time",
                transferInAccount:   "Transfer in account",
                transferAmount:      "Transfer",
                remark:              "Remark",
                totalTransferAmount: "Total Trasfer",
            },
            proportion:                 {
                level:           "Level",
                validBet:        "Valid Bets",
                rebateAmount:    "Commission",
                validNum:        "Valid players",
                netProfix:       "Net profit",
                profitRate:      "Commission Rate ",
                unitSuffix:      "(Unit: 10,000)",
                perRebateAmount: "Rebate amount per 10,000",
                perProfitRate:   "Rebate ratio per 10,000",
                footer:
                                 "Valid conditions: The recharge amount of the lower level is <em> ≥ {0}</em>, and the bet is valid <em> ≥ {1}</em>",
            },
            createAccount:              {
                username:
                                     "Please enter {range} characters including English/numbers/symbols",
                usernameValid:       "Account error, please re-enter",
                passwordLabel:       "Login Password",
                repeatPasswordLabel: "Confirm Password",
                password:
                                     "Please enter {range} characters including English/numbers/symbols",
                repeatPassword:      "Please enter login password again",
                passwordValid:       "Wrong password, please re-enter",
                passwordRepeat:      "Passwords do not match, please re-enter",
                verifyCodeLabel:     "Verification Code",
                verifyCode:          "Enter Verification Code",
                verifyCodeValid:     "Wrong verification code, please re-enter",
                submit:              "Submit Registration",
                model:               {
                    title:        "Registration Successful",
                    account:      "Account",
                    password:     "Password",
                    userAccount:  "Member account",
                    userPassword: "Login Password",
                    importTips:
                                  "Please remember or securely store it, you won't be able to retrieve it again once you leave this page.",
                    button:       "Copy and close",
                },
            },
            agentTransfer:              {
                usernamePlaceholder:       "Enter Member Account",
                password:                  "Password",
                passwordPlaceholder:       "Please enter the password",
                transferAmount:            "Transfer Amount",
                transferAmountPlaceholder: "Enter Transfer Amount",
                availableBalance:          "Balance",
                remark:                    "Remark",
                remarkPlaceholder:         "Enter a note within {length} characters",
                button:                    "Submit Transaction",
                selectPlaceholder:         "Select",
                settingPwdTitle:           "Set Up Withdrawal Password",
                settingPwdContent:         "Please set the withdrawal password first",
                transferSuccess:           "Transferred successfully!",
            },
            tutorialContent:            {
                national:       {
                    bigAgent:       {
                        0: "Total Commission <em>{0}</em>",
                        1: "Direct Performance {0}, Direct Commission <em>{1}</em>",
                        2: "Other Commission <em>{0}</em>",
                    },
                    direct:         {
                        validBet: "Valid Bets {0}",
                        b1Text:   "B1 Contribute Commission <em>{0}</em>",
                        b2Text:   "B2 Contribute Commission <em>{0}</em>",
                        b3Text:   "B3 Contribute Commission <em>{0}</em>",
                    },
                    other:          {
                        validBet: "Valid Bets {0}",
                        c1_1Text: "C1 contributes {0} to A from other {1}%",
                        c1_2Text: "C1 Contribute Commission <em>{0}</em>",
                        c2_1Text: "C2 Contribution Commission <em>{0}</em>",
                        c2_2Text: "C2 Contributes {0} to A from other {1}%",
                        c3_1Text: "C3 Contribution Commission <em>{0}</em>",
                        c3_2Text: "C3 contributes {0} to A from other {1}%",
                    },
                    tutorialDetail: {
                        content:
                            "<h1><strong>Example:</strong></h1><p>Assuming the current commission rate for valid bets is 1% (direct), and the subordinate's commission rate for their subordinate is 30% (other). A was the first person who discovered the opportunity and immediately recruited B1, B2, and B3; B1 further recruited C1 and C2; B2 had no subordinates; B3 further recruited C3. On the second day, B1's valid bets are 500, B2's valid bets are 3,000, B3's valid bets are 2,000, C1's valid bets are 1,000, C2's valid bets are 2,000, and C3's valid bets are 20,000.</p><span>The profit calculation between them is as follows:</span><ul><li><strong>1. B1's Commission</strong> (direct contributions from C1 and C2) = (1000+2000)* 1% = <em>30</em></li><li><strong>2. B2's Commission</strong> (no subordinates) = (0+0)* 1% = <em>0</em></li><li><strong>3. B3's Commission</strong> (direct contribution from C3) = 20000 * 1% = <em>200</em></li><li><strong>4. A's Commission</strong> (contributions from direct subordinates B1, B2, and B3, and other subordinates C1, C2, and C3) are as follows:<ul><li><strong>(1)A's Direct Commission</strong> (direct contributions from B1, B2, and B3) = (500+3000+2000) * 1% = <em>55</em></li><li><strong>(2)A's Other Commission</strong> (other contributions from C1, C2, and C3) = (1,000+2,000+20,000)*1%*30% = <em>69</em></li><li><strong>(3)A's Total Commission</strong> (direct + other) = 55+69 = <em>124</em></li></ul></li><li><strong>5. Summary:</strong><ul><li><strong>(1) A Explanation:</strong> A is the first to discover the opportunity, so he can always extract 30% from the other's subordinates, for example: C1 can contribute 1% * 30% to A, D1 can contribute 1% * 30% *30%, E1 can contribute 1% * 30% * 30% * 30%, infinite subordinates, and so on.</li><li><strong>(2) B Explanation:</strong> Although B is not the first to discover the opportunity, B can still always extract 30% from the subordinate's subordinate, for example: D1 can contribute 1% * 30% to B, E1 can contribute 1%* 30% *30%, F1 can contribute 1% * 30% * 30% * 30%, infinite subordinates, and so on.</li><li><strong>(3) C Explanation:</strong> Although C is discovered later, C can also always extract 30% from the subordinate's subordinate, for example: E1 can contribute 1%* 30% to C, F1 can contribute 1% * 30% *30%, G1 can contribute 1% * 30% * 30% * 30%, infinite subordinates, and so on.</li><li><strong>(4) Rule Summary:</strong> Whether A, B, or C, regardless of the level, as long as they work tirelessly to recruit subordinates infinitely, they can always extract 30% from the subordinate's subordinate. The obtained profits are equally fair, and there is no phenomenon of disadvantage for those who discover the opportunity later.</li></ul></li></ul>",
                    },
                },
                infinite:       {
                    bigAgent:       {
                        0: "Total Performance {0}",
                        1: "Total Commission <em>{0}</em>",
                        2: "Sub's Perf.{0}",
                        3: "Rebate {0}% direct commission <em>{1}</em>",
                        4: "Others' Perf. {0}",
                        5: "Tiered commission {0}% other commission <em>{1}</em>",
                    },
                    direct:         {
                        validBet: "Valid Bets {0}",
                        b1Text:   "B1 Contribute Commission <em>{0}</em>",
                        b2Text:   "B2 Contribute Commission <em>{0}</em>",
                        b3Text:   "B3 Contribute Commission <em>{0}</em>",
                    },
                    other:          {
                        validBet: "Valid Bets {0}",
                        c1_1Text:
                                  "C1 Contributes to A <em>{0}</em><br> From differentials {1}%",
                        c1_2Text: "C1 Contribute Commission <em>{0}</em>",
                        c2_1Text: "C2 Contribution Commission <em>{0}</em>",
                        c2_2Text:
                                  "C2 Contribution to A <em>{0}</em><br> From Tier Differential {1}%",
                        c3_1Text: "C3 Contribution Commission <em>{0}</em>",
                        c3_2Text:
                                  "C3 Contributes to A <em>{0}</em><br> Infinite difference",
                    },
                    tutorialDetail: {
                        content:
                            "<h1><strong>Example:</strong></h1><p>Assuming the current commission rate for valid bets between 0-10,000 is 100 for every 10,000 bets (1%), and for bets above 10,000 is 300 for every 10,000 bets (3%). A is the first to discover the opportunity and immediately recruits B1, B2, and B3; B1 further recruits C1 and C2; B2 has no subordinates; B3 recruits the strong C3. On the second day, B1's valid bets are 500, B2's valid bets are 3,000, B3's valid bets are 2,000, C1's valid bets are 1,000, C2's valid bets are 2,000, and C3's valid bets reach 20,000.</p><span>The profit calculation between them is as follows:</span><ul><li><strong>1. B1's Commission</strong> (direct contributions from C1 and C2) = (1,000+2,000) * 1% = <em>30</em></li><li><strong>2. B2's Commission</strong> (no subordinates) = (0+0) * 1% = <em>0</em></li><li><strong>3. B3's Commission</strong> (direct contribution from C3) = 20,000 * 3% = <em>600</em></li><li><strong>4. A's Commission</strong> (direct contributions from subordinates B1, B2, and B3, and other subordinates C1, C2, and C3) are as follows:<ul><li><strong>(1) A's Direct Commission</strong> (direct contributions from B1, B2, and B3) = (500+3,000+2,000) * 3% = <em>165</em></li><li><strong>(2) A's Other Commission</strong> (other contributions from C1, C2) = (1,000+2,000) * 2% = <em>60</em></li><li><strong>(3) A's Total Commission</strong> (direct + other) = 165 + 60 = <em>225</em></li></ul></li><li><strong>5. Summary:</strong><ul><li><strong>(1) Direct Team:</strong> Refers to the direct subordinates recruited by A, i.e., the direct relationship with A, collectively referred to as the direct team.</li><li><strong>(2) Other Teams:</strong> Refers to the subordinates recruited by A's subordinates, which are beyond the other relationship with A, i.e., subordinates of subordinates, subordinates of subordinates of subordinates, and so on, collectively referred to as other teams; because this agency model can recruit subordinates infinitely, for the sake of explanation, this article only takes a 2-tier structure as an example.</li><li><strong>(3) Explanation of A:</strong> A's direct performance is 5,500 and another performance is 23,000 (due to the strong performance of C3), with a total performance of 28,500, corresponding to a commission rate of 3%. Since B1's total performance is 3,000, only enjoying a 1% commission, while A's total performance is 28500, enjoying a 3% commission rate, there is a tier differential between A and B1, the difference being: 3% - 1% = 2%, this difference is the part contributed by C1 and C2 to A, so C1 and C2 contribute to A: (1,000+2,000) * 2% = 60, there is no tier differential between A and B3, so C3 contributes no commission to A.</li><li><strong>(4) Explanation of B1:</strong> B1 has subordinates C1 and C2, with a direct performance of 3,000, corresponding to a commission rate of 1%.</li><li><strong>(5) Explanation of B2:</strong> B2 may be lazy and not recruit subordinates, thus no income.</li><li><strong>(6) Explanation of B3:</strong> Although B3 joined late and belongs to A's subordinate, its subordinate C3 is strong, with a direct performance of 20,000, allowing B3 to directly enjoy a higher commission rate of 3%.</li><li><strong>(7) Rule Summary:</strong> Regardless of when you join, under whose subordinate, and no matter what level you are in, your income is never affected. You no longer suffer from the losses of others' subordinates, and your recruitment is not restricted. This is a fair and just agency model, and joining late does not mean you will always be at the bottom.</li></ul></li></ul>",
                    },
                },
                netProfit:      {
                    bigAgent:       {
                        0: "Total Commission <em>{0}</em>",
                        1: "Direct Performance {0} Direct Commission <em>{1}</em>",
                    },
                    direct:         {validBet: "Net profit {0}"},
                    a:              {},
                    b1:             {},
                    b2:             {},
                    b3:             {},
                    tutorialDetail: {
                        content:
                            "<h1><strong>Example:</strong></h1><p>Assuming the current net profit commission rate is 1% (i.e., direct), A is the first to discover the opportunity and immediately develops B1, B2, and B3. On the second day, B1's net profit is 500, B2's net profit is 3,000, and B3's net profit is -2,000.</p><span>So A's profit calculation is as follows:</span><ul><li><strong>1. A's Commission</strong> (contributions from direct B1, B2, and B3) = (500+3,000-2,000) * 1% =<em>15</em></li><li><strong>2. Summary</strong><ul><li><strong>(1) Net Profit:</strong> For example, if a user contributed 2,000 during the settlement period (i.e., the platform made a profit of 2,000) and received a 100 discount from the platform during that period, the net profit is 1,900. If a user contributed -2,000 during the settlement period (i.e., the platform lost 2,000), and received a 100 discount from the platform during that period, the net profit is -2,100.</li><li><strong>(2) Explanation of the Calculation Method:</strong> This model is an example based on all members. If only loss-making members are considered, then those with negative net profits are not included in the calculation.</li><li><strong>(3) Explanation of A:</strong> Because B3's net profit is negative, A's total net profit is 1,500, i.e., the total performance of B1, B2, and B3 is 1,500, resulting in a contribution commission of 15.</li></ul></li></ul>",
                    },
                },
                firstLevel:     {
                    bigAgent:       {
                        0: "Total performance {0}",
                        1: "Commission ratio {0}% total commission <em> {1} </em>",
                    },
                    tutorialDetail: {
                        content:
                            "<h1><strong>Example:</strong></h1><p>Assuming the current valid bets within the range of 0-10,000 receive a commission of 100 for every 10,000 (i.e., 1%), and for bets above 10,000, there is a commission of 300 for every 10,000 (i.e., 3%). A is the first to discover the opportunity on this Website and immediately recruits B1, B2, and B3. On the second day, B1 places bets of 3,000, B2 places bets of 5,000, and B3 places bets of 6,000.</p><span>So A's profit calculation is as follows:</span><ul><li><strong>1. A's Commission</strong> (contributions from B1, B2, and B3) = (3,000+5,000+6,000)*3% = <em>420</em></li><li><strong>2. Summary</strong><ul><li><strong>(1) Explanation of A:</strong> Because the total valid bets of A's direct subordinates B1, B2, and B3 are 14,000, the corresponding commission rate is 3%, resulting in a commission contribution of 420 to A.</li><li><strong>(2) Rule Summary:</strong> The more direct subordinates you recruit, the more commission you earn.</li></ul></li></ul>",
                    },
                },
                threeNetProfit: {
                    bigAgent:       {
                        0: "Total Performance {0}",
                        1: "Sub's Perf.{0}",
                        2: "Direct Commission <em>{0}</em>",
                        3: "Others' Perf. {0}",
                        4: "Reward Percentage {0}% Other Commission <em>{1}</em>",
                    },
                    other:          {
                        c1_1Text: "C1 contributes {0} to A from other {1}%",
                        c1_2Text: "C1 Contribute Commission <em>{0}</em>",
                        c2_1Text: "C2 Contribution Commission <em>{0}</em>",
                        c2_2Text: "C2 contributes {0} to A from other {1}%",
                        c3_1Text: "C3 Contribution Commission <em>{0}</em>",
                        c3_2Text: "C3 contributes {0} to A from other {1}%",
                        d1_1Text: "D1 Commission Contribution <em>{0}</em>",
                        d2_2text: "D1 Contribution to B1 <em>{0}</em>",
                    },
                    level:          {
                        0: "Level 1 members <br/> (direct)",
                        1: "Level 2 members <br/> (others)",
                        2: "Level 3 members <br/> (others)",
                    },
                    tutorialDetail: {
                        content:
                            "<h1><strong>Example:</strong></h1><p>Assume that the rebate ratio of the current net profit is 1% (i.e. direct subordinates), and the rebate ratio of subordinates is 30% (i.e. other) , A was the first to discover business opportunities, and immediately developed B1, B2 and B3; B1 further developed C1 and C2; B2 had no subordinates; B3 developed C3, and C1 developed D1. The next day B1's net profit is 500, B2's net profit is 3000, B3's net profit is -2000, C1's net profit is 1000, C2's net profit is 2000, C3's net profit is 20000, D1's net profit is -3000. </p><span>Then the income calculation method between them is as follows: </span><ul><li><strong>1. C1’s commission (direct contribution from D1)</strong>=0*1%= 0</li><li><strong>2. In addition to B1’s direct subordinates C1 and C2, B1’s commission also comes from contributions from other subordinates D1, as follows: </strong><ul><li></strong>(1)B1’s direct commission</strong>(direct contribution from C1 and C2) =(1000+2000)* 1%=<em>30</em></li><li><strong>(2 )B1’s other commission</strong>(D1 contribution)=0*0.1%*30%=<em>0</em></li><li><strong>(3)B1’s total commission</strong >(direct + others) = 30+0 =<em>30</em></li></ul></li><li><strong>3. B2’s commission</strong> (no subordinates) =(0+0)* 1%=<em>0</em></li><li><strong>4. B3’s commission</strong> (direct contribution from C3) = 20000 * 1%=<em >200</em></li><li><strong>5. In addition to contributions from direct subordinates B1, B2 and B3, A’s commission also comes from contributions from other subordinates C1, C2, C3 and D1, as follows: :</strong><ul><li><strong>(1) A’s direct commission</strong>(direct contributions from B1, B2 and B3) = (500+3000-2000) * 1%=<em>15 </em></li><li><strong>(2)A’s other commissions</strong>(from C1, C2 and C3 and D1 contribution)=(1000+2000+20000+0)*1% * 30%=<em>69</em></li><li><strong>(3)A’s total commission</strong>(direct + others) = 15+69 =<em>84</em> </li></ul></li><li><strong>6. Summary </strong><ul><li><strong>(1) Net profit: </strong>For example, during the settlement period, the user If the user contributed 2000 during the settlement period (i.e. the platform made a profit of 2000) and received 100 discounts from the platform, the net profit would be 1900; if the user contributed -2000 during the settlement period (i.e. the platform lost 2000) and received 100 discounts from the platform during the period , then the net profit is -2100. </li><li><strong>(2) Calculation method description: </strong>This model is based on all members. If only loss-making members are calculated, the net profit will not be calculated if it is a negative number. </li><li><strong>(3) A’s explanation: </strong>A is the first to discover business opportunities, so he can always extract 30% of the subordinates from subordinates and the subordinates of subordinates. Among other commissions, because D1’s net profit is negative, the commission contributed by D1 to A is 0. The commission for the contributions of C1, C2 and C3 is 69. In the direct commission, because the net profit contributed by B3 is a negative number, the corresponding negative net profit needs to be subtracted, that is, the total direct commission contributed is 15. </li><li><strong>(4) Description of B1: </strong>Although D1 is another subordinate of B1, no commission is generated, so B1 has no other commission. Only C1 and C2 generated direct commissions for B1, which is 30. </li><li><strong>(5) B2 explanation: </strong>Because B2 has no subordinates, there is no commission for B2. </li><li><strong>(6) B3 explanation: </strong>Because C3 has no subordinates, B3 has no other commissions, only direct commissions, and it is relatively powerful, so it only has direct commissions of 200. </li><li><strong>(7) C1 explanation: </strong>Although D1 is a subordinate of C1, D1’s net profit is negative, so it does not contribute commission to C1, that is, C1’s commission is 0. </li><li><strong>(8) Summary of rules: </strong>No matter which level it is, A, B, C, or D, as long as the platform is profitable, the income will not be affected. The benefits obtained are all equally fair, and there is no such thing as a loss after discovering business opportunities</li></ul></li></ul>",
                    },
                },
            },
        },
        reward:                  {
            base:  {
                topVip:              "Congratulations! You are now the highest VIP level!",
                unReceive:           "Claim",
                noReceive:           "Reward",
                notReceive:          "Not Reached",
                allReceive:          "Claim All",
                distance:            "Remaining",
                needBet:             "Required bet amount of",
                other:               "Other",
                expired:             "Expiring in {0} days",
                expiredTime:         "{x} expired",
                resetTime:           "Reset after {x}",
                expiredHour:         "Expiring in {0}h",
                available:           "Available to claim in {0} days",
                availableHouur:      "Available to claim in {0} hours",
                canReceive:          "Claim",
                hasReceive:          "Claimed",
                awardAmount:         "Bonus",
                otherAward:          "Other rewards",
                hasExpired:          "Expired",
                hasSend:             "Distributed",
                toBeCollected:       "Reward",
                CPF:                 "",
                current:             "Current Level",
                rewardProgress:      "Claim Progress",
                receiving:           "Claiming",
                rewardSuccess:
                                     "Congratulations on receiving the following rewards!",
                gotoHome:            "Back to homepage",
                activity:            "Active level",
                award:               "Bonus",
                tip1:                "Total {x} items, <span>{y} items</span> successfully claimed",
                tip2:                "Claimed: Bonus {0}",
                tip3:                "Active level{0}",
                tip4:                "Failed <span>{x}</span> times",
                templateTitle:       "Reward source",
                isTopVip:            "Congratulations, you are now the highest VIP level!",
                receivedErrorAll:    "",
                receivedError:       "",
                availableCollection: "Available for claiming after {x}",
                claimedBonus:        "Received: <span> bonus {x} </span>",
                activeLevel:         "Active level {x}",
            },
            vip:   {
                rewardMap: {
                    vipPromote:           "VIP {0} Benefits",
                    power:                "VIP Privilege",
                    dailyWithdrawalLimit: "Daily withdrawal total limit",
                    numberWithdrawals:    "Daily withdrawal count limit",
                    freeTimes:            "Number of fee-free transactions per day",
                    promotion:            "Promotion Bonus",
                    day:                  "Daily Bonus",
                    week:                 "Weekly Bonus",
                    month:                "Monthly Bonus",
                    relegation:           "Relegation Requirements",
                    needBets:             "Still need to wager {0} to claim",
                    vipProgressTips1:     "Remaining",
                    vipProgressTips2:     "amount for deposit <strong>{0}</strong>",
                    vipProgressTips3:     "Coding <strong>{0}</strong>",
                    vipProgressTips4:     "Coding <strong>{0}</strong>",
                    needRecharge:         "Deposit for promotion",
                    needBet:              "Bet for promotion",
                    validNeedBet:         "Bet for promotion",
                    monthRelegation:      "Relegation Requirements",
                    needLack:             "Need <strong>{0}</strong> for a promotion",
                    retainLack:           "Need <strong>{0}</strong> avoid relegation",
                    rewardTips0:          "Countdown to receive {x}",
                    rewardTips5:          "Countdown for claim available",
                    rewardTips1:          "",
                    rewardTips2:          "",
                    rewardTips3:          "",
                    rewardTips4:          "",
                    rewardTimeTips:       "Available for claiming after {x}",
                },
                table:     {
                    vipLevelMap:      "VIP Level List",
                    level:            "Level",
                    validBet:         "Total Valid Bets",
                    vipGift:          "Promotion Bonus",
                    dayBonus:         "Daily bonus",
                    weekBonus:        "Weekly Bonus",
                    monthBonus:       "Monthly Bonus",
                    depositChannel:   "Deposit acceleration",
                    personalServer:   "Exclusive Support",
                    totalDeposit:     "Total Deposit",
                    totalBet:         "Wager",
                    lastWeekDeposit:  "Deposit last week",
                    lastWeekBet:      "Turnover last week",
                    lastMonthDeposit: "Last Month's Deposits",
                    unmetTips:        "Decrease one level if not met",
                    lastMonthBet:     "Last month's bets",
                    dayDeposit:       "Daily Deposit",
                    dayBet:           "Daily Wager",
                    validDayBet:      "Daily valid bets",
                    dayBonusLimit:    "Bonus",
                    weekDeposit:      "Weekly Deposit",
                    weekBet:          "Weekly Wagering",
                    validWeekBe:      "Weekly valid bets",
                    weekBonusLimit:   "Bonus",
                    monthDeposit:     "Monthly Deposit",
                    monthBet:         "Wagered this month",
                    validMonthBet:    "Monthly valid bets",
                    monthBonusLimit:  "Bonus",
                    needRechargeTips:
                                      "To promote to the next level, you need to deposit an additional amount based on the accumulated deposit. For example: to upgrade from VIP1 with a deposit requirement of 1000 to VIP2 with a requirement of 2000, the member needs to accumulate 1000 + 2000 = 3000 to upgrade to VIP2, and so on.",
                    needBetTips:
                                      "That is, the amount of promotion to the next level needs to be coded on the basis of the original coding coding, for example: advanced VIP1 coding requirements 1000, advanced VIP2 coding requirements 2000, then members need to cumulate 1000+2000 = 3000 to only be able to take only Promise to VIP2, so on.",
                    validNeedBetTips:
                                      "To upgrade to the next level, you must wager a certain amount of money in addition to your existing cumulative valid bets. For example, the valid bet requirement for upgrading to VIP1 is 1000, and the valid bet requirement for upgrading to VIP2 is 2000. Therefore, members need to accumulate a total of 1000+2000=3000 valid bets to upgrade to VIP2. This applies to all levels.",
                },
                rules:     "VIP Rules Explanation",
                lack:      "Need <strong>{0}</strong>",
            },
            award: {
                table: {
                    time:    "Time",
                    name:    "Name",
                    reward:  "Bonus",
                    operate: "Action",
                },
            },
        },
        customersQuestionDetail: {title: "Problem Details"},
        service:                 {
            customerLiteral:   "\nSupport",
            commonQuestion:    "FAQ",
            hotQuestion:       "Hot concerns",
            contact:           "Contact method",
            copy:              "Copy",
            copyAndOpen:       "Copy to Open",
            immediateContract: "Contact Now",
            search:            "Search",
            weChatLiteral:     "WeChat Customer Service",
            customer:          "Other Customer Service",
            questionRem:       "Didn't find a solution? Please contact",
            online:            {
                title:   "24/7 Customer Service",
                describe:
                         "Chat with the professional customer service online to solve your problems.",
                consult: "Real-time Consultation",
            },
        },
        feedback:                {
            demoModeNotReceived:    "Streamer accounts cannot claim bonuses.",
            toBeCollected:          "Reward",
            toBeCollectedAll:       "Claim All",
            empty:                  "No feedback yet",
            createFeedback:         "Create Feedback",
            mineFeedback:           "My Feedback",
            feedbackContent:        "Feedback Content",
            feedbackReply:          "Official reply",
            feedbackSubtitle:       "Suggestions for revision",
            feedbackContentMessage: "Feedback cannot be empty",
            feedbackPlaceholder:
                                    "Your opinions are valuable to us. All valuable opinions will be accepted, and once accepted, will be rewarded with cash prizes according to their usefulness. We welcome your opinions!",
            feedbackUpload:         "Pictures don't lie",
            feedbackUploadSubtitle: "Easier to be adopted",
            feedbackUploadPlaceholder:
                                    "Supports image and video format, size should not exceed 50MB",
            feedbackRuleTitle:      "Reward Rules",
            feedbackRule:
                                    "We've set up huge bonuses specifically to collect feedback so that we can optimize the system and features to give you a better experience! Once accepted, rewards will be given based on the usefulness (except those not accepted)",
            submit:                 "Submit feedback",
            feedbackTips:           "Thank you for your valuable feedback",
            geetest:                "Please click to verify",
            feedbackID:             "Feedback ID",
            replyContent:           "Official reply",
            award:                  "Bonus",
            pendding:               "Pending",
            adopted:                "Adopted",
            ignored:                "Not adopted",
            deleted:                "Deleted",
        },
        yuebao:                  {
            currentTransferIn: "Balance",
            currentProfix:     "Current Earnings",
            yuebaoProfix:      "Interest Earnings",
            yearRate:          "APR(%)",
            totalIncome:       "Total Earnings",
            allType:           "All Types",
            transferIn:        "Deposit",
            transferOut:       "Withdraw",
            receive:           "Claim",
            profix:            "Earnings",
            receiveProfix:     "Claim Earnings",
            manualPull:        "Manual Retrieval",
            profixRecord:      "Earnings Statement",
            changeRecord:      "Interest Statement",
            rule:              "Interest Rules",
            receiveSussessTip: "Claimed successfully!",
            hourProfixTips:    "(Earnings every hour)",
            table:             {
                time:         "Time",
                type:         "Type",
                changeAmount: "Adjustment",
                incomeAmount: "Earnings amount",
            },
            ruleContent:       {
                desc: {
                    0: "Interest Rules",
                    1: "1. The amount deposited into the Interest will generate earnings after one hour.",
                    2: "2. Earnings are calculated and settled every hour at the beginning of each hour.",
                    3: "3. Each deposit should be at least {0}, and withdrawals are limited to the Interest balance. Earnings must first be claimed to the Interest account before withdrawal.",
                    4: "4. The claimed earnings can be withdrawn once they meet {0} times the value of valid bets.",
                },
                plan: {
                    0: "Earnings Calculation",
                    1: "Hourly Earnings = Interest Balance × Annual Percentage Rate ÷ 365 days ÷ 24 hours",
                    2: "Example: ",
                    3: "With an annual percentage rate of {gold1}%, if Player A initially had 0 in their interest balance and deposited 10K at 12:30, earnings are calculated starting from 14:00 every hour:",
                    4: "10K*{gold2}% ÷ 365 days÷ 24 hours=<em>{gold3}</em>",
                },
            },
            modal:             {
                all:                    "All",
                balance:                "Account Balance",
                transferInAmount:       "Transfer Amount",
                transferInAmountTips:   "Please enter the transfer amount",
                transferInPlaceholder:  "Min deposit {amount}",
                transferOutAmount:      "Transfer Amount",
                transferOutAmountTips:  "Please enter the transfer-out amount",
                transferOutPlaceholder: "Maximum {amount} per transaction",
                receiveTime:            "Expected time for credited earnings: {time}",
                withdrawPassword:       "Verify Withdrawal Password",
                confirmIn:              "Transfer in",
                confirmOut:             "Confirm withdrawal",
                transferSuccess:        "Successfully Transfer",
                transferOutSuccess:     "Successfully Transfer",
                transferOutErrorTips:   "Please enter the correct amount",
            },
        },
        interest:                {
            deposited:                  "Deposited",
            yearRate:                   "APR {rate}%",
            billingCycle:               "Settlement period",
            minute:                     "{minute} minutes",
            hour:                       "{hour} hours",
            day:                        "{day} Days",
            collectedTotal:             "Total claimed",
            interestCap:                "Interest cap",
            cycle:                      "{number} cycles",
            toCollected:                "Reward",
            countdownReceive:           "Countdown to claim",
            firstDepositReceive:        "First Deposit to receive {amount}",
            rule:                       "Interest rules",
            recordDetails:              "Record details",
            NotLimited:                 "Unlimited",
            transferInPlaceholder:
                                        "Minimum single transfer amount {amount}, integers only",
            accountBalance:             "Account Balance",
            transferTips:
                                        "Transfers in and out will not undergo verification, only claimed interest is required",
            countdownSettlement:
                                        "Countdown to the current settlement {time}s",
            transferOutTips:
                                        "You will lose the interest for this cycle if you transfer out!",
            currentTime:                "Current time {time}",
            transferInTips:
                                        "After this deposit, the first interest generation will occur at: {time}",
            receiveTips:                "",
            currentInterest:            "Current interest",
            transferOutPlaceholder:
                                        "Max. amount for this transaction: {amount}",
            anchorAccountNoPermissions: "Streamer account have no permission",
            notOpen:                    "Interest is not yet available",
            ruleContent:                {
                desc: {
                    0: "",
                    1: "",
                    2: "",
                    3: "",
                    4: "",
                    5: "",
                    6: "",
                    7: "",
                    8: "",
                },
                plan: {},
            },
        },
        receiveModal:            {
            kindTips: "Reminder",
            eventContent:
                      "Different events have different turnover requirements. Please carefully read the rules before claiming. Confirming receipt is equivalent to defaulting to all event  rules",
            ok:       "Confirm to claim",
            gotIt:    "I understand, do not remind me",
        },
        browser:                 {
            openMethod1:         "Method one",
            openMethod1Step1:    "Clicked",
            openMethod1Step1End: "Open menu bar",
            openMethod1Step2:    "Select",
            openMethod1Step2End: "Open in browser",
            or:                  "or",
            openMethod2:         "Method two",
            openMethod2Step1:    "Copy",
            openMethod2Step2:    "Paste into browser to open",
        },
        theme16_dz:              {
            playNow:                        "Play Now",
            jackpotTitle:                   "Play for free, the award is waiting for you",
            downloadTitle:                  "Very fast, stable, tentacles and reach",
            downloadSubTitle:               "Get the latest information anytime, anywhere",
            downloadTips:                   "Download software",
            featuredGames:                  "Featured games",
            asideList:                      {
                games:      "Game",
                promotions: "Promotion",
                help:       "Help",
                functions:  "Function",
                member:     "Member",
            },
            freeGet:                        "Get free",
            selven$:                        "R $ 7.00",
            entertainmentGalore:            "Entertainment feast",
            entertainmentFeast:             "Entertainment feast",
            entertainmentFeastTips:
                                            "Get together the world's leading slot game platform, thousands of slot games are waiting for you to play!",
            CategoriesGamePCTips:           "",
            famousProvider:                 "Famous Provider",
            categoriesGameTip_CHESS:
                                            "Gathered here are the world's most popular card games, with tens of thousands of players engaged in online battles and intense PK!",
            categoriesGameTip_FISH:
                                            "Let's enjoy the largest fishing ground in the history, where giant fish full of the screen. Gain explosive wins with only one shot!",
            categoriesGameTip_ELECTRONIC:
                                            "Gather the world's top Slot gaming platforms, thousands of slot machine games are waiting for you to fight!",
            categoriesGameTip_REAL:
                                            "The most beautiful live dealers are available for online casinos, guiding you through various live casino games, including baccarat and more!",
            categoriesGameTip_SPORTS:
                                            "Featuring the world's highest odds markets, offering a diverse range of betting options such as handicap, over/under, halftime/fulltime, correct score, odd/even goals, total goals, combination bets, and more!",
            categoriesGameTip_COCKFIGHTING: "",
            categoriesGameTip_GAMING:
                                            "All global esports, ranging from major to minor, are available here.",
            categoriesGameTip_LOTTERY:
                                            "Dedicating years to the lottery gaming industry, we offer classic lottery types and gameplay.\r\n",
            categoriesGameTip_BLOCK_CHAIN:
                                            "Our explosive blockchain games provide an exciting and innovative entertainment experience.\r",
        },
        theme26_dz:              {
            more:     "More",
            home:     {
                ty:       "Sports Games",
                tydesc:   "HOT top events, enjoy the event storm",
                hgty:     "Crown Sports",
                dz:       "Electronic Games",
                dzdesc:
                          "Gather the world's top Slot gaming platforms, thousands of slot machine games are waiting for you to fight",
                pgdz:     "PG Slot",
                zr:       "Live Casino",
                zrdesc:
                          "The most beautiful dealer interacts online, taking you to play various live games such as baccarat",
                agzr:     "AG Live",
                qp:       "Card Games",
                qpdesc:
                          "Gather the world's most popular card games, with thousands of people online for battle, and hot PK",
                wgqp:     "WG Cards",
                by:       "Fishing Games",
                bydesc:
                          "Legendary fishing grounds await! Cast your line and reel in giants. Hook a dozen in a single day!",
                wgby:     "WG Fishing",
                cp:       "Lottery Games",
                cpdesc:
                          "Dedicated to the lottery gaming industry for many years, offering classic lottery types and gameplay",
                sycp:     "Win-win lottery",
                dj:       "E-Sports",
                djdesc:
                          "All global esports, ranging from major to minor, are available here.",
                sbdj:     "Saba Esports",
                qkl:      "Blockchain",
                qkldesc:
                          "Use blockchain technology for encryption verification to create a fair and just platform",
                jdbqkl:   "JDB Blockchain",
                douji:    "Cockfight",
                doujidesc:
                          "Exclusive on the entire network, high odds, simple and efficient interface, helping you get started easily",
                svdouji:  "SV Cockfighting",
                zq:       "Football",
                lq:       "Blueball",
                wq:       "Tennis",
                snk:      "Snooker",
                pq:       "Volleyball",
                f1:       "F1 Racing",
                bq:       "Baseball",
                glq:      "Rugby",
                more:     "More",
                moreGame: "More Games",
            },
            header:   {
                recharge:         "Deposit",
                withDraw:         "Withdraw",
                membershipCenter: "Membership center",
                helpCenter:       "Help Center",
                serviceCenter:    "Service Center",
            },
            download: {
                minTitle:  "Mobile betting platform for full network players",
                textTitle: "Anytime, anywhere • Enjoy games • Play at will",
                description:
                           "The industry's top entertainment native APP is smooth and stable, convenient and fast. Provide entertainment projects such as sports events, live entertainment, electricity events, chess games, lottery betting, electronic entertainment and other entertainment. Sweep the code download, immediately own!",
            },
            footer:   {activeTitle: "Membership center"},
            vip:      "VIP",
        },
        theme24_dz:              {
            header: {download: "Download APP"},
            home:   {
                downloadAppTxt1: "Free download",
                downloadAppTxt2: "RP888 APP",
                downloadAppTxt3: "Enjoy the game that is posted immediately",
                description:     {
                    title0:
                        "RP888: List Pragmalic Play & Slot88 Slot Machine Website Online",
                    description0:
                        "RP888 is a pragmatic slot machine game website and the best trustworthy online slot machine 88 in Indonesia. Before joining the online slot machine website, an important thing that must be considered and paid special attention to is the provider provided. Here, our RP888 is one of the best online slot options, providing game options from the largest and suitable providers. For example, you can enjoy the slot game options provided by practical game provider Play, and you can also enjoy the very famous Slot88. '",
                    title1:
                        "RP888 as a pragmatic game & slot88 best and trustworthy slot machine website",
                    description1:
                        "The pragmatic game itself is the best and most trusted slot machine gambler in the world. He has stood for decades. The provider is developed by various party or teams with the best qualifications, so it can provide a variety of high -quality game choices, even the most popular games in the world. At present, this supplier provides hundreds of online slot games collection. Even a small crow with a high -level crow or a high -level crow is no exception.",
                    description2:
                        "In addition, Slot88 is also the most famous best slot provider option. This is the provider of GACOR slot machine games. The game also collects many complete online slot games. There are also many options for the slot of the high RTP slot collection. In addition to the pride of the game system, such as the HTML5 that has been supported, it can be compatible with the desktop and mobile versions, and the various benefits provided by the Slot88 website.",
                    title2:
                        "15 Best and Popular GACOR slot machine website list group 2023",
                    description3:
                        "At present, RP888 provides many best and most popular slot machine websites. Not only does the Pragmatic Play & Slot88 provide games, but also the game options of various other slot machine providers. At present, the existence of slot machine providers for the development of online slot machines is indeed increasing. As a player, of course, we also have a lot of confusion, we don't know how to choose the best, most suitable, and accurate choice.",
                    description4:
                        "But here, we will recommend some list of slot machine provider options. These options are proven to be incorrect and very popular. Many are chosen by other gambling enthusiasts. Simulate some suggestions and reference as follows:",
                },
                midContent:      {leftTitle: "Slot machine winner"},
                footer:          {
                    bigTitle1:     "Option",
                    bigTitle2:     "Product",
                    bigTitle3:     "Class $ Support",
                    bigTitle4:     "Deposit",
                    bigTitle5:     "Partnership",
                    bigTitle6:     "Slot machine betting",
                    bigTitle7:     "Toger",
                    bigTitle8:     "Live casino gaming",
                    bigTitle9:     "Sports gaming",
                    bigTitle10:    "Related to us",
                    minTitle1:     "Server appearance",
                    minTitle2:     "Product preference",
                    minTitle3:     "Other service",
                    minTitle4:     "Time rate",
                    minTitle5:
                                   "Online slot machine provider with a variety of options provides players with convenient and interesting games, Unhuk reaches Jackpol",
                    minTitle6:
                                   "The company's interesting to move the best gift in the world is the great victory of the world",
                    minTitle7:
                                   "Fermany game variant options in the world's best company in the company's selection platform",
                    minTitle8:
                                   "SportsBook Gaming Plalform best provides more games, more fingertips, and provide more choices to provide players with more.",
                    bigTitle11:    "Response to play",
                    bigTitle12:    "Supported browser",
                    platformTitle: "Service provider platform",
                    tel:           "",
                    tg:            "@RP888com",
                },
            },
            aside:  {yuebao: "Interest"},
            tabbar: {service: "Support"},
        },
        theme21_dz:              {
            footer: {
                hundredMillion:  "100 Million",
                seconds:         "Seconds",
                many:            "Home",
                totalUser:       "Cumulative number of users",
                saveTime:        "Average deposit time",
                withdrawTime:    "Average withdrawal time",
                partnerPlatform: "Cooperative game platform",
                onlineServie:    "Online customer service service",
                safeTitle:       "Security",
                safeDesc:
                                 "Using encrypted security channels (256 -bit SSL encryption standards) and strict security management system, customer information funds are the most complete guarantee.",
                gameTitle:       "Massive game",
                gameDesc:
                                 "The world's most complete game platform, professional team protects you, 10,000 electronics play with you, so that you have the perfect game experience",
                fastTitle:       "Speed ​​and convenient",
                fastDesc:
                                 "The platform adopts the latest financial processing system independently developed by technology to provide you with a first -class gaming experience and maximize network latency.",
                extrmTitle:      "Enjoy the extreme",
                extrmDesc:
                                 "Autonomous development of full -set applications allows you to enjoy Web and H5, and let you entertain betting anytime, anywhere, and entertainment betting!",
                slogan:          "Different differences have extraordinary differences",
                changeLang:      "Language switch",
            },
            header: {searchHotGame: "Hot game search"},
        },
        theme20_dz:              {
            licenseTitle:   "Qualification license",
            payMethods:     "Payment methods",
            followUs:       "",
            event:          "Event",
            waitingCollect: "Reward",
        },
        theme18_dz:              {
            account: {
                myAccount:    "My account",
                confirmLogin: "Please log in to your </br>account first",
            },
            mid:     {
                task:          "Earn 1 million every month",
                activityTitle: "Invite friends",
            },
            game:    {
                noGame:        "No available games",
                searchHotGame: "",
                about:         "",
            },
        },
        theme36_dz:              {
            aside: {
                discount: "Offer Center",
                bindBank: "Link bank card"
            },
        },
        theme25_dz:              {
            footer: {
                step1:    "first step",
                step2:    "Step 2",
                step3:    "Third step",
                context1: "Register",
                context2: "Deposit",
                context3: "Slot game",
            },
        },
        theme35_dz:              {
            game:     {nav: {hello: "Hello"}},
            download: {
                helpAndSupport:    "Help and support",
                responsibleGaming: "Responsible Gambling",
                userAgient:        "User Agreement",
                bookName:          "Certificate name",
                project:           "Protection",
                payType:           "Payment method",
                videoConcern:      "Channel follow",
                play:              "Play responsibly",
                conditions:        "Terms and Conditions",
            },
        },
        theme31_dz:              {
            download: {
                title: "Download the APP and log in, free <span>R$100,00<span>",
            },
            promote:  {
                title:         "Invite friends",
                promoteShare:  "Promotion Sharing",
                myMoney:       "Commission",
                myPerformance: "Performance",
                myData:        "My Data",
                linkTitle:     "My link",
                promoteTitle:
                               "Promote customers to become agents, earn up to 2.5%of agency commissions, and easily earn monthly income R$1.000.000,00",
                unloginTips:   "Please log in to get the agent link",
                win777:        "",
            },
        },
        theme28_dz:              {
            download: {
                getItOn:    "Get it on",
                googleText: "GOOGLE PLAY",
                appText:    "APP STORE",
            },
        },
        officialChannel:         {
            title:           "Official channel",
            thirdPartyLogin: {
                facebook: "Facebook Login",
                google:   "Google Login",
                line:     "Line Login",
                wechat:   "WeChat Login",
                qq:       "QQ Login",
            },
        },
        theme10_dz:              {
            allGame:  {title: "All games"},
            game:     {
                pgGame:       "PG Games Casino",
                pgGameTips:   "PG games are in full swing",
                btn:          "Click here",
                chess:        "Cards",
                fish:         "Fishing",
                gaming:       "E-Sports",
                electronic:   "Slot",
                blockChain:   "Blockchain",
                sports:       "Sports",
                real:         "Live",
                lottery:      "Lottery",
                cockfighting: "Cockfight",
                chessTips:
                              "Gather the world's most popular card games, with thousands of people online for battle, and hot PK",
                fishTips:
                              "Legendary fishing grounds await! Cast your line and reel in giants. Hook a dozen in a single day!",
                gamingTips:
                              "All global esports, ranging from major to minor, are available here.",
                electronicTips:
                              "Gather the world's top Slot gaming platforms, thousands of slot machine games are waiting for you to fight",
                blockChainTips:
                              "Use blockchain technology for encryption verification to create a fair and just platform",
                sportsTips:   "HOT top events, enjoy the event storm",
                realTips:
                              "The most beautiful dealer interacts online, taking you to play various live games such as baccarat",
                lotteryTips:
                              "Dedicated to the lottery gaming industry for many years, offering classic lottery types and gameplay",
                cockFightingTips:
                              "Exclusive on the entire network, high odds, simple and efficient interface, helping you get started easily",
            },
            jackPot:  {
                title:      "JACKPOT",
                text:       "",
                bottomText: "Play for free, big prize is waiting for you",
            },
            download: {
                title:    "Best Mobile Betting App",
                subTitle: "Extreme speed and stability at your fingertips",
                description:
                          "Download the mobile app to experience the best slots, fishing, poker, live games, sports and blockchain on the internet. 5D screen allows you to have a shocking gaming experience and enjoy games anytime and anywhere",
            },
        },
        theme37_dz:              {
            login:    {
                tips:  "Login/Register to view",
                title: "LOGIN"
            },
            register: {title: "REGISTER"},
        },
    },
}