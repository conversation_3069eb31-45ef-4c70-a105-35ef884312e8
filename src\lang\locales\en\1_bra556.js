export default {
    reg_pattern_0: "Please enter (min) - (max) characters",
    reg_pattern_1: "Please enter (min) - (max) characters, the first character must be a letter, followed by a combination of letters and numbers.",
    reg_pattern_2: "Please enter (min) - (max) characters, combination of chinese character (kanji) and numbers.",
    reg_pattern_3: "Please enter (min) - (max) characters in mailbox format",
    reg_pattern_4: "Please enter (min) - (max) alphanumeric characters. non-case-sensitive. (special characters not allowed)",
    reg_pattern_5: "Please enter (min) - (max) characters, must be a combination of letters and numbers.",
    reg_pattern_6: "Please enter (min) - (max) characters, only numbers allowed",
    reg_pattern_7: "Please enter (min) - (max) characters, a combination of alphanumeric without special character, and at least one uppercase letters.",
    reg_pattern_8: "Please enter (min) - (max) characters, english alphabet and spacing only",
    reg_pattern_9: "Please enter (min) - (max) characters , spacing not allowed",
    reg_pattern_10: "Please enter (min) - (max) characters , chinese character and punctuation marks",
    reg_pattern_11: "Please enter the correct date format (yyyy-MM-dd)",
    reg_pattern_12: "Please enter (min) - (max) characters , must be a combination of letters and a spacing.",
    reg_pattern_13: "Please enter the correct date format (MM-dd-yyyy)",
    reg_pattern_14: "Please enter (min) - (max) characters, that consists of alphabets and numbers only.",
    reg_pattern_15: "Please enter (min) - (max) characters, korean only.",
    reg_pattern_16: "Please enter (min) - (max) characters, Thai only",
    button_submit: "Submit",
    button_confirm: "Confirm",
    button_cancel: "Cancel",
    button_search: "Search",
    button_search_more: "More",
    button_empty: "Empty",
    button_reset: "Reset",
    button_send: "Send",
    button_transfer: "Transfer",
    button_see: "View",
    button_receive: "Receive",
    button_sign: "Sign in",
    label_qrcode: "Qr Code",
    label_all: "All",
    label_ALL: "All",
    label_LOTT: "Lotto",
    label_PVP: "PVP",
    label_RNG: "Slot",
    label_LIVE: "Live",
    label_FISH: "Fishing",
    label_SPORTS: "Sport",
    label_ELOTT: "Lotto",
    label_ELOTT_3rd: "lotto",
    label_date: "Date",
    label_loss_amount: "Net loss",
    label_win_amount: "Net win",
    label_bet: "Betting",
    label_win: "Winning",
    label_rebate: "Agent Rebate",
    label_promotion: "Promotion",
    label_valid_bet: "Valid bet",
    SSC_1: "High-Freq lotto",
    SSC_2: "High-Freq lotto",
    label_other: "Other",
    label_confirm: "Confirm",
    label_clear: "Clear",
    label_selectAll: "Select all",
    label_cardNum: "Card number",
    label_status: "Status",
    label_remark: "Remarks",
    label_current_remark: "Current remark",
    label_new_remark: "New remark",
    label_change_remark: "Update remark",
    label_deposit: "Deposit",
    label_withdrawals: "Withdraw",
    label_play: "Play",
    label_game: "Game",
    label_type: "Types",
    label_wallet: "Wallet",
    label_company: "Vendor",
    label_content: "Content",
    label_email: "Email",
    label_nickname: "Nickname",
    label_payee_name: "Real name",
    label_qq: "QQ number",
    label_close: "Close",
    label_total: "Total",
    label_awarded: "Award",
    label_decimal: "Show Decimal Values",
    label_day: "Day",
    label_today: "Today",
    label_yesterday: "Yesterday",
    label_before_yesterday: "The day before yesterday",
    label_recently_week: "7-Days",
    label_my_account: "My account",
    label_my_wallet: "My purse",
    label_account_totalMoney: "Available Balance",
    label_pwd_login: "Login password",
    label_pwd_pay: "Withdrawal password",
    label_pwd_old: "Old password",
    label_pwd_new: "New password",
    label_pwd_confirm: "Confirm password",
    label_deposit_number: "Deposit number",
    label_deposit_type: "Deposit method",
    label_deposit_time: "Deposit time",
    label_deposit_recently: "Recent withdrawal",
    label_withdrawals_surplus: "Remaining withdrawal (counts)",
    label_withdrawals_number: "Withdrawal Number",
    label_withdrawals_money: "Withdraw amount",
    label_withdrawals_time: "Withdraw time",
    label_transaction_type: "Transaction type",
    label_transaction_time: "Transaction time",
    label_transaction_money: "Balance",
    label_transfer_wallet: "Transfer wallet",
    label_transfer_upline: "Transfer to upline",
    label_transfer_inWallet: "Transfer to the wallet",
    label_transfer_outWallet: "Transfer out of the wallet",
    label_transfer_recently: "Recent transfer record",
    label_money_start: "Starting amount",
    label_money_end: "End amount",
    label_money_handlingFee: "Charges",
    label_return_balance: "Balance Recall",
    label_game_name: "Game name",
    label_game_number: "Game number",
    label_order_number: "Order Ref. Num.",
    label_play_id: "Game ID",
    label_order_search: "Order search",
    label_order_status: "Order status",
    label_bet_time: "Bet time",
    label_bet_money: "Bet amount",
    label_bet_number: "Bet period number",
    label_bet_content: "Bet content",
    label_current_balance: "Current balance",
    label_contest_name: "Game name",
    label_profit_loss: "Profit and loss",
    second_profit_loss: "Profit and loss",
    label_search_number: "Numero search",
    label_wallet_manage: "Wallets",
    label_wallet_more: "More wallets",
    label_wallet_all: "All wallets",
    label_login_lastTime: "Last login time",
    label_login_lastIP: "Last login IP",
    label_login_lastPlace: "Last login location",
    label_setting_data: "Personal info",
    label_google_verifyNum: "Google verification code",
    result_null: "No matched data found",
    result_done_transfer: "Successful Transfer",
    label_account: "Account ID",
    label_agent_name: "Direct upline",
    label_name: "Name",
    label_change_name: "Change name",
    label_new_name: "New Name",
    label_old_name: "Old Name",
    label_order: "Order",
    label_agent_pandect: "Agent Overview",
    label_team_chart: "Team Statistics",
    label_deposit_amount: "Deposit",
    label_withdrawals_amount: "Withdrawal",
    label_prize_amount: "Winnings",
    label_bet_amount: "Bets",
    label_login_counts: "Login Count",
    label_bet_counts: "Bet Count",
    label_deposit_counts: "Deposit Count",
    label_team_information: "Team Info (Today)",
    label_choose_end_time: "Please select the deadline",
    label_team_gross_profit_histogram: "Team Gross View",
    label_today_login: "Login",
    label_today_registration: "Register",
    label_today_first_betting: "First bet",
    label_today_first_withdrawals: "First deposit",
    label_today_online_members: "Online users",
    label_total_balance: "Total balance",
    login_required: "Please login to continue.",
    error_logout: "Please login to continue.",
    recharge_amount: "Deposit amount",
    DEPOSIT: "Deposit",
    SAFE_BOX: "Main Wallet",
    PVP: "PVP",
    RNG: "RNG",
    LOTT: "Lotto",
    CONS: "Consolidated",
    AG: "AG wallet",
    FISH: "Fish",
    LIVE: "Live Casino",
    SPORTS: "Sport",
    ELOTTO: "Lotto",
    ELOTT: "Lotto",
    SEA_LOTT: "SEA lotto",
    VIETNAM_LOTTO: "Lottery",
    ac_subordinate_management_tab_2: "Register Downline",
    ac_link_register: "Link Registration",
    ac_link_manager_header: "Link Management",
    label_open_account_type: "Account Type",
    label_user_name: "Username",
    label_agent: "Agent",
    label_member: "Member",
    label_player: "Player",
    ac_default_password: "Default is 123456",
    label_registration_account: "Username",
    label_rebates_set: "Rebate setting",
    label_select_quota: "Select quota",
    label_not_use_quota: "Disuse quotas",
    label_lowest: "Min",
    label_highest: "Max",
    label_entertainment: "Entertainment",
    label_individual: "Self",
    label_group: "Team",
    label_set_time_limits: "Expiration",
    label_choose_time_limits: "Select deadline",
    label_lott_fraction: "Lottery Quota",
    label_income_total: "Total income",
    error_google_code: "Please enter the correct google verification code",
    label_commission: "Commission",
    label_system_generate: "System",
    ac_link: "Link",
    "invalid.rebate.value": "Invalid rebate setting",
    label_valid_1: "No Expiration",
    label_valid_2: "1 day",
    label_valid_3: "7 days",
    label_valid_4: "30 days",
    label_valid_5: "1 year",
    label_valid_forever: "Permanent",
    label_promotion_channel: "Promotion channels",
    label_forum: "Forum",
    label_qq_group: "QQ group",
    label_user_defined: "Customize",
    label_select_channel: "Select a channel",
    label_qq_promotion: "Promote QQ",
    label_channel: "Channel",
    label_normal: "Normal",
    label_overdue: "Expired",
    label_click_copy: "Invitation Link(click to copy)",
    label_invitation_code: "Code",
    label_registration_population: "New user",
    label_create_time: "Created",
    label_deadline: "Date of expiry",
    label_registration_time: "Registration date",
    label_pre_link: "Previous link",
    label_next_link: "Next link",
    label_direct_subordinate: "Direct downlines",
    label_valid_balance: "Available  Amount",
    label_beneficiary: "Payee",
    label_transfer_type: "Transfer type",
    label_transfer_subordinate: "Transfer to downline",
    label_transfer_money: "Transfer amount",
    label_agency_deposit: "Charge",
    label_unit_people: "user",
    label_unite_yuan_symbol: "¥",
    label_unite_ge: "unit",
    label_series_SSC_1: "High-Freq lotto",
    label_series_SSC_2: "High-Freq lotto",
    label_series_11X5_1: "Lotto 5/11",
    label_series_LF_1: "Low-Freq lotto",
    label_series_PK10_1: "PK10",
    label_series_LHC_1: "HK mark 6",
    label_series_K3_1: "3 Dices",
    label_series_PCB_1: "PC Egg",
    label_series_PVP: "PVP",
    label_series_RNG: "Slot",
    label_series_LIVE: "Live",
    label_series_PVP_null: "PVP",
    label_series_RNG_null: "Slot",
    label_series_LIVE_null: "Live",
    label_series_SPORTS: "Sport",
    label_series_ELOTTO: "Lotto",
    label_series_FISH: "Fish",
    label_series_SPORTS_null: "Sport",
    label_series_ELOTTO_null: "Lotto",
    label_series_FISH_null: "Fish",
    label_series_SEA_LOTT: "SEA lotto",
    label_series_VIETNAM_LOTTO: "Vietnam Lottery",
    label_success_copy: "Successful copy",
    label_downline_reports: "Subordinate Report",
    label_details: "Details",
    label_give_quota: "Grant quota",
    label_game_records: "Game record",
    label_set_agent: "Set as Agent",
    label_send_message: "Send a message",
    label_online: "Online",
    label_team_size: "Team size",
    label_rebate_series: "Rebate Series",
    label_last_login_time: "Last Login",
    label_personal_balance: "Balance",
    label_team_balance: "Team Balance",
    label_operation: "Action",
    label_withdraw: "Withdrawal",
    label_recharge_fee: "Company fee",
    label_merchant_charge_fee: "Deposit fee",
    label_team_deposit: "Deposit",
    label_team_withdraw: "Withdrawal",
    label_team_bet: "Betting",
    label_team_win_prize: "Winning",
    label_team_rebate: "Rebate",
    label_team_day_salary: "Daily salary",
    label_team_promotion: "Promotion",
    label_team_recharge_fee: "Vendor fee",
    label_team_merchant_charge_fee: "Deposit fee",
    label_team_profit_loss: "P&L",
    label_team_loss: "Team net loss",
    label_team_win: "Team net win",
    button_confirm_registration: "Confirm registration",
    info_setting_combine_number_letter_12: "6-12 digits, combination of alphabet letter or number",
    info_setting_combine_number_letter_11: "6-12 digits, combination of capital or small alphabet letter or number , special character not included",
    info_choose_set_time_limits: "Please select a set period",
    info_success_create_link: "Link created successfully",
    info_enter_correct_amount: "Please enter the correct amount",
    info_enter_correct_psw: "Please enter the correct transaction password",
    info_enter_username_or_transaction_type: "Please enter a username or select a transaction type",
    result_success_register: "Registered successfully",
    result_no_login_records: "No login record",
    ac_bet_content: "Accurate registration confirmation",
    ac_bet_content_1: "Registration type",
    ac_specific_info_confirm: "Please Confirm the Information",
    current_url: "Platform URL",
    registerCenterPassword: "Password",
    registerCenterAlertButton: "Copy and close",
    "exists.username.err": "Username already exists",
    "format.username.err": "Username format is wrong",
    "format.pwd.err": "Password format does not match",
    "downline.rebate.update.lower.reb": "series rebate can not be lowered",
    error_format: "The format of remark is incorrect",
    lnkManager_noRegisteredUsers: "No registered users",
    lott_box_title: "Traditional Lottery",
    label_chase_number: "Chasing",
    label_bet_cancel: "Withdrawn",
    label_only_chase: "Tracking count",
    label_not_chase: "Not Tracking list",
    label_gamble_time: "Bet time",
    label_close_account_time: "Settlement time",
    label_gamble_content: "Bet content",
    label_loss_percent: "Odds",
    label_gamble_amount: "Bet amount",
    label_win_count: "Winning amount",
    label_prize_number: "Winning lottery numbers",
    label_settlement_amount: "Sett. amount",
    label_gamble_status: "Bet status",
    label_bet_count: "Bet note",
    label_entry_time: "Admission time",
    label_exit_time: "Exit time",
    label_head_myincome: "My Income",
    label_lotto_play_agent_rebate: "Rebate of Lotto Gameplay",
    label_head_lottery_agentrebate: "Lottery Agent Rebate",
    label_all_subordinate: "All downlines",
    label_agent_rebates: "Agent rebate",
    label_transaction_details: "Transaction Details",
    label_game_play: "Game play",
    label_game_type: "Game Type",
    label_bonus: "Bonus",
    label_bet_rebates: "Bet rebate",
    label_dividend: "Dividend",
    label_day_salary: "Daily Salary",
    label_day_total_salary: "Salary",
    label_hourly_salary: "Hourly Salary",
    status_betting_2: "Processing",
    status_betting_4: "Won",
    status_betting_5: "Lose",
    status_betting_6: "Chasing the withdrawal order",
    status_betting_7: "Abandoned",
    status_betting_8: "Individual withdrawal",
    status_betting_12: "Open withdrawal order",
    status_betting_14: "Pending",
    status_betting_16: "Tie",
    status_betting_17: "User withdraw",
    status_betting_99: "Free Trial",
    label_return: "Return",
    label_sett: "Settled",
    label_unsett: "Unsettlement",
    label_methods: "Method",
    label_success: "Success",
    label_processing: "Processing",
    label_total_amount_of_team_member: "Total number of teams",
    label_amount_of_direct_people: "Direct number",
    label_max_proportion_of_sign_contract: "Maximum %",
    label_valid_time_of_signing: "Contract effective date",
    label_content_of_protocol: "Contract Detail",
    label_team_daily_amount: "Team betting (Daily)",
    label_actual_proportion: "Actual ratio",
    label_amount_of_people_for_valid_betting: "Effective bet counts (user)",
    label_settlement_date: "Settlement date",
    label_team_salary: "Team salary",
    label_self_salary: "My Salary",
    label_cancellation_request_been_sent: "Contract termination request sent",
    fsmgt_success: "Request sent.",
    fsalmgt_A: "Confirmed",
    fsalmgt_PE: "Pending confirmation",
    fsalmgt_PC: "Pending confirmation",
    label_red_envelope_rain: "Falling Lucky Bags",
    label_receiver: "Receiver",
    label_input_receiver: "Please add a recipient",
    label_choosed: "Selected",
    label_all_direct: "All direct",
    label_add: "Add",
    label_type_lucky_bag: "Lucky Bag Type",
    label_share: "Share",
    label_fighting: "Fighting",
    label_total_amount_of_lucky_bag: "Total amount of lucky bag",
    label_input_amount_for_lucky_bag: "Please enter the lucky bag amount",
    label_input_fund_psw: "Please enter the fund password",
    info_success_give_red_package: "Gift sent successfully",
    label_date_not_less_than_90_days: "The date has to be within 90 days",
    label_red_envelope: "Lucky Bag",
    ac_announcement_header: "Announcement",
    ac_announcement_column_2: "Title",
    label_gameType: "Product",
    label_dividendMain: "My dividend",
    label_currentDividend: "Current dividend",
    label_previousDividend: "Previous dividend",
    label_promotion_bonus: "Promotion bonus",
    label_player_rebate: "Player rebate",
    label_bank_recharge_fee: "Bank charges",
    label_expected_dividend: "Estimated dividend",
    abel_merchant_charge_fee: "Vendor charges",
    label_dividendMain_ratesTable: "My dividend rate setting",
    label_periodic_consumption: "Bet Amount",
    label_periodic_loss: "P&L",
    label_dividendMain_bankChargesForm_username: "Username",
    pgBankCharges: "Quick payment",
    mtBankCharges: "Manual Deposit",
    unqrBankCharges: "UnionPay QR",
    unBankCharges: "UnionPay WAP",
    alipayBankCharges: "Alipay QR",
    maqrBankCharges: "Alipay QR transfer",
    atBankCharges: "Alipay bank transfer",
    aliwapBankCharges: "Alipay WAP",
    wechatBankCharges: "Wechat QR",
    wtBankCharges: "Wechat transfer",
    wcwapBankCharges: "Wechat WAP",
    jdBankCharges: "Jingdong QR",
    qqBankCharges: "QQ QR",
    qqwapBankCharges: "QQ WAP",
    wrsBankCharges: "Wechat QR",
    jdwapBankCharges: "JD Payment",
    mwqrBankCharges: "Wechant QR transfer",
    muqrBankCharges: "UnionPay QR transfer",
    eptBankCharges: "Epay",
    label_signing_time: "Signing date",
    label_agreement_effective_time: "Agreement effective date",
    label_history_of_sign_contract: "Contract history",
    label_history_log: "History",
    label_add_signing_contract: "New Contract",
    label_see_signing_contract: "View Contract",
    label_contractManagement_subForm_details: "Reminder : Please fill in the standards and rates for your downline, and every level's value must be higher than previous level.",
    label_sort: "Sorting",
    label_contractManagement_status: "Status",
    label_contractManagement_statusP: "Pending",
    label_contractManagement_statusA: "Confirmed",
    label_contractManagement_statusR: "No contract",
    label_input_value_must_be_bigger_than: "Value must be ≧",
    label_input_value_must_be_bigger_than_1: "Value must be ≧",
    label_input_value_must_be_bigger_than_2: "Value must be >",
    label_input_scope: "Range:",
    info_betText_1: "≧",
    info_totalText_1: "≧",
    info_actText_1: "≧",
    info_betText_2: ">",
    info_totalText_2: ">",
    info_actText_2: ">",
    error_contractManagement_subForm_emptyvalue: "{0} cannot be empty",
    error_contractManagement_subForm_zerovalue: "{0} cannot be 0",
    error_contractManagement_subForm_lessthanlast: "Current {0} must be higher than last {0}",
    error_contractManagement_subForm_lessthangiven: "Current {0} must be greater than last {0}",
    error_contractManagement_subForm_outofrange_lower: "{0} must not be lower than lowest {0}",
    error_contractManagement_subForm_outofrange_higher: "{0} must not be higher than highest {0}",
    info_contract_no_change: "No change detected",
    label_dividendRecords: "Dividend Record",
    label_sub_user_name: "Username",
    label_amount_of_dividend: "Dividend amount",
    label_dividendRecords_disburse: "Disburse",
    label_dividendRecords_status: "Status",
    label_dividendRecords_statusP: "Pending",
    label_dividendRecords_statusA: "Disbursed",
    label_dividendRecords_statusR: "Rejected",
    label_salaryEntry: "My Salary",
    label_standard_of_daily_salary: "Rate",
    label_salaryManagement: "Contract Management",
    label_salaryManagement_editForm_title: "Edit",
    label_salaryManagement_addForm_title: "Add",
    label_salaryManagement_viewForm_title: "Rules",
    label_salaryManagement_subForm_details: "Reminder : Setting value in level must higher than previous level.",
    label_active_requirement_for_sub: "Active member",
    ac_proportion: "Rate",
    label_salaryManagement_status: "Status",
    label_salaryManagement_statusP: "Pending",
    label_salaryManagement_statusA: "Confirmed",
    label_salaryManagement_statusR: "Unsigned",
    label_salaryManagement_disburse_status_P: "Not distributed",
    label_salaryManagement_disburse_status_A: "Disbursed",
    label_salaryManagement_disburse_status_R: "Reject",
    label_salaryManagement_disburse_status_M: "Adjust",
    info_only_allow_one_decimal: "Only allow one digit decimal",
    info_only_allow_two_decimal: "Only allow two digits decimal",
    info_only_allow_integer: "Must be integer",
    label_salaryRecords: "Salary Record",
    label_salaryRecords_status: "Status",
    label_amount_of_daily_salary: "Salary Amount",
    label_no_available_ratio: "There are no rate available for this condition",
    label_hourSalaryEntry: "My Hourly Salary",
    label_hourSalaryRecords: "Hour Salary Record",
    label_hourSalaryRecords_startTime: "Start Time",
    label_hourSalaryRecords_endTime: "End Time",
    label_hourSalaryRecords_frequency: "Frequency",
    label_hourSalaryRecords_salaryRate: "Rate",
    label_hourSalaryRecords_salaryAmount: "Salary amount",
    label_hourSalaryRecords_team_daily_amount: "Team betting(hour) ≥",
    label_hourSalaryRecords_viewForm_title: "Hourly Salary Standard",
    label_hourSalaryRecords_unitValue: "Unit",
    label_hourSalaryEntry_frequency: "Interval",
    label_hourSalaryRecords_frequency_H: "Per hour",
    label_hourSalaryRecords_frequency_BH: "Per 30mins",
    label_hourSalaryRecords_calcBetting: "Calculated amount",
    label_MRSalaryEntry: "My daily salary",
    label_MRSalaryEntry_betVolume: "Team bet volumn(daily) ≥",
    label_MRSalaryRecords: "Daily salary record",
    label_MRSalaryRecords_disburse_status_0: "Pending",
    label_MRSalaryRecords_disburse_status_1: "Received",
    label_MRSalaryRecords_disburse_status_2: "Refuse",
    label_MRSalaryRecords_disburse_status_3: "Expired",
    label_MRSalaryRecords_payoutTime: "Claim Date",
    label_MRSalaryRecords_dueTime: "Expired Date",
    label_MRSalaryRecords_activeMember: "Active Member",
    label_MRSalaryRecords_rate: "Rate",
    ac_yuan: "Dollar",
    ac_day_volume: "Bet amount (Daily)",
    ac_hour_volume: "Bet amount (Hourly)",
    ac_signing_history: "Contract history",
    ac_termination: "Terminate",
    ac_modify: "Modify",
    ac_waiting_to_determined: "Pending",
    ac_initiate_termination: "Terminate contract",
    ac_initiate_contract: "Create contract",
    ac_modify_contract: "Modify contract",
    ac_termination_success: "Contract terminated",
    ac_signed_success: "Contract created",
    ac_reject_termination: "Reject terminate request",
    ac_reject_modify_contract: "Reject modify request",
    ac_waiting_to_modify: "Confirm modify request",
    ac_delete_btn: "Delete",
    ac_rebate_total: "Total Agent Rebate",
    ac_daily_salary: "Total Daily Salary",
    ac_total_commission: "Total commission",
    ac_total_amount_income: "Total Income",
    ac_dividend_requirements: "Dividend requirement : half month standard",
    ac_fixed_amount: "Fixed Amount",
    ac_not_empty: "Cannot be empty",
    confirm_dismissal_of_contract: "Please confirm the contract termination with",
    confirm_of_contract_dividend: "(Dividend Contract)",
    confirm_of_contract_salary: "(Daily Salary Contract)",
    confirm_of_contract_hour: "(Hourly Salary Contract)",
    label_active_number: "Active member",
    label_dividend_ratio: "Rate",
    label_daily_salary_ratio: "Daily Salary rate",
    label_dividend_bet: "Periodic Min bet",
    ac_status_P: "Pending",
    ac_status_A: "Signed",
    ac_status_E: "Pending",
    ac_status_C: "Cancel",
    ac_status_V: "Pending",
    ac_status_R: "Not signed",
    ac_status_DEF: "Not signed",
    ac_input_value_must_be_in: "Value must between",
    ac_input_value_must_be_greater_than: "Value must greater than",
    ac_input_value_must_be_greater_than_short: "Must greater than",
    "request.param.err": "Missing Required Data or Wrong Format, Please Check Again or Contact The Customer Service",
    "request.success": "Success",
    label_advance_settings: "Advanced Settings",
    label_input_password: "Please enter your password",
    label_qq_required: "Please enter the correct QQ number",
    label_tips_text: "Tips",
    label_free_play_function_block: "Please login an actual account to use this function",
    label_unit_dollar: "Dollar",
    label_unit_dime: "Dime",
    label_unit_cent: "Cent",
    label_unit_thousandth: "Thousandth",
    label_unit_1: "Dollar",
    label_unit_2: "Dime",
    label_unit_3: "Cent",
    label_unit_4: "Thousandth",
    label_unit_5: "Ten",
    label_unit_6: "Hundred",
    label_unit_7: "Thousand",
    result_done: "Success",
    info_balance_refreshing: "Updating",
    error_bal_lack: "Balance not enough",
    label_mobile_number: "Mobile No",
    label_mobile_number_old: "Old mobile number",
    label_mobile_number_new: "New mobile number",
    label_Bond: "Security deposit",
    label_bet_limit: "Bet limit",
    label_transfer_in: "Deposit",
    label_transfer_out: "Withdrawal",
    label_synchronous_lever: "Synchronized lever",
    label_proportion_min: "Min PT",
    label_my_setting: "My Setting",
    label_downline_setting: "Downline Setting",
    label_my_percent: "My PT",
    label_open_acount: "Registration",
    label_occupation_report: "PT report",
    label_proportion_max: "Max PT",
    label_proportion_set: "PT rate",
    label_proportion_money: "PT amount",
    label_proportion_setting: "PT setting",
    label_commission_rate: "Rebate rate",
    label_team_bonus: "Team Bonus",
    label_dial_down: "Contact Downline",
    dial_down_success: "Dialing",
    info_downline_pwd: "Edit downline login password",
    info_downline_mobile: "Edit downline mobile number",
    info_downline_status: "Edit downline account status",
    info_downline_game: "Edit downline game status",
    info_downline_name: "Edit downline name",
    status_position_A: "Complete",
    status_position_N: "Pending",
    status_position_E: "Resign contract",
    status_Promotion_0: "Pending",
    status_Promotion_1: "Processed",
    status_Promotion_2: "Bad debt",
    status_member_0: "Blocked",
    status_member_1: "Normal",
    status_member_2: "Login forbidden",
    status_member_5: "System forbidden",
    status_member_6: "Login forbidden",
    status_member_7: "Account deleted",
    status_member_10: "Inactive account",
    status_member_11: "Inactive account",
    status_member_active: "Normal",
    status_member_inactive: "Blocked",
    status_member_unfreeze: "Normal",
    status_member_freeze: "Blocked",
    label_money: "Amount",
    label_balance: "Balance",
    label_account_records: "Total win loss",
    label_total_win_lose: "label_total_win_lose",
    label_win_lose: "Win/lose",
    label_company_credit: "Company",
    label_subordinate: "Downline",
    label_self: "Self",
    "11X5_1": "Lotto 5/11",
    LF_1: "Low-Freq lotto",
    PK10_1: "PK10",
    LHC_1: "Hk mark 6",
    K3_1: "3 Dices",
    PCB_1: "PC egg",
    PVP_null: "PVP",
    RNG_null: "Slot",
    LIVE_null: "Live",
    SPORTS_null: "Sports",
    Lotto: "Lotto",
    FISH_null: "Fish",
    set_withdraw_password: "Please setup your withdrawal password first",
    daily_wage_rules_01: "Rules and regulations",
    daily_wage_rules_02: "1. Daily salary calculated based on the team's total bet volumn the day before with terms agreed between user and upline, and disburse the next day.",
    daily_wage_rules_03: "2. Daily salary will disburse based on the actual active member, even though the bet volumn have met the requirement.",
    daily_wage_rules_04: "3. The platform reserves the right for any final decision and to modify the terms at any time.",
    daily_wage_rules_05: "4. The daily salary will take effect the same day, and disburse the next day.",
    hour_wage_rules_01: "Rules and regulations",
    hour_wage_rules_02: "1. Hourly salary calculated based on the team's total bet volumn previous period before with terms agreed between user and upline, and disburse the period.",
    hour_wage_rules_03: "2. Hourly salary will disburse based on the actual active member, even though the bet volumn have met the requirement.",
    hour_wage_rules_04: "3. The platform reserves the right for any final decision and to modify the terms at any time.",
    label_transaction: "transaction",
    deposit_success: "Deposit Successfully",
    withdrawal_success: "Withdrawal Successfully",
    label_current_nickname: "Current Nick Name",
    label_new_nickname: "New Nick Name",
    team_manangement: "Team Management",
    label_game_status: "Game Status",
    label_account_status: "Account Status",
    label_week: "This Week",
    label_last_week: "Last Week",
    label_month: "This Month",
    label_last_month: "Last Month",
    status_order_initial: "Ordered",
    status_order_win: "Win",
    status_order_lose: "Lose",
    status_order_cancel: "Cancel",
    status_order_tie: "Tie",
    status_order_refund: "Refund",
    status_order_win_half: "Win half",
    status_order_lose_half: "Lose half",
    status_order_cash_out: "Instant cashing",
    reminder_timezone_gmt8: "Reminder: The data above is based on GMT + 8",
    yes: "OK",
    no: "Cancel",
    plan_bet_money: "Planned bet amount",
    valid_bet_money: "Effective bet amount",
    no_announcement: "No announcement",
    display_num: "Rows per page",
    label_autofill: "Auto fill up",
    my_salary: "my_salary",
    salary_record: "salary_record",
    realtime_salary: "realtime_salary",
    single_betting: "single_betting",
    loss_wages: "loss_wages",
    rofit_wages: "rofit_wages",
    label_contributor: "label_contributor",
    lottery_variety: "lottery_variety",
    bet_number: "bet_number",
    single_bet_amount: "single_bet_amount",
    win_or_lose: "win_or_lose",
    salary_ratio: "salary_ratio",
    salary_amount: "salary_amount",
    no_range: "no_range",
    label_team_daily_salary: "Team Salary",
    label_own_daily_salary: "Self Salary",
    label_service_charge: "Service Fee",
    realtime_rules_01: "realtime_rules_01",
    realtime_rules_02: "realtime_rules_02",
    realtime_rules_03: "realtime_rules_03",
    realtime_rules_04: "realtime_rules_04",
    realtime_rules_05: "realtime_rules_05",
    realtime_tips_01: "realtime_tips_01",
    realtime_tips_02: "realtime_tips_02",
    realtime_tips_03: "realtime_tips_03",
    player_profit: "Win/Loss",
    total_games: "Rounds",
    participate_number: "Player #",
    label_more: "More",
    total_record_num: "{0}Records",
    minimum_deposit: "mininum deposit",
    minimum_effective_bet: "mininum bet",
    minimum_betting_days: "mininum bet days",
    platform_rebate: "Player Rebate",
    pay_the_fee: "Deposit fee",
    platform_commission: "Platform fee",
    live_expenditure: "Live stream expenses",
    pnl_details: "Profit and loss details",
    promotion_detail: "Promotion details",
    team_summary: "Team total",
    offer_type: "Promotion type",
    amount_received: "Amount",
    ticket_exchange_amount: "Amount from ticket",
    payment_mode: "Deposit mode",
    rate_percentage: "Rate",
    deposit_amount: "Deposit amount",
    number_of_transactions: "Transaction count",
    pay_the_fee_tips: "* The charges generated through deposit or withdrawal by members will be bear by the agent.",
    filter_empty_member: "Filter empty record",
    level: "Level",
    current_profit_and_loss: "Period P&L",
    current_valid_bet: "Period Valid Bet",
    week_valid_bet: "Periodic / Valid Bet",
    last_period_balance: "Carried Forward P&L",
    product_cost: "Product Charges",
    bear: "YES",
    not_responsible: "NO",
    daily_settlement: "Daily",
    weekly_settlement: "Weekly",
    semi_monthly_settlement: "Semimonthly",
    monthly_settlement: "Monthly",
    daily_settlement_tips: "Disburse next day.",
    weekly_settlement_tips: "From sunday to next saturday as a settlement cycle, the disbursement will be on the next day of settlement.",
    semi_monthly_settlement_tips: "Every 1st to 15th as first half of the month, 16th to monthend will be the second half; 16th is the disbursement date of the first half, the disbursement date of the second half will be on 1st of next month.",
    monthly_settlement_tips: "The calculation will be based on calendar month, the disbursement date is on the first day of next month.",
    last_period_balance_tips: "It will only generate dividend when the P&L is positive.",
    week_valid_bet_tips: "Periodic / Valid Bet：A month is divided into 4 periods (1 ~ 7, 8 ~ 14, 15 ~ 21, 22 ~ end of month), each period must meet the required condition",
    "dividends-static-tips1": "System automatically calculate every dividend cycle's rate.",
    "dividends-static-tips2": "Exact dividend data should be base on the data on dividend disbursement day.",
    reset_frequency_1: "Loss carryforward",
    reset_frequency_0: "No carryforward",
    reset_frequency_2: "Reset monthly",
    reset_frequency_3: "Profit & loss carryforward",
    company_wins_loses: "Company win/lose",
    payout_time: "Disbursement time",
    dividend_adjustment: "Adjustment",
    dividend_service_fee: "Dividend service fee",
    dividend_service_fee_tips: "Period bet amount * Period dividend service fee rate",
    early_stage: "Previous",
    current_stage: "Current",
    Issuance_status: "Status",
    status_pending: "Pending",
    status_accepted: "Disbursed",
    status_rejected: "Rejected",
    expected: "Estimated",
    current_team_profit_loss: "Period P&L",
    cycle_1: "1st to 7th as first cycle.",
    cycle_2: "8th to 14th as second cycle.",
    cycle_3: "15th to 21st as third cycle.",
    cycle_4: "22nd to monthend as fourth cycle",
    dividend_pay_the_fee_tips: "* This is system estimated fee, please use the data provided by sales for the actual fee.",
    product_rate: "Product rate",
    draw_time: "Winning Time",
    channel_name: "Channel name",
    label_user_name_and_remark: "Username/Remark",
    label_personal_details: "User details",
    label_user_level: "Player label",
    label_agent_main: "Upline agent",
    label_last_login: "Last login time",
    label_last_deposit: "Last deposit time",
    options_no_login: "No login record",
    personal_finance: "Personal finance",
    undertake_pay_fee: "Deposit fee",
    undertake_product_cost: "Product Charges",
    winning_lottery: "Winning",
    winning_rebate: "Player Rebate",
    label_login_lastDeposit: "Last deposit time",
    label_record_num: "Total records - {0}",
    blance_info_toolip: "The balance is not real time if the user is currently in game.",
    in_increase_deposit: "Deposit",
    pnl_total: "Total",
    label_floor_rate: "Service fee ratio (%)",
    label_floor_record: "Service fee",
    label_floor_standard: "Standard service fee",
    rule_floor_01: "Team requirement: total team member betting volume more than 1000",
    rule_floor_02: "Targeted requirement from team member is not achieved, the service fee will be given according to the standard participation rate",
    rule_floor_03: "Rule of Total service fee is computed according to the team’s previous day activities sales. The previous day service fee will be pay only on the next day before afternoon 12pm according to computation of rule of total service fee",
    rule_floor_04: "Disclaimer: terms and conditions is subject to change",
    label_floor_my: "My service fee",
    label_floor_myrecord: "Service fee record",
    options_last_deposit: "Last deposit time",
    options_first_deposit: "First deposit time",
    options_no_deposit: "No deposit",
    options_have_deposit: "Deposited",
    options_have_login: "Have login record",
    deposit_record: "Deposit Records",
    dividend_record: "Promotion Records",
    label_main_agent: "Master agent",
    date_start: "Start date",
    date_end: "End date",
    label_downline_collect: "Downline Cash Out",
    collect_success: "Cash out succeed",
    label_team_settlement_loss: "Period settled P&L",
    label_game_wins_loses: "Company win/lose",
    label_agent_data: "Data",
    label_agent_chart: "Charts",
    label_agent_announcement: "Announcement",
    label_agent_contact: "Contact us",
    title_member: "User",
    active_member: "Promotions",
    first_deposit_member: "First deposits",
    login_member: "Logins",
    deposit_member: "Deposits",
    withdraw_member: "Withdrawals",
    bet_member: "Bettings",
    claim_bonus_member: "Promotions",
    dividend_amount: "Promotion amount",
    total_member: "Number of user",
    total_amount: "Total amount",
    table_total_member: "Total count",
    toolip_reset_1: "1. The data will be refreshed during midnight.",
    toolip_reset_2: "2. This week and last week, a week is start from sunday to next saturday.",
    toolip_reset_3: '3. Colour of "Win/Loss" in the report :',
    toolip_reset_3_1: "Red representing positive amount, which is the profit.",
    toolip_reset_3_2: "Green representing negative amount, which is the loss.",
    chart_member_tip1: "Number of user login within chosen time period.",
    chart_member_tip2: "Number of new registered within chosen time period.",
    chart_member_tip3: "Number of user first time deposit within chosen time period.",
    chart_member_tip4: "Number of user received promotion within chosen time period.",
    chart_member_tip5: "Number of user deposited within chosen time period.",
    chart_member_tip6: "Number of user withdrawal within chosen time period.",
    chart_member_tip7: "Number of user bet within chosen time period.",
    receive_dividend_amount: "Promotions",
    chart_amount_tip1: "Total promotion amount claimed within chosen time period.",
    chart_amount_tip2: "Total deposit amount within chosen time period.",
    chart_amount_tip3: "Total withdrawal amount within chosen time period.",
    chart_amount_tip4: "Total valid bet amount within chosen time period.",
    chart_amount_tip5: "Total win/loss amount within chosen time period.",
    register_member: "Registers",
    this_week_tip1: "1. The data will be refreshed every hour.",
    this_week_tip2: "2. A cycle is started from sunday to next saturday.",
    last_week_tip: "A cycle is started from sunday to next saturday.",
    this_month_tip: "The data will be refreshed every hour.",
    hint_info: "Tips : Win/Loss, Team P&L - Red represents a positive amount meaning the player is winning. Green represents a negative amount which is the player is losing. Green color is the profit where you can get a commission.",
    modify_contract: "Modify the contract",
    period_D: "Daily",
    period_W: "Weekly",
    period_H: "Semimonthly",
    period_M: "Monthly",
    ALLEXCLUDE: "All Sports but exclude selected vendor",
    "SPORTS-PARLAY": "Parlay",
    label_exclude_game_wins_loses: "Excluded vendor win/lose",
    label_exclude_rebate: "Excluded vendor rebate",
    dividend_detail_vendor: "Excluded vendors",
    lottDailySalary: "Daily Salary - Lotto",
    depositDailySalary: "Daily Salary - Deposit",
    pvpDailySalary: "Daily Salary - PVP",
    rngDailySalary: "Daily Salary - RNG",
    liveDailySalary: "Daily Salary - Live",
    fishDailySalary: "Daily Salary - Fish",
    sportsDailySalary: "Daily Salary - Sports",
    mainWalletDividend: "Main wallet dividend",
    personal_net_income: "Net amount receives",
    subordinate_gross_ncome: "Downline dividends to pay",
    status_none: "No dividends",
    team_settlement_loss: "Settled P&L",
    team_amount_of_dividend: "Team dividend amount",
    label_devote: "Contributed %",
    label_devote_amount: "Contributed amount",
    label_summary: "Total",
    label_self_title_text: "Direct downlines",
    label_register_method: "Registration Method",
    label_register_url: "Registration URL",
    reg_affiliate_url: "Affiliate URL",
    reg_referral: "Referral",
    reg_direct: "Direct registration",
    reg_agent: "Agent Creation",
    reg_bo: "BO Creation",
    reg_migrate: "Migration",
    reg_web: "Web",
    reg_h5: "Mobile",
    label_dividend_tip: "The data is not real-time and is only an estimate. There may be differences due to variations in the time of data collection.",
    status_unsettled: "-",
    change_dividend_msg: "Upon successful updated, the current contract will be terminated and resign with the new contract",
    downline_tab_agent: "Direct Agent",
    downline_tab_member: "Direct Player",
    label_creditLimit: "Credit limit",
    label_resetType: "Reset type",
    label_update: "Update",
    label_resetType_A: "Automatically",
    label_resetType_M: "Manually",
    info_creditLimit: "Edit credit limit",
    info_price: "Edit price",
    label_creditLimit_set: "Set up credit limit and reset type",
    label_price_set: "Set up price",
    result_success_set: "Updated",
    result_success_price: "Updated",
    label_proportion_title: "Edit Position taking",
    label_price_max: "Must lower than maximun price",
    label_price_min: "Must higher than minimun price",
    label_price_interval: "Price interval",
    label_price: "Price",
    label_reset_success: "Reset succefully",
    label_rate_max: "Must lower than maximun rate",
    label_rate_min: "Must higher than minimun rate",
    label_directDownlineActiveMember: "Direct downline player",
    label_status_incomplete: "Incomplete",
    label_status_ineligible: "Ineligible",
    label_status_tip: "Reminder : Please set up your password in member center before using this function.",
    label_creditIncome: "Credit Report",
    label_playerCreditContribution: "Player Bet Record",
    label_agentLabel: "Label",
    label_amount_total: "Total",
    label_direct_amount: "Subordinate level",
    label_self_amount: "Personal Income",
    label_player_bet: "Player betting",
    label_total_bet: "Total Bet",
    label_price_rebet: "Commission",
    label_proportion: "Position Taking",
    input_agent_account: "Agent Name",
    label_maxBetAmountPerOrder: "Bet Limit per Bet",
    label_maxBetAmountPerNumero: "Bet Limit per Draw",
    label_bet_error1: "Bet Limit per Bet must be less than or equal to Bet Limit per Draw",
    label_bet_error2: "Must be less than or equal to Upline Bet Limit per Bet",
    label_bet_error3: "Must be less than or equal to Upline Bet Limit per Draw",
    label_bet_error4: "Bet Limit per Draw must not be less than Bet Limit per Bet",
    label_uplineBet: "Bet Limit",
    label_uplineBetVal: "Unlimited",
    label_proportion_tip1: "Note",
    label_proportion_tip2: "Upline accounts set maximum and minimum position taking rate for downlines.",
    label_proportion_tip3: "The downline needs to confirm the exact position taking rate.",
    all_bear: "Entirely",
    label_teamJackpotBet: "Jackpot Contribution",
    label_teamJackpotWin: "Jackpot Win",
    label_jackpotContributionRate: "Contribution Rate",
    label_jackpotWin: "Jackpot Win",
    label_jackpotDetail: "Jackpot Details",
    label_gameName: "Game Name",
    charge_bear: "By Percent",
    credit_company_wins_loses: "Win lose",
    credit_team_bet: "Team Betting",
    label_bingo_elotto: "Bingo",
    tx_type_id_8102: "Transfer In",
    tx_type_id_8202: "Transfer Out"
}