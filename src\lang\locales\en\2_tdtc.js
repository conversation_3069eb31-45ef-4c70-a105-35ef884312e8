export default {
  key: "******", //test

  agent_no_do_tip: "Application not processed, withdrawal failed!",

  //jackPot
  JACKPOT: {
    HALLINFO1: "RANK",
    HALLINFO2: "JACKPOT HISTORY",

    RECORD_TITLE1: "JACKPOT HISTORY",
    RECORD_TITLE2: "MY HISTORY",
    RECORD_INFO1: "Time",
    RECORD_INFO2: "Winner",
    RECORD_INFO3: "Game Type",
    RECORD_INFO4: "Bet",
    RECORD_INFO5: "Jackpot Money",
    HELP: "Win big by getting any 3 symbols\nwith the same Gold/Silver/Bronze frame\n in the correct frame",
  },

  GLOBAL: {
    TIP1: "just won",
    TIP2: " in ",
  },

  //logon场景
  LOGON: {
    CHANGE_LAB: "Switch account", //切换账号
    START_LAB: "START", //立即开始
    FACEBOOK_LAB: "FACEBOOK", //FACEBOOK
  },
  //loading场景
  LOADING: {
    LOADING_TIP1: "Please wait...", //请稍等...
    LOADING_TIP2: "Signed in successfully...", //登录成功...
    LOADING_TIP3: "Connecting...", //正在连接...
  },
  //新大厅场景
  NEWHALL: {
    //游戏类型选择
    CALALOG_1: "All", //选项卡 "ALL"
    CALALOG_2: "BATTLE", //选项卡 "BATTLE"
    CALALOG_3: "ARCADE", //选项卡 "GAMBLE"
    CALALOG_4: "SLOT", //选项卡 "SLOT"
    CALALOG_5: "FISHING", //选项卡 "FISHING"
    CALALOG_6: "ONLINE", //选项卡 "VIDEO"
    CALALOG_7: "SPORTS", //选项卡 "SPORTS"
    CALALOG_8: "RECENT", //选项卡 "RECENT"
    TAG_CHARGE: "RECHARGE", //充值
    TAG_CUSTOMER: "CSKH", //客户服务
    TAG_RANK: "Rank", //排行版
    TAG_EXCHANGE: "Withdraw", //兑换
    TAG_BANK: "BANK", //银行
    TAG_DAILY: "Events", //日常活动
    TAG_NOTICE: "Notification", //公告
    TAG_AGENT: "Agent", //代理
    TAG_STAR: "VIP", //VIP
    TAG_REWARD: "Tasks", //任务
    TAG_BACK: "BACK", //返回
    TAG_BONUS: "BONUS", //BONUS
    TAG_REGISTER: "GIFT", //注册送金
    TO_BONUS: "Will be sent as Bonus",
  },
  //代理下级查询
  AGENTCHECK: {
    //代理下级查询
    TIME_NAME_0: "Today", //"今天",
    TIME_NAME_1: "This month", //"本月",
    TIME_NAME_2: "Last month", //"上月",
    TIME_NAME_3: "Custom time", //"自定义",
    DATA_NAME_0: "Recharge", //"充值",
    DATA_NAME_1: "Withdrawal", //"提现",
    DATA_NAME_2: "Reward", //"彩金",
    DATA_NAME_3: "Win/Lose", //"盈亏",
  },
  //查询记录 记录名称
  DATA: {
    DATA_NAME_2: "Register donation account", //"注册赠送",
    DATA_NAME_3: "Win/Lose", //"游戏输赢",
    DATA_NAME_4: "Withdraw", //"兑换",
    DATA_NAME_5: "Help", //"救援",
    DATA_NAME_6: "Mission", //"任务",
    DATA_NAME_7: "Growth Point", //"成长基金",
    DATA_NAME_8: "New Member Bonus Package", //"新人礼包",
    DATA_NAME_9: "Daily Reward", //"签到",
    DATA_NAME_10: "Money Rain", //"红包雨",
    DATA_NAME_11: "Lucky Wheel", //"转盘",
    DATA_NAME_12: "VIP Member Bonus Package", //"vip奖励",
    DATA_NAME_13: "Agent's mission reward", //"推广任务红包",
    DATA_NAME_14: "Revenue withdrawal agent", //"代理返点提现",
    DATA_NAME_15: "Charge", //"充值",
    DATA_NAME_16: "Reward from Admin", //"管理员赠送"
    DATA_NAME_17: "Link with phone", //"绑定手机号"
    DATA_NAME_18: "Daily first deposit", //"每日首存"
    DATA_NAME_25: "Bonus", //"Bonus"
    DATA_NAME_26: "Daily bet points", //下注流水查询
    DATA_NAME_30: "Gift Code", //Gift Code
    DATA_NAME_38: "Dealer Daily Ranking", //"代理每日排行榜"
    DATA_NAME_41: "Jackpot", //"Jackpot"
    DATA_NAME_42: "Reward from Charge", //"充值赠送"
    DATA_NAME_43: "Reward from Gold", //"彩金赠送"
  },

  //游戏名
  GAME: {
    GAME_NAME_6: "GOLDEN FLOWER", //诈金花
    GAME_NAME_50: "GRAB THE ZHUANG NIUNIU", //抢庄牛牛
    GAME_NAME_104: "HUNDRED PEOPLE NIUNIU", //百人牛牛
    GAME_NAME_122: "BACCARAT", //百家乐
    GAME_NAME_123: "BIRDS AND BEASTS", //飞禽走兽,
    GAME_NAME_124: "VIDEO BACCARAT", //视讯百家乐,
    GAME_NAME_130: "HOO HEY HOW", //鱼虾蟹
    GAME_NAME_131: "RED&BLACK", //红黑大战
    GAME_NAME_132: "DRAGON AND TIGER FIGHT", //龙虎斗
    GAME_NAME_133: "XOC DIA", //色碟
    GAME_NAME_134: "SICBO", //色碟
    GAME_NAME_135: "PIRATE KING", //加勒比海盗
    GAME_NAME_136: "GOLDEN CICADA", //金蝉
    GAME_NAME_138: "XOC DIA MD5", //MD5
    GAME_NAME_140: "NEED FOR SPEED ", //极品飞车,
    GAME_NAME_200: "DOU DI ZHU", //斗地主
    GAME_NAME_392: "MAHJONG FOR TWO", //二人麻将
    GAME_NAME_405: "FISHING", //捕鱼
    GAME_NAME_516: "GOD OF WEALTH", //财神到
    GAME_NAME_517: "PLAYERUNKNOWN'S BATTLEGROUNDS", //绝地求生,
    GAME_NAME_518: "THE AVENGERS", //复仇者联盟,
    GAME_NAME_519: "JOURNEY TO THE WEST", //西游记
    GAME_NAME_520: "DUOFU DUOCAI", //多福多彩
    GAME_NAME_521: "WORLD OF WARCRAFT", //魔兽世界,
    GAME_NAME_522: "BUFFALO VALLEY", //野牛农场,
    GAME_NAME_523: "CLASIC FRUITS", //水果拉霸,
    GAME_NAME_524: "GOLD COUNTRY", //黄金矿工,
    GAME_NAME_525: "BARBARIAN NUDGE", //野蛮人
    GAME_NAME_526: "APES GO WILD", //猴子
    GAME_NAME_527: "WILD MONSTERS", //吸血鬼
    GAME_NAME_528: "IDOL", //神像
    GAME_NAME_529: "VIKING", //维京人
    GAME_NAME_601: "RUN FAST", //跑得快
    GAME_NAME_602: "VIETNAM SOUTHERN THIRTEEN",
    GAME_NAME_604: "PHOM",
    GAME_NAME_605: "BLACKJACK", //黑杰克
    GAME_NAME_706: "POKER TEXAS HOLD'EM", //德州扑克
    GAME_NAME_901: "SBTY", //一球成名,
    GAME_NAME_902: "CGCP", //创信
    GAME_NAME_1000: "MINI GOURD CRAB", //迷你鱼虾蟹
    GAME_NAME_1001: "MINI BIG SMALL", //迷你骰宝
    GAME_NAME_1002: "MINI HIGH LOW", //迷你上下
    GAME_NAME_1003: "MINI DIAMOND", //迷你宝石
    GAME_NAME_1004: "MINI POKER", //迷你扑克
    GAME_NAME_1005: "MINI BIG SMALL MD5", //迷你扑克
    GAME_NAME_2000: "", //迷你扑克
    GAME_NAME_2001: "", //迷你扑克
    GAME_NAME_2002: "", //迷你扑克
  },

  //全服广播
  NOTIFYMGR: {
    TEXT_TIP1: "Congrats", //恭喜
  },

  //UserInfoLayer
  USERINFO: {
    LAYER_TITLE: "User Information", //注册账号
    LAB_NAME: "Name", //账号名
    LAB_ID: "ID", //ID
    LAB_LV: "LEVEL", //等级
    LAB_REGISTER: "Register", //注册
    LAB_FACEBOOK: "FACEBOOK", //FACEBOOK登陆
    LAB_BTN_1: "Statement", //变动
    LAB_BTN_2: "Online Game History", //游戏记录
    LAB_BTN_3: "Settings", //设置
    LAB_BTN_4: "Logout",
    LAB_BTN_6: "Language",
  },

  //VipLayer
  VIP: {
    TITLE: `VIP CLUB`, //VIP标题
    NEEDCHARGE: `RECHARGE`, //充值
    TONEXTVIP: ` to upgrade to next level`, //达到下一级别
    BTN_CHARGE: `RECHARGE`,

    EXCLUSIVE_PAO: `Exclusive cannon desk`, //专属炮台
    EXCLUSIVE_FRAME: `Exclusive avatar frame`, //专属头像框
    EXCLUSIVE_SIMPLE: `Preview`,

    CHARGE: `Cumulatively recharged `, //累计充值

    NAME_PAO1: "Bloody Massacre", //炮的名字- -     Bloody Massacre
    NAME_PAO2: "Bloody Massacre", //炮的名字- -     Bloody Massacre
    NAME_PAO3: "Beam of Destruction", //炮的名字- -     Beam of Destruction
    NAME_PAO4: "Lightning Thunderstorm", //炮的名字- -     Lightning Thunderstorm
    NAME_PAO5: "Bursted Lava", //炮的名字- -     Bursted Lava
    NAME_PAO6: "Bursted Lava", //炮的名字- -     Bursted Lava
    NAME_PAO7: "Dragon's Wrath", //炮的名字- -     Dragon's Wrath
    NAME_PAO8: "Heaven’s Judgment", //炮的名字- -     Heaven’s Judgment

    TITLE_AWARDS_UP: `Bonus reward`, //升级奖励
    TITLE_AWARDS_WEEK: `Weekly reward`, //周奖励
    TITLE_AWARDS_MONTH: `Monthly reward`, //月奖励
    TITLE_AWARDS_RETROACTIVE: `Compensative reward`, //补签次数

    STATUS_1: `Not ready`, //不可领取
    STATUS_2: `Can claim`, // 可领取
    STATUS_3: `Claimed`,

    SUCCESS: `Successfully claimed`, // 领取奖励成功
    ERROR: `Failed to claim`, // 领取奖励失败
    ERROR_1: "This week's deposit has not reached %{limite}.", //  本周还需要充值LeftRech才能领取
    ERROR_2: "This month's deposit has not reached %{limite}.", //  本月还需要充值LeftRech才能领取
    TO_BONUS: "Note: Will be sent as Bonus",
  },

  //ActivityHelpLayer
  ACTIVITYHELP: {
    LAB_1: "Rules of wheel", //变动
    LAB_2: "Lucky Wheel", //幸运转盘
    LAB_3:
      "1. Today's bet points will be calculated by the corresponding number\
\nof points (ex: bet 10K gets 1 point)\
\n<br/>2. If the player does not participate in the bet today, there will \
\nbe no accumulated points the next day\
\n<br/>3. Every day the system will automatically update the points at 00:00,\
\nif the player does not use the bonus points after a day, the bonus \
\npoints will be lost.\
\n<br/>4. In each spin, the number of points used to participate in the wheel \
\nwill not be the same and the bonus amount will not be the same.",
  },



  //ActivityLayer  Panel_1
  ACTIVITY_PANEL1: {
    TIP_1: "Daily First Deposit", //每天第一笔存款
    TIP_2:
      "Ex: Member A has 1 deposit order for the first time of the day of %{pay}, will be immediately awarded by TDTC \nwith a prize of %{award}, completing 1 betting round can withdraw money!",
    TITLE_1: "Daily First Deposit", //每天第一笔存款
    TITLE_2: "Reward", //奖金
    BTN_1: "Activity Rules", //操作规则
    BTN_2: "Claim", //获得奖励
  },



  //ActivityLayer  Panel_3


  //ActivityLayer  Panel_4
  ACTIVITY_PANEL4: {
    AWARDITEM4_TIME: "4 Hours",
    AWARDITEM4_SCORE1: "UP TO",
    AWARDITEM4_SCORE: "288 points",
    AWARDITEM3_TIME: "2 Hours",
    AWARDITEM3_SCORE: "128 points",
    AWARDITEM2_TIME: "1 Hours",
    AWARDITEM2_SCORE: "58 points",
    AWARDITEM1_TIME: "30 mins",
    AWARDITEM1_SCORE: "28 points",
    AWARDITEM_BTN_OK: "Get reward",
    TITLE_TIP:
      "You have to claim the reward points yourself, online time will be reset after the reward is claimed. In case the reward is \
still not be claimed,\nonline time will not be counted for the next reward. It will not be cumulated to the next day, it will \
reset at the end of the day.",
  },

  //ActivityLayer  Panel_5


  //ActivityLayer  Panel_6




  //ActivityLayer  Panel_8
  ACTIVITY_PANEL8: {
    ACTIVITY8_STATUS_0: `Not ready`, //不可领取
    ACTIVITY8_STATUS_1: `Can claim`, // 可领取
    ACTIVITY8_STATUS_2: `Claimed`, // 已领取
    ACTIVITY8_TASK: `Daily bet`,
    ACTIVITY8_INFO: `Reach total bet %{money} on any game.`,
    ACTIVITY8_SCHEDULE: `(%{now}/%{all})`,
    ACTIVITY8_BTNOK: `DO`,
    ACTIVITY8_TIME: `Refresh the following mission:`,
    ACTIVITY8_GETERROR_STATUS_0: `Successfully awarded`, //领取成功
    ACTIVITY8_GETERROR_STATUS_1: `In game`, //正在游戏中
    ACTIVITY8_GETERROR_STATUS_2: `You have received`, //已经领取过
    ACTIVITY8_GETERROR_STATUS_3: `Receiving error`, //领取错误
    ACTIVITY8_GETERROR_STATUS_4: `Not ready`, //不可领取
  },

  //ActivityLayer  Panel_9


  //ActivityLayer  Panel_8
  ACTIVITY_PANEL8HELP: {
    TEXT_TITLE: `Rule`,
      INFOS: "- All missions will be refreshed after 23:59:59\
\n\n - Attention, the missions have been completed but \
\nnot claimed",
    INFOS1: `Completion progress`,
    INFOS2: `Bonus`,
    INFOS3: `Mission Information`,
  },

  //ActivityLayer  Panel_help
  ACTIVITY_HELP: {
    TEXT_TITLE: "Activity Rules",
    TEXT_1:
      "1. The bonus is calculating by the first highest of the deposit in the day.",
    TEXT_2:
      "2. The deposit amount will be automatically calculating by the system.",
    TEXT_3:
      "3. In order to ensure that the member information is valid TU88 has the right to ask for some information. If we found out that the member have created more than 1 account or use the fake information (can not be prove) to register, TU88 has the right to cancel, freeze the account,deduct all the bonus and the rewards that received from the rewarding collected points.",
  },

  //ActivityLayer activity1_listItem
  ACTIVITY_LISTITEM1: {
    HB_EXPRIED: "expired",
  },

  //ActivityLayer signIn_daleitem
  ACTIVITY_SIGNINDALEITEM: {
    TEXT_LOT: "Rewards\nreceived",
  },

  //NoticeSubpage1 activity6_listItem
  NOTICESUBPAGE1: {
    BTN_WATCH: "See",
  },

  //BankLayer
  BANK: {
    TIP_SELF: "Balance ", //账户金额
    TIP_BANK: "Amount in the safe", //保险箱金额
    TIP_MONEY_SAVE: "Deposits", //存款
    TIP_MONEY_TAKE: "Withrawal amount", //存款
    TIP_ENTER: "Please enter or select an amount", //请输入或选择金额
    TAG_TAKE: "Withdrawal", //提款
    TAG_SAVE: "Save", //存入保险箱
    BTN_ALL: "All", //全部
    BTN_OK_SAVE: "Save", //存入保险箱
    BTN_OK_TAKE: "Withdrawal", //提取到账号
  },

  //BindBank
  BINDBANK: {
    LAYER_TITLE: "Linked bank card", //绑定银行卡
    TIP_1: "Bank name", //银行
    TIP_2: "Account name", //账户名
    TIP_3: "Account number", //卡号
    TIP_INPUT_1: "Choose your bank", //选择银行
    TIP_INPUT_2: "Input account name", //输入账号名
    TIP_INPUT_3: "Input account number", //输入卡号

    TIP: "Bank info must be correct.\
\nFullname must be written in capitalization, with space, without Vietnamese accents!", //请务必正确输入您的银行信息。\n名字和姓氏用大写字母，空格，不能有空格！"
    BTN_OK: "Confirm", //确认
  },

  //ChangeLoginLayer
  CHANGELOGIN: {
    LAYER_TITLE: "Switch account", //  更改帐户
    TIP_1: "Reset password", //  重设密码
    TIP_2: "Login", //  登录
    TIP_3: "Another account", //  使用其他帐户登录
  },

  //CopyAgentUrlLayer
  COPYAGENTURL: {
    LAYER_TITLE: "Switch account", //代理链接
    TIP: "Click on the text, select all and copy", //全选文本并复制
  },

  //CustomerService
  CUSTOMERSERVICE: {
    LAYER_TITLE: "CSKH", //客服
    BTN_OK: "CSKH", //客服
    LABEL_BOTTOM: "DÙNG ZALO QUÉT MÃ QR\nLIÊN HỆ ZALO-CSKH",
  },


  //EnterPhoneLayer
  ENTERPHONE: {
    LAYER_TITLE: "Phone Number Activate", //激活电话号码
    BIND_TIP_ENTERPHONE:
      "Input your phone number\n(Each phone number will get %{money} one time only)", //“请输入您的电话号码（每个电话号码可以收到 %{ money} VND 1 次)
    TIP_ENTER: "Phone number", //电话号码
    BTN_SMS: "Message", //短消息
    BTN_TELEGRAM: "Telegram", //Telegram
  },

  //ExchangeHelpLayer
  EXCHANGEHELP: {
    LAYER_TITLE: "Withdrawal rules", //提款条件
    TIP: "- The minimum amount is 100 per transaction\
\n- The minimum bet amount is as same as the recharged amount\
\n  (Ex: Recharged 100, bet 100 at least)\
\n- You must leave 1 VND in your account after withdrawing",
    // - 最低提款金额为 100K
    // - 投注分数至少为提款金额的 1 倍
    //    （例如，如果您存入100K，则您的赌注必须达到100K）
    // - 提现后，您必须至少留出 1vnd
  },

  //ExchangeHelpLayer
  EXCHANGE: {
    LAYER_TITLE: "Withdraw",
    LAB_1: "Total balance",
    LAB_2: "Withdrawable amount",
    LAB_3: "Deposits",
    LAB_4: "Bonus balance",

    TIPS_1: "The balance can be used to place bets.",
    TIPS_2:
      "All the money you win from the games, which you can withdraw or use to play all the games",
    TIPS_3:
      "The amount deposited, can be used to play all games but cannot be withdrawn.",
    TIPS_4:
      "For every 10  of losing bet, you will be issued 1\n to the deposit balance.",

    BTN_1: "Withdrawal history",
    BTN_2: "Bank account\ninformation",
    BTN_3: "Withdraw",

    WITHDRAW_LAYER_TITLE: "Balance",
    WITHDRAW_LAB_1: "Withdrawable amount",
    WITHDRAW_LAB_2: "Withdrawal amount",
    WITHDRAW_TIPS_1: "Please enter the withdrawal amount",
    WITHDRAW_BTN_1: "All",
    WITHDRAW_BTN_2: "Withdraw",
  },

  //ExchangeHelpLayer
  EXCHANGELOG: {
    LAYER_TITLE: "Withrawal history", //提款记录
    TITLE_1: "Order ID", //单码
    TITLE_2: "Time", //提款时间
    TITLE_3: "Method", //提款方式
    TITLE_4: "Withrawal amount", //提款金额
    TITLE_6: "Status", //状态
    TIP_HINT: "No record", //暂无记录

    EXCHANGE_RECORD_STATUS_1: "Not yet processed", //未受理
    EXCHANGE_RECORD_STATUS_2: "Processing", //受理中
    EXCHANGE_RECORD_STATUS_3: "Pass", //通过
    EXCHANGE_RECORD_STATUS_4: "do not pass", //没有通过
    EXCHANGE_RECORD_STATUS_5: "Recall", //撤销
    EXCHANGE_RECORD_STATUS_6: "Lock", //锁
  },

  //gamedetailinfo
  GAMEDETAILINFO: {
    LAYER_TITLE: "Bet Record", //余额变动
    TITLE_1: "Time", //时间
    TITLE_2: "Game", //游戏名称
    TITLE_3: "Bet Amount", //投注金额
    TITLE_4: "Win/Loss", //余额变动
    BTN_OK: "Confirm", //确认
    BTN_CANCLE: "Cancel", //取消
  },

  //LoginOther
  LOGINOTHER: {
    LAYER_TITLE: "Login", //登录
    TIP_1: "Phone number", //电话号码
    TIP_2: "Login password", //登录密码
    TIP_ENTER_1: "Input your phone number", //请输入电话号码
    TIP_ENTER_2: "Please input your login password", //请输入您的登录密码
    TIP_ENTER_3: "Input the verify code", //输入确认码
    BTN_OK: "Login",
  },

  //NoticeMailDetail
  MAILDETAIL: {
    LAYER_TITLE: "Details", //细节
  },

  //changeNickName
  CHANGENICKNAME: {
    LAYER_TITLE: "Change display name", //修改昵称
    TIP_1: "Input a new display name", //输入新显示名称
    TIP_2: "An account can only change the display name once", //每个人最多修改1次昵称
    TIP_ENTER: "Input a new display name", //请输入新的名字
    BTN_OK: "Confirm",
  },



  //OnlineGameDetailInfo
  ONLINEGAMEDETAIL: {
    LAYER_TITLE: "Online Game History", //在线游戏记录
    LAYER_TITLE1: "Import/Export history", //资金进出记录
    LAYER_TITLE2: "BET AMOUNT HISTORY", //投注历史
    TIP_DATATYPE_1: "Game Type", //类型
    TIP_DATATYPE_2: "Game Type", //类型
    TIP_SHOWTYPE_1_1: "Order ID", //单码
    TIP_SHOWTYPE_1_2: "Time", //时间
    TIP_SHOWTYPE_1_3: "Method", //状态
    TIP_SHOWTYPE_1_4: "Amount", //金额
    TIP_SHOWTYPE_2_1: "Order ID", //单码
    TIP_SHOWTYPE_2_2: "Time", //时间
    TIP_SHOWTYPE_2_3: "Bet Amount", //投注金额
    TIP_SHOWTYPE_2_4: "Win/Lose", //中奖金额
    BTN_OK: "Confirm", //确认
    BTN_CANCLE: "Cancel", //取消
    TIP_IN: "transfer in",
    TIP_OUT: "transfer out",
  },

  //OptionLayer
  OPTION: {
    LAYER_TITLE: "Settings", //设置
    TIP_1: "Music", //音乐
    TIP_2: "Sound Effects", //音效
  },

  //RankLayer
  RANK: {
    LAYER_TITLE: "Leaderboard", //排行
    TIP_1: "Today's winning amount", //今日输赢
    TIP_2: "Time Online", //在线时间
    TIP_3: "Rank", //等级
    NORANK: "Not yet on the board", //未上榜
  },

  //RechargeLayer
  RECHARGE: {
    LAYER_TITLE: "RECHARGE", //充值
    TIP_1: "Bank", //银行
    TIP_2: "MOMO", //MOMO
    TIP_3: "ONLINE RECHARGE", //在线充值
    TIP_4: "Recharge history", //充值记录
    TIP_5: "GIFTCODE", //礼品码
  },

  //accountLayer
  ACCOUNTLAYER: {
    LAYER_TITLE: "Register an official account", //注册账号
    LAB_NUM: "Phone number", //电话号码
    LAB_PWD: "Login password", //登陆密码
    LAB_REP_PWD: "Verify code", //验证码
    LAB_REP_PWD1: "Verify code", //验证码
    LAB_OK: "Register", //确认
    LAB_COPY: "COPY", //复制
    TIP_PHONE: "Input your phone number", //请输入电话号码
    TIP_BOTYZM: "Input the verify code", //输入验证码
    TIP_PWD: "Please input your login password", //输入登陆密码
    TIP_YZM: "Input the verify code", //输入验证码
    LAB_BTN_BOT: "Telegram", //Telegram
    LAB_BTN_SMS: "SMS", //短信
    TIP_SEND_1_1: "Please text:", //发送短信：
    TIP_SEND1: "<color=#ffffff>%{tip1} </c><color=#6BFF89>%{content}</color>",
    TIP_SEND_2_1: "Send to", //发给
    TIP_SEND_2_2: "(1K/sms)", //1K每条
    TIP_SEND2:
      "<color=#ffffff>%{tip1}  </c><color=#FFE146>%{content} </color><color=#ffffff>%{tip2}</c>", //Telegram
  },

  //ResetPswd
  ResetPswd: {
    LAYER_TITLE: "Reset password", //重设密码
    LAB_NUM: "Phone number", //电话号码
    LAB_PWD: "Login password", //登陆密码
    LAB_REP_PWD: "Verify code", //验证码
    LAB_REP_PWD1: "Verify code", //验证码
    LAB_OK: "Register", //确认
    LAB_COPY: "COPY", //复制
    TIP_PHONE: "Input your phone number", //请输入电话号码
    TIP_BOTYZM: "Input the verify code", //输入验证码
    TIP_PWD: "Please input your login password", //输入登陆密码
    TIP_YZM: "Input the verify code", //输入验证码
    LAB_BTN_BOT: "Telegram", //Telegram
    LAB_BTN_SMS: "SMS", //短信
    TIP_SEND_1_1: "Please text:", //发送短信：
    TIP_SEND1: "<color=#ffffff>%{tip1} </c><color=#6BFF89>%{content}</color>",
    TIP_SEND_2_1: "Send to", //发给
    TIP_SEND_2_2: "(1K/sms)", //1K每条
    TIP_SEND2:
      "<color=#ffffff>%{tip1}  </c><color=#FFE146>%{content} </color><color=#ffffff>%{tip2}</c>", //Telegram
  },

  SELECTHEAD: {
    LAYER_TITLE: "Avatar", //头像
    BTN_1: "Confirm", //确认
  },

  USERDETAIL: {
    LAYER_TITLE: "Statement", //余额变化

    LAYER_TITLE1: "Statement details", //余额变化
    LAYER_TITLE2: "Bet Details", //投注记录

    BTN_OK: "Confirm", //确认
    BTN_CANCLE: "Cancel", //取消

    TIP_DATATYPE_1: "Type", //类型
    TIP_DATATYPE_2: "Game", //类型

    TIP_SHOWTYPE_1_1: "Time", //时间
    TIP_SHOWTYPE_1_2: "Type", //类型
    TIP_SHOWTYPE_1_3: "Balance", //账户余额
    TIP_SHOWTYPE_1_4: "Win/Loss", //余额波动

    TIP_SHOWTYPE_2_1: "Time", //时间
    TIP_SHOWTYPE_2_2: "Game", //游戏名称
    TIP_SHOWTYPE_2_3: "Bet Amount", //投注金额
    TIP_SHOWTYPE_2_4: "Win/Loss", //余额波动
  },

  //AgentLayer
  AGENT: {
    LAYER_TITLE: "Agent", //代理

    BTN_1: "My Agent", //代理数据
    BTN_2: "Agent Process", //代理流程
    BTN_3: "My report", //代理报表
    BTN_4: "Search lower grade", //下属报表
    BTN_5: "My report", //我的报告
    BTN_6: "Agent Rating", //经销商评级
    BTN_7: "Agent Mission", //代理任务
    BTN_8: "Report of the lower grade", //下级报告
    BTN_9: "Daily Ranking", //代理排名
  },

  //AgentLayer pageLayer_1
  AGENT_PAGELAYER1: {
    TEXT_ALLAWARD: "Withrawal history",
    TEXT_TAWARD: "Ready for withdrawing",
    TEXT_BTN_TQJL: "Withrawal history",
    TEXT_BTN_TQFX: "Withdraw",
    TEXT_TA: "Today's commission: ",
    TEXT_TAX: "Agent tax: ",
    TEXT_PERIOD: "Weekly stats: ",
    TEXT_NEWPEOPLE: "New members: ",
    TEXT_PERIOD_TXT: "Immediately",
    TEXT_BTN_ZOOM: "Click on the QR code to copy the agent link",
  },

  //AgentLayer pageLayer_2
  AGENT_PAGELAYER2: {
    TEXT_1: "1st Class Income",
    TEXT_2: "2nd Class  Income",
    TEXT_3: "nth Class Income",
    TEXT_4: "1st Class Income",
    TEXT_5: "2nd Class  Income",
    TEXT_6: "nth Class Income",
    TEXT_7:
      "Agent commission = 30% of 1st Class member's winning tax + 30%x30% of 2nd Class member's winning tax + ... + commission\
        \n from 1st Class member.",
    TEXT_8: "Example:",
    TEXT_9: "B=Your 1st Class member, winning tax is 10K",
    TEXT_10: "B1=Your 2nd Class member, winning tax is 20K",
    TEXT_11:
      "Your commission = 10Kx30% + (20Kx30%)x30%. B's commission = 20Kx30%.",
  },

  //AgentLayer pageLayer_3
  AGENT_PAGELAYER3: {
    TEXT_MYDATA_1: "Active users",
    TEXT_MYDATA_2: "Subordinates:",
    TEXT_MYDATA_3: "Other:",
    TEXT_MYDATA_4: "New users",
    TEXT_MYDATA_5: "Total users:",
    TEXT_MYBANKMONEY_1: "My income:",
    TEXT_MYBANKMONEY_2: "Subordinates:",
    TEXT_MYBANKMONEY_3: "Other:",
    TEXT_TEAM_1: "Total bet:",
    TEXT_TEAM_2: "Subordinates:",
    TEXT_TEAM_3: "Other:",
    TEXT_REPORT: "Time filter",
  },

  //AgentLayer pageLayer_4
  AGENT_PAGELAYER4: {
    TEXT_REPORT: "Time filter",
    TEXT_TEAMOFFER: "Contributed profit ",
    TEXT_TEAMPNUM: "Team members",
    TEXT_TEAMID: "Team member ID",
    TEXT_TEAMCOMSION: "Earned profit",
    TEXT_HINT: "No record ",
  },

  //AgentLayer pageLayer_6
  AGENT_PAGELAYER6: {
    TEXT_MFI: "Income",
    TEXT_INFO: "Display name",
    TEXT_RANK: "Rank",
    TEXT_HINT: "No record ",
  },

  //AgentLayer pageLayer_7
  AGENT_PAGELAYER7: {
    TEXT_TITLE: "Qualified agents: ",
    TEXT_RPTIPS: "No record ",
    TEXT_BTN_WEEK: "Weekly agent board",
    TEXT_BTN_ALL: "Total agent board",
    TEXT_TITLE1: "Agents on task:",
    TEXT_TITLE2: "My agent tasks",
    TETXT_TIME: "Time",
    TEXT_MONEY: "Recharge amount",
    TEXT_ID: "ID",
    TEXT_RTTIPS: "No record",
    TEXT_RECEIVED: "Received",
    TEXT_1:
      "Task details: Each affiliated member invited with a recharge amount of %{money} will be counted as qualified\
        \nThe number of qualified agents who meet the requirements of the Lucky Money levels can receive the corresponding\
        \namount of money in the Lucky Money's envelope.",
  },

  //AgentLayer pageLayer_8
  AGENT_PAGELAYER8: {
    TEXT_TITLE1: "Team member ID",
    TEXT_TITLE2: "Type",
    TEXT_TITLE4: "BET AMOUNT",
    TEXT_TYPE: "Type",
    TEXT_BTN_OK: "Confirm",
    TEXT_BTN_CANCLE: "Cancel",
  },

  //AgentLayer pageLayer_9
  AGENT_PAGELAYER9: {
    agent_rank_tip_dat1: "",
    agent_rank_success: "Received successfully!", //领取成功
    agent_rank_awardErr_1: "in game!", //正在游戏中
    agent_rank_awardErr_2: "Not on the list!", //未上榜
    agent_rank_awardErr_3: "received!", //已经领取
    agent_rank_awardErr_4: "Reward configuration error", //奖励配置错误
    agent_rank_awardErr_5: "Not meeting the promotion requirement", //未达到推广人数需求

    rank_tip: "Not on the list", //未上榜

    TEXT_TITLE_1: "Xếp hạng",
    TEXT_TITLE_2: "Tên tài khoản",
    TEXT_TITLE_3: "Số người giới thiệu",
    TEXT_TITLE_4: "Tiền hoa hồng",
    TEXT_TITLE_5: "Tiền thưởng",
    BTN_GET_REWARD: "Lĩnh thưởng", //获得奖励
  },

  //AgentLayer PopularizeAllLog
  AGENT_POPULARIZEALL: {
    TEXT_TITLE: "Total agent board",
    TEXT_LABEL1: "Rank",
    TEXT_LABEL2: "Name",
    TEXT_LABEL3: "Number of people",
    TEXT_HINT: "No record ",
  },

  //AgentLayer PopularizeWeekLog
  AGENT_POPULARIZEWEEK: {
    TEXT_TITLE: "Weekly agent board",
    TEXT_LABEL1: "Rank",
    TEXT_LABEL2: "Name",
    TEXT_LABEL3: "Number of people",
    TEXT_HINT: "No record ",
  },

  //AgentLayer TuiguangRecordList
  AGENT_TUIGUANGRECORDLIST: {
    TEXT_TITLE: "Withrawal history",
    TEXT_LABEL1: "Time",
    TEXT_LABEL2: "Withrawal amount",
    TEXT_LABEL3: "Status",
    TEXT_HINT: "No record ",
  },

  //RedBagLayer
  REDBAG: {
    TIPS_1: "Ascending Angel's Lucky Money Rain, Online to receive big gifts", //代理数据
    TIPS_2: "Everybody can get gifts, first come first serve.", //代理流程
    TIPS_3: "Remaining lucky money rain time :", //代理报表
    TIPS_4: "Lucky money rain time :", //下属报表
    TIPS_5:
      "Lucky money rain is automatic, players after opening the\nred envelopes will receive a random bonus amount.\nSurprises don't stop, Rewards don't stop.", //我的报告
    BTN_1: "Receive",
  },

  //GoldAndRegister
  GOLDANDREGISTER: {
    BTN_1: "Register now",
  },

  //expand   QueryLayer
  QUERYLAYER: {
    TEXT_TITLE: "Display",
    TEXT_BTN_CONFIRM: "Yes",
    TEXT_BTN_CONCEL: "No",
    TEXT_BTN_CONFIRM2: "Yes",
  },

  //expand   WaitingLayer
  WAITINGLAYER: {
    TEXT_WAITING: "Please wait...",
  },

  //Item  GameIcon
  GameIcon: {
    TEXT_UPDATE: "Pending\nprocessing...",
  },

  //Item  RoomIcon
  ROOMTAG: {
    ROOM_BET_FREE: "FREE TRIAL",
    ROOM_BET_LIMITE1: "PLACE A BET ",
    ROOM_BET_LIMITE2: "BET ",
    ROOM_BET_LIMITE3: "BET TABLE ",
    ROOM_LIMIT: "PLAY",
  },

  //miniGame   btn_miniGame
  MINIGAME: {
    TEXT_GAME1: "HOO HEY HOW",
    TEXT_GAME2: "BIG SMALL",
    TEXT_GAME3: "HIGH LOW",
    TEXT_GAME4: "DIAMOND",
    TEXT_GAME5: "MINI POKER",
    TEXT_GAME6: "BIG SMALL MD5",
  },

  //onlineList   online_cell_1
  ONLINE_CELL: {
    TEXT_LABEL1: "BET",
    TEXT_LABEL2: "WIN",
    TEXT_ROUND: "Rounds",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.",
  },

  //onlineList  onlineList_1
  ONLINELIST: {
    TEXT_TITLE: "Online Players",
  },

  _payType: ["Paymaya", "GCASH", "JuanCash", "Alipay"],

  //Recharge    rechargeInfo_1
  RECHARGEINFO1: {
    TEXT_SUM2: "Account Number:",
    TEXT_SUM3: "Account Name:",
    TEXT_SUM4: "Charge limit:",

    TEXT_SUM1_1: "Method:",
    TEXT_SUM2_1: "Amount:",
    TEXT_SUM3_1: "Sender:",
    TEXT_SUM4_1: "Transfer Content:",

    TEXT_BTN_OK: "RECHARGE",

    TEXT_EG:
      "Note: Please fill in the exact required information in the above transfer content.",
  },

  RECHARGEINFO2: {
    TEXT_SUM2: "Account Number:",
    TEXT_SUM3: "Account Name:",
    TEXT_SUM4: "Charge limit:",

    TEXT_SUM2_1: "Amount:",
    TEXT_SUM3_1: "Sender:",
    TEXT_SUM4_1: "Transfer Content:",

    TEXT_BTN_OK: "RECHARGE",

    TEXT_EG:
      "Note: Please fill in the exact required information in the above transfer content..",
  },

  //Recharge    rechargeInfo_3
  RECHARGEINFO3: {
    TEXT_SUM: "Amount:",
    TEXT_EDITBOX_MONEY: "Input the amount",
    TEXT_BTN_OK: "RECHARGE",
    TEXT_MONEY: "Charge limit: ",
  },

  //Recharge    rechargeInfo_4
  RECHARGEINFO4: {
    TEXT_SUM1: "Recharge history",
    TEXT_ORDERLD: "Order ID",
    TEXT_TIME: "Time ",
    TEXT_MONEY: "Amount",
    TEXT_ONLINETYPE: "Method ",
    TEXT_STATUS: "Status",
    TEXT_HINT: "No record",
  },

  //Recharge    rechargeInfo_5
  RECHARGEINFO5: {
    TEXT_EDITBOX_MONEY: "Enter Giftcode",
    BTN_1: "OK",
  },

  //Recharge    rechargeInfo_6
  RECHARGEINFO6: {
    TIPS_1: "VALUE",
    TIPS_2: "EXCHANGE",
    TIPS_3: "You will not be refunded if you choose the wrong value!",
    TEXT_TIP_1: "Choose card value",
    TEXT_TIP_2: "Enter the serial number",
    TEXT_TIP_3: "Enter card code",
    BTN_1: "Charge",
  },

  //Recharge    rechargeInfo_6help
  RECHARGEINFO6HELP: {
    TITLE: "CARD CHARGE",
    TIPS_1: "INSTRUCTIONS  TO  CHARGE",
    TIPS_2: "STEP",
    TEXT_TIP_1:
      "- Choose     Charge\n- Choose     Network\n- Choose     Card Type\n- Choose     Card Value",
    TEXT_TIP_2: "- Enter     Serie Number\n- Enter     Card Number",
    TEXT_TIP_3: "- Check out the information\n- Confirm",
  },


  //GoldAndRegister
  BONUS: {
    LIST_TITLE: "BONUS",
    LIST_TIP1: "Bonus\nType",
    LIST_TIP2: "Issued\nAmount",
    LIST_TIP3: "Locked\nAmount",
    LIST_TIP4: "Released\nAmount",
    LIST_TIP5: "Status",
    LIST_TIP6: "Releasing\ndate",
    LIST_TIP7: "Expiration\ndate",
    LIST_TIP8: "Expired", //已过期
    LIST_TIP9: "Unexpired", //没过期
    LIST_TIP10: "Received", //已领完
    TO_BONUS_TIP: "BONUS DISTRIBUTION: Lose 10 VND Get 1VND reward",
  },

  BONUS_AWARDSUCCESS: `Congratulations, you get %{money} bonus!`, //恭喜您,获得%{0}元bonus！

  SLOT_GAME_HELP_TIP1: "All wins are multiplied by the wager per payline.", //所有获胜都乘以每条支付线的赌注。
  SLOT_GAME_HELP_TIP2: "Symbol combinations are counted from left to right", //符号组合从左到右计数

  deleteHint: "Confirm to delete Account?", //确认删除账号？

  MINI_UD_TIP_START: "Press Start to play", //点击开始进行下注
  MINI_UD_TIP_LOST: "You lose!", //你输了!
  MINI_UD_POT_INFO: "Jackpot", //爆奖？
  MINI_UD_BET_WIN: "Win", //赢
  MINI_UD_BET_LOST: "Lose", //输

  MINI_SB_WINRANK_BETTIPS: "Your total bet amount is ", //你的赌注是
  MINI_SB_WINRANK_NEEDBETDANWEI: " billion", //你的赌注是

  MINI_BS_LINE_TIP: " ROW",
  MINI_BS_LINE_TIP_NULLLINE: "You must select at least 1 line",

  ERROR_LOGIN_3rd:
    "Failed to connect to the server, please contact Customer Service.", //连接服务器失败
  ERROR_LOGIN:
    "Failed to connect to the server, please contact Customer Service\nerror code:", //连接服务器失败,错误代码:
  ERROR_LOGIN_2: "Failed to connect to the server.", //连接服务器失败
  ERROR_LOGIN500: "Facebook connection failed", //Facebook 连接失败:
  ERROR_LOGIN501: "The server is not available, please try again later", //Facebook 连接失败:

  //登陆游戏错误警告
  ERROR_202: "Game config error, please try again later", //游戏配置出错,请稍后重试
  ERROR_203: "No entry to the room, please try again later", //禁止进入房间,请稍后重试
  ERROR_204: "Gold coin is invalid, please try again later", //金币异常,请稍后重试
  ERROR_205:
    "Gold coins you carrying lower than the minimum entry requirement, the limit is: %{limite}", //携带金币低于最低入场金币,限制金币为:%{limite}
  ERROR_205_1:
    "Gold coins you carrying lower than the minimum entry requirement.", //携带金币低于最低入场金币
  ERROR_206:
    "Gold coins you carrying higher than the highest entry requirement, the limit is: %{limite}.", //携带金币高于最高入场金币,限制金币为:%{limite}
  ERROR_206_1:
    "Gold coins you carrying higher than the highest entry requirement.", //携带金币高于最高入场金币
  ERROR_207: "This room is full, please try again later", //该房间人数已满,请稍后重试
  ERROR_208: "Assigning location error, please try again later", //分配位置出错,请稍后重试
  ERROR_501: "The server is unavailable, please try again later ", //"服务器不可用,请稍后再试",
  ERROR_999: "The server is unavailable, please try again later ", //"服务器不可用,请稍后再试",
  //骰宝
  TXT_BIG: "Big", // BIG
  TXT_SMALL: "Small", // SMALL

  TXT_SMALL_INFO: `Set %{betArea} #%{no}. Result %{winArea},Total bet %{betScore} Refund %{backScore},Receive %{winScore}`,

  BETERROR_0: "Time's Up!", //停止下注
  BETERROR_1: "Parameter error", //参数错误
  BETERROR_2: "Your balance is lower than 20K", //'您的账号余额低于最低下注金额 20K ',
  BETERROR_2_1: "Your balance is lower than %{limite}", //'您的账号余额低于最低下注金额...',
  BETERROR_3: "The bet value is higher than carrying gold coins", //下注值大于携带金币
  BETERROR_4: "The player's maximum bet limit is exceeded", //超过玩家最大下注限制
  BETERROR_5: "The region's maximum bet limit is exceeded", //超过区域最大下注限制
  BETERROR_6: "Reached the maximum bet multiple limit", //达到最大下注倍数限制
  BETERROR_7: "An area cannot bet at the same time", //'区域不能同时押注',
  BETERROR_8: "Reached Tie/Pair bet total limit", //达到和/对子下注总限制
  BETERROR_9: "you are in another game, please return to game to place bet", //你当前正在对战游戏中，请返回游戏里下注
  BETERROR_10:
    "Balance to participate in game room must not be lower than the lowest bet level", //你当前正在对战游戏中，请返回游戏里下注

  LEAVEGAME_TIP: "Don’t you play more for a while?", //"不在多玩一会儿么？"

  //绝地求生
  word_lab_bet: "Bet\nAmount ", // 下注金额
  word_lab_win: "Win\nAmount ", // 赢得金额
  word_lab_speedup: "Turbo", //加速
  word_lab_auto: "Auto", //自动
  word_lab_start: "Start", //开始
  word_lab_freeGames: "10 free\ngames",
  word_lab_own: "Current\nAmount", //拥有金额
  word_lab_left: "Balance", //剩余
  word_lab_kill: "Beat", //击败

  //bjl
  BJL: {
    GAME_STATUS_PLAY: `Playing`, //游戏中
    GAME_STATUS_END: `Reviewing`, //结算中

    ROOM_W_NAME_TIP: `Classic Cards %{roomID}`, //无咪
    ROOM_P_NAME_TIP: `Room No. %{roomID}`, //批量
    ROOM_RULE_TIP: `Bet Amount %{ruleStr}`, //限红
    ROOM_STATUS_TIP: `Playing`, //游戏中

    TABLE_TITLE_1: `Bet level %{str}`, // 限红
    TABLE_TITLE_2: `Lowest bet level of Tie/Pair %{str}`, // 和/对子下限
    TABLE_TITLE_3: `Highest bet level of Tie/Pair %{str}`, // 和/对子上限

    ZHUANG_NUM_TIP: `Banker  %{round}`, //庄
    XIAN_NUM_TIP: `Player %{round}`, //闲
    HE_NUM_TIP: `Tie %{round}`, //和

    LAB_L_Z_TIP: `Banker`, //庄
    LAB_L_X_TIP: `Player`, //闲
    LAB_L_H_TIP: `Tie`, //和

    LAB_L_ROUND: `Round`, //局

    LAB_B_XD_TIP: `Player Pair`, //批量场 闲对
    LAB_B_X_TIP: `Player`, //批量场 闲
    LAB_B_H_TIP: `Tie`, //批量场 和
    LAB_B_Z_TIP: `Banker`, //批量场 庄
    LAB_B_ZD_TIP: `Banker Pair`, //批量场 庄对

    LAB_N_Z_TIP: `B`, //批量场右侧      庄
    LAB_N_X_TIP: `P`, //批量场右侧      闲

    LAB_WUMI_N_Z_TIP: `Next round Banker`, //下局Banker
    LAB_WUMI_N_X_TIP: `Next round Player`, //下局Player

    LAB_ZHUANG_W: "Banker\nWin",
    LAB_XIAN_W: "Player\nWin",
    LAB_HE_w: "Tie",

    LAB_ZHUANG_T: "Banker",
    LAB_XIAN_T: "Player",

    BJL_MP_TIP1: "Classic Bet",
    BJL_MP_TIP2: "ROOM_W_NAME_TIP",
    BJL_MP_TIP3: "Bet level",
    BJL_MP_TIP4: "Place your bets!",
    BJL_MP_TIP5: "Multi Bet",

    LUDAN_T1: "Banker",
    LUDAN_T2: "Player",
    LUDAN_T3: "Tie",
    LUDAN_T4: "Banker Pair",
    LUDAN_T5: "Player Pair",
    LUDAN_T6: "8/9",
    LUDAN_T7: "Round",

    LAB_VAO_GAME: "PLAY NOW",

    BTN_XY: "Bet\nAgain",
    LAB_LINE: "Online",

    LAB_TABLE_T1: "Player Pair",
    LAB_TABLE_T2: "Tie",
    LAB_TABLE_T3: "Banker Pair",
    LAB_TABLE_T4: "Player",
    LAB_TABLE_T5: "Banker",

    LAB_WAIT: "Please wait till next round ...",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
  },

  FISH: {
    PRICE_19: "50X Reward",
    PRICE_20: "50X Reward",
    PRICE_21: "Gold Hammerhead",
    PRICE_22: "60X Reward",
    PRICE_23: "Gold Squarhead",
    PRICE_24: "80X Reward",
    PRICE_25: "Gold Spider",
    PRICE_26: "Gold Arowana",
    PRICE_27: "Gold Frog",
    PRICE_28: "BOSS Crocodile",

    CANNON_TYPE_1: "Default Gun",
    CANNON_TYPE_2: "Slaughtering Gun",
    CANNON_TYPE_3: "Laser Gun",
    CANNON_TYPE_4: "Thunder Gun",
    CANNON_TYPE_5: "Electric Gun",
    CANNON_TYPE_6: "Dragon Gun",
    CANNON_TYPE_7: "Light Gun",

    FISH_TITLE_1: "Change guns",
    FISH_TITLE_2: "Fish Encyclopedia",

    LAB_GET: "Claim",
    LAB_CHANGE: "Switch",
    LAB_C_XZ: "Equipped",
    LAB_C_HINT: "Exclusive",

    LAB_WAIT: "Waiting to Join",

    BTN_LAB_AUTO: "Auto",
    BTN_LAB_SPEED: "Lock",
    BTN_LAB_HELP: "Help",
    BTN_LAB_SET: "Settings",
    BTN_LAB_EXIT: "Exit",
    BTN_LAB_CHANGE: "Switch guns",
  },

  CSD: {
    LAB_SCORE_T: "Balance",

    LAB_LINE_T: "BET",
    LAB_WINSCORE_T: "WIN",
    LAB_SPEED_T: "Turbo",
    LAB_AUTO_T: "Auto",
    LAB_START_T: "Start",
    LAB_STOP_T: "STOP",
    LAB_FREE_T1: "Congratulations on getting\n10 free spins!",
    LAB_FREE_T2: "Free Spin",
    LAB_FREE_T3: "Time",
  },

  //dzpk
  DZPK: {
    dzpk_status_1: "Fold", //弃牌
    dzpk_status_2: "Call", //跟住
    dzpk_status_3: "Raise", //加注
    dzpk_status_4: "All-in", //全压
    dzpk_status_5: "Check", //让牌

    card_type_1: "Royal Flush",
    card_type_2: "Straight Flush",
    card_type_3: "Four of a kind",
    card_type_4: "Full house",
    card_type_5: "Flush",
    card_type_6: "Straight",
    card_type_7: "Three of a kind",
    card_type_8: "Two Pairs",
    card_type_9: "Pair",
    card_type_10: "High card",

    card_kind_1: "High card",
    card_kind_2: "Pair",
    card_kind_3: "Two Pairs",
    card_kind_4: "Three of a kind",
    card_kind_5: "Straight",
    card_kind_6: "Flush",
    card_kind_7: "Full house",
    card_kind_8: "Four of a kind",
    card_kind_9: "Straight Flush",
    card_kind_10: "Royal Straight Flush",

    countdown_t: "The game is starting",

    cell_score: "Small/Big blind bet:%{lCellScore} unlimited",

    LAB_WIN: "YOU WON",
    LAB_LOSt: "YOU LOSE",

    LAB_TITLE: "Total bet",

    LAB_BTN_GIVEUP: "Fold",
    LAB_BTN_FOLLOW: "Call",
    LAB_BTN_PASS: "Call",
    LAB_BTN_ALLIN: "Upper hand",
    LAB_BTN_ADD: "Raise",
    LAB_BTN_ADD_S: "Total bet",
    LAB_BTN_ADD_B: "Big Blind",

    LAB_BTN_T1: "Yield\nFold",
    LAB_BTN_T2: "Check/Fold",
    LAB_BTN_T3: "Call any",
    LAB_BTN_T4: "Call",
    LAB_BTN_T5: "Leave the game",
    LAB_BTN_T6: "Continue playing",

    LAB_WAIT_S: "Please wait till next round ...",
    LAB_WAIT_O: "Waiting for other players...",
  },

  FCZLM: {
    hintNode_1: "Congratulations you won",
    hintNode_2: 'from bonus of the game "Infinity Stone"',
    hintNode_3: "Congratulations you won",
    hintNode_4: "from the free spin",
  },

  FQZS: {
    LUDAN_LONG: "Birds: %{num}",
    LUDAN_HU: "Beasts: %{num}",
    LUDAN_HE: "Shark: %{num}",
    LUDAN_JU: "No. of Games: %{num}",

    LUDAN_T: "Beasts",
    LUDAN_C: "Birds",
    LUDAN_M: "Shark",

    LAB_LUDAN_TITLE: "BIRDS AND BEASTS",
    LAB_LUDAN_T1: "Stats",
    LAB_LUDAN_T2: "Filter\n(Animal)",
    LAB_LUDAN_T3: "Filter\n(Type)",
    LAB_LUDAN_T4: "Last 20 Games",
    LAB_LUDAN_T5: "Game Statistics",
    LAB_LUDAN_T6: "Filter (Animal)",
    LAB_LUDAN_T7: "Leave the game",
    LAB_LUDAN_T8: "Continue playing",

    LAB_GAME_BG_BIRDS: "Birds",
    LAB_GAME_BG_BEASTS: "Beasts",
    LAB_GOLD: "Recharge",
    LAB_ONLINE: "Online",
    LAB_XY: "Bet\nAgain",
    LAB_COUNTDOWN: "Place your bets!",
    LAB_WAIT: "Please wait till next round . . .",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
  },

  JLBHD: {
    SMALLGAME_T1: "Select the icon to get gold", //选择图标获得金币,
    SMALLGAME_T2: "Congratulations you won", //恭喜你赢了,
    SMALLGAME_T3: "from the bonus game Treasure Hunt", //从寻宝奖励游戏,
    SMALLGAME_TIME: "Automatically after:",

    JLBHD_T1: "Congratulations you won",
    JLBHD_T2: "from the free spin",
    JLBHD_T3: "Choose a ship to rob",
    JLBHD_T4: "Congratulations you won",
    JLBHD_T5: "of the game x2",
    JLBHD_T6: "You lose",
    JLBHD_T7: "Congrats, you robbed the treasure successfully",

    JLBHD_BET_T1: "Line(s)",
    JLBHD_BET_T2: "Total Bet:",
    JLBHD_BET_T3: "Bet",
    JLBHD_BET_T4: "Turbo",

    MINIGMAE_TITLE: "TREASURE\nHUNT",

    BOX_TITLE_1: "Notification",
    BOX_TITLE_2: "Choose a row",
    BOX_TITLE_3: "STEAL SHIP",
    BOX_QUIT: "Back",

    BOX_T1: "Back",
    BOX_T2: "Won",

    BOX_BTN_T1: "Even row",
    BOX_BTN_T2: "Odd row",
    BOX_BTN_T3: "All",
    BOX_BTN_T4: "Uncheck",
    BOX_BTN_T5: "KEEP STEALING",
    BOX_BTN_T6: "STOP",

    freeGame: "FREE\nSPINS",
  },

  JDQS: {
    EatChicken_t1: "Winner winner, chicken dinner!",
    EatChicken_t2: "Players beat 10 opponents to win the top spot",
    EatChicken_t3: "Let's have a chicken dish to celebrate",
    EatChicken_t4: "The system will auto-select after %{time}s",
    EatChicken_t5:
      "<color=#E6CC2E><size=60><b>Rating 1</b></size></c><color=#737373><size=40><b>/100</b></size></c>",

    ScoreOwn: "Current\nAmount",
    KillNum: "Defeat",
    RankNum: "Balance",
    BetNum: "Bet\nAmount",
    WinNum: "Winning\nAmount",
    AutoGame: "Auto ",
    SpeedGame: "Turbo",
    StartGame: "Spin",
    StopGame: "Stop",

    binWin: "Congratulations\nyou have received",
    FreeEnd_T1: "Congratulations\nyou have received",
    FreeEnd_T2: "from the free game",

    FreeStart_T1: "Free",
    FreeStart_T2: "to",
    FreeStart_T3: "play",
    FreeStart_T4: "times", //合起来是 Miễn phí chơi 10 lần
  },

  JPFC: {
    CountDown: "Place your bets: ",
    WallBill_T1: "Game Stats",
    WallBill_T2: "Last 20 Games",
    WallBill_T3: "Filter",
    WallBill_T4: "Number of\ngames",
    LAB_WAIT: "Please wait till next round ...",

    LAB_BTN_T1: "Bet Again",
    LAB_BTN_T2: "Recharge",
    LAB_BTN_T3: "Online",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
  },

  HHDZ: {
    //红黑大战
    TXT_HONG: "RED",
    TXT_HEI: "BLACK",
    TXT_ROUND: "games",

    LAB_OL_T1: "The Richman No.",
    LAB_OL_T2: "Bet",
    LAB_OL_T3: "Win",
    LAB_OL_T4: "The Predictor",

    LAB_TITLE_1: "List of players",
    LAB_TITLE_2: "Red vs. Black",

    LAB_WALL_T1: "Last 20 Games",
    LAB_WALL_T2: "Previous Results",
    LAB_WALL_T3: "Card History",
    LAB_WALL_T4: "Card History",
    LAB_WALL_T5: "Card History",

    LAB_S_T1: "Place your bets!",
    LAB_S_T2: "Result",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
    LAB_S_T5: "RED",
    LAB_S_T6: "BLACK",
    LAB_S_T7: "Bonus",
    LAB_S_T8:
      "Three of a kind x10 Straight Flush x5\nFlush x3 Straight x2 Pair x1(9-A)",

    LAB_BTN_T1: "Bet Again",
    LAB_BTN_T2: "Recharge",
    LAB_BTN_T3: "Online",

    TYPE_1: "Single",
    TYPE_2: "Pair",
    TYPE_3: "Straight",
    TYPE_4: "Flush",
    TYPE_5: "Straight Flush",
    TYPE_6: "Three of a kind",
  },

  LHDZ: {
    //龙虎大战
    TXT_HU: "Tiger", // 虎
    TXT_LONG: "Dragon", // 龙
    TXT_LHHE: "Tie", //和
    TXT_LH_BIPAI: "Fight", //比牌
    LAB_SULT: "Place your bets!",

    TXT_ROUND: "games", //局

    LAB_OL_T1: "The Richman No.",
    LAB_OL_T2: "Bet",
    LAB_OL_T3: "Win",
    LAB_OL_T4: "The Predictor",
    LAB_OL_T5: "Time's Up!",
    LAB_OL_T6: "Start",

    LAB_WAIT: "Please wait till next round ...",

    LAB_TITLE_1: "Player list",
    LAB_TITLE_2: "Dragon & Tiger History",
    LAB_LUDAN_T1: "Last 20 Games",
    LAB_LUDAN_T2: "Lines",
    LAB_LUDAN_T3: "Next round's Dragon",
    LAB_LUDAN_T4: "Next round's Tiger",

    LAB_S_T1: "The Predictor",
    LAB_S_T2: "The Richman No.1",

    LAB_BTN_T1: "Bet Again",
    LAB_BTN_T2: "Recharge",
    LAB_BTN_T3: "Online",
  },

  MINIGAME_BS: {
    PAGE1_TITLE_1: "DIAMOND BET HISTORY",
    PAGE1_TITLE_2: "Session",
    PAGE1_TITLE_3: "Time",
    PAGE1_TITLE_4: "Total bet:",
    PAGE1_TITLE_5: "Win Amount",
    PAGE1_TITLE_6: "Spin Result",

    PAGE2_TITLE_1: "DIAMOND JACKPOT HISTORY",
    PAGE2_TITLE_2: "Time",
    PAGE2_TITLE_3: "Account",
    PAGE2_TITLE_4: "Bet level",
    PAGE2_TITLE_5: "Win Amount",
    PAGE2_TITLE_6: "Type",

    PAGE4_TITLE_1: "CHOOSE ROW",
    PAGE4_BTN_1: "EVEN",
    PAGE4_BTN_2: "ODD",
    PAGE4_BTN_3: "ALL",
    PAGE4_BTN_4: "CANCEL",

    PAGE5_TITLE_1: "DIAMOND RANKING ",
    PAGE5_TITLE_2: "Rank",
    PAGE5_TITLE_3: "Display name",
    PAGE5_TITLE_4: "Total Win",

    LAB_BTN_T1: "AUTO-SPIN",
    LAB_BTN_T2: "Spin",
    LAB_BTN_T3: "Turbo",
  },

  MINIGAME_PK: {
    PAGE1_TITLE_1: "MINI POKER BET HISTORY",
    PAGE1_TITLE_2: "Session",
    PAGE1_TITLE_3: "Time",
    PAGE1_TITLE_4: "Total bet:",
    PAGE1_TITLE_5: "Win Amount",
    PAGE1_TITLE_6: "Spin Result",

    PAGE2_TITLE_1: "MINI POKER JACKPOT HISTORY",
    PAGE2_TITLE_2: "Time",
    PAGE2_TITLE_3: "Account",
    PAGE2_TITLE_4: "Bet level",
    PAGE2_TITLE_5: "Win Amount",
    PAGE2_TITLE_6: "Type",

    PAGE5_TITLE_1: "MINI POKER RANKING",
    PAGE5_TITLE_2: "Rank",
    PAGE5_TITLE_3: "Display name",
    PAGE5_TITLE_4: "Total Win",

    LAB_BTN_T1: "AUTO-SPIN",
    LAB_BTN_T2: "Spin",
    LAB_BTN_T3: "Turbo",
  },

  //登陆页面
  LOGIN_KEEPONE: "Keep at least one account", //`至少保留一个账号`,

  warn_stillGame:
    "You are currently in another game, do you want to return to this game immediately?", //"您当前还在其他的游戏中，是否立刻回到该游戏",

  hello: "Hello!", //hello
  GameType: "Game type", //游戏类型
  SelectPlayers: "Select players", //选择玩家
  EntryFee: "Entry fee = ₹%{fee}", //进场
  PointValue: "Point Value = ₹%{point}", //

  NEWORK_ERROR: "There is something wrong with your network,\nplease check it", //

  ChangeNameEditTip: "Enter new username", //
  ChangeNameEditNote:
    "<color=#ffffff>Note:You can modify username </c><color=#0fffff>%{times} time(s)</color><color=#ffffff> this month</c>", //
  MessageTip: "PS:mail will be automatically deleted after %{time} days", //

  NetTipw: "Connection succeeded", //连接成功
  NetError: "Error not identified, please refresh the", //

  gameIconTip: "Coming soon...", //暂未开放
  updateTip: "Updating, please wait!", //正在更新，请稍候！

  Upfailed: "Update failed!", //更新失败

  copyTipw: "Copy successfully", //"复制成功!",

  NoticeNameTimes:
    "You have reached monthly username \nmodification limit,please try again next month.", //
  NoticeSameName: "The name you enter is the same as your current name.", //
  NoticeEmptyName:
    "The name you entered is empty \nUsername should be 4-20 characters..", //
  NoticeNameLength: "Username should be 4-20 characters.", //
  Zfb_take_tip:
    "The withdrawal will be received within 5 minutes. Confirm that the bound online payment account\nnumber is correct before withdrawing. Withdrawal will charge a handling fee of 1.5% of the withdrawal amount.", //提现5分钟内到账，提现前确认绑定的支付宝账\n号准确无误，提现收取提现金额1.5%的手续费。
  Bank_take_tip:
    "The withdrawal will be received within 5 minutes, and the bound bank card information must be confirmed before the withdrawal\nand the withdrawal will be charged a handling fee of 1.5% of the withdrawal amount.", //提现5分钟内到账，提现前确认绑定的银行卡信\n息准确无误，提现收取提现金额1.5%的手续费。
  Zfb_account_tip: "Online payment account not bound", //未绑定支付宝

  bind_tip_inputPhone: "Enter a valid phone number", //请输入正确的手机号
  bind_tip_inputCode: "Enter verification code", //请输入验证码
  bind_tip_inputPwd: "Enter the password", //请输入密码
  bind_tip_checkPwd: "Confirm the password", //请确认密码正确

  bind_tip_code1: "Verification code successfully sent", //发送验证码成功
  bind_tip_code2: "Try again later", //请稍后再试

  bind_tip_success: "Mobile phone number binding successfully", //手机号绑定成功
  bind_tip_success2:
    "Linking successful, your IP has been linked to another account, cannot get the reward!", //绑定成功，注册IP下已绑定过，无法获得奖励
  error_60001: "The verification code has expired", //验证码已过期
  error_60002: "The verification code has not expired", //验证码未过期
  error_60003: "Verification code error", //验证码错误
  error_60004:
    "The number of times the verification code has been sent is exceeded!", //发送验证码超过限制
  error_60010: "Enter a valid phone number", //手机号未注册
  error_60011: "Mobile phone number has been registered", //手机号已注册
  error_60020: "Wrong password", //密码错误
  error_60021: "Password reset failed", //密码重置失败
  error_60030: "Binding failed", //绑定失败
  error_60031: "Mobile phone number has been bound to another account", //手机号已绑定其它账号
  error_fb_60031: "This Facebook has linked other account", //Facebook已绑定其它账号
  error_60041: "Already reached the limit of registrations", //已达到限制注册数量
  error_60043: "Please wait after 1 minute to work again.", //等待一分钟后重试
  error_60050: "The graphic verification code is incorrect", //验证码错误
  bind_tip_error: "Mobile phone number binding failed", //手机号绑定失败

  CHANGE_PWS_SUCCESS: "Successfully changed password", //修改密码成功
  change_pws_error: "Failed to change password", //修改密码失败

  time_1: "Today", //"今日",
  time_2: "Yesterday", //"昨日",
  time_3: "This week", //"本周",
  time_4: "Last week", //"上周",
  time_5: "This month", //"本月",
  time_6: "Last month", //"上月",

  allreadyGet: "Claimed", //已领取
  growthFundNotActive: "Growth fund is not activated", //成长基金未激活
  gameLevelInsufficient: "Not enough level", //
  growthFundFail: "Failed to obtain growth fund!", //获取成长基金失败！
  growthFundSuccess: "Successfully received growth fund!", //领取成长基金成功！
  growthFundErr_1: "Parameter error", //参数错误
  growthFundErr_2: "Growth fund has not been activated", //成长基金还未激活
  growthFundErr_3: "You already claimed", //已经领取过
  growthFundErr_4: "Claim error", //领取错误
  growthFundErr_5: "In game currently", //正在游戏中
  growthFundErr_6: "Level not reached", //等级未达到
  growthFundErr_7: "Maximum receive 5 times", //最多领取5次
  newPeopleAwardFail: "Failed to get New Player Gift data!", //获取新人礼包数据失败！
  newPeopleAwardSuccess: "New Player Gift received successfully", //新人礼包领取成功！
  newPeopleAwardDay: "Day {0}", //
  // newPeopleAwardOneDay: "First day",//
  newPeopleAwardErr_0: "You can only claim the New Player Gift of the day", //只能领取当天的新人礼包
  newPeopleAwardErr_1: "Parameter error", //参数错误
  newPeopleAwardErr_2: "Failed to claim, please register an account to claim!", //领取失败，请注册账号即可领取
  newPeopleAwardErr_3: "Exceeded the day number limit", //超过领取最大天数
  newPeopleAwardErr_4: "Config error, please try again later", //配置有误，请稍后再试
  newPeopleAwardErr_5: "Already claimed today", //今天已经领取过
  newPeopleAwardErr_6: "In game currently", //正在游戏中
  newPeopleAwardErr_7: "Claim failed, please recharge any amount to claim!", //领取失败，请充值任意金额即可领取
  rechargeSignFail: "Failed to query recharge check-in data!", //查询充值签到数据失败！
  rechargeSignSuccess: "Recharge check-in successfully!", //签到成功！
  rechargeSignErr_1: "Parameter error", //参数错误
  rechargeSignErr_2: "Already claimed!", //已经签到过
  rechargeSignErr_3: "The number of re-checkin has been used up!", //补签次数已用完
  rechargeSignErr_4: "The recharge today did not reach %{money}!", //今天充值未达到100K

  rechargeSignErr_5: "In game currently!", //正在游戏中
  turnTableInfoFail: "Failed to get Lucky Wheel data", //获取转盘数据失败
  startTurnTableReqFail: "Drawing request failed", //抽奖请求失败
  turnTableRecordFail: "Failed to get Luck Wheel record data", //获取转盘记录数据失败
  turnTableRecordTypeStr: "{0} received ", //
  startTurnTableErr_1: "Not enough points!", //积分不足！
  startTurnTableErr_2: "In game currently", //正在游戏中！
  startTurnTableAward: "Congratulations, get {0} USD reward!", //恭喜您，获得${0}元奖励！
  getOnlineAwardErr: "Claim error!", //领取错误！
  getOnlineAward: "Congratulations, earn {0} points!", //恭喜您，获得${0}积分！
  rechargeDeposMoneyFail:
    "The deposit amount submitted is incorrect, please try again!", //提交充值金额错误，请重试
  rechargeDeposAcountFail:
    "The submitted account name cannot be empty, please try again!", //提交账户名不能为空，请重试
  rechargeDeposMoneyFail1:
    "The submitted recharge amount should be more than {0} USD and less than {1} USD!", //提交充值金额应大于${0}元，并小于${1}元！
  rechargeDeposTipsFail: "Please take notes!", //请提交备注
  rechargeSuccess: "Submit recharge successfully!", //提交充值成功！
  thanks: "Thanks for participating!", //"谢谢参与！"
  rechargeStatu_1: "Processing", //处理中
  rechargeStatu_2: "Recharge successfully", //充值成功
  rechargeStatu_3: "Recharge failed", //充值失败
  rechargeStatu_4: "Recharging", //充值中

  RECHANGE_TITLE_1: "BANK", //银行
  RECHANGE_TITLE_2: "MOMO", //momo
  RECHANGE_TITLE_3: "ONLINE RECHARGE", //在线
  RECHANGE_TITLE_4: "Recharge history", //记录
  RECHANGE_TITLE_5: "GIFTCODE", //兑换码
  RECHANGE_TITLE_6: "CARD", //刮刮卡
  RECHANGE_TITLE_7: "TIỀN\nĐIỆN TỬ", //加密货币

  RECHAGE_GUA_TIP_INFO_ERROR: "You Need To Enter Full Information!",
  RECHAGE_GUA_TIP_FAIL: "The system is processing your card",

  //兑换码
  GIFTCODE_SUCCESS: "Received successfully, get {0} reward", //领取成功，获得 ${0} 奖励
  GIFTCODE_ERR_1: "Currently in game", //
  GIFTCODE_ERR_2:
    "Each member can only use the giftcode once, you have already used it", //每个玩家只能使用一次礼包码, 你已经使用过了
  GIFTCODE_ERR_3: "Invalid or expired giftcode", //礼包码无效或者过期
  GIFTCODE_ERR_4: "The giftcode is wrong, please input in again", //礼包码错误, 请重新填写
  GIFTCODE_ERR_5: "Please link your phone number", //请绑定手机号
  GIFTCODE_ERR_6: "Please link your bank account and phone number", //请绑定银行信息

  awardErr_1: "In game currently!", //正在游戏中
  awardErr_2: "Already claimed today!", //今日已领取
  awardErr_3: "Does not meet the conditions!", //不满足条件
  awardErr_4: "Failed to obtain relief fund information!", //获取救援金信息失败
  awardSuccess: "Congratulations, get %{money} USD reward!!", //恭喜您，获得${0}元奖励！//`Lĩnh thưởng thành công`,  //领取奖励成功
  awardErr_5: `Failed to claim reward`, //领取奖励失败

  redBagAward: "Congratulation, receive %{money} bonus!", //恭喜您，获得${0}元奖励！
  redBagErr_1: "Coming soon!", //活动未开放
  redBagErr_2: "Coming soon", //未在活动时间内
  redBagErr_3: "Today received reward!", //今日已领取过
  redBagErr_4: "Unqualified!", //未满足领取条件
  redBagErr_5: "In game!", //在游戏中

  networkTip: "Connecting...", //正在连接...

  imCharge: "Recharge now", //立即充值

  //修改后需要确保实际传后台银行代码与名称匹配
  _bankArr: [
    "ธนาคารกรุงเทพ",
    "ธนาคารกสิกรไทย",
    "บริษัท ธนาคารกรุงไทย",
    "ธนาคารทหารไทย",
    "ธนาคารไทยพาณิชย์",
    "ธนาคารซิตี้แบงก์",
    "ธนาคารสแตนดาร์ดชาร์เตอร์ด",
    "ธนาคารซีไอเอ็มบีไทย",
    "ธนาคารยูโอบี",
    "ธนาคารกรุงศรีอยุธยา",
    "ธนาคารออมสิน",
    "ธนาคารอาคารสงเคราะห์",
    "ธนาคารเพื่อการเกษตรและสหกรณ์การเกษตร",
    "ธนาคารเพื่อการส่งออกและนำเข้าแห่งประเทศไทย",
    "ธนาคารแห่งประเทศจีน",
    "ธนาคารอิสลามแห่งประเทศไทย",
    "ธนาคารทิสโก้",
    "ธนาคารเกียรตินาคิน",
    "ธนาคารไอซีบีซี",
    "ธนาคารไทยเครดิตเพื่อรายย่อย",
    "ธนาคารแลนด์ แอนด์ เฮาส์",
    "ธนาคารเอเอ็นแซด",
    "ธนาคารซูมิโตโม มิตซุย ทรัสต์",
    "ธนาคารพัฒนาวิสาหกิจขนาดกลางและขนาดย่อมแห่งประเทศไทย",
  ],

  //实际传后台银行代码
  _bankCodeArr: [
    "BBL",
    "KBANK",
    "KTB",
    "TTB",
    "SCB",
    "CITI",
    "SCBT",
    "CIMBT",
    "UOBT",
    "BAY",
    "GSB",
    "GHB",
    "BAAC",
    "EXIM",
    "BOC",
    "ISBT",
    "TISCO",
    "KKP",
    "ICBCT",
    "TCD",
    "LHFG",
    "ANZ",
    "SMBT",
    "SME",
  ],

  200: "Logged in successfully", //登陆服务器成功
  201: "The player is already logged in", //玩家已经登录
  202: "Please wait for the next round", //请等待下局
  203: "Player is not logged in", //玩家未登录
  204: "Player not found", //玩家未找到
  500: "Connection failed, please contact our Customer service. Error code: 500", //服务器无法连接,请退出并重试
  501: "Failed to connect to the server", //获取服务器数据失败
  502: "Parameter error", //参数错误
  503: "This account is frozen", //:账号被冻结（禁止登录）
  505: "The account has been logged in on another device!", //账号已在其它设备登陆
  509: "Account not found", //账号未找到

  60001: "The verification code has expired", //验证码已过期
  60002: "The verification code has not expired", //验证码未过期
  60003: "Verification code error", //验证码错误
  60004: "The number of times the verification code has been sent is exceeded!", //发送验证码超过限制
  60010: "Mobile phone number is not registered", //手机号未注册
  60011: "Mobile phone number has been registered", //手机号已注册
  60020: "Incorrect password", //密码错误
  60021: "Password reset failed", //密码重置失败
  60030: "Binding failed", //绑定失败
  60031: "Mobile phone number has been bound to another account", //手机号已绑定其它账号
  60040: "Account not found", //token 错误 //账号未找到
  60041: "Reached the limit of registrations", //已达到限制注册数量
  60042: "This account is frozen", //账号被冻结

  1001: "URL copied successfully!", //复制网址成功!
  1004: "Failed to update mailing list!", //获取邮件列表失败
  1005: "After withdrawal, you will have no remaining rewards to share with subordinates. Confirm to withdraw?", //提取后,您将无剩余奖励可以分享给下级。是否确认提取
  1006: "Withdrawal operation", //提现操作
  1007: "Withdrawal information has not been bound yet", //还没绑定提现信息
  1008: "The number of invitees is not enough to receive lucky envelopes.", //邀请人数未达标,无法获取红包
  1009: "Successfully bind the Online payment account", //绑定支付宝成功
  1010: "Binding failed!", //绑定失败!
  1011: "Enter your name and account number", //请输入姓名与卡号
  1012: "Successfully bound the bank card", //绑定银行卡成功
  1013: "Binding failed!", //绑定失败!
  bind_bank_error_1: "Binding failed!", //该账户已经绑定过!
  bind_bank_error_2: "The bank card number has been bound to another account.", //该银行卡号已经绑定其他账号!
  1014: "Please enter complete information", //请输入完整信息
  1015: "Request failed!", //请求失败
  "1016_1": "Ví điện tử",//钱包
  "1016": "Tiền điện tử",//加密货币
  "1017": "Thẻ ngân hàng",//银行卡
  1018: "Failed to obtain binding information", //获取绑定信息失败
  1019: "Enter the exchange amount", //"请输入兑换数量",
  1020: "The online payment account is not bound", //"支付宝未绑定",
  1021: "The bank account is not bound", //银行卡未绑定",
  1022: "Withdrawal successful, amount: {0}", //兑换成功!
  1023: "User information does not exist", //用户信息不存在
  1024: "Not yet bound information", //还未绑定信息
  1025: "In game currently, can not withdraw", //在游戏中,无法兑换
  1026: "Wrong amount", //金额错误
  1027: "Unknown error:", //未知错误
  1028: "No ranking", //暂无排行
  1029: "Logging in...", //正在登陆...
  1030: "Successfully updated avatar and avatar frame", //"修改头像与头像框成功",
  1031: "Reach VIP {0} to claim", //VIP等级达到${0}级时可获得
  1032: "Failed to request player level information", //请求玩家等级信息失败
  1033: "Under review", //审核中
  1034: "Passed", //通过
  1035: "Denied", //拒绝
  1036: "Request bank operation failed", //请求银行操作失败
  1037: "The amount of withrawal/deposit is 0", //"存取数量为0",
  1038: "Display name cannot be empty", //"修改的名字不能为空！",
  1039: "Length of new nickname does not match!", //新昵称长度不符！
  1040: "Request to change nickname error", //请求修改昵称错误
  1041: "Enter your name and online payment account", //请输入支付宝账号与姓名
  1042: "Update completed!!!", //更新完成
  1043: "Exceed the available amount to withdraw", //超过可提现金额
  1044: "The amount is less than {0}, can not withdraw!", //提现金额低于100k，无法提现
  1045: "Successful withdrawal, amount: {0}", //提现成功，金额：

  EXCHANGE_ERROR_CODE_0: "Parameter error", //参数错误
  EXCHANGE_ERROR_CODE_1: "In game currently!", //正在游戏中
  EXCHANGE_ERROR_CODE_2: "Bank card is not bound", //银行卡未绑定
  EXCHANGE_ERROR_CODE_3:
    "The withdrawal amount is more than the balance you bringing", //提现金额大于身上余额
  EXCHANGE_ERROR_CODE_4:
    "The withdrawal amount is less than the minimum amount:%{money}", //提现金额小于最低提现金额%{ money}  val最低提现金额
  EXCHANGE_ERROR_CODE_5:
    "After withdrawal, the minimum balance must be %{money}", //提现后余额最低要留%{ money}       val最低要留多少在银行里  单位分
  EXCHANGE_ERROR_CODE_6: "Need to apply after %{time} mins", //需要间隔%{time}分钟后再申请     val需要的间隔分钟数
  EXCHANGE_ERROR_CODE_7:
    "Bet amount at least equal to %{money}, the amount deposited can be withdrawn", //总流水要大于总充值金额才能提现，您还需要%{ money}流水
  EXCHANGE_ERROR_CODE_8: "This account is frozen", //账号被冻结

  EXCHANGE_ERROR_CODE_11: "Exceeded the hightest withdrawal amount: %{money}", //超过当笔最高提现金额
  EXCHANGE_ERROR_CODE_12:
    "Exceeded the maximum amount of withdrawal per day: %{money}", //超过当天最高提现次数
  EXCHANGE_ERROR_CODE_13:
    "Exceeded the maximum withdrawal amount of the day: %{money}", //超过当天最高提现总金额
  EXCHANGE_ERROR_CODE_14: "Please link your phone number",
  EXCHANGE_ERROR_CODE_15: "Không thể nhận được tỷ giá hối đoái",		//无法获取汇率
  EXCHANGE_ERROR_CODE_16: "Tỷ giá hối đoái đã thay đổi.",		//汇率已改变
  EXCHANGE_ERROR_CODE_17: "Tiền điện tử đã bị đình chỉ.",		//加密货币已暂停使用
  EXCHANGE_ERROR_CODE_18: "Telegram không được liên kết",//Telegram未绑定
  EXCHANGE_ERROR_CODE_19: "Zalo không được liên kết",//Zalo未绑定
  EXCHANGE_ERROR_CODE_20: "E-mail không được liên kết",//E-Mail未绑定
  EXCHANGE_ERROR_CODE_21: "Ngày sinh không được liên kết",//Birthday未绑定
  EXCHANGE_ERROR_CODE_22: "Không xác thực tên thật",//未实名认证
  EXCHANGE_ERROR_CODE_23: "Mật khẩu sai",//密码错误
  EXCHANGE_ERROR_CODE_24: "Cược hiệu quả không đủ, vẫn cần %{money}",//有效下注不足, 还差20k,
  EXCHANGE_ERROR_CODE_24_1: "Cược hiệu quả không đủ, vẫn cần %{money}.\nBạn có thể tiếp tục rút tiền mặt bằng cách khấu trừ phí xử lý là %{charge}.",//有效下注不足, 还差20k, 可以通过扣除 10K 的手续费来继续提现
  EXCHANGE_ERROR_CODE_25: "Signal không được liên kết",//signal未绑定
  EXCHANGE_ERROR_CODE_26: "Xác thực tên thật chưa hoàn tất",//实名认证未完成

  JETTON_TIP: `The existing amount must be more than 20K to be able to bet`,
  JETTON_AREA_LIMIT: `The max betting limit of the area has been reached %{money} gold coins`, //已达区域最大下注限制%{ money}金币
  JETTON_USER_LIMIT: `Personal max bet limit of %{money} coins has been reached`, //已达个人最大下注限制%{ money}金币
  JETTON_USER_TIMESLIMIT: `You need to carry at least %{money} gold coins to bet`, //"需要至少携带50金币才可下注",
  JEXIT_TIP:
    "You have already placed a bet. If you log out, the system will automatically take care of you and will not affect the settlement of gold coins. Are you sure to log out?", // `您当前已投注，退出游戏系统会自动帮您托管，并不影响金币结算，确认退出游戏吗？`,
  DEXIT_TIP:
    "If you leave now, the system will automatically take care of your cards, and you will not be responsible for losing. Are you sure to log out?", //'现在离开系统会自动托管帮您出牌，输了概不负责哟。确认退出吗？',

  LoginTip: `Logging in `, //登陆中

  toast_bet1:
    "You can place a bet with a balance of 20k or more, please recharge", //50元余额以上才可下注，请您充值
  toast_bet2: "Insufficient balance, please recharge", //金币不足，请您充值
  exitHint: "Leave Game?", //确认离开游戏

  heroHint_1: "Does not own the hero!", //未拥有该英雄
  heroHint_2: "The current lineup is full!", //当前阵容已满员！
  heroHint_3: "Must choose 3 heroes!", //必须选择3个上阵英雄！
  heroHint_4: "Heroes cannot be changed at this stage!", //当前阶段无法更换英雄！

  buyTipw1:
    "You now have %{soul} Soul Shards. After deducting the shards, you still need to spend %{gold} gold coins. Do you want to buy it?", //您现在拥有%{soul}个灵魂碎片，扣除碎片\n后还需花费%{gold}金币，是否购买？
  buyTipw2: "Not enough gold coins to buy this hero.", //金币不足，无法购买该英雄

  ERMJFan_0: "Big Four Winds", //大四喜
  ERMJFan_1: "Big Three Dragons", //大三元
  ERMJFan_2: "Nine Gates", //九莲宝灯
  ERMJFan_3: "Four Kongs", //四杠
  ERMJFan_4: "Seven Shifted Pairs", //连七对
  ERMJFan_5: "Blessing of Heaven", //天和
  ERMJFan_6: "Blessing of Earth", //地和
  ERMJFan_7: "Hand of Man", //人和
  ERMJFan_8: "Million Stone", //百万石
  ERMJFan_9: "Little Four Winds", //小四喜
  ERMJFan_10: "Little Three Dragons", //小三元
  ERMJFan_11: "All Honours", //字一色
  ERMJFan_12: "Four Concealed Pungs", //四暗刻
  ERMJFan_13: "Pure Terminal Chows", //一色双龙会
  ERMJFan_14: "Quadruple Chow", //一色四同顺
  ERMJFan_15: "Four Pure Shifted Pungs", //一色四节高
  ERMJFan_16: "Four Pure Shifted Chows", //一色四步高
  ERMJFan_17: "Three Kongs", //三杠
  ERMJFan_18: "All Terminals and Honours", //混幺九
  ERMJFan_19: "7 Pairs", //七对
  ERMJFan_20: "Full Flush", //清一色
  ERMJFan_21: "Pure Triple Chow", //一色三同顺
  ERMJFan_22: "Pure Shifted Pungs", //一色三节高
  ERMJFan_23: "Pure Straight", //清龙
  ERMJFan_24: "Pure Shifted Chows", //一色三步高
  ERMJFan_25: "Three Concealed Pungs", //三暗刻
  ERMJFan_26: "Heaven Wait", //天听
  ERMJFan_27: "Upper Four", //大于五
  ERMJFan_28: "Lower Four", //小于五
  ERMJFan_29: "Big Three Winds", //三风刻
  ERMJFan_30: "Last Tile Draw", //妙手回春
  ERMJFan_31: "Last Tile Claim", //海底捞月
  ERMJFan_32: "Out with Replacement Tile", //杠上开花
  ERMJFan_33: "Rob Kong", //抢杠和
  ERMJFan_34: "All Pungs", //碰碰和
  ERMJFan_35: "Half Flush", //"混一色",
  ERMJFan_36: "Melded Hand", //全求人
  ERMJFan_37: "Two Concealed Kongs", //双暗杠
  ERMJFan_38: "Two Dragon Pungs", //双箭刻
  ERMJFan_39: "Outside Hand", //全带幺
  ERMJFan_40: "Fully Concealed Hand", //不求人
  ERMJFan_41: "Two Melded Kongs", //双明杠
  ERMJFan_42: "Last Tile", //和绝张
  ERMJFan_43: "Riichi", //立直
  ERMJFan_44: "Dragon Pung", //"箭刻",
  ERMJFan_45: "Prevalent Wind", //圈风刻
  ERMJFan_46: "Seat Wind", //门风刻
  ERMJFan_47: "Concealed Hand", //门前清
  ERMJFan_48: "All Chows", //平和
  ERMJFan_49: "Tile Hog", //四归一
  ERMJFan_50: "Two Concealed Pungs", //双暗刻
  ERMJFan_51: "Concealed Kong", //暗杠
  ERMJFan_52: "All Simples", //断幺
  ERMJFan_53: "258 Jong", //"二五八将",
  ERMJFan_54: "Terminal and Honour Tiles", //幺九头
  ERMJFan_55: "Wait Report", //"报听",
  ERMJFan_56: "Pure Double Chow", //一般高
  ERMJFan_57: "Short Straight", //连六
  ERMJFan_58: "Two Terminal Chows", //老少副
  ERMJFan_59: "Pung of Terminals or Honours", //幺九刻
  ERMJFan_60: "Melded Kong", //明杠
  ERMJFan_61: "Edge Wait", //边张
  ERMJFan_62: "Closed Wait", //坎张
  ERMJFan_63: "Single Sling", //单吊将
  ERMJFan_64: "Self Draw", //"自摸",

  ERMJRESULT_1: "This player won", //本家胡
  ERMJRESULT_2: "This player lost", //本家输
  ERMJRESULT_3: "Opponent won", //对家胡
  ERMJRESULT_4: "Opponent lost", //对家输

  ERMJRESULT_5: "Claimed", //已领取
  ERMJRESULT_6: "No records", //暂无记录

  JLBHDERR_1: "Game status error", //游戏状态错误
  JLBHDERR_2: "Invalid bet amount", //非法的押注金额
  JLBHDERR_3: "Invalid bet times", //非法的押注线数
  JLBHDERR_4: "Less than how much money is not allowed to bet", //少于多少钱不让下注
  JLBHDERR_5: "Don't have enough money to bet", //身上的钱不够下注

  //miniyxx
  MINIYXX_TIPS1: "SESSION:",
  MINIYXX_TIPS2: "Lucky Number:",
  MINISB_TIPS0: "Please fill in.", //请输入信息
  // MINISB_TIPS1: "User information does not exist.",//玩家信息不存在
  MINISB_TIPS1: "The chat room is off.", //聊天服务器已关闭
  MINISB_TIPS2: "Data error.", //超过最大的聊天长度
  MINISB_TIPS3: "Please try again later.", //两次聊天时间间隔少于1秒
  MINISB_TIPS4: "Your content contains sensitive words, please re-enter.", //您的信息含有敏感词汇,请重新输入。
  MINISB_TIPS5: "The chat room is off", //聊天室已关闭
  MINISB_TIPS6: "You have been banned from speaking by an administrator.", //你已经被管理员禁言
  MINISB_TIPS7: "Your balance must be at least %{money} to chat", //你余额20.000以上才能聊天

  zjh: {
    allBets: "Total bet:", // 总投注
    allIn: "All In",
    followAlways: "Follow\nAll", // 跟到最后
    unalways: "Cancel\ncall",
    compare: "Compare", // 比牌
    giveupCompare: "Fold",
    follow: "Call", // 跟注
    lockCard: "Check", //
    cancelCompare: "Cancel\nexposing", // 放弃比牌
    wait: "The game is starting",
    waitOther: "Please wait till next round",
    youWin: "YOU WON",
    youLost: "YOU LOSE",

    state: "Remove",
    stateAdd: "Add money",
    stateAllIn: "All In",
    stateCMP: "Compare",
    stateFollow: "Call",
    stateLockCard: "Check",
    cardType: "A pair",
    cardType0: "Single",
    cardType1: "Pair",
    cardType2: "Straight",
    cardType3: "Flush",
    cardType4: "Straight Flush",
    cardType5: "Triple",
  },

  yxx: {
    bet: "Bet\nAgain",
    history: "History",
    hold: "Hold to automatically bet again",
    data: "Result",
    button: "button",
    title1: "History",
    title2: "Jackpot History",
    tip: "GET THE JACKPOT WHEN 3 DICE SHOW THE SAME MASCOT",
    t1: "Deer",
    t2: "Shrimp",
    t3: "Chicken",
    t4: "Gourd",
    t5: "Fish",
    t6: "Crab",
    t11: "Session",
    t22: "Time",
    t33: "Result",
    t44: "Jackpot Money",
    t55: "Jackpot Winners",
    t66: "Reward",
    waitOther: "Please wait till next round",
    charge: "Recharge",
    online: "Online",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
    start: "Start",
    end: "Time's Up!",
  },

  xyj: {
    start: "SPIN",
    stop: "STOP",
    curBets: "Line(s)",
    lineBet: "Total Bet:",
    bet: "BET",
    free: "FREE",
    wallet: "Balance",
    close: "Back",
    title: "JOURNEY TO THE WEST", // 西游记
    youWin: "CONGRATS, YOU WON", // 恭喜你赢了
    bonusGame: "FROM THE BORROWING HEAVENLY WEAPON BONUS GAMES",
  },

  wow: {
    auto: "Auto",
    speed: "Turbo",
    gold: "BALANCE",
    bet: "Bet Amount",
    mua: "buy",
    begin: "START",
    stop: "STOP",
    rule: "Game rules",
    skill: "Skill\ndescription",
    level: "Stage\npassing reward",
    selectHero: "Tap the icon below to choose a hero",
    stoneDesc1: "Soul shards can be used to summon new heroes",
    stoneDesc2: "Defeat the BOSS in every stage to get shards",
    stoneDesc3: "BOSS in free games won't drop shards",
    name1001: "Snow storm",
    desc1001: "Damage dealt on a large area, damaged enemy will be frozen.",
    name1002: "Frost Arrow",
    desc1002: "Summon Frost Arrow, create single damage to a single unit.",
    name1003: "Groundbreaking",
    desc1003:
      "Crack the ground in straight line, deal AOE damage, push enemies to both side and stun them.",
    name1004: "Lighting chain",
    desc1004:
      "Attack a unit using a lightning bolt and then jump to the nearby enemies.",
    name1005: "Explosive Shot",
    desc1005:
      "Fire Explosive shot on enemy, deal massive AOE damage and extra damage.",
    name1006: "Silent Shot",
    desc1006: "Aim and shot the unit, stun them.",
    name1007: "Witch’s Curse",
    desc1007:
      "Place a totem to turn the nearest units into frogs and deal large damage.",
    name1008: "Venom Snake Totem",
    desc1008:
      "Place a totem to summon venomous snake Woking’s barrier, dealt damage to enemy within range.",
    name1009: "Meteor Shower",
    desc1009:
      "Summon meteor shower to deal massive damage in range with a stun effect.",
    name1010: "Sentinel Eagle",
    desc1010:
      "Release the Owl to pierce through the battel, deal damage to the first unit.",
    name1011: "Imprisonment Circle",
    desc1011: "Inflicts slow effect on the target and deal massive damage.",
    name1012: "Crescent blade",
    desc1012: "Teleport to an enemy and deal damage",
  },

  jclb: {
    game: "GOLDEN CICADA",
    forReal: "PLAY\nGAME",
    package: "FORTUNE POUCH",
    phuc: "HAPPY",
    smellTitle: "FIND TREASURE",
    doubleScoreTitle: "LUCKY BAG",
    speedSlow: "Turbo",
    game2: "GAME X2",
    minigame2: "MINI GAME X 2",
    win: "WON",
    rule: "TUTORIALS",
    freeTimes: "Remaining free spins",
    title: "Choose 1 of 4 cards",
    titleWin: "Congrats, you passed the challenge!",
    titleLost: "You lose",
    tipBonusWin: "Congratulations you won",
    tipOtherWin: "Congratulations you won",
    typeBonus: "from the bonus of the game",
    typeMini: "of the game x2",
    btn_close: "BACK",
    typeOther: "from the free spin",
    titleSmell: "Choose symbol ",
    tipSmell: "(End)",
  },

  // 拉霸通用
  slot: {
    allinTips: "Do you want All-In?\n%{score}",
    win: "WIN",
    stop: "STOP",
    turn: "SPIN",
    lastWin: "LATEST WIN",
    titleCoinLeft: "BALANCE",
    totalbet: "Total Bet:",
    allIn: "All-In",
    auto: "AUTO\nSPIN",
    helpTitle: "PAYING TABLE",
    btnOK: "CONTINUE",
    freeGame: "FREE SPINS",
    freeGameDot: "FREE SPINS:",
    freeGameCongratulate: "Congrats!",
    freeGameFound: "YOU HAVE FOUND GOLD COMB!",
    wild: "WILD",
    curBets: "Line(s)",
    lineBet: "Total Bet:",
    bet: "BET",
    autoSpin: "Hold to auto-spin",
    close: "Back",
  },

  gold: {
    lineNumber: "35LINES",
    freeGameTitle: "STAGE:",
    freeGameTip1: "TURN A SQUARE TO FIND GOLD!",
    freeGameTip2: "CAREFULLY, FLIPPING THE BLANK BOX MAY CAUSE SINK HOLE.",
    freeGameWin: "YOU WON:",
    freeGameWelcom: "WELCOME TO GOLD MINE BONUS!",
  },

  fruits: {
    wild: "WILD",
    wildDesc:
      "WILD replaces all symbols except FREE SPINS. Appears on all reels.",
    freeDesc:
      "The free spins start when 3 FREE SPINS symbols appear on the line.",
    item1: "NUDGE",
    baseGame: "BASE GAME",
    baseDesc1:
      "After the spin is completed, some reels will move up or down one position if it creates a winning combination. The game always chooses the move that is in the player's favor.",
    baseDesc2: "- 2 movable reels..",
    baseDesc3: "- 3 movable reels..",
  },
  conan: {
    lineNumber: "25LINES",
    wild: "WILD",
    wildDesc: "WILD replaces all symbols.\nAppears only on reels 1,2,4 and 5.",
    free: "NUDGE FREE SPINS",
    freeDesc: "10 free spins start when 8 NUDGE blooms occur during a spin.",
    item1: "NUDGE",
    desc: "Any win on rows causes the reels to move down one position and another advance to begin. NUDGE continues as long as there are winning combinations.",
  },
  buffalo: {
    lineNumber: "50LINES",
    wildDesc: "WILD replaces all symbols except BONUS and FREE SPINS.",
    freeDesc:
      "The free spins start when 3 or more FREE SPINS symbols appear in anywhere on the reels.",
  },

  vampire: {
    lineNumber: "40LINES",
    wild: "ROAMING WILD",
    wildDesc:
      "In the main game, the WILD symbol moves to the left during the spin. The WILD symbol moves to reel 5 when the bet changes.",
    freeSpinsDesc1:
      "During the free spins, the WILD symbol covers all reels 2 and 4.",
    freeSpinsDesc2:
      "Free spins start when 3 or more FREE SPINS symbols appear anywhere on the reels.",
    batBonus: "BAT BONUS",
    batBonusDesc:
      "BAT BONUS starts when 3 bonus symbols appear in any match on the reels. Only appear on reels 1, 3 and 5.",
    minitop1: "Faults Remaining:",
    minitop2: "CHOOSE REAL bats that are EASY TO RECEIVE REWARDS!",
    minitop3: "YOU WIN:",
    miniStart: "WELCOME TO BAT BONUS!",
  },

  monkey: {
    lineNumber: "25 LINES",
  },

  dfdc: {
    bet: "BET",
    speed: "SPEED\nUP",
    stop: "STOP",
    spin: "SPIN",
    holdAuto: "HOLD FOR AUTO",
    win: "WIN",
    totalWin: "TOTAL WIN",
    niceWin: "NICE WIN",
    freeSpins: "FREE SPINS:",
    freetimeStartTitle: "FREE SPINS AWARDED",
    freetimeButStart: "START",
    freetimeEndTitle: "TOTAL WIN",
    freetimeButEnd: "COLLECT",
    btnBack: "Quit",
    btnRecord: "Record",
    btnHelp: "Help",
    btnSetting: "Settings",

    wild: "WILD",
    wildDesc:
      "Appearing on reels 2,3 and 4 only. Substitutes for all symbols, including SCATTER.",
    scatter: "SCATTER",
    scatterDesc:
      "3 or more consecutive \non 3 consecutive reels,\
        \nbeginning on the leftmost reel,\nwins Mystical Gong Free Spins!\n9,10,J,Q,K,A does not appear in \nfree Spin and may triggrt more \nfree spin.",
    gameRuleTitle: "GAME RULES",
    gameRule1: "243 WAYS TO WIN!",
    gameRule2:
      "All wins pay in any position \nfrom left to right on adjacent \nreels, beginning with the left \nmost reel.",
    gameRule3:
      "Only the highest win is paid for \neach combination of symbols.",
    gameRuleClose: "BACK TO GAME",
  },

  sd: {
    vs: "Result",
    chan: "EVEN",
    le: "ODD",
    continue: "Bet\nAgain",
    charge: "Recharge",
    online: "Online",
    wait: "Please wait till next round",
    start: "Start",
    end: "Time's Up!",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
  },

  sb: {
    T: "B",
    X: "S",
    vs: "Compare",
    title: "Tai(3-5-3)",
    lab3: "Dice 3",
    lab2: "Dice 2",
    lab1: "Dice 1",
    luzi_s: "Small",
    luzi_b: "Big",
    trendTitle: "SICBO History",
    trend20: "Win/lose within the latest 20 rounds.",
    continue: "Bet\nAgain",
    charge: "Recharge",
    online: "Online",
    wait: "Please wait till next round",
    start: "Start",
    end: "Time's Up!",
    LAB_S_T3: "The Predictor",
    LAB_S_T4: "The Richman No.1",
  },

  mdsb: {
    online: "Online",
    continue: "Bet\nAgain",
    history: "History",
    tip1: "MD5 CHAIN",
    tip2: "Result",
    copy: "COPY",
    back: "BACK",
    help: "HELP",
    setting: "OPTION",
    wait: "Please wait till next round",
    start: "Start",
    end: "Time's Up!",
    trendTitle: "SICBO MD5 History",
    T: "B",
    X: "S",
    vs: "Compare",
    title: "Tai(3-5-3)",
    lab3: "Dice 3",
    lab2: "Dice 2",
    lab1: "Dice 1",
    luzi_s: "Small",
    luzi_b: "Big",
    recordTitle: "GAME STATISTICS",
  },

  qznn: {
    dealer: "Grab time",
    bet: "Place your bets!",
    open: "Reveal the cards",
    confirm: "Reveal",
    waiting: "Please wait till next round...",
    willStart: "The game is starting",
    waitOthers: "Waiting for other players",
    cai: "Dealer",
    mult: "Grab",
    winTitle: "YOU WIN",
    loseTitle: "YOU LOSE",
    dont: "Not grab",
    niu: "Niu ",
    zero: "Niu 0",
    niuniu: "Niu   Niu",
    niu11: "Four of a kind",
    niu12: "5 DUKES",
    curBet: "Bet level",
  },

  nfpdk: {
    game: "Vietname Southern Thirteen",
    first: "Start first",
    bet: "Bet",
    pass: "Skip",
    name: "Name",
    cardNum: "Remaining cards",
    deal: "Penalty cards",
    score: " Checkout",
    winTitle: "YOU WON",
    loseTitle: "YOU LOSE",
    continue: "Continue",
    leave: "Leave the game",
    btnPass: "Skip turn",
    outCard: "Play cards",
    ready: "Start",
    control: "TURN OFF\nAUTO-PLAY",
  },

  phom: {
    sortOutCard: "ARRANGE CARDS", //整理牌
    drawCards: "DRAW CARD", //抓牌//DRAW CARDS//วาดไพ่
    outCard: "PLAY CARD", //出牌
    disCards: "SEND CARD", //丢弃牌
    eatCards: "TAKE CARD", //吃牌
    showPhom: "PLAY PHOM", //显示PHOM

    bet: "Bet: ",
    ready: "READY",
    lastEatCards: "Last Take",
    noPhom: "Móm",

    cardType_1: "FIRST",
    cardType_2: "SECOND",
    cardType_3: "THIRD",
    cardType_4: "LAST",
    cardType_5: "MÓM",
    cardType_6: "WIN",
    cardType_7: "COMPENSATE WIN",
    cardType_8: "BACKRUPT",
    cardType_9: "ALL WIN",
    cardType_10: "WIN WITH NO PHOM",
  },

  mini_yxx: {
    good: "GREAT FORTUNE", // 幸运
    title: "JACKPOT LEADERBOARD",
    item1: "Rank",
    item2: "Display name",
    item3: "Total Win",
    round: "SESSION:",
    gameTitle: "GREAT\nFORTUNE",
    continue: "Bet\nAgain",
    page0: "Gourd & Crab Rankings",
    page0_rate: "Rank",
    page0_name: "Character name",
    page0_win: "Total Win",
    page1: "GOURD CRAB BET HISTORY",
    page1_item: "BET SLOT",
    page1_bet: "BET AMOUNT",
    page1_win: "Win",
    page1_list: "YOUR LUCKY NUMBER",
    page1_boom: "SESSION'S JACKPOT NUMBER",
    page4: "SESSION HISTORY",
    page4_label1: "Session",
    page4_label2: "LUCKY NUMBER",
    page5: "JACKPOT HISTORY",
    page5_label1: "Time",
    page5_label2: "Honor Board",
    page5_label3: "Total bet",
    page5_label4: "JACKPOT AMOUNT",
    page5_label5: "JACKPOT TYPE",
  },

  mini_ud: {
    title: "HIGH LOW JACKPOT HISTORY",
    time: "Time",
    user: "Account",
    bet: "Bet level",
    win: "Win Amount",
    type: "Type",
    page1Title: "HIGH LOW BET HISTORY",
    round: "SESSION:",
    page1Bet: "Total bet",
    result: "Result",
    tip: "Press Start to play",
    gameTitle: "HIGH/LOW",
    end: "NEW\nSPIN",
    start: "START",
  },

  mini_sb: {
    retrunMoney: "Symmetric refund",
    input: "Enter text…",
    rank: "Rank",
    name: "Display name",
    win: "Total Win",
    sumBet: "Total Bet Reached",
    sumBet2: "Total bet",
    reward: " Reward",
    betTip: "Your total bet amount is 0",
    honor: "Honor Board",
    reward2: "Reward",
    statisticTitle: "SESSION STATISTIC",
    time: "Bet time",
    user: "Player",
    bet: "Bet Amount",
    back: "Return",
    midTip: " Bet / Return",
    historyTitle: "SESSION HISTORY",
    betHistoryTitle: "BIG SMALL BET HISTORY",
    round: "Session",
    time2: "Time",
    player: "Details",
    send: "Submit",
    page6_title1: "BIG SMALL LEADERBOARD",
    page6_title2: "LEADERBOARD HISTORY",
    tai: "Big",
    xiu: "Small",
    btnTxt: "Dice",
    btnBet: "BET",
    allIn: "All IN",
    gameBet: "BET AMOUNT",
    cancel: "CANCEL",
  },

  //blackjack
  BLACKJACK_MINBET: "Cược tối thiểu: %{money}",
  BLACKJACK_MAXBET: "Cược tối đa: %{money}",
  BLACKJACK_NEXTBET1: "Cược tiếp: %{money}",
  BLACKJACK_NEXTBET2: "Cược tiếp",
  BLACKJACK_CARDTYPE1: "Oác", //爆点
  BLACKJACK_CARDTYPE3: "Năm rồng nhỏ", //5龙
  BLACKJACK_CARDTYPE4: "Black Jack", //黑杰克
  BLACKJACK_AUTOBET: "Hệ thống tự động đặt cược %{money}",
  BLACKJACK_SPLITCARD_1: "Chẻ trụ", //
  BLACKJACK_SPLITCARD_2: "Chẻ trụ(%{money})", //
  BLACKJACK_DOUBLEBET_1: "Cược gấp đôi", //
  BLACKJACK_DOUBLEBET_2: "gấp đôi(%{money})", //
  BLACKJACK_BUYINSURANCE1: "Xác nhận", //
  BLACKJACK_BUYINSURANCE2: "Xác nhận %{money}", //

  mini_sbmd5: {
    btnBet: "BET",
    gameBet: "BET AMOUNT",
    cancel: "CANCEL",
    startTitle_md5: "MD5 CHAIN",
    endTitle_md5: "Result",

    betHistoryTitle: "BIG SMALL BET HISTORY",
    round: "Session",
    time2: "Time",
    sumBet2: "Total bet",
    win: "Win Amount",
    player: "Details",

    historyTitle: "SESSION HISTORY",
    btnTxt: "Dice",

    statisticTitle: "SESSION STATISTIC",
    time: "Bet time",
    user: "Player",
    bet: "Bet Amount",
    MD5String: "MD5 CHAIN:",
    ResultString: "Result Chain:",

    midTip: "MD5 Result Checking Tutorial",

    Rating: "BIG - SMALL MD5 RANKING",
    rank: "Rank",
    name: "Display name",

    input: "Enter text…",
    send: "Submit",

    help_1Title: "BIG - SMALL MD5 INTRODUCTION",
    help_1InfoHelp:
      "<color=#FFFA00>· </c><color=#FFFFFF>Big-Small MD5 </color><color=#808080>is a type of Big - Small game that is non-balanced, using the MD5 algorithm with the advantage:</color>\
        \n<color=#808080>            - The dice result will be shown before starting another section.</color>\
        \n<color=#808080>            - Using a 3rd party matching system to verify results.</color>",
    help_1BtnHelp: "Introduction and instructions for checking MD5 results.",

    help_2Title: "VERIFY MD5",
  },
};