export default {
  bottomModal: {
    tip1: "We would like to add it to your home screen for the latest free bets and bonus updates.",
    tip2: "Continue to use H5",
    tip3: "Enjoy more benefits!",
  },
  ok: "OK",
  more: "More",
  less: "Less",
  reward: "Reward",
  // tab-bar
  home:         "Home",
  notification: "Notification",
  withdrawal:   "Withdrawal",
  recharge:     "Recharge",
  mine:         "Profile",
  login:        "Login",
  register:     "Register",
  // menu
  lobby:     "Lobby",
  favorite:  "Favorite",
  recent:    "Recent",
  casino:    "Casino",
  slot:      "Slot",
  cards:     "Cards",
  live:      "Live",
  sports:    "Sports",
  e_sports:  "E-Sports",
  fishing:   "Fishing",
  platform:  "Platform",
  lottery:   "lottery",
  hot_games: "Hot Games",
  // other
  agent:     "Agent",
  vip:       "Vip",
  statement: "Statement",
  tasks:     "Tasks",
  events:    "Events",
  rank:      "",
  // btn
  all:      "All",
  logout:   "Logout",
  go:       "GO",
  claim:    "Claim",
  claimed:  "Claimed",
  language: "Language",
  username: "<PERSON>rna<PERSON>",
  phone: "Phone",
  password:                   "Password",
  regex: {
    username: "Please enter 4 - 48 alphanumeric without special character."
  },

  events_page: {
    type: {
      5: "Daily Relief", //救济金 p6    9  -
      6: "",
      7: "Growth Funds", //等级奖励 p2   5 -
      8: "Bonus Package For New Member", //新人奖励 p9   2 -
      9: "Daily Rewards", //每日奖励 p3   6
      10: "Angel's Lucky Money Rain", //天降红包 p7    4 -
      11: "Lucky Wheel", //轮盘 p5      8
      18: "Daily First Deposit", //首冲 p1      1 -
      26: "Daily Bets Rewards", //流水奖励 p8   3 -
      38: "",
      45: "",
      46: "",
      100: "",
      200: "",
      201: "",
      1002: "",
      1004: "",
      1005: "",
      1006: "",
      1007: "",
    },
    5: {
      TEXT_GETSCORE8: "Loss amount of the day ",
      TEXT_GETSCORE9: "Relief for your loss",
      TEXT_BTN_GET: "Claim",
      TEXT_HINT: "Counted time    00:00 - 23:59:59",
      TEXT_HINT2: "The relief for your loss today will be ready on the next day.",
      // TEXT_HINT2_COPY: "",
      TEXT_LS: "When you bet, you will receive daily relief in case you lose",
      TEXT_LS_COPY: "this will help you turn defeat into victory.",
    },
    6: {
      LAYER_TITLE: "Daily Tasks", //日常任务
      BUTTON_1: "Common Tasks", //日常任务
      BUTTON_2: "Recharging Tasks", //充值任务
      BUTTON_3: "Battle Tasks ", //对抗任务
      BUTTON_4: "Arcade tasks", //电子任务Electronic tasks
      BUTTON_5: "Fishing tasks", //钓鱼任务
      TIP_1: "Current cumulated points:", //当前累积积分:

      BTN_GO: "Finished", //去完成
      BTN_DEPOSIT_NOW_GO: "Finished", //Deposit now
      BTN_LET_START: "Finished", //let start
      BTN_LET_PLAY: "Finished", //let play
      BTN_GET: "Claim", //领取奖励
      BTN_FINISH: "Claimed", //已完成

      TASK_TIMES: "Time", //次
      TASK_ROUNDS: "games", //局

      TASK_NAME_PAGE_1_1: "Daily login tasks", //每日登录任务
      TASK_NAME_PAGE_1_2: "Tasks to claim daily", //每天领取奖励的任务

      TASK_NAME_PAGE_2_1: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_2: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_3: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_4: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_5: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_6: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_7: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_8: "Daily recharging task", //每日充值任务
      TASK_NAME_PAGE_2_9: "Daily recharging task", //每日充值任务

      TASK_NAME_PAGE_3_1: "Daily VIETNAM SOUTHERN THIRTEEN tasks", //玩南方跑得快
      TASK_NAME_PAGE_3_2: "Daily VIETNAM SOUTHERN THIRTEEN tasks", //玩南方跑得快
      TASK_NAME_PAGE_3_3: "Daily GOLDEN FLOWER tasks", //每天玩 炸金花
      TASK_NAME_PAGE_3_4: "Daily POKER TEXAS HOLD'EM tasks", //每天玩 POKERTEXASHOLD'EM
      TASK_NAME_PAGE_3_5: "Daily GRAB THE ZHUANG NIUNIU tasks", //每天玩 抢庄牛牛

      TASK_NAME_PAGE_4_1: "Daily BIRDS AND BEATS tasks", //飞禽走兽 任务
      TASK_NAME_PAGE_4_2: "Daily NEED FOR SPEED tasks", //极品飞车 任务
      TASK_NAME_PAGE_4_3: "Daily XOC DIA tasks", //色碟 任务
      TASK_NAME_PAGE_4_4: "Daily RED AND BLACK tasks", //红黑大战 任务
      TASK_NAME_PAGE_4_5: "Daily DRAGON AND TIGER FIGHT tasks", //龙虎大战 任务
      TASK_NAME_PAGE_4_6: "Daily SICBO tasks", //大小 任务
      TASK_NAME_PAGE_4_7: "Daily Hoo Hey How  tasksy", //鱼虾蟹 任务
      TASK_NAME_PAGE_4_8: "Daily BACCARAT   tasks", //百家乐 任务

      TASK_NAME_PAGE_5_1: "Daily FISHING tasks", //任务 每天捕鱼
      TASK_NAME_PAGE_5_2: "Daily FISHING tasks", //任务 每天捕鱼
      TASK_NAME_PAGE_5_3: "Daily FISHING tasks", //任务 每天捕鱼

      TASK_MISSION_PAGE_1_1: "Login the game once", //登陆游戏
      TASK_MISSION_PAGE_1_2: "Claim from the game once", //赢得一局比赛

      TASK_MISSION_PAGE_2_1: "Daily cumulative recharging task: %{money}", //每天累计充值 50K
      TASK_MISSION_PAGE_2_2: "Daily cumulative recharging task: %{money}", //每天累计充值 100K
      TASK_MISSION_PAGE_2_3: "Daily cumulative recharging task: %{money}", //每天累积充值 300K
      TASK_MISSION_PAGE_2_4: "Daily cumulative recharging task: %{money}", //每天累计充值 500K
      TASK_MISSION_PAGE_2_5: "Daily cumulative recharging task: %{money}", //每天累计充值 1M
      TASK_MISSION_PAGE_2_6: "Daily cumulative recharging task: %{money}", //每天累计充值 2M
      TASK_MISSION_PAGE_2_7: "Daily cumulative recharging task: %{money}", //每天累计充值 3M
      TASK_MISSION_PAGE_2_8: "Daily cumulative recharging task: %{money}", //每天累计充值 5M
      TASK_MISSION_PAGE_2_9: "Daily cumulative recharging task: %{money}", //每天累计充值 10M

      TASK_MISSION_PAGE_3_1:
                             "Play %{num} games at VIETNAM SOUTHERN THIRTEEN (any room)", //到南方跑得快 玩10局
      TASK_MISSION_PAGE_3_2:
                             "Play %{num} games at VIETNAM SOUTHERN THIRTEEN (5k room)", //到南方跑得快 5k桌 玩10局
      TASK_MISSION_PAGE_3_3: "Play %{num} games at GOLDEN FLOWER (any room)", //炸金花 玩10局
      TASK_MISSION_PAGE_3_4:
                             "Play %{num} games at POKER TEXAS HOLD'EM (any room)", //POKERTEXASHOLD'EM 玩10局
      TASK_MISSION_PAGE_3_5:
                             "Play %{num} games at GRAB THE ZHUANG NIUNIU (any room)", //抢庄牛牛，玩10局

      TASK_MISSION_PAGE_4_1: "Win %{num} games at Birds and Beasts", //飞禽走兽 赢得 20 场比赛
      TASK_MISSION_PAGE_4_2: "Win %{num} games at NEED FOR SPEED", //极品飞车 赢得 20 场比赛
      TASK_MISSION_PAGE_4_3: "Win %{num} games at XOC DIA", //色碟  扑克 赢得 20 场比赛
      TASK_MISSION_PAGE_4_4: "Win %{num} games at RED AND BLACK", //红黑大战 赢得 20 场比赛
      TASK_MISSION_PAGE_4_5: "Win %{num} games at DRAGON AND TIGER FIGHT", //龙虎大战 赢得 20 场比赛
      TASK_MISSION_PAGE_4_6: "Win %{num} games at SICBO", //大小 赢得 20 场比赛
      TASK_MISSION_PAGE_4_7: "Win %{num} games at HOO HEY HOW", //鱼虾蟹 赢得 20 场比赛
      TASK_MISSION_PAGE_4_8: "Win %{num} games at Baccarat", //百家乐 赢得 20 场比赛

      TASK_MISSION_PAGE_5_1: "Total bet %{money} in 1 room", //桌 1K   下注 200K
      TASK_MISSION_PAGE_5_2: "Total bet %{money} in 10 room", //桌 10K 下注 500K
      TASK_MISSION_PAGE_5_3: "Total bet %{money} in 100 room", //桌 100K 下注 1 M
    },
    7: {
      TIP_1:
                 'One-time recharge amount \n reaches %{money}, you can activate the \npackage "Growth Fun"',
      ITEM_TIP1: "Reach Lv.%{data} to claim",
      ITEM_TIP2: "Reward  %{data}",
      BTN_1: "Claim",
      BTN_2: "Claimed",
    },
    8: {
      TEXT_TIP1: "When you register to become an official member",
      TEXT_TIP2: "LOWEST {0} OR MORE",
      TEXT_TIP3:
                 "In the first 3 days, you just need to log in, no need to deposit, you can participate to receive the new membership reward.(bonus package)",
      TEXT_TIP4: "VIP 2 and above log in on even days to get double bonus",
    },
    9: {
      SUN: "Sun", //周日
      MON: "Mon", //周一
      TUE: "Tue",
      WED: "Wed",
      THUR: "Thur",
      FRI: "Fri",
      SAT: "Sat", //周六
      BTN_VIPHINT: "Compensation reward explanation",
      BTN_RESTOCK: "Compensative reward",
      BTN_SIGN: "Claim now",
      EXPLAIN_1: "VIP tiers can get more rewards on Saturday, Sunday",
      EXPLAIN_2: "VIP1-3 get 2x bonus on weekends",
      EXPLAIN_3: "VIP4-6 get 3x bonus on weekends",
      EXPLAIN_4: "VIP7-8 get 5x bonus on weekends",
    },
    10: {
      TEXT_BTN_GET: "Receive",
      TEXT_BTN_LIST: "SEE LUCKY STAR",
      TEXT_TIP1:
          "Ascending Angel's Lucky Money Rain, Online to receive big gifts",
      TEXT_TIP2: "Everybody can get gifts, first come first serve.",
      TEXT_TIP3: "Remaining time:",
      TEXT_TIP4: "Lucky money rain time:",
      TEXT_TIP6:
          "Lucky money rain is automatic, players after opening the\
            \nred envelopes will receive a random bonus amount. \
            \nSurprises don't stop, Rewards don't stop.",
      //redBag    redBag_help
      REDBAG_HELP: {
        TEXT_TITLE: "Activity Rules",
        TEXT_HELP_1:
                    "1.Every day the deposit amount must reach 100 and the number of bet points must reach 200 to be able to participate.",
        TEXT_HELP_2:
                    "2. The time to participate each day is: 12:00 and 21:00, the duration of 2 times Descending Angels' lucky money rain is 1 hour.",
        TEXT_HELP_3:
                    "3. Lucky money will be automatically distributed, Players after opening will receive random lucky money, each time the highest bonus is 388.",
        TEXT_HELP_4:
                    "4.Players have the opportunity to receive the reward of Descending \
              Angels, Rules of Descending Angels: Based on last 2 numbers of lucky \
              money as lucky numbers to judge whether the player receive the favor \
              of the angel or not, fixed lucky number is: 18, 38, 88. \
              If player receive lucky money ending in 18, 38, 88 will be the lucky star, the bonus will be % of current account balance ,details are as follows:\
                 \n   The last 2 numbers of lucky money 18 corresponds to 18% of the player's current account balance.\
                 \n   The last 2 numbers of lucky money 38 corresponds to 38% of the player's current account balance.\
                 \n   The last 2 numbers of lucky money 88 corresponds to 88% of the player's current account balance.\
                 \n   Example: Player account balance is 10M, received lucky money is 58088, lucky bonus will be: 10M * 88% = 8.8M\
                 \n   Unlimited number of lucky recipients, unlimited bonus amount, the higher the account balance, the higher the bonus will be received.",
        TEXT_HELP_5:
                    "5.The bonus of Descending Angels will be paid automatically, after the player\
              wins the lucky star, the bonus and lucky money will be paid at the same time and announced on the bulletin board..",
        TEXT_HELP_6:
                    "6.All kinds of money, just need to bet x1 of the bonus amount to be able to withdraw.",
        TEXT_HELP_7:
                    "7. It is strictly forbidden to use multiple accounts to participate, If it is detected\
               that the player is using multiple accounts to participate in this activity, the eligibility to participate \
               in all activities will be canceled, with the same IP, the same name, the same bank account, the same phone,\
               the same wifi, and the same public network are all considered to be using multiple accounts.",
      },
      //redBag    redBag_list
      REDBAG_LIST: {
        TEXT_TITLE: "SEE LUCKY STAR",
        TEXT_TITLE1: "Time",
        TEXT_TITLE2: "Account",
        TEXT_TITLE3: "Lucky Money",
        TEXT_TITLE4: "Reward",
      },
    },
    11: {
      TURNTABLE_TITLE_0: "Silver Wheel",
      TURNTABLE_TITLE_1: "Gold Wheel",
      TURNTABLE_TITLE_2: "Diamond Wheel",
      IMAGE_LOSING: "Thanks",
      AMOUNT_BET: "Bet Amount",
      ACCUMULATE_POINT: "Current cumulated points",
      BTN_TYPE3_S_DIAMOND: "Diamond Wheel",
      BTN_TYPE3_S_POINTS: "Point 20000",
      BTN_TYPE3_H_DIAMOND: "Diamond Wheel",
      BTN_TYPE3_H_POINTS: "Point 20000",
      BTN_TYPE2_S_GOLD: "Gold Wheel",
      BTN_TYPE2_S_POINTS: "Point 5000",
      BTN_TYPE2_H_GOLD: "Gold Wheel",
      BTN_TYPE2_H_POINTS: "Point 5000",
      BTN_TYPE1_S_SILVER: "Silver Wheel",
      BTN_TYPE1_S_POINTS: "Point1000",
      BTN_TYPE1_H_SILVER: "Silver Wheel",
      BTN_TYPE1_H_POINTS: "Point1000",
      RECORD_TOP_TIME: "Time",
      RECORD_TOP_TYPE: "Treasure type",
      RECORD_TOP_AWARD: "Won",
      BTN_WINHISTORY: "Reward records",
      BTN_SPINRESULTS: "Your results",
    },
  },
  notice: {
    title: "Notification",
  },
  ad: {
    title: "Events",
    tab: {
      1: "VIP bonus",
      2: "Daily rewards",
      3: "Daily First Deposit",
      4: "All-Be-Agent",
      5: "Growth Funds",
      6: "Lucky Wheel",
      7: "Daily Relief",
      8: "Ascending Angel's Lucky Money Rain",
      9: "Dealer Daily Ranking",
      10: "BONUS PACKAGE FOR NEW MEMBER",
      // 11: "",
      // 12: "",
      // 13: "",
      // 14: "",
      // 15: "",
      // 16: "",
      18: "Trợ cấp",
      19: "Phần thưởng đăng nhập hằng ngày",
      20: "Quà tặng khuyến mãi",
      21: "Mưa lì xì đỉnh cao",
    },
    panel: {
      1: {
        th: {
          0: "VIP level",
          1: "VIP bonus",
        },
      },
      2: {
        th: {
          0: "Tasks",
          1: "Progress",
        },
        tr: {
          0: "Common tasks",
          1: "Recharge tasks",
          2: "Battle tasks",
          3: "Arcade tasks",
          4: "Fishing tasks",
        },
        tip: {
          0: "Tips: Finish task to receive corresponding points and exchange rewards.",
        },
      },
      3: {
        th: {
          0: "Daily first deposit",
          1: "bonus",
        },
        tip: {
          0: "1. The activity time is calculated according to the first daily recharge. The higher the daily first recharge amount, the more rewards you will get.",
          1: "2. The system automatically checks the recharge amount of members, and if there is any problem, it will be checked according to TDTC",
          2: "3. In order to ensure that the member's registration information is qualified, if it is found that the member maliciously uses multiple accounts or forged information to register, BOSS has the right to cancel the participation in the event, freeze the account, and deduct the bonus.",
        },
      },
      4: {
        th: {
          0: "Subordinates",
          1: "Commission Amount",
        },
        tip: {
          0: "Tips: All-Be-Agent, everyone can be an agent of our system, when your subordinate win a bet, you will get 30% of the winning tax. The more subordinates you hvae, the higher the commission, other rewards can reach 6M for the agent with the number of subordinates meeting our conditions.",
        },
      },
      5: {
        th: {
          0: "LEVEL",
          1: "Reward amount",
        },
        tip: {
          0: 'Tips: One-time recharge with 100k can trigger the "Grow-up Points" event, members who reach any level will receive corresponding rewards.',
        },
      },
      6: {
        tip: {
          0: "Tips:Points of Lucky Wheel are calculated based on yesterday's total bet (bet 10k will receive 1 point) or during the event (online time) to receive online points, the Lucky Wheel point will automatically reset to 0 and be calculated from the beginning at 0:00 every day.",
        },
      },
      7: {
        th: {
          0: "Loss of the day",
          1: "Relief amount",
        },
        tip: {
          0: "Tips: Based on the member's actual daily loss amount to calculate. The bonus activities are not included in the actual loss, the member can rely on the actual loss to receive the relief bonus.",
        },
      },
      8: {
        th: {
          0: "TWO ENDING NUMBERS OF LUCKY MONEY",
          1: "%ACCOUNT BALANCE AT WINNING TIME",
        },
        tip: {
          0: "Activity time: 12:00 and 21:00",
          1: "Players can open lucky money pouch to receive random lucky money, each time the highest bonus is: 388K.",
        },
      },
      9: {
        th: {
          0: "Ranking",
          1: "Recommender",
          2: "Bonus",
        },
      },
      10: {
        grade: "grade",
        tr: {
          0: {
            0: "Amount of the log in days",
            1: "The amount of odds day",
            2: "The amount of even day",
          },
          1: {
            0: "Amount of daily recharge",
            1: "Arbitrary value",
            2: "Arbitrary value",
          },
          2: {
            0: "Accumulated bonus amount",
          },
        },
        tip: {
          // 0: ""
        },
      },
      0: {
        th: {
          0: "",
        },
        tr: {
          0: "",
        },
        tip: {
          0: "",
        },
      },
    },
  },
};
