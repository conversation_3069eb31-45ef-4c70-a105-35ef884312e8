import json
from openpyxl import Workbook


# 读取JSON文件
with open('cn.json', 'r', encoding='utf-8') as f:
    cn = json.load(f)

with open('en.json', 'r', encoding='utf-8') as f:
    en = json.load(f)

with open('pt.json', 'r', encoding='utf-8') as f:
    pt = json.load(f)
    # print(json_data)
# 创建Excel文件和工作表
workbook = Workbook()
worksheet = workbook.active
# 写入表头
worksheet.cell(row=1, column=1, value="键名-不翻译")
worksheet.cell(row=1, column=2, value="翻译-cn")
worksheet.cell(row=1, column=3, value="翻译-en")
worksheet.cell(row=1, column=4, value="翻译-pt")
row_index = 2
# 将JSON数据写入工作表
for item in en:
    # 写入数据
    worksheet.cell(row=row_index, column=1, value=item)
    if item in cn:
        worksheet.cell(row=row_index, column=2, value=cn[item])
    worksheet.cell(row=row_index, column=3, value=en[item])
    worksheet.cell(row=row_index, column=4, value=pt[item])
    row_index = row_index + 1

# 保存Excel文件
workbook.save('a6-i18n.xlsx')
