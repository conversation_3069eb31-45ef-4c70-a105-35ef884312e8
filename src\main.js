// import "babel-polyfill"
// import 'core-js/stable';
// import 'regenerator-runtime/runtime';
import Vue from "vue";
import i18n from "@/lang";
import router from "./router";
import store from "@/store";
import App from "./App.vue";
// import '@vant/touch-emulator';

import "bootstrap";
import VueAwesomeSwiper from "vue-awesome-swiper";
Vue.use(VueAwesomeSwiper);
/*import VModal from "vue-js-modal/dist/index.js";
Vue.use(VModal);*/


import { Button, Steps, Modal, Form, Input } from "ant-design-vue";

Vue.use(Button)
Vue.use(Steps)
Vue.use(Modal)
Vue.use(Form)
Vue.use(Input)
Vue.use(Input.Password)
// Global Components
// import "@/global-components";

import cover from "@/utils/cover";
window.cover = cover;
Vue.prototype.cover = cover;

// import 'daterangepicker/daterangepicker'
// import 'daterangepicker/daterangepicker.css'

import "@/utils/filter"



import Paginate from "vuejs-paginate";
Vue.component("paginate", Paginate);

Vue.config.productionTip = false;

// if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
  new Vue({
    router,
    store,
    i18n,
    render: (h) => h(App),
  }).$mount("#root");

// } else {
//   new Vue({
//     router,
//     store,
//     i18n,
//     render: (h) => h(App),
//   }).$mount("#app");
// }

