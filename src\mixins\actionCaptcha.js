export const actionCaptcha = {
  data() {
    return {
      captchaPlatform: 3,
      aliCaptchaInstance: null,
      captchaInstance: [],
      configGeetest: {
        captchaId: "",
        product: "bind",
        language: "eng",
        logo: false,
        hideSuccess: true,
        feedback: "",
      },
      configTCaptcha: {
        CaptchaAppId: "",
      },
      validate: {
        captcha_0: "",
        captcha_1: "",
        captcha_2: "",
        captcha_3: "",
      }
    };
  },
  beforeDestroy() {
    for (let i = 0; i < this.captchaInstance.length; i++) {
      this.captchaInstance[i] && this.captchaInstance[i].destroy();
    }
  },
  mounted() {
    window.aliHandlerName = "";
    this.captchaPlatform = Number(process.env.VUE_APP_CAPTCHA_PLATFORM);
    switch (this.captchaPlatform) {
      case 1:
        this.configGeetest.captchaId = process.env.VUE_APP_GEETESTID;
        this.configGeetest.language = this.$store.state.language === "en" ? "eng" : "pon";
        break;
      case 2:
        this.configTCaptcha.CaptchaAppId = process.env.VUE_APP_TCAPTCHAAPPID;
        break
    }
  },
  methods: {
    initActionCaptcha(onSuccess, id= "") {
      iconsole("=== init capt", id)
      let that = this;
      switch (this.captchaPlatform) {
        case 1:
          initGeetest4(this.configGeetest, function (captcha) {
            captcha
                .onReady(function () {
                })
                .onSuccess(()=>{
                  let result = that.captchaInstance[id].getValidate();
                  if (!result) {
                    return;
                  }
                  window.validate = {
                    captcha_0: result.lot_number,
                    captcha_1: result.captcha_output,
                    captcha_2: result.pass_token,
                    captcha_3: result.gen_time,
                  }
                  onSuccess();
                })
                .onError(function () {
                });
            that.captchaInstance[id] = captcha;
          });
          break
        case 2:
          try {
            this.captchaInstance[id] = new TencentCaptcha(this.configTCaptcha.CaptchaAppId, (res)=>{
              iconsole(res)
              if (res.ret === 0 && res.ticket){
                window.validate = {
                  captcha_0: res.ticket,
                  captcha_1: res.randstr,
                  captcha_2:"",
                  captcha_3: "",
                }
                onSuccess();
              }
            }, {userLanguage: that.$store.state.language});

          } catch (error) {
            iconsole(error)
            $toast.fail({
              message: this.$t("action_captcha_error"),
            });
          }
          break
        case 3:
          if (window.aliMethod === undefined) window.aliMethod = [];
          window.aliMethod[id] = onSuccess;
          iconsole(window.aliMethod)
          if (this.aliCaptchaInstance) return;
          let getInstance = (instance) => {
            this.aliCaptchaInstance = instance
          }

          let captchaVerifyCallback = (captchaVerifyParam) => {
            iconsole(captchaVerifyParam)
            iconsole("=== captchaVerifyCallback", window.aliMethod[window.aliHandlerName])
            window.validate = {
              captcha_0: captchaVerifyParam,
              captcha_1: "",
              captcha_2:"",
              captcha_3: "",
            }
            if (window.aliMethod[window.aliHandlerName]) {
              window.aliMethod[window.aliHandlerName]();
            } else {
              iconsole("ali captcha method err")
            }
            return {
              captchaResult: true, // 验证码验证是否通过，boolean类型，必选
              // bizResult: true, // 业务验证是否通过，boolean类型，可选；若为无业务验证结果的场景，bizResult可以为空
            }
          }
          window.initAliyunCaptcha({
            SceneId: '1p6ysr3fh',
            mode: 'popup',
            //页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
            element: '#captcha-element',
            // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
            button: '#captcha-button',
            // 业务请求(带验证码校验)回调函数，无需修改
            captchaVerifyCallback: captchaVerifyCallback,
            // 业务请求结果回调函数，无需修改
            onBizResultCallback: this.onBizResultCallback,
            // 绑定验证码实例函数，无需修改
            getInstance: getInstance,
            // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
            slideStyle: {
              width: 320,
              height: 40,
            },
            language: "vi",
          });



      }
    },
    onBizResultCallback (bizResult) {
      iconsole("=== onBizResultCallback", bizResult)
      this.aliCaptchaInstance.refresh();
    },
    showActionCaptcha(handlerName) {
      iconsole("=== showActionCaptcha method", handlerName);
      let that = this;
      this.$validator.validateAll().then(function (t) {
        if (t) {
          switch (that.captchaPlatform) {
            case 1:
              that.captchaInstance[handlerName].showCaptcha();
              break
            case 2:
              that.captchaInstance[handlerName].show();
            case 3:
              window.aliHandlerName = handlerName
              const element = document.getElementById("captcha-button");
              if (element) {
                element.click();
              }
          }
        }
      });
    },
  },
};
