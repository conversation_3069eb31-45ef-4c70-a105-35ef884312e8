import {
  ROUTE_PLATFORM_REDPACKETRAIN,
  ROUTE_PLATFORM_REDPACKETRAINDETAIL,
  ROUTE_RECORDER_QUERY_QUERYREDPACKETRAIN,
} from "@/api";
import moment from "moment/moment";
import {activitybase} from "@/mixins/activity/activitybase";

export const redpacketrain = {
  mixins: [activitybase],
  data() {
    return {
      index: 0,
      Details: {
        details: [],
        maxReward: 0,
        minChargeamount: 0,
        received: [],
        userYestodayChargeamount: 0,
      },
      Query: {
        ownnerRecords: [],
        winnerRecords: [],
      },
    };
  },
  mounted() {
    this.detail();
    // this.submit()
    this.query();
  },
  computed: {
    btnReceive() {
      return function (index) {
        return this.Details.received[index].received ? this.$t('button_receive_already') : this.$t('button_receive')
      }
    }
  },
  methods: {
    valid(index) {
      if (this.Details.received[index].received) {
        return this.$t('button_receive_already');
      }
      if (!this.checkBegin(this.Details.details[index].beginTime, this.Details.details[index].endTime)) {
        return this.$t('583');
      }
      if (this.Details.minChargeamount > this.Details.userYestodayChargeamount) {
        return this.$t('580');
      }
      return "";
    },
    changeLog(index) {
      this.index = index
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_REDPACKETRAINDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit(index) {
      let msg = this.valid(index)
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_REDPACKETRAIN, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,

      })
        .then((res) => {
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.Details.received[index].received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYREDPACKETRAIN, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
