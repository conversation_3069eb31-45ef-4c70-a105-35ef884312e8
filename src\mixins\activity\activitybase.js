import moment from "moment";

export const activitybase = {
    // computed: {
    //     beginTime() {
    //         return moment(Number(moment(new Date()).startOf("day").format('x')) + this.Details.beginTime * 1000).format("HH:mm:ss");
    //     },
    //     endTime() {
    //         return moment(Number(moment(new Date()).startOf("day").format('x')) + this.Details.endTime * 1000).format("HH:mm:ss");
    //     },
    //     begin() {
    //         return (new Date()).getTime() - Number(moment(new Date()).startOf("day").format('x')) > this.Details.beginTime * 1000
    //             && (new Date()).getTime() - Number(moment(new Date()).startOf("day").format('x')) < this.Details.endTime * 1000
    //     },
    // },
    methods: {
        checkBegin(begin, end) {
            return (new Date()).getTime() - Number(moment(new Date()).startOf("day").format('x')) > begin * 1000
                && (new Date()).getTime() - Number(moment(new Date()).startOf("day").format('x')) < end * 1000
        },
    }
}