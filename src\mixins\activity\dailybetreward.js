import {
  ROUTE_PLATFORM_DAILYBETREWARD,
  ROUTE_PLATFORM_DAILYBETREWARDDETAIL,
  ROUTE_RECORDER_QUERY_QUERYDAILYBETREWARD,
} from "@/api";
import {dot} from "@/mixins/dot";

export const dailybetreward = {
  mixins: [dot],
  data() {
    return {
      Details: {
        details: [],
        received: false,
        userYestodayBetamount: 0,
      },
      Query: {
        records: []
      },
      swiperOptions: {
        speed: 1000,
        autoplay: {
          delay: 500,
          disableOnInteraction: false,
        },
        direction: "vertical",
        slidesPerView: "auto",
        observer: true,
        observeParents: !0,
        preloadImages: !1,
        spaceBetween: 15,
      },
    };
  },
  mounted() {
    this.detail();
    // this.submit()
    this.query();
  },
  computed: {
    currentRow() {
      return function (item, index) {
        return (this.Details.userYestodayBetamount >= item.betAmount && this.Details.userYestodayBetamount <  this.Details.details.slice().reverse()[index+1].betAmount);
      }
    },
    btnReceive() {
      return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
    },
    maxReward() {
      return this.Details.details.length ? this.Details.details[0].reward : 0
    }
  },
  methods: {
    valid() {
      if (this.Details.received) {
        return this.$t('button_receive_already')
      }
      if (this.Details.details.length && this.Details.userYestodayBetamount < this.Details.details.slice().reverse()[0].betAmount) {
        return this.$t('581');
      }
      return "";
    },
    detail() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_DAILYBETREWARDDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_DAILYBETREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.event_dailyReward()
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.Details.received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYDAILYBETREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
