import {
    ROUTE_PLATFORM_DAILYCHECKIN,
    ROUTE_PLATFORM_DAILYCHECKINDETAIL,
    ROUTE_RECORDER_QUERY_QUERYDAILYCHECKIN
} from "@/api";

export const dailycheckin = {
    data() {
        return {
            detail: [],
            minBetamount: 0,
            minChargeamount: 0,
            canReceiveDailyId: 0,
            userTodayBetamount: 0,
            userTodayChargeamount: 0,
            loggerDailyId: 0,
        }
    },
    mounted() {
        this.dailycheckindetail()
    },
    computed: {
        btnCheck() {
            return function (item) {
                return this.canReceiveDailyId && this.loggerDailyId+1 === item.dailyId
            };
        }
    },
    methods: {
        dailycheckindetail() {
            this.$protoApi(ROUTE_PLATFORM_DAILYCHECKINDETAIL, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.minBetamount = res.minBetamount
                    this.minChargeamount = res.minChargeamount
                    this.userTodayBetamount = res.userTodayBetamount
                    this.userTodayChargeamount = res.userTodayChargeamount
                    this.detail = res.detail.confs
                    this.querydailycheckin()
                })
                .catch(() => {
                })
                .finally(() => {
                });
        },
        querydailycheckin() {
            this.$protoApi(ROUTE_RECORDER_QUERY_QUERYDAILYCHECKIN, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.canReceiveDailyId = res.canReceiveDailyId
                    this.loggerDailyId = res.loggerDailyId
                })
                .catch(() => {
                })
                .finally(() => {
                });
        },
        dailycheckin() {
            if (this.minChargeamount > this.userTodayChargeamount || this.minBetamount > this.userTodayBetamount) {
                $toast.fail({
                    message: this.$t('580'),
                });
                return
            }
            this.$protoApi(ROUTE_PLATFORM_DAILYCHECKIN, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.loggerDailyId += 1
                    this.canReceiveDailyId = 0

                    $toast.success({
                        icon: "passed",
                        message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
                    });
                })
                .catch(() => {
                })
                .finally(() => {
                });
        },
    }
}