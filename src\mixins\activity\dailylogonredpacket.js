import {
    ROUTE_PLATFORM_DAILYLOGONREDPACKET,
    ROUTE_PLATFORM_DAILYLOGONREDPACKETDETAIL,
    ROUTE_RECORDER_QUERY_QUERYDAILYLOGONREDPACKET,
} from "@/api";
import {activitybase} from "@/mixins/activity/activitybase";

export const dailylogonredpacket = {
    mixins: [activitybase],
    data() {
        return {
            Details: {
                beginTime: 0,
                endTime: 0,
                minChargeamount: 0,
                received: false,
                userYestodayChargeamount: 0,
            },
            records: []
        }
    },
    mounted() {
        this.detail();
        this.query()
    },
    computed: {
        btnReceive() {
            return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
        }
    },
    methods: {
        valid() {
            if (this.Details.received) {
                return this.$t('button_receive_already');
            }
            if (!this.checkBegin(this.Details.beginTime, this.Details.endTime)) {
                return this.$t('583');
            }
            if (this.Details.minChargeamount > this.Details.userYestodayChargeamount) {
                return this.$t('580');
            }
            return "";
        },
        detail() {
            this.$protoApi(ROUTE_PLATFORM_DAILYLOGONREDPACKETDETAIL, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Details = res
                })
                .catch(() => {})
                .finally(() => {});
        },
        submit() {
            let msg = this.valid()
            if (msg) {
                $toast.fail({
                    message: msg,
                });
                return;
            }
            this.$protoApi(ROUTE_PLATFORM_DAILYLOGONREDPACKET, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    $toast.success({
                        icon: "passed",
                        message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
                    });
                    this.Details.received = true
                    this.query()
                })
                .catch(() => {})
                .finally(() => {});
        },
        query() {
            this.$protoApi(ROUTE_RECORDER_QUERY_QUERYDAILYLOGONREDPACKET, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.records = res.ownnerRecords
                })
                .catch(() => {})
                .finally(() => {});
        },
    },
};
