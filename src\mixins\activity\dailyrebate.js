import {
  ROUTE_PLATFORM_DAILYREBATEDETAIL,
  ROUTE_PLATFORM_DAILYREBATE,
  ROUTE_RECORDER_QUERY_QUERYDAILYREBATE,
} from "@/api";

export const dailyrebate = {
  data() {
    return {
      Details: {
        details: [],
        show: [],
        received: false,
        yestodayCardsBetamount: 0,
        yestodayCasinoBetamount: 0,
        yestodayEsportsBetamount: 0,
        yestodayFishBetamount: 0,
        yestodayLiveBetamount: 0,
        yestodayLotteryBetamount: 0,
        yestodaySlotBetamount: 0,
        yestodaySportsBetamount: 0,
      },
      Query: {
        records: [],
      },
    };
  },
  mounted() {
    this.detail();
    this.query();
  },
  computed: {
    btnReceive() {
      return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
    },
  },
  methods: {
    valid() {
      if (this.Details.received) {
        return this.$t("button_receive_already");
      }
      return "";
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_DAILYREBATEDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_DAILYREBATE, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          $toast.success({
            icon: "passed",
          });
          this.Details.received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYDAILYREBATE, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
