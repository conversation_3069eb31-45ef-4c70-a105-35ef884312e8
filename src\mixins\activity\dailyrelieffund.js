import {
  ROUTE_PLATFORM_DAILYRELIEFFUND,
  ROUTE_PLATFORM_DAILYRELIEFFUNDDETAIL,
  ROUTE_RECORDER_QUERY_QUERYDAILYRELIEFFUND,
} from "@/api";

export const dailyrelieffund = {
  data() {
    return {
      userYestodayLosescore: 0,
      Details: {
        details: [],
        maxReward: 0,
        received: false,
        userYestodayLosescore: 0,
      },
      Query: {
        records: [],
      },
      swiperOptions: {
        speed: 1000,
        autoplay: {
          delay: 500,
          disableOnInteraction: false,
        },
        direction: "vertical",
        slidesPerView: "auto",
        observer: true,
        observeParents: !0,
        preloadImages: !1,
        spaceBetween: 15,
      },
    };
  },
  mounted() {
    this.detail();
    this.query();
  },
  computed: {
    currentRow() {
      return function (item, index) {
        return (this.Details.userYestodayLosescore >= item.loseScore && this.Details.userYestodayLosescore <  this.Details.details.slice().reverse()[index+1].loseScore);
      }
    },
    btnReceive() {
      return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
    },
    maxReward() {
      return this.Details.details.length ? this.Details.details[0].reward : 0
    }
  },
  methods: {
    valid() {
      if (this.Details.received) {
        return this.$t("button_receive_already");
      }
      if (
        this.Details.details.length &&
        this.Details.userYestodayLosescore <
          this.Details.details.slice().reverse()[0].loseScore
      ) {
        return this.$t("588");
      }
      return "";
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_DAILYRELIEFFUNDDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_DAILYRELIEFFUND, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.Details.received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYDAILYRELIEFFUND, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
