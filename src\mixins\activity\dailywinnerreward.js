import {
  ROUTE_PLATFORM_DAILYWINNERREWARD,
  ROUTE_PLATFORM_DAILYWINNERREWARDDETAIL,
  ROUTE_RECORDER_QUERY_QUERYDAILYWINNERREWARD,
} from "@/api";

export const dailywinnerreward = {
  data() {
    return {
      Details: {
        details: [],
        received: false,
        userYestodayWinscore: 0,
      },
      Query: {
        records: [],
      },
      swiperOptions: {
        speed: 1000,
        autoplay: {
          delay: 500,
          disableOnInteraction: false,
        },
        direction: "vertical",
        slidesPerView: "auto",
        observer: true,
        observeParents: !0,
        preloadImages: !1,
        spaceBetween: 15,
      },
    };
  },
  mounted() {
    this.detail();
    this.query();
  },
  computed: {
    currentRow() {
      return function (item, index) {
        return (this.Details.userYestodayWinscore >= item.winScore && this.Details.userYestodayWinscore <  this.Details.details.slice().reverse()[index+1].winScore);
      }
    },
    btnReceive() {
      return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
    },
    maxReward() {
      return this.Details.details.length ? this.Details.details[0].reward : 0
    }
  },
  methods: {
    valid() {
      if (this.Details.received) {
        return this.$t("button_receive_already");
      }
      if (
        this.Details.details.length &&
        this.Details.userYestodayWinscore <
          this.Details.details.slice().reverse()[0].winScore
      ) {
        return this.$t('587');
      }
      return "";
    },

    detail() {
      this.$protoApi(ROUTE_PLATFORM_DAILYWINNERREWARDDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_DAILYWINNERREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.Details.received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYDAILYWINNERREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
