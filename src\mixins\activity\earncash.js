import {
  ROUTE_PLATFORM_EARNCASHBYPROMOTEDETAIL,
  ROUTE_PLATFORM_EARNCASHBYPROMOTESTARTWHEEL,
  ROUTE_PLATFORM_EARNCASHBYPROMOTEWITHDRAWAL,
  ROUTE_RECORDER_QUERY_QUERYEARNCASHWITHDRAWAL,
} from "@/api";

export const earncash = {
  data() {
    return {
      timer: null,
      showWheel: true,
      showRes: false,
      showWithdraw: false,
      showShare: false,
      countDownElement: "0d 00:00:00",
      upIng: false,
      upIngText: "",
      percentage: 0,
      stepActive: 1,
      preCashAmount: 0,
      Details: {
        currentCashAmount: 0,
        currentGoldcoin: 0,
        currentRound: 0,
        currentRoundEndtime: 0,
        everyRoundActivityValidtime: 0,
        freeLotteryCount: 0,
        freegivingLotteryCount: 0,
        freegivingTimeInterval: 0,
        goldcoinToCashOfCash: 0,
        goldcoinToCashOfGoldcoin: 0,
        lotteryCount: 0,
        wheelConf: [],
        withdrawalAmount: 10000,
        withdrawalValidtime: 0,
        state: 0,
        promoteUrl: "",
      },
      StartWheel: {
        code: 0,
        currentCashAmount: 0,
        currentFreeLotteryCount: 0,
        currentGoldCoin: 0,
        currentLotteryCount: 0,
        goldcoinToCashAmount: 0,
        rewardType: 0,
        rewardValue: 0,
        rewardWheelId: 0,
        state: 0,
      },
      rewardValue: 0,
      Query: {
        ownnerRecords: [],
      },
    };
  },
  mounted() {
    this.blocks[0].imgs = this.blocksImgs
    this.timer = setInterval(()=>{
      if (this.blocks[0].imgs[1].src === "img/activity/15/bg_5.png") {
        this.blocks[0].imgs[1].src = "img/activity/15/bg_5_1.png"
      } else {
        this.blocks[0].imgs[1].src = "img/activity/15/bg_5.png"
      }
    }, 500)
    if (this.$store.getters.isLogin) {
      this.detail();
    }
    // this.query();
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  computed: {
    shareText() {
      return `Receba ${this.Details.withdrawalAmount / 100} BRL de graça,Pix SAQUE RÁPIDO `
    },
    showUrl() {
      return `${this.Details.promoteUrl}?code=${this.$store.state.account.userId}`;
    },
    cha() {
      return (this.Details.withdrawalAmount - this.Details.currentCashAmount) / 100
    },
    btnText() {
      if (this.upIng && this.rewardValue) {
        return "+" + this.upIngText
      } else {
        return this.$store.state.token.token ? this.Details.freeLotteryCount + this.Details.lotteryCount : "1"
      }
    },
  },
  methods: {
    share: function(type) {
      let url;
      switch (type) {
        case "facebook":
          url = "https://www.facebook.com/sharer/sharer.php?u=" + encodeURIComponent(this.showUrl) + "&t=" + encodeURIComponent(this.shareText)
          // url = "fb://share/?link=" + this.showUrl
          break;
        case "telegram":
          url = "https://t.me/share/url?url=" + encodeURIComponent(this.showUrl) + "&text=" + encodeURIComponent(this.shareText)
          break;
        case "twitter":
          url = "https://twitter.com/intent/tweet?text" + encodeURIComponent(this.shareText) + "&url=" + encodeURIComponent(this.showUrl)
          break;
        case "whatsapp":
          url = "https://api.whatsapp.com/send?text=".concat(this.shareText) + encodeURIComponent(this.showUrl)
          // href = "whatsapp://send?text=" + encodeURIComponent(this.shareText) + encodeURIComponent("\n\n" + this.showUrl) + "&via=lopscoop"
          // location.href = `https://api.whatsapp.com/send?phone=${t}&text=${this.$language.format("spins.Response")}` + this.shareTxt : location.href = "whatsapp://send?phone=+" + t + "&text=" + encodeURIComponent(this.$language.format("spins.Response") + this.shareTxt) + encodeURIComponent("\n\n" + this.url) + "&via=lopscoop"
          break;
        case "email":
          url = "mailto:?body=" + encodeURIComponent(this.shareText)
          break;
        case "sms":
          url = "sms:?addresses=" + "" + "&body=" + encodeURIComponent(this.shareText + this.showUrl)
          break;
      }

      const userAgent = navigator.userAgent;
      if (/Android/i.test(userAgent) && 'jsBridge' in window) {
        window.open("browser:" + url)
      } else {
        setTimeout(()=>{
          window.open(url)
        })
      }
    },
    numTo(finalNum) {
      this.upIng = true;

      let rate = 30;
      let time = 1000;
      let step = finalNum / (time / rate);
      let count = 1;
      let that = this
      let timer = setInterval(() => {
        count = count + step;
        that.upIngText = count.toFixed(0)
        if (count > finalNum) {
          count = finalNum;
          that.upIngText = count
          clearInterval(timer);
          timer = null;
          setTimeout(()=>{
            that.upIng = false;
          }, 1000)
        }
      }, rate);
    },
    percentageTo(start, end) {
      if (!end) return
      let rate = 30;
      let time = 1000;
      let step = end / (time / rate);
      let count = start;
      let timer = setInterval(() => {
        count = count + step;
        this.percentage = count.toFixed(0)

        if (count > end) {
          count = end;
          this.percentage = count
          clearInterval(timer);
          timer = null;
        }
      }, rate);
    },
    countExpires(endTime) {
      let that = this
      const timer = setInterval(function () {
        const now = new Date().getTime();
        const distance = endTime - now;

        if (distance < 0) {
          clearInterval(timer);
          that.countDownElement = "0d 00:00:00";
          return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        that.countDownElement =
          days + "d " + (hours<10?"0":'') + hours + ":" + (minutes<10?"0":'') + minutes + ": " + (seconds<10?"0":'') + seconds;
      }, 1000);
    },
    valid() {
      if (!this.$store.getters.isLogin) {
        this.$store.commit("setActivity15Step", 0);
        if (this.$store.state.webType === 1) {
          this.$modal.show("loginPopupModal");
        } else {
          this.$router.push("/m/login").catch(()=>{})
        }
        return;
      }
      if (this.Details.state) {
        $toast.fail({
          message: this.$t("589"),
        });
        this.showWheel = false
        return;
      }
      this.luckywheelstart();
    },
    endCallback(blocksImgs, blocksImgsSelected, front = false) {
      this.preCashAmount = this.Details.currentCashAmount / 100;
      this.Details.currentCashAmount = this.StartWheel.currentCashAmount;
      this.Details.currentGoldCoin = this.StartWheel.currentGoldCoin;
      this.Details.freeLotteryCount = this.StartWheel.currentFreeLotteryCount;
      this.Details.lotteryCount = this.StartWheel.currentLotteryCount;
      this.Details.goldcoinToCashOfCash = this.StartWheel.goldcoinToCashAmount;
      this.Details.state = this.StartWheel.state;
      this.rewardValue = this.StartWheel.rewardType === 1 ? this.StartWheel.rewardValue / 100 : this.StartWheel.rewardValue;
      let that = this
      if (front) {
        setTimeout(() => {
          that.showWheel = false;
          that.showRes = true;
        }, 1600);
      } else {
        if (this.Details.state) {
          this.stepActive = 2
          setTimeout(()=>{that.showWithdraw = true}, 1300)
        }
      }
      this.numTo(this.rewardValue)
      this.percentageTo(this.percentage, this.Details.currentCashAmount / 100)
      function startInterval() {
        let count = 0;
        let intervalId = setInterval(function() {
          if (count % 2 === 0) {
            that.blocks[0].imgs = blocksImgsSelected
          } else {
            that.blocks[0].imgs = blocksImgs
          }
          count++;
          if (count === 6) {
            clearInterval(intervalId);
          }
        }, 200);
      }
      startInterval();
    },
    luckywheelstart() {
      let index = 0;
      this.$protoApi(ROUTE_PLATFORM_EARNCASHBYPROMOTESTARTWHEEL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.$refs.myLucky.play();
          for (let i = 0; i < this.Details.wheelConf.length; i++) {
            if (this.Details.wheelConf[i].wheelId === res.rewardWheelId) {
              index = i;
            }
          }
          this.StartWheel = res;
        })
        .catch(() => {})
        .finally(() => {
          let that = this;
          setTimeout(() => {
            that.$refs.myLucky.stop(index);
          }, 0);
        });
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_EARNCASHBYPROMOTEDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
          this.countExpires(this.Details.currentRoundEndtime)
          this.percentageTo(0, this.Details.currentCashAmount/100)
          if (res.state === 1) {
            this.stepActive = 2
          }
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYEARNCASHWITHDRAWAL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    withdrawal(round) {
      this.$protoApi(ROUTE_PLATFORM_EARNCASHBYPROMOTEWITHDRAWAL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
        round: round
      })
          .then((res) => {
            $toast.success({
              message: this.$options.filters['currency'](res.cashAmount),
              icon: "passed",
            });
            this.query();
          })
          .catch(() => {})
          .finally(() => {});
    },
  },
};
