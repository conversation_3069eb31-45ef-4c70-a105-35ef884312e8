import {
    ROUTE_PLATFORM_FIRSTCHARGEREWARD,
    ROUTE_PLATFORM_FIRSTCHARGEREWARDDETAIL,
    ROUTE_RECORDER_QUERY_QUERYFIRSTCHARGEREWARD,
} from "@/api";

export const firstchargereward = {
    data() {
        return {
            Details: {
                details: [],
                maxReward: 0,
                minChargeamount: 0,
                received: false,
                userFirstChargeamount: 0,
            },
            Query: {
                record:{}
            },
            swiperOptions: {
                speed: 1000,
                autoplay: {
                  delay: 500,
                  disableOnInteraction: false,
                },
                direction: "vertical",
                slidesPerView: "auto",
                observer: true,
                observeParents: !0,
                preloadImages: !1,
                spaceBetween: 15,
            },
        };
    },
    mounted() {
        this.detail();
        // this.submit()
        this.query();
    },
    computed: {
        currentRow() {
            return (this.Details.userFirstChargeamount >= this.Details.userFirstChargeamount && this.Details.userFirstChargeamount <  this.Details.userFirstChargeamount);
        },
        received() {
            return function (id) {
                for (const record of this.Query.records) {
                    if (record.vip === id){
                        return true
                    }
                }
                return false
            }
        },
        btnReceive() {
            return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
        }
    },
    methods: {
        valid() {
            if (this.Details.received) {
                return this.$t('button_receive_already')
            }
            if (this.Details.details.length && this.Details.userFirstChargeamount < this.Details.details.slice().reverse()[0].chargeamount) {
                return this.$t('580');
            }
            return "";
        },
        detail() {
            this.$protoApi(ROUTE_PLATFORM_FIRSTCHARGEREWARDDETAIL, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Details = res;
                })
                .catch(() => {})
                .finally(() => {});
        },
        submit() {
            let msg = this.valid()
            if (msg) {
                $toast.fail({
                    message: msg,
                });
                return;
            }
            this.$protoApi(ROUTE_PLATFORM_FIRSTCHARGEREWARD, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    $toast.success({
                        icon: "passed",
                        message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
                    });
                    this.Details.received = true
                    this.query()
                })
                .catch(() => {})
                .finally(() => {});
        },
        query() {
            this.$protoApi(ROUTE_RECORDER_QUERY_QUERYFIRSTCHARGEREWARD, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Query = res;
                })
                .catch(() => {})
                .finally(() => {});
        },
    },
};
