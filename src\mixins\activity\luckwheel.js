import {
    ROUTE_PLATFORM_LUCKYWHEELDETAIL,
    ROUTE_PLATFORM_LUCKYWHEELSTART,
    ROUTE_RECORDER_QUERY_QUERYLUCKYWHEEL,
} from "@/api";
import {activitybase} from "@/mixins/activity/activitybase";

export const luckwheel = {
    mixins: [activitybase],
    data() {
        return {
            index: 0,
            Details: {
                beginTime: 0,
                confs: [],
                endTime: 0,
                maxReward: 0,
                minChargeamount: 0,
                received: false,
                userYestodayChargeamount: 0,
            },
            Query: {
                ownnerRecords: [],
                winnerRecords: [],
            },


        };
    },
    created() {
        this.luckywheeldetail();
        this.queryluckywheel();
    },
    mounted() {
        this.$refs.myLucky.init();
        // this.$refs.myLucky.play();
    },
    computed: {
      btnCheck() {
          return !this.Details.received && this.checkBegin(this.Details.beginTime, this.Details.endTime) && this.Details.minChargeamount <= this.Details.userYestodayChargeamount
      }
    },
    methods: {
        changeLog(index) {
            this.index = index
        },
        // 抽奖结束会触发end回调
        endCallback(prize) {
            // iconsole(prize);
        },
        valid() {
            if (this.Details.received) {
                $toast.fail({
                    message: this.$t('button_receive_already')
                });
                return
            }
            if (!this.checkBegin(this.Details.beginTime, this.Details.endTime)) {
                $toast.fail({
                    message: this.$t('583')
                });
                return
            }
            if (this.Details.minChargeamount > this.Details.userYestodayChargeamount) {
                $toast.fail({
                    message: this.$t('580')
                });
                return
            }
            this.luckywheelstart()
        },
        luckywheeldetail() {
            this.$protoApi(ROUTE_PLATFORM_LUCKYWHEELDETAIL, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Details = res;
                })
                .catch(() => {
                });
        },
        luckywheelstart() {
            let index = 0;
            this.$protoApi(ROUTE_PLATFORM_LUCKYWHEELSTART, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.$refs.myLucky.play();
                    for (let i = 0; i < this.Details.confs.length; i++) {
                        if (this.Details.confs[i].luckyWheelId === res.luckyWheelId) {
                            index = i;
                        }
                    }
                    $toast.success({
                        icon: "passed",
                        message: res.reward ? this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward) : 'Thanks',
                    });
                    this.Details.received = true
                    this.queryluckywheel()
                })
                .catch(() => {
                })
                .finally(() => {
                    let that = this
                    setTimeout(() => {
                        that.$refs.myLucky.stop(index);
                    }, 0);
                });
        },
        queryluckywheel() {
            this.$protoApi(ROUTE_RECORDER_QUERY_QUERYLUCKYWHEEL, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Query = res
                })
                .catch(() => {
                });
        },
    },
};
