import {
  ROUTE_PLATFORM_MONTHLYREWARD,
  ROUTE_PLATFORM_MONTHLYREWARDDETAIL,
  ROUTE_RECORDER_QUERY_QUERYMONTHLYREWARD,
} from "@/api";

export const monthlyreward = {
  data() {
    return {
      Details: {
        maxReward: 0,
        minChargeamount: 0,
        received: false,
        userTotalBetAmount: 0,
        userTotalChargeAmount: 0,
        rewardReceiptDay: 0,
      },
      Query: {
        records: [],
      },
    };
  },
  mounted() {
    this.detail();
    this.query();
  },
  computed: {
    btnReceive() {
      return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
    }
  },
  methods: {
    valid() {
      if (this.Details.received) {
        return this.$t("button_receive_already");
      }
      if (this.Details.minChargeamount > this.Details.userTotalChargeAmount) {
        return this.$t("580");
      }
      if (new Date().getDate() !== this.Details.rewardReceiptDay) {
        return this.$t("583");
      }
      return "";
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_MONTHLYREWARDDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_MONTHLYREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.Details.received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYMONTHLYREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
