import {
  ROUTE_PLATFORM_SATURDAYCARNIVALDETAIL,
  ROUTE_PLATFORM_SATURDAYCARNIVALREWARD,
  ROUTE_RECORDER_QUERY_QUERYSATURDAYCARNIVAL,
} from "@/api";

export const saturdaycarnival = {
  data() {
    return {

      Details: {
        maxReward: 0,
        minChargeamount: 0,
        received: false,
        userTotalBetAmount: 0,
        userTotalChargeAmount: 0,
      },
      Query: {
        records: [],
      },
    };
  },
  mounted() {
    this.detail();
    this.query();
  },
  computed: {
    btnReceive() {
      return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
    },
  },
  methods: {
    valid() {
      if (this.Details.received) {
        return this.$t("button_receive_already");
      }
      if (new Date().getDay() !== 6) {
        return this.$t("583");
      }
      return "";
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_SATURDAYCARNIVALDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit() {
      let msg = this.valid()
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_SATURDAYCARNIVALREWARD, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.Details.received = true
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYSATURDAYCARNIVAL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
