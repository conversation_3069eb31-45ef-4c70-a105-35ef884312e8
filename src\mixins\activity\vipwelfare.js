import {
  ROUTE_PLATFORM_VIPWELFARE,
  ROUTE_PLATFORM_VIPWELFAREDETAIL,
  ROUTE_RECORDER_QUERY_QUERYVIPWELFARE,
} from "@/api";
import {dot} from "@/mixins/dot";

export const vipwelfare = {
  mixins: [dot],
  data() {
    return {
      Details: {
        userTotalBetamount: 0,
        details: []
      },
      Query: {
        maxVip: 0,
        records: [],
      },
    };
  },
  mounted() {
    this.detail();
    // this.submit()
    this.query();
  },
  computed: {
    received() {
      return function (id) {
        for (const record of this.Query.records) {
          if (record.vip === id){
            return true
          }
        }
        return false
      }
    },
    btnReceive() {
      return function (id) {
        return this.received(id) ? this.$t('button_receive_already') : this.$t('button_receive')
      }
    }
  },
  methods: {
    valid(item) {
      if (this.received(item.vip)) {
        return this.$t('button_receive_already');
      }
      if (this.Details.userTotalBetamount < item.totalBetAmount) {
        return this.$t('581');
      }
     return ""
    },
    detail() {
      this.$protoApi(ROUTE_PLATFORM_VIPWELFAREDETAIL, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
    submit(item) {
      let msg = this.valid(item)
      if (msg) {
        $toast.fail({
          message: msg,
        });
        return;
      }
      this.$protoApi(ROUTE_PLATFORM_VIPWELFARE, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
        vip: item.vip
      })
        .then((res) => {
          this.event_vipReward(item.vip)
          $toast.success({
            icon: "passed",
            message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
          });
          this.query()
        })
        .catch(() => {})
        .finally(() => {});
    },
    query() {
      this.$protoApi(ROUTE_RECORDER_QUERY_QUERYVIPWELFARE, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Query = res;
        })
        .catch(() => {})
        .finally(() => {});
    },
  },
};
