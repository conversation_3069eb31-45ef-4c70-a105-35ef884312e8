import {
    ROUTE_PLATFORM_weeklybetreward,
    ROUTE_PLATFORM_weeklybetrewarddetail,
    ROUTE_RECORDER_QUERY_queryweeklybetreward
} from "@/api";


export const weeklybetreward = {
    data() {
        return {
            Details: {
                details: [],
                received: false,
                lastweekBetamount: 0,
                thisweekBetamount: 0,
            },
            Query: {
                records: []
            },
            swiperOptions: {
                loop: false,
                speed: 1000,
                autoplay: {
                    delay: 500,
                    disableOnInteraction: false,
                },
                direction: "vertical",
                slidesPerView: "auto",
                observer: true,
                observeParents: !0,
                preloadImages: !1,
                spaceBetween: 15,
            },
        };
    },
    mounted() {
        this.detail();
        // this.submit()
        this.query();
    },
    computed: {
        currentRow() {
            return function (item, index) {
                return (this.Details.lastweekBetamount >= item.betAmount && this.Details.lastweekBetamount <  this.Details.details.slice().reverse()[index+1].betAmount);
            }
        },
        btnReceive() {
            return this.Details.received ? this.$t('button_receive_already') : this.$t('button_receive')
        }
    },
    methods: {
        valid() {
            if (this.Details.received) {
                return this.$t('button_receive_already');
            }
            if (this.Details.details.length && this.Details.lastweekBetamount < this.Details.details.slice().reverse()[0].betAmount) {
                return this.$t('581');
            }
            return "";
        },
        detail() {
            this.$protoApi(ROUTE_PLATFORM_weeklybetrewarddetail, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Details = res;
                })
                .catch(() => {})
                .finally(() => {});
        },
        submit() {
            let msg = this.valid()
            if (msg) {
                $toast.fail({
                    message: msg,
                });
                return;
            }
            this.$protoApi(ROUTE_PLATFORM_weeklybetreward, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    $toast.success({
                        icon: "passed",
                        message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.reward),
                    });
                    this.Details.received = true
                    this.query()
                })
                .catch(() => {})
                .finally(() => {});
        },
        query() {
            this.$protoApi(ROUTE_RECORDER_QUERY_queryweeklybetreward, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
            })
                .then((res) => {
                    this.Query = res;
                })
                .catch(() => {})
                .finally(() => {});
        },
    },
};
