import { TDTC_ROURE } from "@/api/tdtc";
import { FieldRenderType } from "@/utils/common";

export const agentMission = {
  data() {
    return {
      hasMore: true,
      page: 2,
      column0: [
        {
          label: this.$t("AGENT_PAGELAYER7.TETXT_TIME"),
          prop: "registerdate",
        },
        {
          label: this.$t("AGENT_PAGELAYER7.TEXT_ID"),
          prop: "user_id",
        },
        {
          label: this.$t("AGENT_PAGELAYER7.TEXT_MONEY"),
          prop: "score",
          render: FieldRenderType.formatGold,
        },
      ],
      column1: [
        {
          label: this.$t("AGENT_POPULARIZEWEEK.TEXT_LABEL1"),
          prop: "registerdate",
          index: true,
        },
        {
          label: this.$t("AGENT_POPULARIZEWEEK.TEXT_LABEL2"),
          prop: "nickname",
          render: FieldRenderType.hideStr,
        },
        {
          label: this.$t("AGENT_POPULARIZEWEEK.TEXT_LABEL3"),
          prop: "spreadnum",
        },
      ],
      res: {
        spread: [],
        num: 0,
        totalnum: 0,
        need_recharge: 0,
        spread2: [],
      },
      rank: {
        0: [],
        1: [],
      },
      // resB: {},
      index: 0,
      swiperOptions: {
        slidesPerView: "auto",
        spaceBetween: 12,
      },
    };
  },
  mounted() {
    this.infoA();
  },
  watch: {
    index: function (n, o) {
      if (n && !this.rank[n - 1].length) {
        this.queryRank(n - 1);
      }
    },
  },
  methods: {
    infoA() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_SPREAD_INFO_A)
        .then((res) => {
          // QueryAgentSpread1Response
          Object.assign(this.res, res);
          this.res = res;
          this.page = 2;
        })
        .catch(() => {});
    },
    infoB() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_SPREAD_INFO_B, {
        page: this.page,
      })
        .then((res) => {
          // QueryAgentSpread2Response
          iconsole(this.res.spread2);
          if (res["spread"]) {
            this.res.spread2 = this.res.spread2.concat(res["spread"]);
            this.page++;
          } else {
            this.hasMore = false;
          }
        })
        .catch(() => {
          this.hasMore = false;
        });
    },
    queryRank(timetype) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_SPREAD_RANK, {
        timetype: timetype,
      })
        .then((res) => {
          // QueryAgentSpreadRank
          this.rank[timetype] = res["rank"];
        })
        .catch(() => {
          this.hasMore = false;
        });
    },
    getAward(getindex) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_AGENT_SPREAD_AWARD, {
        getindex: getindex,
      })
          .then((res) => {
            // GetAgentSpreadAwardResponse
            if (res["code"]) {
              window.$toast.fail(this.$t('501'));
            } else {
              if (!res["outcode"]) {
                $toast.success({
                  icon: "passed",
                  message: this.$t('VIP.SUCCESS', [this.$options.filters['formatGold'](res["score"])]),
                });
                this.infoA()
              } else if (res["outcode"] === 1) {
                window.$toast.fail(this.$t('202'));
              } else if (res["outcode"] === 2) {
                window.$toast.fail(this.$t('growthFundErr_3'));
              } else if (res["outcode"] === 3) {
                window.$toast.fail(this.$t('1008'));
              }
            }
          })
          .catch(() => {
          });
    },
  },
};
