import {
    ROUTE_RECORDER_QUERY_QUERYAGENTINCOMES,
} from "@/api";
import {TDTC_ROURE} from "@/api/tdtc";

export const agentincomes = {
    data() {
        return {
            res: {
                "selfactivenum" : 0,
                "nextactivenum" : 0,
                "otheractivenum" : 0,
                "selfaddspreadnum" : 0,
                "nextaddspreadnum" : 0,
                "otheraddspreadnum" : 0,
                "selftotalspreadnum" : 0,
                "nexttotalspreadnum" : 0,
                "othertotalspreadnum" : 0,
                "totalrebate" : 0,
                "nextrebate" : 0,
                "totalmoney" : 0,
                "nexttotalmoney" : 0,
                "platformmoney" : 0,
                "nextplatformmoney" : 0,
                "rechargemoney" : 0,
                "nextrechargemoney" : 0,
            }
        };
    },
    mounted() {
        this.detail(1)
    },
    methods: {
        detail(timetype) {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_REPORT, {
                'timetype': timetype,
            })
                .then((res) => {
                    Object.assign(this.res, res)
                })
                .catch(() => {})
        }
    },
}