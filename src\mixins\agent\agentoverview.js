import VueQr from "vue-qr";
import {
    ROUTE_PLATFORM_GETAGENTDRAW,
    ROUTE_RECORDER_QUERY_QUERYAGENTOVERVIEW,
} from "@/api";
import {TDTC_ROURE} from "@/api/tdtc";

export const agentoverview = {
    components: {
        VueQr,
    },
    data() {
        return {
            swiperOptions: {
                loop: true,
                speed: 1000,
                // autoplay: {
                //   delay: 0,
                //   disableOnInteraction: false,
                // },
                direction: "vertical",
                slidesPerView: "auto",
            },
            res: {
                url: "",
                promocode: "",
                drawmoney: 0,
                allrebate: 0,
                todayrebat: 0,
                newadd: 0,
                rate: 30,
            },
            overview: {
                todayIncome: 0,
                todayRegisters: 0,
                todayValidInvitees: 0,
                yesterdayIncome: 0,
                totalDraw: 0,
                totalRebate: 0,
                minDraw: 0,
                url: ""
            },
        };
    },
    computed: {
        showUrl() {
            let url = this.$store.getters.inJsBridge ? this.$store.state.configs.web_game_website :  location.origin
            return `${url}?code=${this.$store.state.account.userId}`;
        },
    },
    mounted() {
        this.agentoverview()
    },
    methods: {
        agentoverview() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_INFO)
                .then((res) => {
                    Object.assign(this.res, res)
                })
                .catch(() => {})
        },
        getagentdraw() {
            window.$Dialog.confirm({
                title: this.$t("in_popup_prompt"),
                message: this.$t("in_sure_withdraw"),
                confirmButtonText: this.$t("ok"),
                cancelButtonText: this.$t("button_cancel"),
            })
                .then(() => {
                    this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_AGENT_DRAW)
                        .then((res) => {
                            if (res["code"]) {
                                window.$toast.fail(this.$t('501'));
                            } else {
                                if (!res["outcode"]) {
                                    $toast.success({
                                        icon: "passed",
                                        message: this.$t('1045', [this.$options.filters['formatGold'](res["score"])]),
                                    });
                                    this.agentoverview()
                                } else if (res["outcode"] === 1) {
                                    window.$toast.fail(this.$t('1044', [this.$options.filters['formatGold'](res["need_score"])]));
                                } else if (res["outcode"] === 2) {
                                    window.$toast.fail(this.$t('1043'));
                                } else if (res["outcode"] === 3) {
                                    window.$toast.fail(this.$t('1007'));
                                } else if (res["outcode"] === 4) {
                                    window.$toast.fail(this.$t('agent_no_do_tip'));
                                }
                            }
                        })
                        .catch(() => {})
                })
                .catch(() => {
                    // on cancel
                });
        },

    },
}