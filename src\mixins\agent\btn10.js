import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType, getWebGameTitle} from "@/utils/common";
import VueQr from 'vue-qr'

export const btn10 = {
    components: {
        VueQr,
    },
    data() {
        return {
            help:            false,
            index:           0,
            userNowAward:    0,
            lab_tips:        "",
            currentActivity: {
                activityType: 0,
                awardType:    0,
                status:       0,
                withdrawRate: 0
            },
            column0:         [
                {
                    label: "Xếp hạng",
                    prop:  "rank",
                },
                {
                    label:  "Biệt danh người dùng",
                    render: FieldRenderType.hideStr,
                    prop:   "nick_name",
                },
                {
                    label: "Số lượng giới thiệu",
                    prop:  "spread_num",
                },
                {
                    label:          "Xếp hạng nhiệm vụ giới thiệu",
                    prop:           "rank",
                    render:         FieldRenderType.customTemplate,
                    customTemplate: item => this.res.rank_info[item - 1]["spread_num"] + " / " + this.res.confs[item - 1]["extra_spread"],
                },
                {
                    label:          "Phần thưởng",
                    prop:           "rank",
                    render:         FieldRenderType.customTemplate,
                    customTemplate: item => this.res.rank_info[item - 1]["spread_num"] >= this.res.confs[item - 1]["extra_spread"] ? this.$options.filters['formatGold']((this.res.confs[item - 1]["award_score"] + this.res.confs[item - 1]["extra_award_score"])) : this.$options.filters['formatGold'](this.res.confs[item - 1]["award_score"])
                },
            ],
            column1:         [
                {
                    label: "Xếp hạng",
                    prop:  "rank",
                },
                {
                    label: "Xếp hạng nhiệm vụ giới thiệu",
                    prop:  "extra_spread",
                },
                {
                    label:  "Phần thưởng",
                    prop:   "award_score",
                    render: FieldRenderType.formatGold,
                },
                {
                    label:          "phần thưởng đầy đủ",
                    prop:           "rank",
                    render:         FieldRenderType.customTemplate,
                    customTemplate: item => this.$options.filters['formatGold']((this.res.confs[item - 1]["award_score"] + this.res.confs[item - 1]["extra_award_score"]))
                },
            ],
            res:             {
                code:           0,  // 610不在活动时间内   200成功
                begin_time:     "", // 开始时间
                end_time:       "", // 结束时间
                now_rank:       0,  // 自己当前排名
                yesterday_rank: 0,  // 昨日排名
                max_rank:       0,  // 最大排名
                confs:          [], // 排行榜配置
                rank_info:      [], // 排行榜信息
                spread_url:     "", // 推广链接
                flag:           0,  // 奖励领取状态     0不可领取  1可领取  2已领取
                active_status:  0,  // 活动状态   0活动未开   1活动开了  但是时间未开始    2进行中   3结束72小时内   4结束超过72小时
                spread_num:     0,  // 自己当前推广了多少人
            },
            res52:           {
                need_bet:      0,
                need_recharge: 0,
            }
        }
    },
    computed: {
        showUrl() {
            return `${this.res.spread_url}?code=${this.$store.state.account.userId}`;
        },
    },
    mounted() {
        Object.assign(this.currentActivity, this.$store.state.activitySwitchDetails.find(item => item.activityType === 49))
        // this.query51()
        this.query53()
    },
    methods: {
        get1030() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_PROMOT_GIFT_GET_AWARD)
                .then((res) => {
                    if (res['code'] === 200) {
                        let awardType = res["award_type"] ? res["award_type"] : 1

                        let t_key = "awardSuccess"
                        if (awardType === 1) {
                            t_key = "MONEY_AWARDSUCCESS";
                        } else if (awardType === 2) {
                            t_key = "BONUS_AWARDSUCCESS";
                        } else if (awardType === 3) {
                            t_key = "POINT_AWARDSUCCESS";
                        }

                        $toast.success({
                            icon:    "passed",
                            message: this.$t(t_key, {money: this.$options.filters['formatGold'](res['award_score '])}),
                        });

                    } else if (res["code"] === 1) {//1正在游戏中
                        window.$toast.fail(this.$t("agent_rank_awardErr_1"))
                    } else if (res["code"] === 3) {//3未上榜
                        window.$toast.fail(this.$t("agent_rank_awardErr_2"))
                    } else if (res["code"] === 4) {//4已经领取
                        window.$toast.fail(this.$t("agent_rank_awardErr_3"))
                    } else if (res["code"] === 5) {//5活动奖励已经发放结束
                        window.$toast.fail(this.$t("agent_rank_awardErr_5_1"))
                    }
                })
                .catch(() => {
                })
        },
        query51() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_PROMOT_GIFT_POP_INFO)
                .then((res) => {
                    // Object.assign(this.res, res)
                })
                .catch(() => {
                })
        },
        query52() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_PROMOT_GIFT_RULE_INFO)
                .then((res) => {
                    Object.assign(this.res52, res)
                })
                .catch(() => {
                })

        },
        query53() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_PROMOT_GIFT_MAIN_INFO, {'url_type': 1})
                .then((res) => {
                    Object.assign(this.res, res)


                    let rank_info = this.res.rank_info;
                    let confs = this.res.confs;
                    let spreadNum = this.res.spread_num;
                    let nowRank = this.res.now_rank;

                    this.userNowAward = nowRank > 0 ? (spreadNum >= confs[nowRank - 1]["extra_spread"] ? (confs[nowRank - 1]["award_score"] + confs[nowRank - 1]["extra_award_score"]) : confs[nowRank - 1]["award_score"]) : 0

                    if (nowRank === 1) {//第一名
                        this.lab_tips = this.$t("AGENT_PROMOTION_TIPS2");

                    } else if (nowRank !== 0) {//不是第一名 但是上榜了
                        let nextRank = this.getNextGearRanking(confs, nowRank)
                        let people = rank_info[nextRank - 1]["spread_num"] - spreadNum + 1
                        let ranking = nextRank
                        let awardAmount = (people + spreadNum) >= confs[ranking - 1]["extra_spread"] ? (confs[ranking - 1]["award_score"] + confs[ranking - 1]["extra_award_score"]) : confs[ranking - 1]["award_score"]
                        this.lab_tips = this.$t("AGENT_PROMOTION_TIPS1", {
                            people:      people,
                            ranking:     ranking,
                            awardAmount: this.$options.filters['formatGold'](awardAmount)
                        });

                    } else {//没有上榜
                        let people = 0
                        let ranking = 0
                        let awardAmount = 0

                        if (rank_info.length === confs.length) {
                            people = rank_info[rank_info.length - 1]["spread_num"] - spreadNum + 1
                            ranking = this.getUserRankingByPeople(rank_info, people + spreadNum)
                            awardAmount = (people + spreadNum) >= confs[ranking - 1]["extra_spread"] ? (confs[ranking - 1]["award_score"] + confs[ranking - 1]["extra_award_score"]) : confs[ranking - 1]["award_score"]

                        } else if (rank_info.length < confs.length) {
                            people = 1
                            ranking = rank_info.length + 1
                            awardAmount = confs[ranking - 1]["award_score"]
                        }
                        this.lab_tips = this.$t("AGENT_PROMOTION_TIPS1", {
                            people:      people,
                            ranking:     ranking,
                            awardAmount: this.$options.filters['formatGold'](awardAmount)
                        });
                    }
                })
                .catch(() => {
                })
                .finally(()=>{
                    setTimeout(()=>{
                        this.query52()
                    }, 300)
                })
        },
        //获取上一档次的排名
        getNextGearRanking(confs, nowRank) {
            for (let i = confs.length - 1; i >= 0; i--) {
                const info = confs[i];
                const info_now = confs[nowRank - 1];
                if ((info["award_score"] + info["extra_award_score"]) > (info_now["award_score"] + info_now["extra_award_score"])) {
                    return i + 1; //返回排名
                }
            }
        },
        //通过人数获取用户排名
        getUserRankingByPeople(rankInfo, people) {
            for (let i = 0; i < rankInfo.length; i++) {
                if (rankInfo[i]["spread_num"] < people) {
                    return i + 1; //返回排名
                }
            }
            return 0
        }
    },
};
