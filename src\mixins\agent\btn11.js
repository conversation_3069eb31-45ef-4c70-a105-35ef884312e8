import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType, getWebGameTitle} from "@/utils/common";

export const btn11 = {
  data() {
    return {
      column: [
        {
          label: "Tên trò chơi",
          prop: "kind_id",
          render: FieldRenderType.customTemplate,
          customTemplate: getWebGameTitle,
        },
        {
          label: "Tỷ lệ hoàn tiền",
          prop: "rate",
          render: FieldRenderType.customTemplate,
          customTemplate: value => value * 100 + "%",
        },
      ],
      res: {
        data: [],
        second_rate: 0,
      }
    }
  },
  mounted() {
    this.detail()
  },
  methods: {
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_THIRD_REBATE_RATE)
          .then((res) => {
            Object.assign(this.res, res)
          })
          .catch(() => {
          })
    },
  },
};
