import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";

export const btn4 = {

    data() {
        return {
            processing: false,
            hasMore: true,
            column: [
                {
                    label: this.$t("AGENT_PAGELAYER4.TEXT_TEAMID"),
                    prop: "user_id",
                },
                {
                    label: this.$t("AGENT_PAGELAYER4.TEXT_TEAMOFFER"),
                    prop: "totalrebate ",
                    default: 0,
                    render: FieldRenderType.formatGoldWithK,
                    style: {
                        width: "3rem"
                    }
                },
                {
                    label: this.$t("AGENT_PAGELAYER4.TEXT_TEAMPNUM"),
                    prop: "spreadnum",
                    default: 0,
                },
                {
                    label: this.$t("AGENT_PAGELAYER4.TEXT_TEAMCOMSION"),
                    prop: "platformmoney",
                    default: 0,
                    render: FieldRenderType.formatGoldWithK
                },
            ],
            types: [
                this.$t("time_1"),
                this.$t("time_2"),
                this.$t("time_3"),
                this.$t("time_4"),
                this.$t("time_5"),
                this.$t("time_6"),
            ],
            form: {
                "type": 0,
                "page": 1,
            },
            datas: [],
        };
    },
    methods: {
        typeChange(value, index) {
            this.form.type = index
            this.showPicker = false
        },
        search(paginate = false) {
            if (!paginate) {
                this.form.page = 1;
                this.finished = false;
                this.datas = [];
            }
            this.processing = true;


            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_NEXT_REPORT, {
                'timetype': this.form.type,
                'page': this.form.page,
            })
                .then((res) => {
                    // QueryAgentNextReportResponse
                    iconsole(res)
                    if (this.$store.state.webType === 1) {
                        if (res['next_report']) this.datas = res['next_report'];
                    } else {
                        if (res['next_report']){
                            this.datas = this.datas.concat(res['next_report']);
                            this.form.page++;
                        } else {
                            this.hasMore = false
                        }

                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.hasMore = false
                })
                .finally(() => {
                    this.processing = false;
                });
        },
    },
};
