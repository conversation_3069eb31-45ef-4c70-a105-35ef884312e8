import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";

export const btn6 = {
  data() {
    return {
      column: [
        {
          label: this.$t("AGENT_PAGELAYER6.TEXT_RANK"),
          prop: "",
          index: true,
        },
        {
          label: this.$t("AGENT_PAGELAYER6.TEXT_INFO"),
          prop: "nickname",
          render: FieldRenderType.hideStr,
        },
        {
          label: this.$t("AGENT_PAGELAYER6.TEXT_MFI"),
          prop: "totalrebate",
          render: FieldRenderType.currency,
        },
      ],
      records: []
    }
  },
  mounted() {
    this.detail()
  },
  methods: {
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_REBATE_RANK)
          .then((res) => {
             if (res.rank) this.records = res.rank
          })
          .catch(() => {})
    },
  },
};
