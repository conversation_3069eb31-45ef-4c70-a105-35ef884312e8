import { ROUTE_RECORDER_QUERY_QUERYAGENTDRAWLOG } from "@/api";
import moment from "moment";
import { menu } from "@/mixins/menu";
import { debounce, FieldRenderType } from "@/utils/common";
import { TDTC_ROURE } from "@/api/tdtc";

export const btn9 = {
  mixins: [menu],
  data() {
    return {
      hasMore: true,
      column: [
        {
          label: this.$t("AGENT_PAGELAYER9.TEXT_TITLE_1"),
          prop: "rank",
        },
        {
          label: this.$t("AGENT_PAGELAYER9.TEXT_TITLE_2"),
          prop: "nick_name",
          render: FieldRenderType.hideStr,
        },
        {
          label: this.$t("AGENT_PAGELAYER9.TEXT_TITLE_3"),
          prop: "spread_num",
        },
        {
          label: this.$t("AGENT_PAGELAYER9.TEXT_TITLE_4"),
          prop: "rebate",
          render: FieldRenderType.currency,
        },
        {
          label: this.$t("AGENT_PAGELAYER9.TEXT_TITLE_5"),
          prop: "award",
          render: FieldRenderType.currency,
        },
      ],
      types: [this.$t("time_1"), this.$t("time_2")],
      form: {
        type: 1,
        page: 1,
      },
      datas: [],
      res: {
        rank_type: 0,
        data: [],
        self_rank: 0,
        flag: 0,
      },
    };
  },
  mounted() {
    this.search();
  },
  methods: {
    typeChange(value, index) {
      this.form.type = index + 1;
      this.showPicker = false;
      this.search();
    },
    search() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_DAILY_RANK, {
        timetype: this.form.type,
      })
        .then((res) => {
          // QueryAgentRankResp
          Object.assign(this.res, res);
        })
        .catch(() => {});
    },
    award() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_AGENT_RANK_AWARD)
        .then((res) => {
          // GetAgentRankResp
          if (!res["code"]) {
            $toast.success({
              icon: "passed",
              message: this.$t('AGENT_PAGELAYER9.agent_rank_success'),
            });
          } else {
            window.$toast.fail(this.$t('AGENT_PAGELAYER9.agent_rank_awardErr_'+res['code']));
          }
        })
        .catch(() => {});
    },
  },
};
