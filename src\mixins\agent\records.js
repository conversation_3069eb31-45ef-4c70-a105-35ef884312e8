import {
    ROUTE_RECORDER_QUERY_QUERYAGENTRECORDS
} from "@/api";
import {menu} from "@/mixins/menu";
import {debounce} from "@/utils/common";

export const records = {
    mixins: [menu],
    data() {
        return {
            datas: [],
            types: ['All', 'betting_rewards', 'recharge_rewards'],
            form: {
                status: 0,
                // beginTime: moment(new Date()).startOf("day").format("x"),
                // endTime: moment(new Date()).endOf("day").format("x"),
            },
        };
    },
    mounted() {
        this.paginate.pageSize = 13
    },
    methods: {
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1;
                this.finished = false
                this.datas = []
            }
            this.processing = true;
            debounce(() => {
            this.$protoApi(ROUTE_RECORDER_QUERY_QUERYAGENTRECORDS, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
                beginTime: this.form.beginTime,
                endTime: this.form.endTime,
                page: this.paginate.page,
                pageSize: this.paginate.pageSize,
            })
                .then((res) => {
                    if (this.$store.state.webType === 1) {
                        this.paginate.total = res.counts;
                        this.datas = res.datas;
                    } else {
                        this.datas = this.datas.concat(res.datas);
                        this.paginate.page++

                        if (res.counts === 0 || this.datas.length >= res.counts) {
                            this.finished = true;
                        }
                        this.loading = false;
                    }
                })
                .catch(() => {})
                .finally(() => {
                    this.processing = false;
                });
            })();
        },
    },
}