import {
    ROUTE_RECORDER_QUERY_QUERYAGENTDRAWLOG,
} from "@/api";
import moment from "moment";
import { menu } from "@/mixins/menu";
import { FieldRenderType } from "@/utils/common";
import {TDTC_ROURE} from "@/api/tdtc";

export const reportOfTheLower = {
    mixins: [menu],
    data() {
        return {
            hasMore: true,
            column: [
                {
                    label: this.$t("AGENT_PAGELAYER8.TEXT_TITLE1"),
                    prop: "user_id",
                },
                {
                    label: this.$t("AGENT_PAGELAYER8.TEXT_TITLE4"),
                    prop: "data",
                    default: 0,
                    render: FieldRenderType.currency,
                },
            ],
            types: [
                this.$t("AGENTCHECK.DATA_NAME_0"),
                this.$t("AGENTCHECK.DATA_NAME_1"),
                this.$t("AGENTCHECK.DATA_NAME_2"),
                this.$t("AGENTCHECK.DATA_NAME_3"),
            ],
            form: {
                "type": 1,
                "page": 1,
            },
            datas: [],
        };
    },
    mounted() {
        this.paginate.pageSize = 13
    },
    methods: {
        typeChange(value, index) {
            this.form.type = index + 1
            this.showPicker = false
        },
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1;
                this.finished = false;
                this.datas = [];
            }
            this.processing = true;

            let startDate = moment(new Date(Number(this.form.beginTime)));
            let endDate = moment(new Date(Number(this.form.endTime)));
            let today = moment(new Date());

            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_NEXT_INFO, {
                "type": this.form.type,
                "page": this.paginate.page,
                "beginday": today.diff(startDate, 'days'),
                "endday": today.diff(endDate, 'days'),
            })
                .then((res) => {
                    // QueryAgentNextMoneyResp
                    iconsole(res)
                    if (this.$store.state.webType === 1) {
                        this.paginate.total = res['total_page'];
                        if (res['next_info']) this.datas = res['next_info'];
                    } else {
                        if (res['next_info']){
                            this.datas = this.datas.concat(res['next_info']);
                            this.paginate.page++;
                        } else {
                            this.hasMore = false
                        }

                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.hasMore = false
                })
                .finally(() => {
                    this.processing = false;
                });
        },
    },
};
