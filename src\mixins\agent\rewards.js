import {
    ROUTE_PLATFORM_AGENTSPREADINFO,
    ROUTE_PLATFORM_GETAGENTSPREAD,
} from "@/api";

export const rewards = {
    data() {
        return {
            spreadawardinfo: {
                data: [],
                spreadNum: 0,
            },
        };
    },
    mounted() {
        this.agentspreadawardinfo()
    },
    methods: {
        agentspreadawardinfo() {
            this.$protoApi(ROUTE_PLATFORM_AGENTSPREADINFO, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
                awardIndex: 1,
            })
                .then((res) => {
                    this.spreadawardinfo = res;
                })
                .catch(() => {})
                .finally(() => {});
        },
        getagentspreadaward(item) {
            if (item.spreadNum > this.spreadawardinfo.spreadNum || item.flag) {
                return;
            }
            this.$protoApi(ROUTE_PLATFORM_GETAGENTSPREAD, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                token: this.$store.state.token.token,
                awardIndex: item.awardIndex,
            })
                .then((res) => {
                    $toast.success({
                        message: this.$store.state.configs.currency_symbol + ' ' + this.$options.filters['currency'](res.awardScore),
                        icon: "passed",
                    });
                    this.agentspreadawardinfo()
                })
                .catch(() => {})
                .finally(() => {});
        },
    },
}