import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";

export const withdrawrecords = {
  data() {
    return {
      column: [
        {
          label: this.$t("EXCHANGELOG.TITLE_2"),
          prop: "createtime",
        },
        {
          label: this.$t("EXCHANGELOG.TITLE_4"),
          prop: "score",
          render: FieldRenderType.formatGold,
        },
        {
          label: this.$t("EXCHANGELOG.TITLE_6"),
          prop: "flag",
          render: FieldRenderType.customTemplate,
          customTemplate: this.customTemplate,
        },
      ],
      records: []
    }
  },
  mounted() {
    this.detail()
  },
  methods: {
    customTemplate(value) {
      switch (value) {
        case 2:
          return "Từ chối";//拒绝
        case 0:
          return "Đang xét duyệt";  //审核中
        default:
          return "Thông qua";//通过
      }
    },
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AGENT_DRAW)
          .then((res) => {
             if (res.draw) this.records = res.draw
          })
          .catch(() => {})
    },
  },
};
