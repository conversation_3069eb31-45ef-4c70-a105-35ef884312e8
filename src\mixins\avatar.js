import {debounce} from "@/utils/common";
import {ROUTE_LOGON_ICON} from "@/api";
import {TDTC_ROURE} from "@/api/tdtc";

export const avatar = {
    data() {
        return {
            iconIndex: this.$store.state.account.icon,
        }
    },
    methods: {
        changeIcon() {
            if (this.$store.state.account.icon === this.iconIndex) return
            debounce(() => {
                this.$tdtcApi.getQueryInfo(TDTC_ROURE.HALL_MODIFY_FACE, {
                    face_id: this.iconIndex,
                    face_frame: 1
                })
                    .then((res) => {
                        // ModifySystemFaceResponse
                        this.$store.commit("setIcon", this.iconIndex);
                        $toast.success({
                            message: this.$t('label_success'),
                            icon: 'passed',
                        })
                        this.$emit('closeShow')
                    })
                    .catch(() => {});
            })();
        },
    }
}