import { debounce } from "@/utils/common";
import { ROUTE_RECORDER_QUERY_ORDERS } from "@/api";
import { menu } from "@/mixins/menu";

export const bet = {
  mixins: [menu],
  data() {
    return {
      types: [
        this.$t('label_sett'),
        this.$t('label_unsett')
      ],
      categoryId: 1,
      // currentPage: 1,
      processing: false,
      vendors: [
        {
          platformId: "",
          platformName: "",
        },
      ],
      records: [],
      platformIndex: 0,
      form: {
          status: 0,
      //     beginTime: moment(new Date()).startOf("day").format("x"),
      //     endTime: moment(new Date()).endOf("day").format("x"),
      },
      total: {
          bet: 0,
          award: 0,
          profit: 0,
      },
    };
  },
  mounted() {
    this.paginate.pageSize = 13;
    this.refreshVendor(1);
  },
  methods: {
    refreshVendor(categoryId) {
      this.categoryId = categoryId;
      for (const game of this.$store.state.platform.games) {
        if (game.categoryId === this.categoryId) {
          this.vendors = game.platforms;
          break;
        }
      }
      this.platformIndex = 0;
    },
    resetTotal() {
      this.total.bet = 0;
      this.total.award = 0;
      this.total.profit = 0;
    },
    search(paginate = false) {
      if (!paginate) {
        this.paginate.page = 1;
        this.finished = false
        this.records = []
      }
      this.processing = true;
      this.resetTotal();
      debounce(() => {
        this.$protoApi(ROUTE_RECORDER_QUERY_ORDERS, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          beginTime: this.form.beginTime,
          endTime: this.form.endTime,
          page: this.paginate.page,
          pageSize: this.paginate.pageSize,
          status: this.form.status,
          platformId: this.$store.state.platform.platforms[this.platformIndex].platformId,
          // platformId: this.vendors.length
          //   ? this.vendors[this.platformIndex].platformId
          //   : 0,
        })
          .then((res) => {
            if (this.$store.state.webType === 1) {
              this.paginate.total = res.counts;
              this.records = res.records;
              for (const row of res.records) {
                this.total.bet += row.betAmount;
                this.total.award += row.settleAmount;
                this.total.profit += row.settleAmount - row.betAmount;
              }
            } else {
              this.records = this.records.concat(res.records);
              this.paginate.page++

              if (res.counts === 0 || this.records.length >= res.counts) {
                this.finished = true;
              }
              this.loading = false;

              for (const row of this.records) {
                this.total.bet += row.betAmount;
                this.total.award += row.settleAmount;
                this.total.profit += row.settleAmount - row.betAmount;
              }
            }
          })
          .catch(() => {})
          .finally(() => {
            this.processing = false;
          });
      })();
    },
  },
};
