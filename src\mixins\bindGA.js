import { debounce } from "@/utils/common";
import {
  ROUTE_LOGON_OAUTHDETAILS,
  ROUTE_LOGON_OAUTHBIND,
  ROUTE_LOGON_OAUTHBINDCAPTCHA,
} from "@/api";
import VueQr from "vue-qr";
import {actionCaptcha} from "@/mixins/actionCaptcha";

export const bindGA = {
  mixins: [actionCaptcha],
  components: {
    VueQr,
  },
  data() {
    return {
      code: "",
      Details: {
        platformName: "",
        downloadUrlOauthAndroid: "",
        downloadUrlOauthIos: "",
        oauthSecret: "",
        oauthFlag: 0,
      },
    };
  },
  computed: {
    authUrl() {
      return `otpauth://totp/${this.$store.state.account.userId}?secret=${this.Details.oauthSecret}&issuer=${this.Details.platformName}`;
    },
  },
  mounted() {
    this.initActionCaptcha(this.bind<PERSON><PERSON><PERSON><PERSON><PERSON>, "bindG<PERSON><PERSON>and<PERSON>");
    this.details();
  },
  methods: {
    openDownUrl() {
      let ag = navigator.userAgent;
      let ios =
        ag.indexOf("iPhone") > -1 ||
        ag.indexOf("iPad") > -1 ||
        ag.indexOf("iPod") > -1;
      let android = ag.indexOf("Android") > -1;
      let url = "";
      if (android) {
        url = this.Details.downloadUrlOauthAndroid;
        if ("jsBridge" in window) {
          url = "browser:" + url;
          window.open(url, "_self");
          return;
        }
      } else if (ios) {
        url = this.Details.downloadUrlOauthIos;
      }
      if (url) {
        setTimeout(() => {
          window.open(url);
        });
      }
    },
    details() {
      this.$protoApi(ROUTE_LOGON_OAUTHDETAILS, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
      })
        .then((res) => {
          this.Details = res;
        })
        .catch(() => {});
    },
    submit() {
      if (!this.code) {
        $toast.fail({
          message: this.$t("enter_google_verifyNum"),
        });
        return;
      }
      debounce(() => {
        this.$protoApi(ROUTE_LOGON_OAUTHBIND, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          code: this.code,
        })
          .then((res) => {
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });

            if (this.$store.state.webType > 1) {
              let that = this;
              setTimeout(() => {
                that.$router.back();
              }, 1000);
            } else {
              this.$emit("closeShow");
              this.$emit("refresh");
            }
          })
          .catch(() => {});
      })();
    },
    bindGaHandler() {
      this
          .$protoApi(ROUTE_LOGON_OAUTHBINDCAPTCHA, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            token: this.$store.state.token.token,
            code: this.code,
            captcha_0: window.validate.captcha_0,
            captcha_1: window.validate.captcha_1,
            captcha_2: window.validate.captcha_2,
            captcha_3: window.validate.captcha_3,
            platformId: this.captchaPlatform,
          })
          .then((res) => {
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });

            if (this.$store.state.webType > 1) {
              setTimeout(() => {
                this.$router.back();
              }, 1000);
            } else {
              this.$emit("closeShow");
              this.$emit("refresh");
            }
          })
          .catch(() => {
            this.code = ""
          });
    },
  },
};
