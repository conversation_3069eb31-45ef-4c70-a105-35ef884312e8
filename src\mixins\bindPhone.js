import { debounce } from "@/utils/common";
import {
  ROUTE_CAPTCHA,
  ROUTE_LOGON_SMS,
  ROUTE_LOGON_BINDPHONE,
  ROUTE_LOGON_SMSCAPTCHA,
  ROUTE_LOGON_BINDPHONECAPTCHA,
} from "@/api";
import { actionCaptcha } from "@/mixins/actionCaptcha";

export const bindPhone = {
  mixins: [actionCaptcha],
  data() {
    return {
      smsBtnNum: 1,
      hasSend: 0,
      showCurrentPassword: false,
      showNewPassword: false,
      showConfirmPassword: false,
      captcha: {},
      form: {
        phone_code: "",
        captcha: "",
        CurrentPassword: "",
        phone: "",
      },
      countdown: 0,
    };
  },
  mounted() {
    // this.initActionCaptcha(this.submitSmsCaptcha);
    this.initActionCaptcha(this.submitCaptcha, "submitCaptcha");
    // this.getCaptcha();
    this.setInterval();
  },
  methods: {
    onSmsFocus(btnNum) {
      this.hasSend = btnNum
      this.$nextTick(()=>{
        this.$refs.smsCode.focus();
      })
    },
    onSmsBtn(btnNum) {
      this.smsBtnNum=btnNum;
      if (btnNum === 1) {
        this.form.phone_code = this.$store.getters.tempAccount
      } else {
        this.form.phone_code = ""
      }
    },
    getCaptcha() {
      debounce(() => {
        this.$protoApi(ROUTE_CAPTCHA)
          .then((res) => {
            this.captcha = res;
          })
          .catch(() => {});
      }, 500)();
    },
    submitCaptcha() {
      debounce(() => {
        this.$protoApi(ROUTE_LOGON_BINDPHONECAPTCHA, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          phone: this.$store.state.phonePreApi + this.form.phone,
          code: this.form.phone_code,
          captcha_0: window.validate.captcha_0,
          captcha_1: window.validate.captcha_1,
          captcha_2: window.validate.captcha_2,
          captcha_3: window.validate.captcha_3,
          platformId: this.captchaPlatform,
        })
          .then((res) => {
            this.$store.commit("setLogon", res);
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });

            if (this.$store.state.webType > 1) {
              setTimeout(() => {
                this.$router.back();
              }, 1000);
            } else {
              this.$emit("closeShow");
              this.$emit("refresh");
            }
          })
          .catch(() => {
            if (this.smsBtnNum !== 1) {
              this.form.phone_code = ""
            }
          });
      })();
    },
    submit() {
      let e = this;
      this.$validator.validateAll().then(function (t) {
        t ? e.bindPhone() : e.getCaptcha();
      });
    },
    submitSmsCaptcha() {
      this.$protoApi(ROUTE_LOGON_SMSCAPTCHA, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
        phone: this.$store.state.phonePreApi + this.form.phone,
        captcha_0: window.validate.captcha_0,
        captcha_1: window.validate.captcha_1,
        captcha_2: window.validate.captcha_2,
        captcha_3: window.validate.captcha_3,
        platformId: this.captchaPlatform,
      })
        .then((res) => {
          this.$store.commit("setSmsExpiresTime", Date.now() + 120 * 1000);
          $toast.success({
            message: this.$t("label_success"),
            icon: "passed",
          });
          setTimeout(() => {
            this.setInterval();
          });
        })
        .catch(() => {});
    },
    submitSms() {
      if (this.countdown > 0) {
        return;
      }
      let e = this;
      this.$validator.validateAll().then(function (t) {
        t ? e.sms() : e.getCaptcha();
      });
    },
    setInterval() {
      this.countdown = Math.round(
        (this.$store.state.smsExpiresTime - Date.now()) / 1000
      );
      if (this.countdown) {
        this.timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown -= 1;
          } else {
            clearInterval(this.timer);
          }
        }, 1000);
      }
    },
    sms() {
      debounce(() => {
        this.$protoApi(ROUTE_LOGON_SMS, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          phone: this.$store.state.phonePreApi + this.form.phone,
          id: this.captcha.id,
          captcha: this.form.captcha,
        })
          .then((res) => {
            this.$store.commit("setSmsExpiresTime", Date.now() + 120 * 1000);
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });
            setTimeout(() => {
              this.setInterval();
            });
          })
          .catch(() => {})
          .finally(() => {
            this.form.captcha = "";
            this.getCaptcha();
          });
      })();
    },
    bindPhone() {
      debounce(() => {
        this.$protoApi(ROUTE_LOGON_BINDPHONE, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          phone: this.$store.state.phonePreApi + this.form.phone,
          code: this.form.phone_code,
          id: this.captcha.id,
          captcha: this.form.captcha,
        })
          .then((res) => {
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });

            if (this.$store.state.webType > 1) {
              let that = this;
              setTimeout(() => {
                that.$router.back();
              }, 1000);
            } else {
              this.$emit("closeShow");
              this.$emit("refresh");
            }
          })
          .catch(() => {
            this.getCaptcha();
          })
          .finally(() => {
            this.form.captcha = "";
            this.form.phone_code = "";
          });
      })();
    },
  },
};
