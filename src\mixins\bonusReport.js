import {debounce} from "@/utils/common";
import {ROUTE_RECORDER_QUERY_BONUS} from "@/api";
import {menu} from "@/mixins/menu";

export const bonusReport = {
    mixins: [menu],
    data() {
      return {
          processing: false,
          records: [],
          total: 0,
      }
    },

    mounted() {
        this.paginate.pageSize = 13;
    },
    methods: {
        resetTotal() {
            this.total = 0;
        },
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1
                this.finished = false
                this.records = []
            }
            this.processing = true;
            this.resetTotal();
            debounce(() => {
                this.$protoApi(ROUTE_RECORDER_QUERY_BONUS, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    beginTime: this.form.beginTime,
                    endTime: this.form.endTime,
                    page: this.paginate.page,
                    pageSize: this.paginate.pageSize,
                })
                    .then((res) => {
                        if (this.$store.state.webType === 1) {
                            this.paginate.total = res.counts
                            this.records = res.records;
                        } else {
                            this.records = this.records.concat(res.records);
                            this.paginate.page++

                            if (res.counts === 0 || this.records.length >= res.counts) {
                                this.finished = true;
                            }
                            this.loading = false;
                        }
                    })
                    .catch(() => {
                    })
                    .finally(() => {
                        this.processing = false;
                    });
            })();
        },
    }

}