
export const check = {
    computed: {
        downUrl() {
            if (/iPhone|iPod|iPad/i.test(navigator.userAgent)) {
                return this.$store.state.configs.download_url_ios
            } else {
                return this.$store.state.configs.download_url_android
            }
        },
        isIos() {
            return /iPhone|iPod|iPad/i.test(window.navigator.userAgent)
        },
        devicePlatform() {
            return /iPhone|iPod|iPad/i.test(window.navigator.userAgent) ? 'IOS' : 'ANDROID'
        },
        isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent);

        },
        standalone() {
            return ("standalone" in window.navigator) && window.navigator.standalone
        }
    },
    methods: {
        checkPlatAndGameStatus(item) {
            switch (this.$store.state.account.role) {
                case 1: // 管理员
                    return true
                default: // 普通玩家
                    for (const plat of this.$store.state.platform.platforms) {
                        if (item.platformId === plat.platformId) {
                            return plat.status !== 2 && item.status !== 2
                        }
                    }
                    return false
            }
        },
        getService() {
            window.open(
                this.$store.state.configs.customer_web,
                "service",
                "width=440,height=750"
            );
        },
        goUrl(url, flag = true) {
            if (flag) {
                this.$router.push({
                    path: '/m/seamless/go',
                    query: {
                        go: url
                    }}).catch(()=>{})
            } else {
                const userAgent = navigator.userAgent;
                if (/Android/i.test(userAgent) && 'jsBridge' in window) {
                    url = "browser:" + url
                    window.open(url, "_self")
                } else {
                    setTimeout(()=>{
                        window.open(url)
                    })
                }
            }
        }
    }
}