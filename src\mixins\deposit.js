import {debounce} from "@/utils/common";
import {ROUTE_PAYMENT_CHARGE, ROUTE_PAYMENT_PAYMENTS, ROUTE_RECORDER_QUERY_PAYMENTS} from "@/api";
import {menu} from "@/mixins/menu";
import moment from "moment";
import {dot} from "@/mixins/dot";

export const deposit = {
    mixins: [menu, dot],
    data() {
        return {
            processing: false,
            currentPlatformType: 1,
            currentPaymentIndex: 0,
            currentChannelIndex: 0,
            chargeAmount: '',
            errAmount: false,
            paymentsAll: [],
            records: [],
            statusActions: [
                this.$t("All"),
                this.$t("status_deposit_1"),
                this.$t("status_deposit_2"),
                this.$t("status_deposit_3"),
                this.$t("status_deposit_4"),
            ],
            form: {
                status: 0,
                paymentIndex: 0,
                beginTime: moment(new Date()).startOf("day").format("x"),
                endTime: moment(new Date()).endOf("day").format("x"),
            },
            total: {
                received: 0,
                fee: 0
            },
        };
    },
    computed: {
        paymentActions() {
            let payments = [
                {
                    paymentId: 0,
                    paymentName: this.$t("All")
                }
            ]
            for (const payment of this.paymentsAll) {
                payments.push({
                    paymentId: payment.paymentId,
                    paymentName: payment.paymentName
                })
            }
            return payments
        },
        showAmount: {
            get () {
                if (!this.chargeAmount) return ''
                return this.chargeAmount / 100
            },
            set (v) {
                this.chargeAmount =  v * 100
            }

        },
        payments() {
            return this.paymentsAll.filter((i) => i.platformType === this.currentPlatformType)
        },
        // channels() {
        //     if (
        //         this.payments[this.currentPaymentIndex] &&
        //         this.payments[this.currentPaymentIndex].channels
        //     ) {
        //         switch (this.$store.state.account.role) {
        //           case 1:
        //               return this.payments[this.currentPaymentIndex].channels;
        //           default:
        //               return this.payments[this.currentPaymentIndex].channels.filter(e=> e.status !== 2);
        //         }
        //     }
        //     return [];
        // },
        channels() {
            let list = []
            this.payments.forEach((i) => {
                if (i.channels.length) {
                    i.channels.forEach((j)=>{
                        if (this.$store.state.account.role === 1) {
                            list.push(j)
                        } else {
                            if (j.status !== 2) {
                                list.push(j)
                            }
                        }

                    })
                }
            })
            return list;
        },
        amounts() {
            if (
                this.channels[this.currentChannelIndex] &&
                this.channels[this.currentChannelIndex].amounts
            ) {
                return this.channels[this.currentChannelIndex].amounts.filter(
                    (e) => e > 0
                );
            }
            return [];
        },
        paymentMin() {
            return this.channels[this.currentChannelIndex] ? this.channels[this.currentChannelIndex].paymentMin/100 : ''
        },
        paymentMax() {
            return this.channels[this.currentChannelIndex] ? this.channels[this.currentChannelIndex].paymentMax/100 : ''
        },
        convertedAmount() {
            return this.currency(1/this.channels[this.currentChannelIndex].channelRate*this.showAmount, false, true)
        },
        paymentsExtraRate() {
            return function (paymentId) {
                let channelExtraRate
                this.paymentsAll.forEach((i) => {
                    if (i.paymentId === paymentId) {
                        channelExtraRate =  i.channelExtraRate
                    }
                })
                return channelExtraRate
            }
        }
    },
    mounted() {
        this.paginate.pageSize = 13;
        this.chargeAmount = ''
        this.getPayments();
    },
    methods: {
        changePlatformType(index) {
            this.currentPlatformType = index
            this.currentPaymentIndex = 0
            this.currentChannelIndex = 0
            this.chargeAmount = ''
        },
        changePayment(index) {
            this.currentPaymentIndex = index
            this.currentChannelIndex = 0
            this.chargeAmount = ''
        },
        changeChannel(index) {
            this.currentChannelIndex = index
            this.chargeAmount = ''
        },
        doCharge(){
            if (!this.payments.length) return
            if (this.chargeAmount < this.channels[this.currentChannelIndex].paymentMin || this.chargeAmount > this.channels[this.currentChannelIndex].paymentMax) {
                if (this.$store.state.webType === 2 ) {
                    window.$toast.fail(this.$t("The amount is err"));
                }
                this.errAmount = true
                let that = this
                setTimeout(()=>{
                    that.errAmount = false
                }, 1000)
                return false
            }
            debounce(() => {
                this.event_rechargeClick()
                const {advertiseType, bundleId} = this.event_Param()
                this.$protoApi(ROUTE_PAYMENT_CHARGE, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    paymentId: this.channels[this.currentChannelIndex].paymentId,
                    channelId: this.channels[this.currentChannelIndex].channelId,
                    amount: this.chargeAmount, // 单位分
                    platform: this.$store.state.webType,
                    advertiseType: advertiseType,
                    bundleId: bundleId,
                })
                    .then((res) => {
                        if (this.$store.state.webType > 1) {
                            this.$router.push({
                                path: "/m/seamless/go",
                                query: {
                                    go: res.paymentUrl
                                }
                            }).catch(()=>{})
                        } else {
                            window.open(
                                res.paymentUrl,
                                "service",
                                "width=500,height=750"
                            );
                        }
                    })
                    .catch(() => {});
            })();
        },
        getPayments() {
            debounce(() => {
                this.$protoApi(ROUTE_PAYMENT_PAYMENTS, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                })
                    .then((res) => {
                        switch (this.$store.state.account.role) {
                            case 1:
                                this.paymentsAll = res.payments;
                                break;
                            default:
                                this.paymentsAll = res.payments.filter(e=> e.status !== 2);
                        }
                        this.changeChannel(new Date().getTime() % this.channels.length)
                        // for (let i = 0; i < this.channels.length; i++) {
                        //     if (this.channels[i].recommend) {
                        //         this.changeChannel(i)
                        //         break
                        //     }
                        // }
                    })
                    .catch(() => {});
            })();
        },
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1
                this.finished = false
                this.records = []
            }
            this.processing = true;
            this.resetTotal();
            debounce(() => {
                this.$protoApi(ROUTE_RECORDER_QUERY_PAYMENTS, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    beginTime: this.form.beginTime,
                    endTime: this.form.endTime,
                    page: this.paginate.page,
                    pageSize: this.paginate.pageSize,
                    paymentId: this.paymentActions[this.form.paymentIndex].paymentId,
                    status: this.form.status
                })
                    .then((res) => {
                        if (this.$store.state.webType === 1) {
                            this.paginate.total = res.counts
                            this.records = res.records;
                            for (const row of res.records) {
                                this.total.received += row.comfirmAmount;
                                this.total.fee += (row.chargeAmount - row.comfirmAmount);
                            }
                        } else {
                            this.records = this.records.concat(res.records);
                            this.paginate.page++

                            if (res.counts === 0 || this.records.length >= res.counts) {
                                this.finished = true;
                            }
                            this.loading = false;
                        }
                    })
                    .catch(() => {
                    })
                    .finally(() => {
                        this.processing = false;
                    });
            })();
        },
        resetTotal() {
            this.total = {
                received: 0,
                fee: 0
            };
        },
    },
}