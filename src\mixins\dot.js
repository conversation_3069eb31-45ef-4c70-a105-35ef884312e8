import store from "@/store";
import { logEvent } from "firebase/analytics";
export const dot = {
    methods: {
        toParams(param) {
            let params = [];
            for (let key in param) {
                let value = param[key];
                let type = 'string';

                if (typeof value == 'number') {
                    type = value - Math.floor(value) === 0 ? 'long' : 'double';
                }
                params.push({
                    key,
                    value,
                    type,
                });
            }
            return JSON.stringify(params)
        },
        rb(event_name, extra={}) {
            const myHeaders = new Headers();
            myHeaders.append("Content-Type", "application/json");
            const raw = JSON.stringify({
                "link_id": store.state.dotKeys.rb.link_id,
                "event_name": event_name,
                "extra": extra
            });
            const requestOptions = {
                method: "POST",
                headers: myHeaders,
                body: raw,
                redirect: "follow"
            };
            fetch("https://pwa-backend-prod.roibest.com/report/fb/event", requestOptions)
                .then((response) => response.text())
                .then((result) => iconsole(result))
                .catch((error) => console.error(error));
        },
        event_Param() {
            if (store.state.dotKeys.afKey) {
                return {
                    advertiseType : 1,
                    bundleId : store.state.dotKeys.afKey,
                }
            }
            if (store.state.dotKeys.pixelId) {
                return {
                    advertiseType : 2,
                    bundleId : store.state.dotKeys.pixelId,
                }
            }
            if (store.state.dotKeys.kwai) {
                return {
                    advertiseType : 3,
                    bundleId : store.state.dotKeys.kwai,
                }
            }
            if (store.state.dotKeys.firebase) {
                return {
                    advertiseType : 4,
                    bundleId : store.state.dotKeys.firebase,
                }
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {

            }
            return {
                advertiseType : 0,
                bundleId : "",
            }
        },
        event_login(userID) {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('login', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'setCustomerUserId', userID);
                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'login',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'login');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'login')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('login')
                }
            }
        },
        event_logout() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('logout', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'logout',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'logout');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'logout')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('logout')
                }
            }
        },
        event_registerClick() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('registerClick', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {


                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'registerClick',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'registerClick');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'registerClick')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('registerClick')
                }
            }
        },
        event_register() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('register', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {


                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'register',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'CompleteRegistration');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('completeRegistration')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'register')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('completeRegistration')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('CompleteRegistration')
                }
            }
        },
        event_rechargeClick() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('rechargeClick', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'rechargeClick',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'rechargeClick');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('addToCart')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'rechargeClick')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('addToCart')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('rechargeClick')
                }
            }
        },
        event_firstrecharge(param) {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('firstrecharge', this.toParams(param))
            }
            if (store.state.dotKeys.afKey) {


                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'firstrecharge',
                        eventValue: param
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'Purchase', param);
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('firstDeposit', param);
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('purchase', param);
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'firstrecharge');
                logEvent(window.analytics, 'purchase', param)
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('firstDeposit', param)
                    this.rb('purchase', param)
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('Purchase', param)
                }
            }
        },
        event_recharge(param) {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('recharge', this.toParams(param))
            }
            if (store.state.dotKeys.afKey) {


                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'recharge',
                        eventValue: param
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'CustomizeProduct', param);
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('purchase', param)
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'purchase', param)
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('purchase', param)
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('CustomizeProduct', param)
                }

            }
        },
        event_withdrawClick() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('withdrawClick', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'withdrawClick',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'InitiateCheckout');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'withdrawClick')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('InitiateCheckout')
                }
            }
        },
        event_withdrawOrderSuccess(param) {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('withdrawOrderSuccess', this.toParams(param))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'withdrawOrderSuccess',
                        eventValue: param
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'withdrawOrderSuccess', param);
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'withdrawOrderSuccess', param)
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('withdrawOrderSuccess', param)
                }
            }
        },
        event_enterGame(game) {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('enterGame', this.toParams({
                    platformId: game.platformId,
                    gameName: game.gameName,
                }))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'enterGame',
                        eventValue: {
                            platformId: game.platformId,
                            gameName: game.gameName,
                        }
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'enterGame', {
                    platformId: game.platformId,
                    gameName: game.gameName,
                });
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'enterGame', {
                    platformId: game.platformId,
                    gameName: game.gameName,
                })
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('enterGame', {
                        platformId: game.platformId,
                        gameName: game.gameName,
                    })
                }
            }
        },
        event_vipReward(vip) {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('vipReward', this.toParams({
                    vip: vip
                }))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'vipReward',
                        eventValue: {
                            vip: vip
                        }
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'vipReward', {
                    vip: vip
                });
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'vipReward', {
                    vip: vip
                })
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('vipReward', {
                        vip: vip
                    })
                }
            }
        },
        event_dailyReward() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('dailyReward', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {


                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'dailyReward',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'dailyReward');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'dailyReward')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('dailyReward')
                }
            }
        },
        event_enterEventCenter() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('enterEventCenter', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'enterEventCenter',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'enterEventCenter');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'enterEventCenter')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('enterEventCenter')
                }
            }
        },
        event_enterTask() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('enterTask', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'enterTask',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'enterTask');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'enterTask')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('enterTask')
                }
            }
        },
        event_enterCashback() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('enterCashback', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {

                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'enterCashback',
                        eventValue: {}
                    });

            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'enterCashback');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'enterCashback')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('enterCashback')
                }
            }
        },
        event_enterPromote() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('enterPromote', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {
                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'enterPromote',
                        eventValue: {}
                    });
            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'enterPromote');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'enterPromote')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('enterPromote')
                }
            }
        },
        event_bannerClick() {
            if ('jsBridge' in window) {
                window.jsBridge.postMessage('bannerClick', this.toParams({}))
            }
            if (store.state.dotKeys.afKey) {
                    AF('pba', 'event', {
                        eventType: 'EVENT',
                        eventName: 'bannerClick',
                        eventValue: {}
                    });
            }
            if (store.state.dotKeys.pixelId) {
                fbq('track', 'bannerClick');
            }
            if (store.state.dotKeys.kwai) {
                'kwaiq' in window && window.kwaiq.instance(store.state.dotKeys.kwai).track('contentView')
            }
            if (store.state.dotKeys.firebase) {
                logEvent(window.analytics, 'bannerClick')
            }
            if (store.state.dotKeys.rb.link_id && store.state.dotKeys.rb.uuid && store.state.dotKeys.rb.channel_id) {
                if (store.state.dotKeys.rb.channel_id === "9") { // 快手
                    this.rb('contentView')
                }

                if (store.state.dotKeys.rb.channel_id === "4") { // fb
                    this.rb('bannerClick')
                }
            }
        },


    }
}
