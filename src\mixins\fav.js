import {debounce} from "@/utils/common";
import {ROUTE_PLATFORM_STOREFAVORITE} from "@/api";

export const fav = {
    computed: {
        isFav() {
            let fav = false;
            for (const e of this.$store.state.platform.favoriteGames) {
                if (
                    e.gameId === this.game.gameId &&
                    e.platformId === this.game.platformId
                ) {
                    fav = true;
                    break;
                }
            }
            return fav;
        },
    },
    methods: {
        toggleFav() {
            if (!this.$store.getters.isLogin) {
                if (this.$store.state.webType === 1 ){
                    this.$modal.show('loginPopupModal')
                } else {
                    this.$router.push('/m/login').catch()
                }
                return
            }
            debounce(() => {
                this.$protoApi(ROUTE_PLATFORM_STOREFAVORITE, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    platformId: this.game.platformId,
                    gameId: this.game.gameId,
                    action: this.isFav,
                })
                    .then((res) => {
                        this.$store.commit("setFavoriteGames",res);
                    })
                    .catch(() => {})
                    .finally(() => {

                    });
            })();
        },
    },
};
