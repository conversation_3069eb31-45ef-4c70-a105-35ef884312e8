export const game = {
    methods: {
        startGame(e, freeBtn) {
            if ((!freeBtn && !this.$store.getters.isLogin) || freeBtn && !this.$store.state.token.token) {
                this.$modal.show('loginPopupModal')
            } else {
                this.$router.push({
                    path: "/seamless",
                    query: {
                        gameId: e.gameId,
                        platformId: e.platformId,
                        gameName: e.gameName,
                        mode: freeBtn ? '2' : '1'
                    }
                }).then(r => {})
            }

        },
        startMobileGame(e) {
            if (!this.$store.getters.isLogin) {
                this.$router.push("/m/login")
            } else {
                this.$store.commit("setMobileChooseGame", {
                    show: true,
                    freeCheck: this.freeCheck(e),
                    normalCheck: this.normalCheck(e),
                    platformId: e.platformId,
                    gameId: e.gameId,
                    gameName: e.gameName,
                    firmType: e.firmType.toUpperCase(),
                    firmCode: e.firmCode,
                })
            }

        },
        freeCheck(game){
            for (const platform of this.$store.state.platform.platforms) {
                if (game.platformId === platform.platformId) {
                    return platform.freeGame && game.freeGame
                }
            }
            return false
        },
        normalCheck(game){
            for (const platform of this.$store.state.platform.platforms) {
                if (game.platformId === platform.platformId) {
                    return platform.normalGame && game.normalGame
                }
            }
            return false
        },
        maintenanceCheck(game){
            for (const platform of this.$store.state.platform.platforms) {
                if (game.platformId === platform.platformId) {
                    return platform.status || game.status
                }
            }
            return false
        }
    },
};
