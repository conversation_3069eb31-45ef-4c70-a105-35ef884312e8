import ClipboardJS from 'clipboard';
export const init = {
  computed: {
    mainRouterKey: function () {
      return ["/", "/index", "/login", "/register"].includes(this.$route.path)
          ? "index"
          : this.$route.fullPath;
    },
  },
  mounted() {
    let r = function() {
      var t = document.getSelection();
      if (!t.rangeCount)
        return function() {}
            ;
      for (var e = document.activeElement, n = [], r = 0; r < t.rangeCount; r++)
        n.push(t.getRangeAt(r));
      switch (e.tagName.toUpperCase()) {
        case "INPUT":
        case "TEXTAREA":
          e.blur();
          break;
        default:
          e = null
      }
      return t.removeAllRanges(),
          function() {
            "Caret" === t.type && t.removeAllRanges(),
            t.rangeCount || n.forEach(function(e) {
              t.addRange(e)
            }),
            e && e.focus()
          }
    }
    let copyFunc = function(t, e={}) {
      var n, a, s, c, l, u, d = !1;
      e || (e = {}),
          n = e.debug || !1;
      try {
        if (s = r(),
            c = document.createRange(),
            l = document.getSelection(),
            (u = document.createElement("span")).textContent = t,
            u.ariaHidden = "true",
            u.style.all = "unset",
            u.style.position = "fixed",
            u.style.top = 0,
            u.style.clip = "rect(0, 0, 0, 0)",
            u.style.whiteSpace = "pre",
            u.style.webkitUserSelect = "text",
            u.style.MozUserSelect = "text",
            u.style.msUserSelect = "text",
            u.style.userSelect = "text",
            u.addEventListener("copy", function(r) {
              if (r.stopPropagation(),
                  e.format)
                if (r.preventDefault(),
                "undefined" === typeof r.clipboardData) {
                  n && console.warn("unable to use e.clipboardData"),
                  n && console.warn("trying IE specific stuff"),
                      window.clipboardData.clearData();
                  var o = i[e.format] || i.default;
                  window.clipboardData.setData(o, t)
                } else
                  r.clipboardData.clearData(),
                      r.clipboardData.setData(e.format, t);
              e.onCopy && (r.preventDefault(),
                  e.onCopy(r.clipboardData))
            }),
            document.body.appendChild(u),
            c.selectNodeContents(u),
            l.addRange(c),
            !document.execCommand("copy"))
          throw new Error("copy command was unsuccessful");
        d = !0
      } catch (r) {
        n && console.error("unable to copy using execCommand: ", r),
        n && console.warn("trying IE specific stuff");
        try {
          window.clipboardData.setData(e.format || "text", t),
          e.onCopy && e.onCopy(window.clipboardData),
              d = !0
        } catch (r) {
          n && console.error("unable to copy using clipboardData: ", r),
          n && console.error("falling back to prompt"),
              a = function(t) {
                var e = (/mac os x/i.test(navigator.userAgent) ? "⌘" : "Ctrl") + "+C";
                return t.replace(/#{\s*key\s*}/g, e)
              }("message"in e ? e.message : o),
              window.prompt(a, t)
        }
      } finally {
        l && ("function" == typeof l.removeRange ? l.removeRange(c) : l.removeAllRanges()),
        u && document.body.removeChild(u),
            r()
      }
      return d
    }
    this.$store.commit("initConfig");
    const clipboard = new ClipboardJS('.copy-btn');
    const that = this
    clipboard.on('success', function(e) {
      let t = e.text
      e.clearSelection();
      copyFunc(e.text)
      $toast.success({
        message: that.$t('label_copy_success'),
        icon: 'passed',
        // duration: 0
      })
    });
  },
};
