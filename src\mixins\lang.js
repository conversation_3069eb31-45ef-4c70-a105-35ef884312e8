import {dot} from "@/mixins/dot";
import {ROUTE_PLATFORM_BANNERETAILS} from "@/api";

export const lang = {
    mixins: [dot],
    data() {
        return {
            supportLanguages: ["PT", "EN"],
            banner: {
                banner: 0,
                id: 0,
                messages: [],
            },

            swiperOptions: {
                loop: true,
                speed: 1000,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: ".banner-pagination",
                    clickable: true,
                },
                observer: true,
                observeParents: !0,
                preloadImages: 1,
                slidesPerView: 1,
            },
        }
    },
    computed: {
        bannersNotice: function () {
            return this.$store.getters.bannersNotice
        },
        className(item) {
            return item === this.$store.state.language ? "selected" : "";
        },
        currentLanguage() {
            return this.$store.state.language.toUpperCase();
        },
        changeLang() {
            return list => {
                if (!list.length) return {
                    contents: "",
                    id: 0,
                    imageUrl: "",
                    language: 1,
                    title: "",
                }
                let data = {}
                function select(langId) {
                    for (const item of list) {
                        if (item.language === langId) {
                            return item
                        }
                    }
                    return false
                }
                data = select(this.$store.state.languages[this.$store.state.language])
                if (data) return data
                data = select(this.$store.state.languages['en'])
                if (data) return data
                return list[0]
            }
        }
    },
    methods: {
        goPromo: function (id) {
            this.$store.commit("setCurrentPromotionId", id);
            id
                ? this.$router.push("/promotions#".concat(id))
                : this.$router.push("/promotions");
            document.body.scrollTo({
                top: 0,
                left: 0,
                behavior: "smooth",
            });
        },
        goDetail(id) {
            let route = { path: "/m/activityDetail", query: { id: id } };
            if (this.$route.path === route.path && this.$route.query.id == id) return;
            if (this.$store.state.showMobileSide) {
                this.$store.commit("setShowMobileSide");
            }
            let that = this
            setTimeout(()=>{
                that.$router.push(route).catch(()=>{});
            }, 300)
        },
        bannerDetail(id) {
            this.$protoApi(ROUTE_PLATFORM_BANNERETAILS, {
                channel: this.$store.state.channel,
                device: this.$store.state.device,
                id: id,
            })
                .then((res) => {
                    if(res.banner) this.banner = res.banner
                })
                .catch(() => {})
                .finally(() => {});
        },
        setHelpIndex(index) {
            this.$store.commit("setHelpIndex", index);
            this.$refs.helpContent.scrollTop = 0;
        },
        selectLanguage(e) {
            e = e.toLowerCase();
            this.$i18n.locale = e;
            localStorage.setItem("hisLang", e);
            window.brLang = this.$i18n.messages[e];
            this.$store.commit("setLanguage", e);
        },
    }
}