import { debounce } from "@/utils/common";
import {
  ROUTE_CAPTCHA,
  ROUTE_LOGON_PASSWD,
  ROUTE_LOGON_PASSWDCAPTCHA,
} from "@/api";
import { MD5 } from "crypto-js";
import { logon } from "@/mixins/logon";
import {actionCaptcha} from "@/mixins/actionCaptcha";

export const loginPwd = {
  mixins: [logon, actionCaptcha],
  data() {
    return {
      showCurrentPassword: false,
      showNewPassword: false,
      showConfirmPassword: false,
      captcha: {},
      form: {
        captcha: "",
        CurrentPassword: "",
        NewPassword: "",
        ConfirmPassword: "",
      },
    };
  },
  mounted() {
    this.initActionCaptcha(this.captchaPwdHandler, "captchaPwdHandler")
    // this.getCaptcha();
  },
  methods: {
    validator(val) {
      return this.form.NewPassword === this.form.ConfirmPassword;
    },
    getCaptcha() {
      debounce(() => {
        this.$protoApi(ROUTE_CAPTCHA)
          .then((res) => {
            this.captcha = res;
          })
          .catch(() => {});
      }, 500)();
    },
    submit(values) {
      let e = this;
      this.$validator.validateAll().then(function (t) {
        t ? e.passwd() : e.getCaptcha();
      });
    },
    captchaPwdHandler() {
      this
          .$protoApi(ROUTE_LOGON_PASSWDCAPTCHA, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            token: this.$store.state.token.token,
            passwd: MD5(this.form.NewPassword).toString(),
            passwdOld: MD5(this.form.CurrentPassword).toString(),
              captcha_0: window.validate.captcha_0,
              captcha_1: window.validate.captcha_1,
              captcha_2: window.validate.captcha_2,
              captcha_3: window.validate.captcha_3,
              platformId: this.captchaPlatform,
          })
          .then((res) => {
            this.$store.commit("setToken", res);
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });

            if (this.$store.state.webType > 1) {
              setTimeout(() => {
                this.$router.back();
              }, 1000);
            } else {
              this.$emit("closeShow");
            }
          })
          .catch(() => {});
    },
    passwd() {
      debounce(() => {
        this.$protoApi(ROUTE_LOGON_PASSWD, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          passwd: MD5(this.form.NewPassword).toString(),
          passwdOld: MD5(this.form.CurrentPassword).toString(),
          id: this.captcha.id,
          captcha: this.form.captcha,
        })
          .then((res) => {
            this.$store.commit("setToken", res);
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });

            if (this.$store.state.webType > 1) {
              let that = this;
              setTimeout(() => {
                that.$router.back();
              }, 1000);
            } else {
              this.$emit("closeShow");
            }
          })
          .catch(() => {
            this.getCaptcha();
          });
      })();
    },
  },
};
