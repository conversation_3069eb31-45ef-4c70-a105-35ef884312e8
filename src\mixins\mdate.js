import moment from "moment/moment";

export const mdate = {
    data() {
        return {
            show: false,
            minDate: moment().subtract(29, 'days').toDate(),
            maxDate: new Date(),
            columns: ['杭州', '宁波', '温州'],
            showPicker: false,
            form: {
                // beginTime: 0,
                // endTime: 0,
                beginTime: moment(new Date()).startOf("day").format("x"),
                endTime: moment(new Date()).endOf("day").format("x"),
            }
        };
    },
    mounted() {
        this.setDate(0)
    },
    computed:{
        date() {
            // return `${this.formatDate(new Date(this.form.beginTime))} - ${this.formatDate(new Date(this.form.beginTime))}`
            return moment(new Date(parseInt(this.form.beginTime))).format('MM/DD')+' - ' + moment(new Date(parseInt(this.form.endTime))).format('MM/DD')
        },
        defaultDate(){
            return [new Date(parseInt(this.form.beginTime)), new Date(parseInt(this.form.endTime))]
        },
        tab() {
            if (this.form.beginTime == moment(new Date())
                .add(0, 'd')
                .startOf('day').format('x') && this.form.endTime == moment(new Date())
                .add(0, 'd')
                .endOf('day').format('x')) {
                return 0
            }else if (this.form.beginTime == moment(new Date())
                .add(-1, 'd')
                .startOf('day').format('x') && this.form.endTime == moment(new Date())
                .add(-1, 'd')
                .endOf('day').format('x')) {
                return 1
            }else if (this.form.beginTime == moment(new Date())
                .add(-6, 'd')
                .startOf('day').format('x') && this.form.endTime == moment(new Date())
                .add(0, 'd')
                .endOf('day').format('x')) {
                return 7
            }
        }
    },
    methods: {
        formatDate(date) {
            return `${date.getMonth() + 1}/${date.getDate()}`;
        },
        onConfirmDate(date) {
            const [start, end] = date;
            iconsole(date)
            this.show = false;
            this.form.beginTime = start.getTime()
            this.form.endTime = end.getTime() + 86399999
            iconsole(this.form.beginTime,  this.form.endTime)
        },
        onConfirmTudo(value) {
            this.value = value;
            this.showPicker = false;
        },
        setDate(t) {
            switch (t) {
                case 7:
                    this.form.beginTime = moment(new Date()).add(-6, "d").startOf("day").format("x");
                    this.form.endTime = moment(new Date()).endOf("day").format("x");
                    break;
                default:
                    this.form.beginTime = moment(new Date())
                        .add(-1 * t, "d")
                        .startOf("day").format("x");
                    this.form.endTime = moment(new Date())
                        .add(-1 * t, "d")
                        .endOf("day").format("x");
            }
            iconsole(this.form.beginTime, this.form.endTime)
        },
    },
}