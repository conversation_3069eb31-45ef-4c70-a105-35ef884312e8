import { debounce } from "@/utils/common";
import {
  ROUTE_PLATFORM_FAVORITE,
  ROUTE_PLATFORM_GAMES,
  ROUTE_PLATFORM_LATEST, ROUTE_PLATFORM_PLATFORMGAMES,
} from "@/api";

export const menu = {
  data() {
    return {
      processing: false,
      loading: false,
      finished: false,
      list: [],
      platforms: [],
      paginate: {
        total: 0,
        page: 1,
        pageSize: 18,
        to: 1,
      },
      vendorArr: [5, 6],
    };
  },
  computed: {
    filter() {
      return this.$store.state.menu.filter
    },
    pageCount() {
      if (Math.ceil(this.paginate.total / this.paginate.pageSize)) {
        return Math.ceil(this.paginate.total / this.paginate.pageSize);
      } else {
        return 0;
      }
    },
    menuOptions() {
      return {
        "-4": {
          title: this.$t("platform"),
          icon: "icon-platform",
          dataType: "game",
          show: false,
        },
        "-3": {
          title: this.$t("lobby"),
          icon: "icon-home",
          dataType: "game",
          show: true,
        },
        "-2": {
          title: this.$t("favorite"),
          icon: "icon-fav",
          dataType: "game",
          show:     !!this.$store.getters.isLogin,
        },
        "-1": {
          title:    this.$t("recent"),
          icon:     "icon-recent",
          dataType: "game",
          show:     false,
        },
        0: {
          title: this.$t("hot_games"),
          icon: "img/category/hot.png",
          dataType: "game",
          show: true,
        },
        1: {
          title: this.$t("casino"),
          icon: "icon-casino",
          dataType: "game",
          show: true,
        },
        2: {
          title: this.$t("fishing"),
          icon: "icon-fish",
          dataType: "game",
          show: true,
        },
        3: {
          title: this.$t("cards"),
          icon: "icon-cards",
          dataType: "game",
          show: true,
        },
        4: {
          title: this.$t("live"),
          icon: "icon-live",
          dataType: "game",
          show: true,
        },
        5: {
          title: this.$t("sports"),
          icon: "icon-sports",
          dataType: "vendor",
          show: true,
        },
        6: {
          title: this.$t("e_sports"),
          icon: "icon-e-sports",
          dataType: "vendor",
          show: true,
        },
        7: {
          title: this.$t("slot"),
          icon: "icon-slots",
          dataType: "game",
          show: true,
        },
        8: {
          title: this.$t("lottery"),
          icon: "icon-lottery-",
          dataType: "game",
          show: true,
        },
      };
    },
  },
  mounted() {
    if (["-3", "-4"].includes(this.$store.state.platform.currentCategory)) {
      return;
    }
    if (this.$store.state.webType === 1) {
      this.loadGame();
    }
  },
  methods: {
    updateFilter(e) {
      this.$store.commit("setMenuFilterPlatformId", e);
      this.setPage(1);
      this.list = [];
      this.loading = true;
      this.finished = false;
      this.loadGame();
    },
    setPage(v) {
      this.paginate.page = v;
    },
    handleItemClick(gameCategory) {
      this.$store.commit("setCurrentCategory", gameCategory);
      document.body.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth",
      });
    },
    setCategory(index) {
      if (!this.$store.getters.isLogin && index === "-2") {
        if (this.$store.state.webType === 1) {
          this.$modal.show('loginPopupModal')
        } else {
          this.$router.push('/m/login').catch(()=>{})
        }
        return
      }
      this.$store.commit("setCurrentCategory", index);
    },
    menuItem(index= 0) {
      return (
        this.menuOptions[index] ?? {
          title: "",
          icon: "",
          dataType: "",
          show: true,
        }
      );
    },
    loadGame() {
      debounce(() => {
        if (this.$store.state.platform.currentCategory === "-1") {
          this.$protoApi(ROUTE_PLATFORM_LATEST, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            token: this.$store.state.token.token,
          })
            .then((res) => {
              this.list = res.games;
              this.finished = true;
              this.loading = false;
            })
            .catch(() => {});
        } else if (this.$store.state.platform.currentCategory === "-2") {
          this.$protoApi(ROUTE_PLATFORM_FAVORITE, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            token: this.$store.state.token.token,
          })
            .then((res) => {
              this.$store.commit("setFavoriteGames",res);
              this.finished = true;
              this.loading = false;
            })
            .catch(() => {});
        } else if (this.$store.state.platform.currentCategory === "-4") {
          this.$protoApi(ROUTE_PLATFORM_PLATFORMGAMES, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            platformId: this.$store.state.platform.gamePlatforms[this.$store.state.platform.currentPlatformIndex].platformId,
          })
              .then((res) => {
                this.finished = true;
                this.loading = false;
                this.$store.commit("setPlatformGames", res)
              })
              .catch(() => {});
        } else {
          if (this.$store.state.platform.currentCategory <= 0) return;
          this.$protoApi(ROUTE_PLATFORM_GAMES, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            page: this.paginate.page,
            pageSize: this.paginate.pageSize,
            categoryId: this.$store.state.platform.currentCategory,
            platformId: this.filter.platformId,
          })
            .then((res) => {
              this.$store.commit('updatePlatforms', res.platforms)
              if (this.$store.state.webType === 1) {
                this.list = res.games;
              } else {
                this.list = this.list.concat(res.games);
                this.paginate.page++

                if (res.counts === 0 || this.list.length >= res.counts) {
                  this.finished = true;
                }
                this.loading = false;
              }

              this.paginate.total = res.counts;
              switch (this.$store.state.account.role) {
                case 1:
                  this.platforms = res.platforms;
                  break;
                default:
                  this.platforms = res.platforms.filter(e=> e.status !== 2);
              }
            })
            .catch(() => {});
        }
      })();
    },
  },
};
