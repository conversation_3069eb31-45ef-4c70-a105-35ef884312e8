import { debounce } from "@/utils/common";
import { ROUTE_PLATFORM_TRANSACTIONPASSWD } from "@/api";
import { MD5 } from "crypto-js";

export const paymentPwd = {
  data() {
    return {
      showPassword: false,
      showConfirmPassword: false,
      user: {
        password: "",
        confirmPassword: "",
      },
    };
  },
  methods: {
    submitTransactionpasswd() {
      let e = this;
      this.$validator.validateAll().then(function (t) {
        t && e.transactionpasswd();
      });
    },
    transactionpasswd() {
      debounce(() => {
        this.$protoApi(ROUTE_PLATFORM_TRANSACTIONPASSWD, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          transactionPasswd: MD5(this.user.password).toString(),
        })
          .then((res) => {
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });
            this.$emit("closeShow");
            if (this.$store.state.webType > 1) {
              let that = this
              setTimeout(() => {
                that.$router.back();
              }, 1000);
            } else {
              this.$emit("refresh");
            }
          })
          .catch(() => {});
      })();
    },
  },
};
