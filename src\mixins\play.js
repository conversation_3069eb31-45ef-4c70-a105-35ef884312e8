import {debounce, GAME_TYPE, getFirmcode, getFirmtype, getQueryInfoUrl, ShiftConnectPackageUrl} from "@/utils/common";
import { ROUTE_PLATFORM_BALANCE, ROUTE_PLATFORM_LOGONGAME, ROUTE_PLATFORM_TRANSFEROUTALL } from "@/api";
import {dot} from "@/mixins/dot";

export const play = {
  data() {
    return {
      refresh: false,
      gameSrc: "",
    };
  },
  mixins: [dot],
  computed: {
    balance() {
      return this.refresh ? '****' : this.currency(this.$store.state.balance)
    }
  },
  methods: {
    logout() {
      if (this.$store.state.webType === 1) {
        this.cover.init(
            {
              title: this.$t("in_popup_prompt"),
              html: this.$t("in_sure_logout"),
              icon: "info",
              btn: {
                cancel: this.$t("button_cancel"),
                confirm: this.$t("ok"),
              },
            },
            (e) => {
              if ("confirm" === e) {
                this.$store.commit("setLogout");
                this.event_logout()
                setTimeout(()=>{
                  window.location.reload()
                }, 200)
              }
            }
        );
      } else {
        window.$Dialog.confirm({
          title: this.$t("in_popup_prompt"),
          message: this.$t("in_sure_logout"),
          confirmButtonText: this.$t("ok"),
          cancelButtonText: this.$t("button_cancel"),
        })
            .then(() => {
              this.$store.commit("setLogout");
              this.event_logout()
              this.$store.commit('setMobileHeadShowDrop')
              setTimeout(()=>{
                window.location.href = '/'
              }, 200)
            })
            .catch(() => {
              // on cancel
            });
      }
    },
    launchGame(game) {
        this.event_enterGame(game)
        if (game.platformId === 100) {
            this.localGame(game)
        }else {
            this.thirdGame(game)
        }
    },
    thirdGame(game) {
        this.$tdtcApi.getThirdGame("enterGame", {
            firmtype: game.firmType,
            firmcode: game.firmCode,
            device: "Mobile", // ? "Mobile" : "PC"
            lang: "vi", //vi en tha
        })
            .then((result) => {
                if (result["code"] === 0) {

              /*       window.$Dialog.confirm({
                     message: result["data"],
                     confirmButtonText: 'enter',
                     cancelButtonText: 'cancel'
                     })
                     .then(() => {*/
                    this.$store.commit("setThirdGame", game)

                    if (['CMD', 'BBIN'].includes(game.firmType)) {
                        const userAgent = navigator.userAgent;
                        if (/Android/i.test(userAgent) && 'jsBridge' in window) {
                            let url = "browser:" + result["data"]
                            window.open(url, "_self")
                        } else {
                            setTimeout(()=>{
                                let w = window.open(result["data"])
                                if (!w) {
                                    window.$Dialog.confirm({
                                        title: this.$t("in_popup_prompt"),
                                        message: this.$t("in_sure_window_open"),
                                        confirmButtonText: this.$t("ok"),
                                        cancelButtonText: this.$t("button_cancel"),
                                    })
                                        .then(() => {
                                            window.open(result["data"])
                                        })
                                        .catch(() => {});
                                }
                            })
                        }
                    } else {
                        this.$router.push({
                            path: '/m/seamless',
                            query: {
                                go: result["data"]
                            }}).catch(()=>{})
                    }

                  /*   })
                     .catch(() => {
                     // on cancel
                     this.$router.back()
                     });*/

                } else {
                    /*let showMsg = this.$t("ERROR_LOGIN_2")
                    if (result["msg"]) {
                        showMsg = showMsg + ":" + result["msg"]
                    }

                    if (result["code"]) {
                        showMsg = showMsg + " CODE:" + result["code"]
                    }
                    window.$toast.fail(showMsg);*/
                    window.$toast.fail(result["msg"]);
                }
            })
            .catch(() => {
                ShiftConnectPackageUrl()
            }).finally(()=>this.$store.commit("resetMobileChooseGame"));

    },
    leaveThirdGame() {
        if (!this.$store.state.thirdGame.firmCode) {
            this.refreshBalance()
            return
        }
        this.$tdtcApi.getThirdGame("leaveGame", {
            firmtype: this.$store.state.thirdGame.firmType,
            firmcode: this.$store.state.thirdGame.firmCode,
        })
            .then((result) => {
            })
            .catch(() => {
                ShiftConnectPackageUrl()
            }).finally(()=>{
            this.refreshBalance()
            this.$store.commit("setThirdGame", {
                firmType: "",
                firmCode: "",
            })
        });
    },
    localGame(game) {
        this.$protoApi(ROUTE_PLATFORM_LOGONGAME, {
            channel: this.$store.state.channel,
            device: this.$store.state.device,
            token: this.$store.state.token.token,
            platformId: game.platformId,
            gameId: game.gameId,
            platform: this.$store.state.webType,
            mode: game.mode,
        })
            .then((res) => {
                // return

     /*            window.$Dialog.confirm({
                 message: res.url,
                 confirmButtonText: 'enter',
                 cancelButtonText: 'cancel'
                 })
                 .then(() => {*/


                if (game.gameId >= 2002) {
                    res.url = res.url + "&queryurl=" + getQueryInfoUrl()
                }


                if (this.$store.state.webType === 1) {
                    if (res.outlook) {
                        window.open(res.url)
                        let that = this
                        setTimeout(()=>{
                            that.$router.back()
                        }, 100)
                    } else {
                        this.gameSrc = res.url;
                    }
                }

                if (this.$store.state.webType === 2) {
                    if (res.outlook) {
                        const userAgent = navigator.userAgent;
                        if (/Android/i.test(userAgent) && 'jsBridge' in window) {
                            let url = "browser:" + res.url
                            window.open(url, "_self")
                        } else {
                            setTimeout(()=>{
                                let w = window.open(res.url)
                                if (!w) {
                                    window.$Dialog.confirm({
                                        title: this.$t("in_popup_prompt"),
                                        message: this.$t("in_sure_window_open"),
                                        confirmButtonText: this.$t("ok"),
                                        cancelButtonText: this.$t("button_cancel"),
                                    })
                                        .then(() => {
                                            window.open(res.url)
                                        })
                                        .catch(() => {});
                                }
                            })
                        }
                    } else {
                        this.$router.push({
                            path: '/m/seamless',
                            query: {
                                go: res.url
                            }}).catch(()=>{})
                    }
                }

         /*        })
                 .catch(() => {
                 // on cancel
                 this.$router.back()
                 });*/
            })
            .catch(() => {
                let that = this
                setTimeout(()=>{
                    if (this.$store.state.webType === 1) {
                        that.$router.back()
                    }
                }, 1000)
            }).finally(()=>this.$store.commit("resetMobileChooseGame"));
    },
    refreshBalance() {
      this.refresh = true;
      let that = this
      setTimeout(()=>{
        that.refresh = false
      },1000)
      debounce(() => {
        if (!this.$store.state.token.token) return;
        this.$protoApi(ROUTE_PLATFORM_BALANCE, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
        })
          .then((res) => {
            this.$store.commit("setBalance", res);
          })
          .catch(() => {})
          .finally(() => {
            this.refresh = false
          });
      })();
    },
    transferoutall() {
      this.refresh = true;
      debounce(() => {
        this.$protoApi(ROUTE_PLATFORM_TRANSFEROUTALL, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
        })
            .then((res) => {
              this.$store.commit("setBalance", res);
            })
            .catch(() => {})
            .finally(() => {
              this.refresh = false
            });
      })();
    },
  },
};
