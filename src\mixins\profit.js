import {menu} from "@/mixins/menu";
import {debounce} from "@/utils/common";
import {ROUTE_RECORDER_QUERY_ACCOUNTPROFITANDLOSS} from "@/api";

export const profit = {
    data() {
        return {
            categoryIndex: 0,
            records: [],
        }
    },
    mixins: [menu],
    mounted() {
        this.paginate.pageSize = 13;
    },
    computed: {
        categories() {
            let list = [{
                categoryId: 0,
                categoryName: this.$t('All')
            }]
            for (const item of this.$store.state.platform.categories) {
                list.push({
                    categoryId: item.categoryId,
                    categoryName: this.menuOptions[item.categoryId].title
                })
            }
            return list
        }
    },
    methods: {
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1;
                this.finished = false
                this.records = []
            }
            this.processing = true;
            debounce(() => {
                this.$protoApi(ROUTE_RECORDER_QUERY_ACCOUNTPROFITANDLOSS, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    beginTime: this.form.beginTime,
                    endTime: this.form.endTime,
                    page: this.paginate.page,
                    pageSize: this.paginate.pageSize,
                    categoryId: this.categories[this.categoryIndex].categoryId,
                })
                    .then((res) => {
                        if (this.$store.state.webType === 1) {
                            this.paginate.total = res.counts;
                            this.records = res.records;
                        } else {
                            this.records = this.records.concat(res.records);
                            this.paginate.page++

                            if (res.counts === 0 || this.records.length >= res.counts) {
                                this.finished = true;
                            }
                            this.loading = false;
                        }
                    })
                    .catch(() => {})
                    .finally(() => {
                        this.processing = false;
                    });
            })();
        },
    }
}