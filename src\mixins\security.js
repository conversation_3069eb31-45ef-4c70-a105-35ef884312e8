import { debounce } from "@/utils/common";
import {ROUTE_LOGON_ACCOUNTMESSAGE, ROUTE_PLATFORM_ACCOUNTINFO} from "@/api";

export const security = {
  data() {
    return {
      info: {
        oauth: false,
        payments: 0,
        totalRecharge: 0,
        transactionPasswd: false,
        vip: [],
        withdrawals: 0,
        accountMessage: false,
        telegram : "",
        facebook : "",
        email : "",
        birthday : "",
      },
      safe: {
        score: this.$t("label_low"),
        img: '',
        star: 1,
      }
    };
  },
  mounted() {
    this.getInfo();
  },
  computed: {
    expWith() {
      let current = {
        exp: 0,
        lv: 0
      }
      let next = {}
      for (const row of this.info.vip) {
        if (this.$store.state.account.vip === row.lv) {
          current = row
        }
        if (this.$store.state.account.vip + 1 === row.lv) {
          next = row
        }
      }
      if (!next) return `width: 100%`;
      let progress = (this.info.totalRecharge - current.exp) / (next.exp - current.exp) * 100

      return progress > 100 ? `width: 100%` : `width: ${progress}%`
    }
  },
  methods: {
    getInfo() {
      debounce(() => {
        this.$protoApi(ROUTE_PLATFORM_ACCOUNTINFO, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
        })
          .then((res) => {
            this.info = res;
            if (this.info.transactionPasswd && this.info.oauth) {
              this.safe.score = this.$t("label_high")
              this.safe.img = 'high grade-high'
              this.safe.star = 5
            } else if (this.info.transactionPasswd || this.info.oauth) {
              this.safe.score = this.$t("label_mid")
              this.safe.img = 'mid grade-mid'
              this.safe.star = 3
            }
          })
          .catch(() => {})
          .finally(() => {
            this.refresh = false;
          });
      })();
    },
    submit() {
      this.$protoApi(ROUTE_LOGON_ACCOUNTMESSAGE, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        token: this.$store.state.token.token,
        email: this.info.email,
        facebook: this.info.facebook,
        telegram: this.info.telegram,
        birthday: this.info.birthday,
      })
          .then((res) => {
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });
            if (!this.info.accountMessage) {
              this.$emit("refresh");
            }
            if (this.$store.state.webType === 1) {
              setTimeout(()=>{
                this.$emit('closeShow')
              }, 500)
            } else {
              setTimeout(()=>{
                this.$router.back()
              }, 500)
            }

          })
          .catch(() => {});
    }
  },
};
