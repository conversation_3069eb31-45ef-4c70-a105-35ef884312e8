import {TDTC_ROURE} from '@/api/tdtc'

export const social = {
  data() {
    return {
      res: {
        telegram : "",
        telegram_status : 0,
        zalo : "",
        zalo_status : 0,
        email : "",
        email_status : 0,
        whatsapp : "",
        whatsapp_status : 0,
        birthday : "",
        birthday_status : 0,
        signal : "",
        signal_status : 0,
      },
      info: {
        telegram : "",
        telegram_status : 0,
        zalo : "",
        zalo_status : 0,
        email : "",
        email_status : 0,
        whatsapp : "",
        whatsapp_status : 0,
        birthday : "",
        birthday_status : 0,
        signal : "",
        signal_status : 0,
      },
    };
  },
  mounted() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_SOCIAL_INFO)
          .then((res) => {
            // QuerySocialBindInfoResp
            Object.assign(this.res, res);
            this.info = {
                  telegram : "",
                  telegram_status : 0,
                  zalo : "",
                  zalo_status : 0,
                  email : "",
                  email_status : 0,
                  whatsapp : "",
                  whatsapp_status : 0,
                  birthday : "",
                  birthday_status : 0,
                  signal : "",
                  signal_status : 0,
            }
            // Object.assign(this.info, res);
            // if (this.info.telegram) this.info.telegram = this.$options.filters['hideStr'](this.info.telegram)
          })
          .catch(() => {});
    },
    submit() {
      if (this.info.telegram && this.info.telegram === this.res.telegram) {
        window.$toast.fail("Không gửi cùng một tài khoản Telegram");
        return
      }
      if (this.info.signal && this.info.signal === this.res.signal) {
          window.$toast.fail("Không gửi cùng một tài khoản Signal");
          return
      }
      if (this.info.zalo && this.info.zalo === this.res.zalo) {
        window.$toast.fail("Không gửi cùng một tài khoản Zalo");
        return
      }
      if (this.info.email && this.info.email === this.res.email) {
        window.$toast.fail("Không gửi cùng một tài khoản E-Mail");
        return
      }
      if (this.info.whatsapp && this.info.whatsapp === this.res.whatsapp) {
        window.$toast.fail("Không gửi cùng một tài khoản WhatsApp");
        return
      }
      if (this.info.birthday && this.info.birthday === this.res.birthday) {
        window.$toast.fail("Không tải lên cùng ngày sinh");
        return
      }
      if (!this.info.signal &&
         !this.info.telegram &&
         !this.info.zalo &&
         !this.info.email &&
         !this.info.whatsapp &&
         !this.info.birthday) {
        window.$toast.fail("Không thay đổi");
        return
      }
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.HALL_BIND_SOCIAL, {
        telegram: this.info.telegram,
        signal: this.info.signal,
        zalo: this.info.zalo,
        email: this.info.email,
        whatsapp: this.info.whatsapp,
        birthday: this.info.birthday,
      })
          .then((res) => {
            if (res.code === 200) {
              $toast.success({
                icon: "passed",
                message: this.$t("bindSocialAccountSuccess"),
              });
              this.getInfo();
            } else {
              window.$toast.fail(this.$t("bindSocialError"+res.code));
            }
          })
          .catch(() => {});
    }
  },
};
