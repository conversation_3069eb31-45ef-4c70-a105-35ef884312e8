import moment from "moment/moment";

export const tdDate = {
    data() {
        return {
            show: false,
            minDate: moment().subtract(29, 'days').toDate(),
            maxDate: new Date(),
            columns: [],
            showPicker: false,
            form: {
                // beginTime: 0,
                // endTime: 0,
                beginTime: moment(new Date()).startOf("day").format("x"),
                endTime: moment(new Date()).endOf("day").format("x"),
            }
        };
    },
    mounted() {
        this.setDate(0)
    },
    computed:{
        date() {
            // return `${this.formatDate(new Date(this.form.beginTime))} - ${this.formatDate(new Date(this.form.beginTime))}`
            return moment(new Date(parseInt(this.form.beginTime))).format('MM/DD')+' - ' + moment(new Date(parseInt(this.form.endTime))).format('MM/DD')
        },
        defaultDate(){
            return [new Date(parseInt(this.form.beginTime)), new Date(parseInt(this.form.endTime))]
        },
    },
    methods: {
        onConfirmDate(date) {
            const [start, end] = date;
            iconsole(date)
            this.show = false;
            this.form.beginTime = start.getTime()
            this.form.endTime = end.getTime() + 86399999
            iconsole(this.form.beginTime,  this.form.endTime)
        },
        setDate(t) {
            switch (t) {
                case 7:
                    this.form.beginTime = moment(new Date()).add(-6, "d").startOf("day").format("x");
                    this.form.endTime = moment(new Date()).endOf("day").format("x");
                    break;
                default:
                    this.form.beginTime = moment(new Date())
                        .add(-1 * t, "d")
                        .startOf("day").format("x");
                    this.form.endTime = moment(new Date())
                        .add(-1 * t, "d")
                        .endOf("day").format("x");
            }
            iconsole(this.form.beginTime, this.form.endTime)
        },
    },
}