export const events = {
    data() {
        return {
            icons:  {
                5:   "#icon-a-zu33",
                7:   "#icon-a-zu333",
                8:   "#icon-a-zu3314",
                9:   "#icon-a-zu335",
                10:  "#icon-a-zu332",
                11:  "#icon-a-zu336",
                18:  "#icon-a-zu331",
                26:  "#icon-a-zu3313",
                200: "#icon-a-zu338",
                201: "#icon-a-zu3312",
                45:  "#icon-a-zu3311",
                46:  "#icon-a-zu3310",
                202: "#icon-a-zu339",
            },
            events: [
                46, // :12,        //推广现金 Kiếm tiền bằng cách quảng cáo
                // 49, // 推广豪礼
                51, // 至尊红包雨
                52, // 补助金
                53, // 签到现金奖金
                18, // :0,         //每日首冲 Nạp tiền lần đầu mỗi ngày
                45, // :9,         //累计充值 Phần thưởng tích lũy nạp tiền
                201, // :10,       //见面礼 Quà gặp mặt
                200, // :11,       //分享奖励 Thưởng chia sẻ
                8, // :8,          //新人礼包 Gói thưởng hội viên mới
                26, // :7,         //流水奖励 Điểm cược mỗi ngày
                10, // :6,         //红包雨 Mưa lì xì thiên sứ giáng trần
                7, // :1,          //成长基金 Tích điểm trưởng thành
                9, // :2,          //签到奖励 Lĩnh thưởng hàng ngày
                11, // :3,         //转盘 Thưởng Online trực tuyến
                // 11, // :4,         //在线奖励 Vòng quay may mắn
                5, // :5,          //救援金 Cứu trợ hàng ngày
            ]
        }
    },
    computed: {
        activityTypes() {
            const types = this.$store.state.activitySwitchDetails.map(item => item.activityType);
            return this.events.filter(type => types.includes(type))
        }
    },
    methods:  {
        go(activityType) {
            this.$router.push("/m/events/" + activityType)
        }
    }

}