export const activity_base = {
    data() {
        return {
            currentActivity: {
                activityType: 0,
                awardType: 0,
                status:0,
                withdrawRate: 0
            },
        };
    },
    mounted() {
        Object.assign(this.currentActivity, this.$store.state.activitySwitchDetails.find(item => item.activityType === parseInt(this.$route.path.split('/').pop(), 10)))
    },
    methods: {
        intervalExpires(distance) {
            let that = this
            const timer = setInterval(function () {

                if (distance < 0) {
                    clearInterval(timer);
                    that.intervalDownElement = "0d 00:00:00";
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor(
                    (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
                );
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                that.intervalDownElement =
                    days + "d " + (hours < 10 ? "0" : '') + hours + ":" + (minutes < 10 ? "0" : '') + minutes + ": " + (seconds < 10 ? "0" : '') + seconds;
                distance -= 1000;
            }, 1000);
        },
    }
}