import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from '@/utils/common'
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type10 = {
    mixins: [activity_base],
    data() {
        return {
            res: {
                sys_now_time: 0,
                time_info: [],
                timepoint: [0, 0]
            },
            records: [],
            column: [
                {
                    label: this.$t("events_page.10.REDBAG_LIST.TEXT_TITLE1"),
                    prop: "record_time",
                    render: FieldRenderType.datetime_s,
                },
                {
                    label: this.$t("events_page.10.REDBAG_LIST.TEXT_TITLE2"),
                    prop: "nick_name",
                    render: FieldRenderType.hideStr,
                },
                {
                    label: this.$t("events_page.10.REDBAG_LIST.TEXT_TITLE3"),
                    prop: "award_score",
                    render: FieldRenderType.currency,
                },
                {
                    label: this.$t("events_page.10.REDBAG_LIST.TEXT_TITLE4"),
                    prop: "lucky_award",
                    render: FieldRenderType.currency,
                },
            ],
        }
    },
    mounted() {
        this.detail();
        this.query()
    },
    methods: {

        detail() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_REDPACK_INFO)
                .then((res) => {

                    this.res.sys_now_time = res['sys_now_time'] && res['sys_now_time'] > res['duration'] ? res['sys_now_time'] - res['duration'] : 0;
                    this.res.timepoint = res['timepoint'];
                    this.res.time_info = res['time_info'];
                })
                .catch(() => {})
        },
        submit() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_REDPACK_RAIN)
                .then((res) => {
                    if (res['code'] === 200) {
                        if (this.currentActivity.awardType  === 1) {
                            $toast.success({
                                icon: "passed",
                                message: this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                            });
                        } else if (this.currentActivity.awardType  === 2) {
                            $toast.success({
                                icon: "passed",
                                message: this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                            });
                        } else if (this.currentActivity.awardType  === 3) {
                            $toast.success({
                                icon: "passed",
                                message: this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                            });
                        } else {
                            $toast.success({
                                icon: "passed",
                                message: this.$t('redBagAward'),
                            });
                        }
                        this.detail();
                    } else {
                        window.$toast.fail(this.$t('redBagErr_'+res['code']));
                    }
                })
                .catch(() => {})
        },
        query() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_REDPACK_LOG)
                .then((res) => {
                    this.records = res['record_list']
                })
                .catch(() => {})
        },
    },
};
