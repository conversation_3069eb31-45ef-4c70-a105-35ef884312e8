import { activitybase } from "@/mixins/activity/activitybase";
import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type11 = {
  mixins: [activitybase, activity_base],
  data() {
    return {
      column: [
        {
          label: this.$t("events_page.11.RECORD_TOP_TIME"),
          prop: "str",
        },
        {
          label: this.$t("events_page.11.RECORD_TOP_TYPE"),
          prop: "turntable_type",
          render: FieldRenderType.customTemplate,
          customTemplate: this.customTemplate,
        },
        {
          label: this.$t("events_page.11.RECORD_TOP_AWARD"),
          prop: "award_money",
          render: FieldRenderType.formatGoldWithK,
        },
      ],
      column3: [
        { label: this.$t("events_page.10.REDBAG_LIST.TEXT_TITLE2"), prop: "user_id"},
        {
          label: this.$t("events_page.11.RECORD_TOP_TIME"),
          prop: "str",
        },
        {
          label: this.$t("events_page.11.RECORD_TOP_TYPE"),
          prop: "turntable_type",
          render: FieldRenderType.customTemplate,
          customTemplate: this.customTemplate,
        },
        {
          label: this.$t("events_page.11.RECORD_TOP_AWARD"),
          prop: "award_money",
          render: FieldRenderType.formatGoldWithK,
        },
      ],
      res: {
        now_score: 0,
        today_bet: 0,
        conf1000: [],
        conf5000: [],
        conf20000: [],
      },
      records: {
        info1: [],
        info2: [],
        info3: [],
      },
      wheelRes: {
        award_money: 0,
        id: 0,
        code: 0,
        need_score: 0
      },
      wheelIndex: 0,
      Details: {
        beginTime: 0,
        confs: [],
        endTime: 0,
        maxReward: 0,
        minChargeamount: 0,
        received: false,
        userYestodayChargeamount: 0,
      },
      Query: {
        ownnerRecords: [],
        winnerRecords: [],
      },
    };
  },
  created() {
    this.luckywheeldetail();
    this.queryluckywheel();
  },
  mounted() {
    this.$nextTick(()=>{
      this.$refs.myLucky.init();
    })
  },
  computed: {
    conf() {
      let conf = []
      switch (this.wheelIndex) {
        case 2:
          conf = this.res.conf20000
          break
        case 1:
          conf = this.res.conf5000
          break
        default:
          conf = this.res.conf1000
      }
      return conf
    }
  },
  methods: {
    customTemplate(value) {
      switch (value) {
        case 2:
          return this.$t("events_page.11.BTN_TYPE3_H_POINTS");
        case 1:
          return this.$t("events_page.11.BTN_TYPE2_H_POINTS");
        default:
          return this.$t("events_page.11.BTN_TYPE1_H_POINTS");
      }
    },
    // 抽奖结束会触发end回调
    endCallback() {
      if (this.wheelRes['code'] === 0 && this.wheelRes['id'] !== 0) {
        if (this.currentActivity.awardType  === 1) {
          $toast.success({
            icon: "passed",
            message: this.wheelRes.award_money
                      ? this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](this.wheelRes.award_money)} ): "Thanks",
          });
        } else if (this.currentActivity.awardType  === 2) {
          $toast.success({
            icon: "passed",
            message: this.wheelRes.award_money
                      ? this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](this.wheelRes.award_money)} ): "Thanks",
          });
        } else if (this.currentActivity.awardType  === 3) {
          $toast.success({
            icon: "passed",
            message: this.wheelRes.award_money
                      ? this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](this.wheelRes.award_money)} ): "Thanks",
          });
        } else {
          $toast.success({
            icon: "passed",
            message: this.wheelRes.award_money
                      ? this.$options.filters["currency"](this.wheelRes.award_money, true, false, true)
                      : "Thanks",
          });
        }
        this.luckywheeldetail();
      } else {
        window.$toast.fail(this.$t('startTurnTableErr_'+this.wheelRes['code']));
      }
    },
    luckywheeldetail() {
      this.$tdtcApi.getQueryInfo
        (TDTC_ROURE.QUERY_TUENTABLE_INFO)
        .then((res) => {
          this.res.now_score = res['now_score']
          this.res.today_bet = res['today_bet']
          this.res.conf1000 = []
          this.res.conf5000 = []
          this.res.conf20000 = []
          res['info'].forEach(item => {
            if (item['turntable_type'] === 2) {
              this.res.conf20000.push(item)
            } else if (item['turntable_type'] === 1) {
              this.res.conf5000.push(item)
            } else {
              this.res.conf1000.push(item)
            }
          })
        })
        .catch(() => {});
    },
    luckywheelstart() {

      let valid = true
      switch (this.wheelIndex) {
        case 0:
          if (this.res.now_score < 100000) valid = false
          break
        case 1:
          if (this.res.now_score < 500000) valid = false
          break
        case 2:
          if (this.res.now_score < 2000000) valid = false
          break
      }
      if (!valid) {
        window.$toast.fail(this.$t('startTurnTableErr_1'));
        return
      }
      let index = 0;
       this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_TUENTABLE, {
        'turntabletype': this.wheelIndex,
      })
          .then((res) => {
            // StartTurnTableResponse
            this.$refs.myLucky.play();
            Object.assign(this.wheelRes, res)
            for (let i = 0; i < this.conf.length; i++) {
              if (this.conf[i]['id'] === res['id']) {
                index = i;
              }
            }
          })
          .catch(() => {})
           .finally(() => {
             let that = this;
             setTimeout(() => {
               that.$refs.myLucky.stop(index);
             }, 0);
           });
    },
    queryluckywheel() {
      this.$tdtcApi.getQueryInfo
        (TDTC_ROURE.QUERY_TUENTABLE_RECORDS)
        .then((res) => {
          Object.assign(this.records, res);
        })
        .catch(() => {});
    },
  },
};
