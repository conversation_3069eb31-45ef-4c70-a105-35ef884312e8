import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type18 = {
    mixins: [activity_base],
    data() {
        return {
            conf: [],
            column: [
                {
                    label: this.$t('ad.panel.3.th.0'),
                    prop: "loss_money",
                    render: FieldRenderType.formatGold,

                },
                {
                    label: this.$t('ad.panel.3.th.1'),
                    prop: "award_money",
                    render: FieldRenderType.formatGold,
                },
            ],
        };
    },
    mounted() {
        this.query24();
    },
    methods: {
        query24() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_DAILY_RECHARGE_INFO)
                .then((res) => {
                    this.conf = res.conf
                })
                .catch(() => {})
        },
        submit() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_DAILY_RECHARGE)
                .then((res) => {
                    switch (res.code) {
                        case 1:
                            window.$toast.fail(this.$t('awardErr_1'));
                            break
                        case 2:
                            window.$toast.fail(this.$t('awardErr_2'));
                            break
                        case 3:
                            window.$toast.fail(this.$t('awardErr_3'));
                            break
                        case 4:
                            window.$toast.fail(this.$t("EXCHANGE_ERROR_CODE_7", { money: this.$options.filters['currency'](res['awardMoney'], true, false, true) }));
                            break
                        default:
                            let t_key = "awardSuccess"
                            if (this.currentActivity.awardType  === 1) {
                                t_key = "MONEY_AWARDSUCCESS";
                            } else if (this.currentActivity.awardType  === 2) {
                                t_key = "BONUS_AWARDSUCCESS";
                            } else if (this.currentActivity.awardType  === 3) {
                                t_key = "POINT_AWARDSUCCESS";
                            }
                            $toast.success({
                                icon: "passed",
                                message: this.$t(t_key, { money: this.$options.filters['formatGold'](res['award_money'])} ),
                            });
                    }
                })
                .catch(() => {})
        },
    },
};
