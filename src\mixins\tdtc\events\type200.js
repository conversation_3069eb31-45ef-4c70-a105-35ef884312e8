import { TDTC_ROURE } from "@/api/tdtc";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type200 = {
  mixins: [activity_base],
  data() {
    return {
      res: {},
    };
  },
  mounted() {
    this.detail();
  },
  methods: {
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_ACTIVE200_URL)
        .then((res) => {
          this.res = res;
        })
        .catch(() => {});
    },
  },
};
