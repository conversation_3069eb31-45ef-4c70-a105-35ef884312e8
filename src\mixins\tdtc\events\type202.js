import { TDTC_ROURE } from "@/api/tdtc";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type202 = {
  mixins: [activity_base],
  data() {
    return {
      res: {
        recharge_gift_apply_url: "",
        recharge_gift_money: 0,
        recharge_gift_result_url: "",
      },
    };
  },
  mounted() {
    this.query204();
  },
  methods: {
    go() {
      window.open(this.res.recharge_gift_apply_url)
    },
    query204() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_RECHARGE_GIFT)
        .then((res) => {
          Object.assign(this.res, res)
        })
        .catch(() => {});
    },
  },
};
