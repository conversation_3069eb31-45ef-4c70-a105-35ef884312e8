import {TDTC_ROURE} from "@/api/tdtc";
import moment from 'moment'
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type26 = {
  mixins: [activity_base],
  data() {
    return {
      res: {},
    };
  },
  mounted() {
    this.detail();
  },
    computed: {
      t() {
          const now = moment();
          const tomorrowStart = moment(now).startOf('day').add(1, 'days');
          iconsole(now.diff(tomorrowStart))
          return tomorrowStart.diff(now);

      }
    },
  methods: {
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_BETAWARD_INFO)
          .then((res) => {
            this.res = res
          })
          .catch(() => {})

    },
    submit(index) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_BET_AWARD, {
        getindex: index
      })
          .then((res) => {
            if (!res['code']) {
                if (this.currentActivity.awardType  === 1) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
                    });
                } else if (this.currentActivity.awardType  === 2) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
                    });
                } else if (this.currentActivity.awardType  === 3) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
                    });
                } else {
                    $toast.success({
                        icon: "passed",
                        message: this.$t('ACTIVITY_PANEL8.ACTIVITY8_GETERROR_STATUS_0'),
                    });
                }
              this.detail();
            } else {
              window.$toast.fail(this.$t('ACTIVITY_PANEL8.ACTIVITY8_GETERROR_STATUS_'+res['code']));
            }
          })
          .catch(() => {})
    },
  },
};
