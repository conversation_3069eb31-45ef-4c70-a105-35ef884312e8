import {TDTC_ROURE} from "@/api/tdtc";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type46 = {
  mixins: [activity_base],
  data() {
    return {
      showNum: false,
      timer: null,
      showWheel: true,
      showRes: false,
      showWithdraw: false,
      showShare: false,
      countDownElement: "0d 00:00:00",
      upIng: false,
      upIngText: "",
      percentage: 0,
      stepActive: 1,
      preCashAmount: 0,
      Details: {
        currentCashAmount: 0,
        currentGoldcoin: 0,
        currentRound: 0,
        currentRoundEndtime: 0,
        everyRoundActivityValidtime: 0,
        freeLotteryCount: 0,
        freegivingLotteryCount: 0,
        freegivingTimeInterval: 0,
        goldcoinToCashOfCash: 0,
        goldcoinToCashOfGoldcoin: 0,
        lotteryCount: 0,
        wheelConfigs: [],
        withdrawalAmount: 10000,
        withdrawalValidtime: 0,
        state: 0,
        promoteUrl: "",

        AgentBetScore: 0,
        AgentRechargeAmount: 0,
      },
      StartWheel: {
        code: 0,
        currentCashAmount: 0,
        currentFreeLotteryCount: 0,
        currentGoldCoin: 0,
        currentLotteryCount: 0,
        goldcoinToCashAmount: 0,
        rewardType: 0,
        rewardValue: 0,
        rewardWheelId: 0,
        state: 0,
      },
      rewardValue: 0,
      Query: {
        ownnerRecords: [],
      },
    };
  },
  mounted() {
    this.blocks[0].imgs = this.blocksImgs
    this.timer = setInterval(()=>{
      if (this.blocks[0].imgs[1].src === "img/activity/15/bg_5.png") {
        this.blocks[0].imgs[1].src = "img/activity/15/bg_5_1.png"
      } else {
        this.blocks[0].imgs[1].src = "img/activity/15/bg_5.png"
      }
    }, 500)
    if (this.$store.getters.isLogin) {
      this.detail();
    }
    // this.query();
  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  computed: {
    shareText() {
      return `Receba ${this.Details.withdrawalAmount / 100} BRL de graça,Pix SAQUE RÁPIDO `
    },
    showUrl() {
      return `${this.Details.promoteUrl}?code=${this.$store.state.account.userId}`;
    },
    cha() {
      return (this.Details.withdrawalAmount - this.Details.currentCashAmount)
    },
    btnText() {
      // if (this.upIng && this.rewardValue) {
      //   return "+" + this.upIngText
      // } else {
        return this.$store.state.token.token ? this.Details.freeLotteryCount + this.Details.lotteryCount : "1"
      // }
    },
  },
  methods: {
    share: function(type) {
      let url;
      switch (type) {
        case "facebook":
          url = "https://www.facebook.com/sharer/sharer.php?u=" + encodeURIComponent(this.showUrl) + "&t=" + encodeURIComponent(this.shareText)
          // url = "fb://share/?link=" + this.showUrl
          break;
        case "telegram":
          url = "https://t.me/share/url?url=" + encodeURIComponent(this.showUrl) + "&text=" + encodeURIComponent(this.shareText)
          break;
        case "twitter":
          url = "https://twitter.com/intent/tweet?text" + encodeURIComponent(this.shareText) + "&url=" + encodeURIComponent(this.showUrl)
          break;
        case "whatsapp":
          url = "https://api.whatsapp.com/send?text=".concat(this.shareText) + encodeURIComponent(this.showUrl)
          // href = "whatsapp://send?text=" + encodeURIComponent(this.shareText) + encodeURIComponent("\n\n" + this.showUrl) + "&via=lopscoop"
          // location.href = `https://api.whatsapp.com/send?phone=${t}&text=${this.$language.format("spins.Response")}` + this.shareTxt : location.href = "whatsapp://send?phone=+" + t + "&text=" + encodeURIComponent(this.$language.format("spins.Response") + this.shareTxt) + encodeURIComponent("\n\n" + this.url) + "&via=lopscoop"
          break;
        case "email":
          url = "mailto:?body=" + encodeURIComponent(this.shareText)
          break;
        case "sms":
          url = "sms:?addresses=" + "" + "&body=" + encodeURIComponent(this.shareText + this.showUrl)
          break;
      }

      const userAgent = navigator.userAgent;
      if (/Android/i.test(userAgent) && 'jsBridge' in window) {
        window.open("browser:" + url)
      } else {
        setTimeout(()=>{
          window.open(url)
        })
      }
    },
    numTo(finalNum) {
      this.upIng = true;

      let rate = 30;
      let time = 1000;
      let step = finalNum / (time / rate);
      let count = 1;
      let that = this
      let timer = setInterval(() => {
        count = count + step;
        that.upIngText = count.toFixed(0)
        if (count > finalNum) {
          count = finalNum;
          that.upIngText = count
          clearInterval(timer);
          timer = null;
          setTimeout(()=>{
            that.upIng = false;
          }, 1000)
        }
      }, rate);
    },
    percentageTo(start, end) {
      if (end < 0.01) return
      let rate = 30;
      let time = 1000;
      let step = end / (time / rate);
      let count = Number(start);
      let timer = setInterval(() => {
        count = count + step;
        this.percentage = count.toFixed(0)

        if (count > end || !end) {
          count = end;
          this.percentage = count
          clearInterval(timer);
          timer = null;
        }
      }, rate);
    },
    countExpires(endTime) {
      let that = this
      const timer = setInterval(function () {
        const now = new Date().getTime();
        const distance = endTime - now;

        if (distance < 0) {
          clearInterval(timer);
          that.countDownElement = "0d 00:00:00";
          return;
        }

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor(
          (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)
        );
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        that.countDownElement =
          days + "d " + (hours<10?"0":'') + hours + ":" + (minutes<10?"0":'') + minutes + ": " + (seconds<10?"0":'') + seconds;
      }, 1000);
    },
    valid() {
      if (!this.$store.getters.isLogin) {
        this.$store.commit("setActivity15Step", 0);
        if (this.$store.state.webType === 1) {
          this.$modal.show("loginPopupModal");
        } else {
          this.$router.push("/m/login").catch(()=>{})
        }
        return;
      }
      if (this.Details.state) {
        $toast.fail({
          message: this.$t("589"),
        });
        this.showWheel = false
        return;
      }
      this.luckywheelstart();
    },
    endCallback(blocksImgs, blocksImgsSelected, front = false) {
      this.preCashAmount = this.Details.currentCashAmount / 100;
      this.Details.currentCashAmount = this.StartWheel.currentCashAmount;
      this.Details.currentGoldCoin = this.StartWheel.currentGoldCoin;
      this.Details.freeLotteryCount = this.StartWheel.currentFreeLotteryCount;
      this.Details.lotteryCount = this.StartWheel.currentLotteryCount;
      this.Details.goldcoinToCashOfCash = this.StartWheel.goldcoinToCashAmount;
      this.Details.state = this.StartWheel.state;
      this.rewardValue = this.StartWheel.rewardType === 1 ? this.StartWheel.rewardValue / 100 : this.StartWheel.rewardValue;
      let that = this
      if (front) {
        setTimeout(() => {
          that.showWheel = false;
          that.showRes = true;
        }, 1600);
      } else {
        if (this.Details.state) {
          this.stepActive = 2
          setTimeout(()=>{that.showWithdraw = true}, 1300)
        }
      }
      this.numTo(this.rewardValue)
      this.percentageTo(this.percentage, (this.Details.currentCashAmount/this.Details.withdrawalAmount*100).toFixed(2))
      function startInterval() {
        let count = 0;
        let intervalId = setInterval(function() {
          if (count % 2 === 0) {
            that.blocks[0].imgs = blocksImgsSelected
          } else {
            that.blocks[0].imgs = blocksImgs
          }
          count++;
          if (count === 6) {
            clearInterval(intervalId);
            that.showNum = true
            setTimeout(()=>{
              that.showNum = false
            }, 1000)
          }
        }, 200);
      }
      startInterval();
    },
    luckywheelstart() {
      if (this.Details.freeLotteryCount + this.Details.lotteryCount === 0) return
      let index = 0;
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_EARNCASH_BY_PROMOTE_LOTTERY)
          .then((res) => {
            this.$refs.myLucky.play();
            for (let i = 0; i < this.Details.wheelConf.length; i++) {
              if (this.Details.wheelConf[i]['wheel_id'] === res['reward_wheel_id']) {
                index = i;
              }
            }
            let StartWheel = {
                  code: res['code'] ?? 0,
                  currentCashAmount: res['current_cash_amount'] ?? 0,
                  currentFreeLotteryCount: res['current_free_lottery_count'] ?? 0,
                  currentGoldCoin: res['current_gold_coin'] ?? 0,
                  currentLotteryCount: res['current_lottery_count'] ?? 0,
                  goldcoinToCashAmount: res['goldcoin_to_cash_amount'] ?? 0,
                  rewardType: res['reward_type'] ?? 0,
                  rewardValue: res['reward_value'] ?? 0,
                  rewardWheelId: res['reward_wheel_id'] ?? 0,
                  state: res['state'] ?? 0,
            }
            this.StartWheel = StartWheel;
          })
          .catch(() => {})
          .finally(() => {
            let that = this;
            setTimeout(() => {
              that.$refs.myLucky.stop(index);
            }, 0);
          });
    },
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_EARNCASH_BY_PROMOTE_DETAIL)
          .then((res) => {
            let Details = {
                  currentCashAmount: res['current_cash_amount'] ?? 0,
                  currentGoldcoin: res['current_goldcoin'] ?? 0,
                  currentRound: res['current_round'] ?? 0,
                  currentRoundEndtime: res['current_round_endtime'] * 1000 ?? 0,
                  everyRoundActivityValidtime: res['every_round_activity_validtime'] ?? 0,
                  freeLotteryCount: res['free_lottery_count'] ?? 0,
                  freegivingLotteryCount: res['freegiving_lottery_count'] ?? 0,
                  freegivingTimeInterval: res['freegiving_time_interval'] ?? 0,
                  goldcoinToCashOfCash: res['goldcoin_to_cash_of_cash'] ?? 0,
                  goldcoinToCashOfGoldcoin: res['goldcoin_to_cash_of_goldcoin'] ?? 0,
                  lotteryCount: res['lottery_count'] ?? 0,
                  wheelConf: res['wheel_configs'] ?? [],
                  withdrawalAmount: res['withdrawal_amount'] ?? 0,
                  withdrawalValidtime: res['withdrawal_validtime'] ?? 0,
                  state: res['state'] ?? 0,
                  promoteUrl: res['promote_url'] ?? "",

                  AgentBetScore: res['agent_bet_score'] ?? 0,
                  AgentRechargeAmount: res['agent_recharge_amount'] ?? 0,
            }
            // iconsole(Details)
            this.Details = Details
            this.countExpires(this.Details.currentRoundEndtime)
            this.percentageTo(0, (this.Details.currentCashAmount/this.Details.withdrawalAmount*100).toFixed(2))
            if (res.state === 1) {
              this.stepActive = 2
            }
          })
          .catch(() => {})
    },
    query() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_EARNCASH_BY_PROMOTE_LOG)
          .then((res) => {
            // QueryEarnCashByPromoteWithdrawalLogResp
            this.Query.ownnerRecords = res['ownner_records'] ?? [];
          })
          .catch(() => {})
    },
    withdrawal(round) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_EARNCASH_BY_PROMOTE_WITHDRAWAL, {round})
          .then((res) => {
            if (res['code'] === 200) {
              $toast.success({
                message: this.$options.filters['formatGold'](res['cash_amount']),
                icon: "passed",
              });
            } else {
              window.$toast.fail(this.$t('ACTIVITY_PROMOTE_TIP_ERROR_'+res['code']));
            }

            this.query();
          })
          .catch(() => {})
    },
  },
};
