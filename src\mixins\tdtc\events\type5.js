import {TDTC_ROURE} from "@/api/tdtc";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type5 = {
  mixins: [activity_base],
  data() {
    return {
      conf: [],
    };
  },
  mounted() {
    this.detail();
  },
  methods: {
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_DAILY_RESCUE_INFO)
          .then((res) => {
            this.conf = res.conf
          })
          .catch(() => {})
    },
    submit() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_DAILY_RESCUE)
          .then((res) => {
              switch (res.code) {
                  case 1:
                      window.$toast.fail(this.$t('awardErr_1'));
                      break
                  case 2:
                      window.$toast.fail(this.$t('awardErr_2'));
                      break
                  case 3:
                      window.$toast.fail(this.$t('awardErr_3'));
                      break
                  default:
                      let t_key = "awardSuccess"
                      if (this.currentActivity.awardType  === 1) {
                          t_key = "MONEY_AWARDSUCCESS";
                      } else if (this.currentActivity.awardType  === 2) {
                          t_key = "BONUS_AWARDSUCCESS";
                      } else if (this.currentActivity.awardType  === 3) {
                          t_key = "POINT_AWARDSUCCESS";
                      }
                      $toast.success({
                          icon: "passed",
                          message: this.$t(t_key, { money: this.$options.filters['formatGold'](res['award_money'])} ),
                      });
              }
          })
          .catch(() => {})
    },
  },
};
