import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from '@/utils/common'
import {activity_base} from '@/mixins/tdtc/events/activity_base'
import VueQr from 'vue-qr'

export const type51 = {
    components: {
        VueQr,
    },
    mixins: [activity_base],
    data() {
        return {
            share: false,
            liXi: false,
            res: {
                config: { // 类型:SrprActiveConfig,活动配置
                        active_start_time:       "", //类型:string,活动开始时间
                        active_end_time:         "", //类型:string,活动结束时间
                        highest_award:           0, //类型:int64,最高红包金额
                        scene_id:                0, //类型:int32,场景ID
                        invite_url:              "", //类型:string,邀请链接
                        redpacket_time_countdown:0, //类型:int32,红包雨倒计时
                        invite_reward_pro:       0, //类型:int32,用户通过推广链接注册并产生流水后,奖励被邀请人[首充金额的百分比]给邀请人,注:数值在0~100
                        withdraw_rate:           0, //类型:double,所有资金只需投注足够x1奖金即可提现
                        dl_gv_rg_amount:         0, //类型:int64,每日充值多少金额
                        dl_gv_rg_count:          0, //类型:int32,每日充值多少金额后获得多少次抽奖机会
                        dl_gv_rg_max:            0, //类型:int32,充值后获得的抽奖机会上限
                        dl_gv_invite_pcnt:       0, //类型:int32,邀请多人
                        dl_gv_invite_cnt:        0, //类型:int32,邀请多少人后获得多少次抽奖机会
                        dl_gv_invite_max:        0, //类型:int32,每日邀请上限
                        dl_rg:                   0, //类型:int64,红包雨有效用户资格定义:每日[充值多少金额]才能获得1次抽奖机会
                        dl_b:                    0, //类型:int64,红包雨有效用户资格定义:每日[下注多少金额]才能获得1次抽奖机会
                        time_configs:            [], //类型:[]SrprTimeConfig,场次配置
                },
                ranking_records: [], // 类型:[]SrprRankingRecord,排行榜记录
                lottery_count:    0, // 类型:int32,剩余抽奖次数
                extract_amount:   0, // 类型:int64,剩余提现金额
                lty_count_by_invite: 0, // 类型:int32,当日通过邀请获得的额外抽奖次数
                lty_count_by_recharge:     0, // 类型:int32,当日通过充值获得的额外抽奖次数
            },
            column: [
                {
                    label: "Xếp hạng",
                    prop: "ranking_id",
                },
                {
                    label: "Biệt danh người dùng",
                    prop: "nickname",
                    render: FieldRenderType.hideStr,
                },
                {
                    label: "Số lần lì xì",
                    prop: "packet_count",
                },
                {
                    label: "Số tiền lì xì",
                    prop: "packet_amount",
                    render: FieldRenderType.formatGold,
                },
            ],
        }
    },
    mounted() {
        this.query43();
    },
    computed: {
        showUrl() {
            return `${this.res.config.invite_url}?code=${this.$store.state.account.userId}`;
        },
        inTime() {

        }
    },
    methods: {

        query43() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.SRPR_DETAIL)
                .then((res) => {
                    Object.assign(this.res, res)
                })
                .catch(() => {})
        },
        get45() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.SRPR_EXTRACT)
                .then((res) => {
                    $toast.success({
                        icon: "passed",
                        message: `Tổng số tiền nhận được: ${this.$options.filters['formatGold'](res['extract_amount'])} VND`,
                    });
                    this.res.extract_amount = res['remaining_extract_amount']
                })
                .catch(() => {})
        },

    },
};
