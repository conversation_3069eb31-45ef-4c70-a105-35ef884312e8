import {TDTC_ROURE} from "@/api/tdtc";
import moment from "moment";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type52 = {
    mixins: [activity_base],
    data() {
        return {
            minDate:          moment().startOf('month').toDate(),
            maxDate:          new Date(),
            intervalDownElement: "0d 00:00:00",
            res:              {
                receive_records: [], // 类型:[]SubsidyReceiveData, 签到详情
                // {
                //     day:  		类型:int32, 天数标识,数值在(1,31)对应每个月的天数
                //     flag: 		类型:int32, 标识，//0:无,1:未领取,2:已领取
                //     month: 		类型:int32, 月份
                // }
                today_amount:           0, //类型:int64,今日补助金
                today_receive_duration: 0, //类型:int32,今日补助金领取倒计时
                today_expired_duration: 0, //类型:int32,今日补助金失效倒计时
                total_amount:           0, //类型:int64,总的可领取的补助金
                today_expired_amount:   0, //类型:int64,今日失效补助金
                total_expired_amount:   0, //类型:int64,总的失效补助金
                config:                 { //类型:SubsidyConfigData,活动相关配置
                    active_start_time:     "",// 类型:string,活动开始时间
                    active_end_time:       "",// 类型:string,活动结束时间
                    tax_to_award_pro:      0,// 类型:int32,税收到补助金的占比
                    receive_time:          0,// 类型:int32,补助金开始领取的时间(单位:小时)
                    receive_time_duration: 0,// 类型:int32,补助金持续领取时长(单位:小时)
                    active_end_duration:   0,// 类型:int64,活动结束倒计时(单位:秒)
                },
            },
        }
    },
    mounted() {
        this.query47()
    },
    methods: {
        query47() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_SUBSIDY_DETAILS)
                .then((res) => {
                    // QueryRechargeSignResponse
                    Object.assign(this.res, res)
                    this.intervalExpires(this.res.config.active_end_duration * 1000)
                })
                .catch(() => {
                })
        },
        get48() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_AGENT_RANK_AWARD_SUBSIDY_RECEIVE).then((res) => {
                $toast.success({
                    icon: "passed",
                    message: this.$t("awardSuccess", { money: this.$options.filters['formatGold'](res['receive_amount'])} ),
                });
                this.query47();
            })
                .catch(() => {
                })
        },
    }
}