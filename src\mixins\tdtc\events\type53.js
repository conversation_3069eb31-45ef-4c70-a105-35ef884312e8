import {TDTC_ROURE} from "@/api/tdtc";
import moment from "moment";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type53 = {
    mixins: [activity_base],
    data() {
        return {
            minDate: moment().startOf('month').toDate(),
            maxDate: new Date(),
            intervalDownElement: "0d 00:00:00",
            res:     {
                active_start_time:   "",//类型:string,活动开始时间
                active_end_time:     "",//类型:string,活动结束时间
                bind_phone:          0,//类型:int32,是否绑定了手机号（0:未绑定,1:已绑定）
                checkin_duration:    0,//类型:int32,签到倒计时(数值大于0的时候显示)
                checkin_day_id:      0,//类型:int32,签到天数标识
                today_is_checkin:    0,//类型:int32,今天是否签到(0:未签到,1:已签到)
                checkin_max_day_id:  0,//类型:int32,签到最大天数标识
                active_end_duration: 0,//类型:int64,活动结束倒计时
                last_check_in_time:  "",//类型:string,最后一次签到时间
            },
        }
    },
    mounted() {
        this.query49()
    },
    methods: {
        query49() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_FD_CHECK_DETAILS)
                .then((res) => {
                    Object.assign(this.res, res)
                    this.intervalExpires(this.res.active_end_duration * 1000)
                })
                .catch(() => {
                })
        },
        get50() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_FD_CHECK).then((res) => {
                $toast.success({
                    icon: "passed",
                    message: this.$t("ad.panel.19.SIGNIN_SUCCESS"),
                });
                this.query49()
            })
                .catch(() => {
                })
        },
    }
}