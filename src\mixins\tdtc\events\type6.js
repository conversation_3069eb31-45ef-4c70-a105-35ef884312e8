import { TDTC_ROURE } from "@/api/tdtc";
import {dot} from '@/mixins/dot'
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type6 = {
  data() {
    return {
      res: {},
      index: 0,
      swiperOptions: {
        slidesPerView: "auto",
        spaceBetween: 12,
      },
    };
  },
  mixins: [dot, activity_base],
  mounted() {
    this.event_enterTask()
    this.query18();
    this.$nextTick(()=>{
      let index = this.$route.query.index ?? 0
      this.index = parseInt(index)
    })
  },
  computed: {
    lab_progress() {
      let lab = [0, 0, 0, 0, 0];
      let task_finished = [0, 0, 0, 0, 0]
      let task_all = [0, 0, 0, 0, 0]
      let info = this.res.info ?? []
      for (let i = 0; i < info.length; i++) {
        let index = info[i]['task_type1'] - 1
        let flag = info[i]['flag'] ? info[i]['flag'] : 0

        if (flag !== 0) {
          task_finished[index] += 1
        }
        task_all[index] += 1
      }

      for (let i = 0; i < task_all.length; i++) {
        let all = task_all[i]
        let finished = task_finished[i]
        lab[i] = finished + " / " + all
      }
      return lab
    }
  },
  methods: {
    query18() {
      this.$tdtcApi.getQueryInfo
        (TDTC_ROURE.QUERY_DAILY_TASK_INFO)
        .then((res) => {
          this.res = res;
        })
        .catch(() => {});
    },
    getLivenessAward(index) {
      this.$tdtcApi.getQueryInfo
        (TDTC_ROURE.GET_DAILY_TASK_MONEY, {
          getindex: index,
        })
        .then((res) => {
          if (!res["code"]) {
            if (this.currentActivity.awardType  === 1) {
              $toast.success({
                icon: "passed",
                message: this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
              });
            } else if (this.currentActivity.awardType  === 2) {
              $toast.success({
                icon: "passed",
                message: this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
              });
            } else if (this.currentActivity.awardType  === 3) {
              $toast.success({
                icon: "passed",
                message: this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
              });
            } else {
              $toast.success({
                icon: "passed",
                message: this.$t("VIP.SUCCESS"),
              });
            }
            this.query18();
          } else if(res["code"] === 1) {
            window.$toast.fail(this.$t("awardErr_1"));
          } else if(res["code"] === 2) {
            window.$toast.fail(this.$t("awardErr_2"));
          } else if(res["code"] === 3) {
            window.$toast.fail(this.$t("growthFundErr_4"));
          } else if(res["code"] === 4) {
            window.$toast.fail(this.$t("startTurnTableErr_1"));
          }
        })
        .catch(() => {});
    },
    getTaskItemAward(item) {
      //iconsole(item)
      this.$tdtcApi.getQueryInfo
        (TDTC_ROURE.GET_DAILY_TASK_SCORE, {
          'tasktype1': item['task_type1'],
          'tasktype2': item['task_type2'],
        })
        .then((res) => {
          if (!res["code"]) {
            if (this.currentActivity.awardType  === 1) {
              $toast.success({
                icon: "passed",
                message: this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
              });
            } else if (this.currentActivity.awardType  === 2) {
              $toast.success({
                icon: "passed",
                message: this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
              });
            } else if (this.currentActivity.awardType  === 3) {
              $toast.success({
                icon: "passed",
                message: this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['award_money'])} ),
              });
            } else {
              $toast.success({
                icon: "passed",
                message: this.$t("VIP.SUCCESS"),
              });
            }
            this.query18();
          } else if (res["code"] === 2) {
            window.$toast.fail(this.$t("growthFundErr_7"));
          } else {
            window.$toast.fail(this.$t("growthFundErr_4"));
          }
        })
        .catch(() => {});
    },
  },
};
