import {TDTC_ROURE} from "@/api/tdtc";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type7 = {
  mixins: [activity_base],
  data() {
    return {
        QueryGrowLevelResponse: {
            growlevel: 0,
            experience: 0,
            upgrade_experience: 0,
        },
        res: {
            flag: 0,
            getindex: [],
            growfund_money: 0,
            conf: [],

        },
    };
  },
  mounted() {
      this.queryGrowLevel()
  },
  methods: {
    detail() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_GROW_FUND)
          .then((res) => {
            Object.assign(this.res, res)
          })
          .catch(() => {})

    },
      queryGrowLevel() {
          this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_GROW_LEVEL)
              .then((res) => {
                  Object.assign(this.QueryGrowLevelResponse, res)
                  this.detail();
              })
              .catch(() => {
              })
      },
    submit(index) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_GROW_FUND, {
        getindex: index
      })
          .then((res) => {
            if (!res['outcode']) {
                if (this.currentActivity.awardType  === 1) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                    });
                } else if (this.currentActivity.awardType  === 2) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                    });
                } else if (this.currentActivity.awardType  === 3) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                    });
                } else {
                    $toast.success({
                        icon: "passed",
                        message: this.$t('growthFundSuccess'),
                    });
                }
              this.detail();
            } else {
              window.$toast.fail(this.$t('growthFundErr_'+res['outcode']));
            }
          })
          .catch(() => {})
    },
  },
};
