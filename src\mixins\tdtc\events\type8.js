import {TDTC_ROURE} from "@/api/tdtc";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type8 = {
  mixins: [activity_base],
  data() {
    return {
      res: {},
    };
  },
  mounted() {
    this.query12();
  },
    computed: {
      totalAward() {
          if (this.res['conf']) {
              let award = 0;
              this.res['conf'].forEach((item)=>{
                  award += item['award_money']
              })
              return award
          }
          return 0
      }
    },
  methods: {
    query12() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_NEW_PEOPLE_AWARD)
          .then((res) => {
            this.res = res
          })
          .catch(() => {})
    },
    submit() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_NEW_PEOPLE_AWARD)
          .then((res) => {
            if (!res['outcode']) {
                if (this.currentActivity.awardType  === 1) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("MONEY_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                    });
                } else if (this.currentActivity.awardType  === 2) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("BONUS_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                    });
                } else if (this.currentActivity.awardType  === 3) {
                    $toast.success({
                        icon: "passed",
                        message: this.$t("POINT_AWARDSUCCESS", { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                    });
                } else {
                    $toast.success({
                        icon: "passed",
                        message: this.$t('newPeopleAwardSuccess'),
                    });
                }
                this.query12();
            } else {
              window.$toast.fail(this.$t('newPeopleAwardErr_'+res['outcode']));
            }
          })
          .catch(() => {})
    },
  },
};
