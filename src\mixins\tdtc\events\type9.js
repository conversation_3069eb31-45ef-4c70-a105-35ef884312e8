import {TDTC_ROURE} from "@/api/tdtc";
import moment from "moment";
import {activity_base} from '@/mixins/tdtc/events/activity_base'

export const type9 = {
    mixins: [activity_base],
    data() {
        return {
            minDate: moment().startOf('month').toDate(),
            maxDate: new Date(),
            selectDay: new Date().getDate(),
            signed: false,
            res: {
                nowyear: 0,
                nowmonth: 0,
                nowday: 0,
                info: [],
                need_recharge: 0,
                award_money: 0,
            },



            detail: [],
            minBetamount: 0,
            minChargeamount: 0,
            canReceiveDailyId: 0,
            userTodayBetamount: 0,
            userTodayChargeamount: 0,
            loggerDailyId: 0,
        }
    },
    mounted() {
        this.dailycheckindetail()
    },
    computed: {
        btnCheck() {
            return function (item) {
                return this.canReceiveDailyId && this.loggerDailyId+1 === item.dailyId
            };
        }
    },
    methods: {
        dailycheckindetail() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_RECHARGE_SIGN)
                .then((res) => {
                    // QueryRechargeSignResponse
                    Object.assign(this.res, res)
                })
                .catch(() => {})
        },
        dailycheckin() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_RECHARGE_SIGN, {getindex: this.selectDay}).then((res) => {
                    if (res['outcode']) {
                        if (res['outcode'] === 4) {
                            window.$toast.fail(this.$t('rechargeSignErr_' + res["outcode"], { money: (this.res['need_recharge'] / 100) }));
                        } else {
                            window.$toast.fail(this.$t('rechargeSignErr_' + res["outcode"]));
                        }
                    } else {
                        let t_key = "awardSuccess"
                        if (this.currentActivity.awardType  === 1) {
                            t_key = "MONEY_AWARDSUCCESS";
                        } else if (this.currentActivity.awardType  === 2) {
                            t_key = "BONUS_AWARDSUCCESS";
                        } else if (this.currentActivity.awardType  === 3) {
                            t_key = "POINT_AWARDSUCCESS";
                        }
                        $toast.success({
                            icon: "passed",
                            message: this.$t(t_key, { money: this.$options.filters['formatGold'](res['awardmoney'])} ),
                        });
                        this.dailycheckindetail()
                    }
                })
                .catch(() => {})
        },
    }
}