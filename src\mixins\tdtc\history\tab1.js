import moment from "moment";
import {DATA_NAME, FieldRenderType, GAME_TYPE, getFirmcode, getFirmtype, getWebGameTitle} from "@/utils/common";
import { TDTC_ROURE } from "@/api/tdtc";

export const tab1 = {
  data() {
    return {
      hasMore: true,
      processing: false,
      column: [
        {
          label: this.$t("ONLINEGAMEDETAIL.TIP_SHOWTYPE_2_1"),
          prop: "orderNo",
        },
        {
          label: this.$t("ONLINEGAMEDETAIL.TIP_SHOWTYPE_2_2"),
          prop: "betTime",
        },
        {
          label: this.$t("ONLINEGAMEDETAIL.TIP_SHOWTYPE_2_3"),
          prop: "betAmount",
          default: 0,
          render: FieldRenderType.formatGold,
        },
        {
          label: this.$t("ONLINEGAMEDETAIL.TIP_SHOWTYPE_2_4"),
          prop: "netAmount",
          default: 0,
          render: FieldRenderType.formatGold,
        },
      ],
      form: {
        type: 0,
        page: 1,
      },
      res: {
        code: 0,
        data: {
          data: [],
          totalCount: 0,
          otherData: null,
        },
      },
    };
  },
  computed: {
    types() {
      let list = []
      Object.values(GAME_TYPE).forEach(kindId => {
        list.push(getWebGameTitle(kindId))
      })
      return list
    }
  },
  methods: {
    typeChange(value, index) {
      this.form.type = index;
      this.showPicker = false
    },
    search(paginate = false) {
      if (!paginate) {
        this.form.page = 1;
        this.finished = false;
        this.res = {
          code: 0,
          data: {
            data: [],
            totalCount: 0,
            otherData: null,
          }
        }
      }
      this.processing = true;

      this.$tdtcApi.getThirdGame("queryBetRecord", {
        firmtype: getFirmtype(Object.values(GAME_TYPE)[this.form.type]),
        firmcode: getFirmcode(Object.values(GAME_TYPE)[this.form.type]),
        begintime: (this.form.beginTime / 1000) | 0,
        endtime: (this.form.endTime / 1000) | 0,
        page: this.form.page,
        rows: 15,
      })
          .then((res) => {
            if (this.$store.state.webType === 1) {
              if (res["data"]["data"]) this.res = res;
            } else {
              if (res["data"]["data"]) {
                this.res.data.data = this.res.data.data.concat(res["data"]["data"]);
                this.res.data.totalCount = res['data']['totalCount']
                this.res.data.otherData = res['data']['otherData']
                this.form.page++;
              } else {
                this.hasMore = false;
              }

              this.loading = false;
            }
          })
          .catch(() => {
            this.hasMore = false;
          })
          .finally(() => {
            this.processing = false;
          });
    },
  },
};
