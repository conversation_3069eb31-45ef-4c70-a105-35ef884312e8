import {TDTC_ROURE} from "@/api/tdtc";

export const rank = {
    data() {
        return {
            tab0: {
                rank_data:       0,
                rankinglistinfo: [],
            },
            tab1: {
                rank_data:       0,
                rankinglistinfo: [],
            },
            tab2: {
                rank_data:       0,
                rankinglistinfo: [],
            }
        }
    },
    computed: {
        inRank0() {
            let index = -1
            this.tab0.rankinglistinfo.forEach((v, i)=>{
                if (v['user_id'] === this.$store.state.account.userId) {
                    index = i
                }
            })
            iconsole(index)
            return index

        },
        inRank1() {
            let index = -1
            this.tab1.rankinglistinfo.forEach((v, i)=>{
                if (v['user_id'] === this.$store.state.account.userId) {
                    index = i
                }
            })
            return index
        },
        inRank2() {
            let index = -1
            this.tab2.rankinglistinfo.forEach((v, i)=>{
                if (v['user_id'] === this.$store.state.account.userId) {
                    index = i
                }
            })
            return index
        },
    },
    methods:  {
        queryTab0() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_WIN_SCORE_RANK)
                .then((res) => {
                    // GetRankingListResponse
                    Object.assign(this.tab0, res)
                })
                .catch(() => {
                })
        },
        queryTab1() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_ONLINE_TIME_RANK)
                .then((res) => {
                    // GetRankingListResponse
                    Object.assign(this.tab1, res)
                })
                .catch(() => {
                })
        },
        queryTab2() {
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_GROW_LEVEL_RANK)
                .then((res) => {
                    // GetRankingListResponse
                    Object.assign(this.tab2, res)
                })
                .catch(() => {
                })
        },
    }
}