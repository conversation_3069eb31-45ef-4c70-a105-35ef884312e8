import { TDTC_ROURE } from "@/api/tdtc";
import {splitNumber} from '@/utils/common'

export const recharge = {
  data() {
    return {
      tabIndex: 0,
      btnNum: [],
      datas: {
        cryptoPay: [],
        bankTransfer: [],
        alipay: [],
        guaguaka: [],
        onlinePayment: [],
      },


      refresh: false,
      bankArr: [
        "VIB",
      ],
      bankCodeArr: [
        "VIB",
      ],

      selectBankName: false,
      bankNameIndex: -1,
      walletIndex: -1,
      withdrawAmount: null,
      readonly: {
        tab0: false,
        tab1: false,
        tab2: false,
      },
      UserScoreInfoResponse: {
        bonus_score: 0,
        info: {
          score: 0,
          insurescore: 0,
          point: 0,
        },
      },
      GetExchangeBindInfoResponse: {
        bank_name: "",
        bank_account: "",
        bank_desposit: "",
        alipay_account: "",
        alipay_name: "",
        wallet_account: "",
        wallet_name: "",
      },
      WalletConfigResp: {
        name_list: [],
      },
      ApplyExchangeRespone: {
        val: 0
      }
    };
  },
  mounted() {

  /*    RECHANGE_TITLE_1: "BANK", //银行
        RECHANGE_TITLE_2: "MOMO", //momo
        RECHANGE_TITLE_3: "ONLINE RECHARGE", //在线
        RECHANGE_TITLE_4: "Recharge history", //记录
        RECHANGE_TITLE_5: "GIFTCODE", //兑换码
        RECHANGE_TITLE_6: "CARD", //刮刮卡
        RECHANGE_TITLE_7: "TIỀN\nĐIỆN TỬ", //加密货币*/
    // let shoplab = splitNumber(716892)
    let shoplab = splitNumber(this.$store.state.configs.shoplab)
    for (let index = 0; index < shoplab.length; index++) {
      if (shoplab[index] > 0) {
        this.btnNum.push(shoplab[index])
      }
    }
    this.btnNum = this.btnNum.includes(3) ? this.btnNum.filter(item => item !== 8 && item !== 9) : this.btnNum

    this.btnNum.push(5)
    // this.btnNum.push(4)
    this.query()
  },
  methods: {
    query() {
      this.$tdtcApi.getRecharge({
        method:'get',
        url:'/front/rechcfg/map',
        params: {
          userId: this.$store.state.account.userId,
          signature: this.$store.state.token.token
        }
      })
        .then((res) => {
          this.progressResult(res)
        })
        .catch(() => {
          let result = {
            success: true,
            t: {
              "rechCfgs": [
                {
                  "id": 5700,
                  "rechType": "onlinePayment",
                  "rechName": "Online Pay",
                  "payeeName": "THẺ CÀO",
                  "para1": "Viettel:0.82,Mobifone:0.8,Vinaphone:0.72,Vietnamobile:0.62",
                  "minMoney": 1000000,
                  "maxMoney": *********,
                  "onlineTypeId": 5700,
                  "onlineType": 101,
                  "pcMobile": 0,
                  "banks": [
                    {
                      "value": "Viettel",
                      "ico": "Viettel",
                      "bankName": "Viettel"
                    },
                    {
                      "value": "Mobifone",
                      "ico": "Mobifone",
                      "bankName": "Mobifone"
                    },
                    {
                      "value": "Vinaphone",
                      "ico": "Vinaphone",
                      "bankName": "Vinaphone"
                    },
                    {
                      "value": "Vietnamobile",
                      "ico": "Vietnamobile",
                      "bankName": "Vietnamobile"
                    }
                  ],
                  "sort": 0,
                  "onlineTypeFlag": 2,
                  "currency": ""
                },
                {
                  "id": 5738,
                  "rechType": "cryptoPay",
                  "rechName": "TIỀN ĐIỆN TỬ",
                  "payeeName": "TRC20",
                  "payee": "TFZvEjxPRV9JZkgSXB7MnQqQAbirktDw9y",
                  "minMoney": 10,
                  "maxMoney": 50000,
                  "rebateFee": 0.01,
                  "sort": 0,
                  "exchangeRate": "25560",
                  "currency": "USDT"
                },
                {
                  "id": 5728,
                  "rechType": "bankTransfer",
                  "rechName": "Online Pay",
                  "payeeName": "NGÂN HÀNG CHUYỂN KHOẢN 5",
                  "remark2": "世界",
                  "minMoney": ********,
                  "maxMoney": *********00,
                  "onlineTypeId": 6885,
                  "onlineType": 101,
                  "pcMobile": 0,
                  "sort": 1,
                  "onlineTypeFlag": 0
                },
                {
                  "id": 5725,
                  "rechType": "onlinePayment",
                  "rechName": "Online Pay",
                  "payeeName": "NGÂN HÀNG ONLINE",
                  "remark2": "V8pay",
                  "minMoney": 5000000,
                  "maxMoney": ***********,
                  "onlineTypeId": 6715,
                  "onlineType": 101,
                  "pcMobile": 0,
                  "sort": 3,
                  "onlineTypeFlag": 0,
                  "currency": ""
                },
                {
                  "id": 457,
                  "rechType": "onlinePayment",
                  "rechName": "Online Pay",
                  "payeeName": " NGÂN HÀNG Chuyển khoản",
                  "minMoney": ********,
                  "maxMoney": ***********,
                  "onlineTypeId": 5517,
                  "onlineType": 101,
                  "pcMobile": 0,
                  "sort": 3,
                  "onlineTypeFlag": 0,
                  "currency": ""
                },
                {
                  "id": 5739,
                  "rechType": "onlinePayment",
                  "rechName": "Online Pay",
                  "payeeName": "NGÂN HÀNG CHUYỂN KHOẢN 3",
                  "remark2": "PAYSPEC",
                  "minMoney": 5000000,
                  "maxMoney": *********00,
                  "onlineTypeId": 7196,
                  "onlineType": 101,
                  "pcMobile": 0,
                  "sort": 4,
                  "onlineTypeFlag": 0
                },
                {
                  "id": 5740,
                  "rechType": "onlinePayment",
                  "rechName": "Online Pay",
                  "payeeName": "MOMO3",
                  "remark2": "FastPay MOMO",
                  "minMoney": 5000000,
                  "maxMoney": **********,
                  "onlineTypeId": 7788,
                  "onlineType": 102,
                  "pcMobile": 0,
                  "sort": 6,
                  "onlineTypeFlag": 0
                }
              ],
              "rechTypes": [
                {
                  "name": "Online Pay",
                  "value": "onlinePayment"
                },
                {
                  "name": "Bank Transfer",
                  "value": "bankTransfer"
                },
                {
                  "name": "MOMO Pay",
                  "value": "alipay"
                },
                {
                  "name": "Weixin Pay",
                  "value": "weixin"
                },
                {
                  "name": "CFT Pay",
                  "value": "cft"
                },
                {
                  "name": "System Add",
                  "value": "adminAddMoney"
                },
                {
                  "name": "cryptoPay",
                  "value": "cryptoPay"
                }
              ]
            }
          }

          // this.progressResult(result)

        });
    },
    progressResult(result) {
      if (!result['success']) {
        window.$toast.fail(this.$t("501"));
      } else {
        let data = result["t"];
        let rechTypes = data["rechTypes"];
        let rechCfgs = data["rechCfgs"];

        for (let j = 0; j < rechCfgs.length; j++) {
          let banksInfo = rechCfgs[j];
          let type = banksInfo["rechType"]
          let info = {};

          info["id"] = banksInfo["id"]
          info["min"] = banksInfo["minMoney"]
          info["max"] = banksInfo["maxMoney"]
          info["bankName"] = banksInfo["rechName"]
          info["bankUser"] = banksInfo["payeeName"]
          info["pageDesc"] = banksInfo["pageDesc"] || ""

          if (type === "cryptoPay") {
            //usdt
            info["payee"] = banksInfo["payee"]            //虚拟币地址
            info["id"] = banksInfo["id"]                //支付ID
            info["exchangeRate"] = banksInfo["exchangeRate"]       //汇率
            info["rebateFee"] = banksInfo["rebateFee"]       //手续费
            info["currency"] = banksInfo["currency"]
            this.datas.cryptoPay.push(info)
          } else if (type === "bankTransfer") {
            info["bankAccount"] = banksInfo["payee"]
            info["qr"] = banksInfo["qrCode"]
            info["id"] = banksInfo["id"]
            this.datas.bankTransfer.push(info)
          } else if (type === "alipay") {
            info["bankAccount"] = banksInfo["payee"]
            info["qr"] = banksInfo["qrCode"]
            this.datas.alipay.push(info)
          } else if (type === "onlinePayment") {
            if (banksInfo["onlineTypeFlag"] === 2) {
              info["bankName"] = banksInfo["payeeName"]
              info["onlineType"] = banksInfo["onlineType"]
              info["onlineTypeId"] = banksInfo["onlineTypeId"]
              info["banks"] = banksInfo["banks"]
              info["para1"] = banksInfo["para1"]
              this.datas.guaguaka = info
            } else {
              info["onlineTypeFlag"] = banksInfo["onlineTypeFlag"]    //1跳转到外部
              info["bankName"] = banksInfo["payeeName"]
              info["onlineType"] = banksInfo["onlineType"]
              info["onlineTypeId"] = banksInfo["onlineTypeId"]
              info["banks"] = banksInfo["banks"]
              this.datas.onlinePayment.push(info)
            }
          }
        }
      }
    },
  },
};
