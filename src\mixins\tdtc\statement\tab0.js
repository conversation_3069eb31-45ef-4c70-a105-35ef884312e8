import moment from "moment";
import {DATA_NAME, FieldRenderType} from "@/utils/common";
import { TDTC_ROURE } from "@/api/tdtc";


export const tab0 = {
    data() {
        return {
            hasMore: true,
            processing: false,
            column: [
                {
                    label: this.$t("USERDETAIL.TIP_SHOWTYPE_1_1"),
                    prop: "datestr",
                },
                {
                    label: this.$t("USERDETAIL.TIP_SHOWTYPE_1_3"),
                    prop: "total_score",
                    default: 0,
                    render: FieldRenderType.currency
                },
                {
                    label: this.$t("USERDETAIL.TIP_SHOWTYPE_1_4"),
                    prop: "change_score",
                    default: 0,
                    render: FieldRenderType.currency
                },
            ],
            types: Object.values(DATA_NAME),
            form: {
                type: 1,
                page: 1,
            },
            res: {
                info: [],
                data1: 0,
                data2: 0,
            },
        };
    },
    methods: {
        typeChange(value, index) {
            this.form.type = index;
            this.showPicker = false
        },
        search(paginate = false) {
            if (!paginate) {
                this.form.page = 1;
                this.finished = false;
                this.res.info = [];
            }
            this.processing = true;


            let startDate = moment(new Date(Number(this.form.beginTime)));
            let endDate = moment(new Date(Number(this.form.endTime)));
            let today = moment(new Date());
            this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_MONEY_INFO, {
                type: Number(Object.keys(DATA_NAME)[this.form.type]),
                page: this.form.page,
                beginday: today.diff(startDate, "days"),
                endday: today.diff(endDate, "days"),
            })
                .then((res) => {
                    // QueryMoneyInfoResponse
                    if (this.$store.state.webType === 1) {
                        if (res["info"]) this.res.info = res["info"];
                    } else {
                        if (res["info"]) {
                            this.res.info = this.res.info.concat(res["info"]);
                            this.form.page++;
                        } else {
                            this.hasMore = false;
                        }

                        this.loading = false;
                    }
                })
                .catch(() => {
                    this.hasMore = false;
                })
                .finally(() => {
                    this.processing = false;
                });
        },
    },
};
