import { TDTC_ROURE } from "@/api/tdtc";
import { merge } from 'lodash';
import {dot} from '@/mixins/dot'
import { SHA256 } from "crypto-js";

export const withdraw = {
  mixins: [dot],
  data() {
    return {
      refresh: false,
      bankArr: [
        "VIB",
        "SHBVN",
        "VPBank",
        "BIDV",
        "Vietinbank",
        "SHB",
        "Abbank",
        "AgriBank",
        "VietcomBank",
        "Techcombank",
        "ACB",
        "SCB",
        "MB",
        "EximBank",
        "SACOMBANK",
        "DongABank",
        "GPBank",
        "Saigonbank",
        "PGBank",
        "OceanBank",
        "NamABank",
        "TPBank",
        "HDBank",
        "VAB",
        "OCB",
        "SeaBank",
        "MSB",
        "VietBank",
        "CAKE",
        "CBBank",
        "CIMB",
        "HSBC",
        "IVB",
        "KienlongBank",
        "NCB",
        "VietCapitalBank",
        "WooriBank",
        "NGAN HANG TMCP BAO VIET (BVB)",
        "NGAN HANG LIEN DOANH VIET - NGA (VRB)",
        "NGAN HANG TMCP BUU DIEN LIEN VIET (LPB)",
        "NGAN HANG TMCP DAI CHUNG VIET NAM (PVCOMBANK)",
      ],
      bankCodeArr: [
        "VIB",
        "SHBVN",
        "VPBank",
        "BIDV",
        "Vietinbank",
        "SHB",
        "Abbank",
        "AgriBank",
        "VietcomBank",
        "Techcombank",
        "ACB",
        "SCB",
        "MB",
        "EximBank",
        "SACOMBANK",
        "DongABank",
        "GPBank",
        "Saigonbank",
        "PGBank",
        "OceanBank",
        "NamABank",
        "TPBank",
        "HDBank",
        "VAB",
        "OCB",
        "SeaBank",
        "MSB",
        "VietBank",
        "CAKE",
        "CBBank",
        "CIMB",
        "HSBC",
        "IVB",
        "KienlongBank",
        "NCB",
        "VietCapitalBank",
        "WooriBank",
        "NGAN HANG TMCP BAO VIET (BVB)",
        "NGAN HANG LIEN DOANH VIET - NGA (VRB)",
        "NGAN HANG TMCP BUU DIEN LIEN VIET (LPB)",
        "NGAN HANG TMCP DAI CHUNG VIET NAM (PVCOMBANK)",
      ],
      tabIndex: 0,
      selectBankName: false,
      selectWallet: false,
      bankNameIndex: -1,
      walletIndex: -1,
      withdrawAmount: null,
      withdrawPassword: "",
      showPassword: false,
      sliderValue: 0,
      readonly: {
        tab0: false,
        tab1: false,
        tab2: false,
      },
      UserScoreInfoResponse: {
        bonus_score: 0,
        info: {
          score: 0,
          insurescore: 0,
          point: 0,
        },
      },
      GetExchangeBindInfoResponse: {
        bank_name: "",
        bank_account: "",
        bank_desposit: "",
        alipay_account: "",
        alipay_name: "",
        wallet_account: "",
        wallet_name: "",
        set_passwd: false,
      },
      QueryWithDrawCondition: false,
      QueryWithDrawConditionResp: {
        phone: false,
        telegram: false,
        zalo: false,
        email: false,
        birthday: false,
        realname: false,
        withdraw_bet: 0,
        signal: false,
        idcard: false,
      },
      WalletConfigResp: {
        name_list: [],
      },
      ApplyExchangeRespone: {
        val: 0
      }
    };
  },
  mounted() {
    this.query39();
    this.query27();
    this.query41();
  },
  computed: {
    walletNames() {
      return this.WalletConfigResp.name_list.map(
        (wallet) => wallet["wallet_name"]
      );
    },
  },
  watch: {
    tabIndex: function (n, o) {
      if (n === 2 && !this.WalletConfigResp.name_list.length) {
        this.query205();
      } else if (n === 1) {
        this.$tdtcApi
          .getQueryInfo(TDTC_ROURE.QUERY_CRYPTO_RATE)
          .then((res) => {
            if (res['val']) this.ApplyExchangeRespone.val = res['val']
          })
          .catch(() => {});
      }
    },
    withdrawAmount: function (n, o) {
      if (n === 0) {
        this.withdrawAmount = null
      }
      if (n > this.UserScoreInfoResponse.info.insurescore / 100) {
        this.withdrawAmount = this.UserScoreInfoResponse.info.insurescore / 100
      }
    }
  },
  methods: {
    onInputSlider(value) {
      this.withdrawAmount = value
      // iconsole('当前值：' + value);
    },
    onBlur() {
      if (this.tabIndex === 1) {
        let step = Math.floor(this.withdrawAmount / (this.ApplyExchangeRespone.val / 100));
        this.withdrawAmount = (this.ApplyExchangeRespone.val / 100) * step
      }
    },
    query205() {
      this.$tdtcApi
        .getQueryInfo(TDTC_ROURE.QUERY_WALLET_NAME)
        .then((res) => {
          if (res["name_list"])
            this.WalletConfigResp.name_list = res["name_list"];
          this.GetExchangeBindInfoResponse.wallet_name
            ? (this.walletIndex = this.walletNames.indexOf(
                this.GetExchangeBindInfoResponse.wallet_name
              ))
            : (this.walletIndex = -1);
        })
        .catch(() => {});
    },
    query39() {
      this.refresh = true;
      this.$tdtcApi
        .getQueryInfo(TDTC_ROURE.QUERY_USER_SCORE_INFO)
        .then((res) => {
          // UserScoreInfoResponse
          this.UserScoreInfoResponse = merge(this.UserScoreInfoResponse, res);
        })
        .catch(() => {})
        .finally(() => {
          setTimeout(() => {
            this.refresh = false;
          }, 600);
        });
    },
    query27() {
      this.$tdtcApi
        .getQueryInfo(TDTC_ROURE.QUERY_EXCHANGE_BIND_INFO)
        .then((res) => {
          // GetExchangeBindInfoResponse
          Object.assign(this.GetExchangeBindInfoResponse, res);
          res["bank_desposit"]
            ? (this.bankNameIndex = this.bankCodeArr.indexOf(res["bank_desposit"]))
            : (this.bankNameIndex = -1);
          res["wallet_name"] && this.walletNames.length
            ? (this.walletIndex = this.walletNames.indexOf(res["wallet_name"]))
            : (this.walletIndex = -1);
          if (res["bank_name"]) {
            this.readonly.tab0 = true;
          }
          if (res["alipay_account"]) {
            this.readonly.tab1 = true;
          }
          if (res["wallet_account"]) {
            this.readonly.tab2 = true;
          }
        })
        .catch(() => {});
    },
    query41() {
      this.$tdtcApi
          .getQueryInfo(TDTC_ROURE.QUERY_WITHDRAW_CONDITION)
          .then((res) => {
            Object.assign(this.QueryWithDrawConditionResp, res);
            if (
                this.QueryWithDrawConditionResp.phone &&
                this.QueryWithDrawConditionResp.telegram &&
                this.QueryWithDrawConditionResp.zalo &&
                this.QueryWithDrawConditionResp.email &&
                this.QueryWithDrawConditionResp.birthday &&
                // this.QueryWithDrawConditionResp.realname &&
                !this.QueryWithDrawConditionResp.withdraw_bet &&
                this.QueryWithDrawConditionResp.signal &&
                this.QueryWithDrawConditionResp.idcard
            ) {
              this.QueryWithDrawCondition = true
            }
          })
          .catch(() => {});
    },
    do1023() {
      let data;
      switch (this.tabIndex) {
        case 0:
          data = {
            bind_type: 2,
            account_data: this.GetExchangeBindInfoResponse.bank_account,
            account_owner: this.GetExchangeBindInfoResponse.bank_name,
            account_name: this.bankCodeArr[this.bankNameIndex],
          };
          break;
        case 1:
          data = {
            bind_type: 1,
            account_data: this.GetExchangeBindInfoResponse.alipay_account,
            account_owner: "TRC20",
          };
          break;
        case 2:
          data = {
            bind_type: 3,
            account_data: this.GetExchangeBindInfoResponse.wallet_account,
            wallet_id: this.WalletConfigResp.name_list.filter(
              (item) =>
                item["wallet_name"] === this.walletNames[this.walletIndex]
            )[0]["wallet_id"],
          };
          break;
      }
      // SetExchangeBindInfo
      this.event_withdrawClick()
      this.$tdtcApi
        .getQueryInfo(TDTC_ROURE.HALL_BIND_EXCHANGE, data)
        .then((res) => {
          // SetExchangeBindInfoRespose
          if (res["code"] === 200) {
            $toast.success({
              icon: "passed",
              message: this.$t("1012"),
            });
            this.query27();
          } else {
            if (![1, 2, 3].includes(res["code"])) {
              window.$toast.fail(this.$t("1013"));
            }

            if (this.tabIndex === 0) {
              window.$toast.fail(this.$t("bind_bank_error_" + res["code"]));
            } else if (this.tabIndex === 1) {
              if (res["code"] === 1) {
                window.$toast.fail(this.$t("bind_bank_error_" + res["code"]));
              } else {
                window.$toast.fail(this.$t("bind_USDT_error_" + res["code"]));
              }
            } else if (this.tabIndex === 2) {
              if (res["code"] === 1) {
                window.$toast.fail(this.$t("bind_bank_error_" + res["code"]));
              } else {
                window.$toast.fail(this.$t("bind_Wallet_error_" + res["code"]));
              }
            }
          }
        })
        .catch(() => {});
    },
    do1024(agree_rate = 0) {
      if (this.GetExchangeBindInfoResponse.set_passwd) {
        if (this.withdrawPassword.length < 6 || this.withdrawPassword.length > 20) {
          window.$toast.fail("Độ dài mật khẩu không đáp ứng yêu cầu 6-20 ký tự");
          return
        }
      }
      let req_type;
      switch (this.tabIndex) {
        case 0:
          req_type = 2;
          break;
        case 1:
          req_type = 1;
          break;
        case 2:
          req_type = 3;
          break;
      }
      this.$tdtcApi
        .getQueryInfo(TDTC_ROURE.HALL_APPLY_EXCHANGE, {
          req_type: req_type,
          exchange_money: this.withdrawAmount * 100,
          passwd: this.GetExchangeBindInfoResponse.set_passwd ? SHA256(this.withdrawPassword).toString() : "",
          agree_rate: agree_rate
        })
        .then((res) => {
          if (res["code"] === 200) {
            $toast.success({
              icon: "passed",
              message: this.$t("1022", [
                this.$options.filters["formatGold"](res["service_charge"] ? res["val"] - res["service_charge"] : res["val"]),
              ]),
            });
            this.withdrawAmount = null;
            this.query39();
          } else if (res["code"] === 24) {
            if (res["service_charge"]) {
              window.$Dialog.confirm({
                title: this.$t("in_popup_prompt"),
                message: this.$t("EXCHANGE_ERROR_CODE_24_1", {
                  money: this.$options.filters["formatGold"](res["val"]),
                  charge: this.$options.filters["formatGold"](res["service_charge"]),
                }),
                confirmButtonText: this.$t("ok"),
                cancelButtonText: this.$t("button_cancel"),
              })
                  .then(() => {
                    this.do1024(1)
                  })
                  .catch(() => {
                    // on cancel
                  });
            } else {
              window.$toast.fail(
                  this.$t("EXCHANGE_ERROR_CODE_" + res["code"], {
                    money: this.$options.filters["formatGold"](res["val"]),
                  })
              );
            }
          } else if (!res["code"]) {
            window.$toast.fail(this.$t("EXCHANGE_ERROR_CODE_0"));
          } else if ([1, 2, 3, 8, 14, 15, 16, 17].includes(res["code"])) {
            window.$toast.fail(this.$t("EXCHANGE_ERROR_CODE_" + res["code"]));
          } else if ([4, 5, 7, 11, 12, 13].includes(res["code"])) {
            window.$toast.fail(
              this.$t("EXCHANGE_ERROR_CODE_" + res["code"], {
                money: this.$options.filters["formatGold"](res["val"]),
              })
            );
          } else if (res["code"] === 6) {
            window.$toast.fail(
              this.$t("EXCHANGE_ERROR_CODE_" + res["code"], {
                time: res["val"],
              })
            );
          } else {
            window.$toast.fail(this.$t("EXCHANGE_ERROR_CODE_" + res["code"]));
          }
        })
        .catch(() => {})
        .finally(() => {});
    },
    onSelectBankName(value, index) {
      this.bankNameIndex = index;
      this.selectBankName = false
    },
    onSelectWalletName(value, index) {
      this.walletIndex = index;
      this.selectWallet = false
    },
  },
};
