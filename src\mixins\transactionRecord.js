import {menu} from "@/mixins/menu";
import {debounce} from "@/utils/common";
import {ROUTE_RECORDER_QUERY_TRANSFER} from "@/api";
import moment from "moment/moment";

export const transactionRecord = {
    mixins: [menu],
    data() {
        return {
            processing: false,
            records: [],
            actions: [
                this.$t("All"),
                this.$t("Transfer In"),
                this.$t("Transfer Out")
            ],
            form: {
                action: 0,
            },
            total: 0,
        };
    },
    mounted() {
        this.paginate.pageSize = 6;
    },
    methods: {
        getPlatformName(platformId) {
            for (const platform of this.$store.state.platform.platforms) {
                if (platform.platformId === platformId) {
                    return platform.platformName;
                }
            }
        },
        resetTotal() {
            this.total = 0;
        },
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1
                this.finished = false
                this.records = []
            }
            this.processing = true;
            this.resetTotal();
            debounce(() => {
                this.$protoApi(ROUTE_RECORDER_QUERY_TRANSFER, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    beginTime: this.form.beginTime,
                    endTime: this.form.endTime,
                    action: this.form.action,
                    page: this.paginate.page,
                    pageSize: this.paginate.pageSize,
                })
                    .then((res) => {
                        if (this.$store.state.webType === 1) {
                            this.paginate.total = res.counts
                            this.records = res.records;
                            for (const row of res.records) {
                                if (row.action === 1) {
                                    this.total += row.score;
                                } else {
                                    this.total -= row.score;
                                }
                            }
                        } else {
                            this.records = this.records.concat(res.records);
                            this.paginate.page++

                            if (res.counts === 0 || this.records.length >= res.counts) {
                                this.finished = true;
                            }
                            this.loading = false;
                            for (const row of this.records) {
                                if (row.action === 1) {
                                    this.total += row.score;
                                } else {
                                    this.total -= row.score;
                                }
                            }

                        }
                    })
                    .catch(() => {
                    })
                    .finally(() => {
                        this.processing = false;
                    });
            })();
        },
    }
}