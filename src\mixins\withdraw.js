import { modal } from "@/mixins/modal";
import { MD5 } from "crypto-js";
import { debounce } from "@/utils/common";
import {
  ROUTE_PLATFORM_ADDWALLET,
  ROUTE_PLATFORM_WITHDRAWALDETAILS,
  ROUTE_PLATFORM_WITHDRAWAL,
} from "@/api";
import { play } from "@/mixins/play";
import store from "@/store";

export const withdraw = {
  mixins: [modal, play],
  data() {
    return {
      showPassword: false,
      showConfirmPassword: false,
      showWithdrawPassword: false,
      dotIndex: 1,
      withdrawalTypeIndex: 0,
      withdrawalPlatIndex: 0,
      bankIndex: 0,
      transactionPasswd: false,
      withdrawalPlatforms: [],
      withdrawalTypes: [],
      withdrawalWallets: [],
      totalTurnover: 0,
      bindPhone: false,
      form: {
        cardName: "",
        cardAddress: "",
        transactionPasswd: "",
        confirmTransactionPasswd: "",
      },
      withdraw: {
        amount: 0,
        pwd: "",
      },
    };
  },
  mounted() {
    let i = 0
    if (this.$route.query.i) {
      i = this.$route.query.i
    }
    // this.withdrawaldetails(i);
  },
  computed: {
    convertedAmount() {
      let amount = this.withdraw.amount ? 1/this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.platformRate*this.withdraw.amount : 0
      return this.currency(amount, false, true)
    },
    commission() {
      let amount = this.withdraw.amount ? 1/this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.platformRate*this.withdraw.amount*this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.platformCommission : 0
      return this.currency(amount, false, true)
    },
    cardStr() {
      if (!this.withdrawalTypes.length) return;
      return (
        "(" +
        this.walletList(this.withdrawalTypeIndex).length +
        "/" +
        this.withdrawalTypes[this.withdrawalTypeIndex].platformMaxWallets +
        ")"
      );
    },
    withdrawLimitStr() {
      if (!this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1]) return "";
      return (
        this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.minScore / 100 +
        " ~ " +
        this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.maxScore / 100
      );
    },
    noItem() {
      return function (withdrawalTypeIndex) {
        if (!this.withdrawalTypes.length) return;
        return (
            this.withdrawalTypes[withdrawalTypeIndex] &&
            this.walletList(withdrawalTypeIndex).length <
            this.withdrawalTypes[withdrawalTypeIndex].platformMaxWallets
        );
      }
    },
    walletList() {
      return function (withdrawalTypeIndex) {
        let list = [];
        for (const wallet of this.withdrawalWallets) {
          for (const platform of this.withdrawalPlatforms) {
            if (
                wallet.platformId === platform.platformId &&
                platform.platformType ===
                this.withdrawalTypes[withdrawalTypeIndex].platformType
            ) {
              list.push({
                wallet: wallet,
                platform: platform,
              });
              break;
            }
          }
        }
        return list;
      }
    },
  },
  methods: {
    goWithdrawHistory() {
      this.$store.commit("setPcMcPage", "transactionHistory");
      this.$store.commit("setTransactionHistoryIndex", 2);
    },
    dotChange(index) {
      if (this.dotIndex === index) {
        return;
      }
      this.dotIndex = index;
      let g = $(".eWallet-item");
      let e = g.eq($(this).index());
      let R = $("div.carousel-page.js-eWallet-page");
      let M = $("div.carousel-list.js-eWallet-list");
      1 < R.find(".js-carousel-dot").length &&
        ($(this).addClass("active").siblings().removeClass("active"),
        M.prepend(e),
        e.addClass("animation").siblings().removeClass("animation"));
    },
    switchWithdrawalType(index) {
      this.withdrawalTypeIndex = index;
      this.dotIndex = 1;
      for (let i = 0; i < this.withdrawalPlatforms.length; i++) {
        if (
          this.withdrawalTypes[this.withdrawalTypeIndex].platformType ===
          this.withdrawalPlatforms[i].platformType
        ) {
          this.withdrawalPlatIndex = i;
          break;
        }
      }
      this.form = {};
      this.withdraw = {};
    },
    withdrawaldetails(index) {
      // setTimeout(() => {
        this.$protoApi(ROUTE_PLATFORM_WITHDRAWALDETAILS, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
        })
          .then((res) => {
            switch (this.$store.state.account.role) {
              case 1:
                this.withdrawalPlatforms = res.withdrawalPlatforms;
                this.withdrawalTypes = res.withdrawalTypes;
                break
              default:
                this.withdrawalPlatforms = res.withdrawalPlatforms.filter(e=> e.status !== 2);
                this.withdrawalTypes = res.withdrawalTypes.filter(e=> e.status !== 2);
            }
            this.transactionPasswd = res.transactionPasswd;
            this.withdrawalWallets = res.withdrawalWallets;
            this.totalTurnover = res.totalTurnover;
            this.bindPhone = res.bindPhone
            this.switchWithdrawalType(index);
          })
          .catch(() => {});
      // }, 0);
    },
    withdrawal() {
      let errMsg = "";
      let amount = this.withdraw.amount * 100;
      if (!(typeof amount === 'number' && /^\d+(\.\d+)?$/.test(this.withdraw.amount)) ||
        !this.$store.state.balance ||
        !amount ||
        this.$store.state.balance < amount ||
        this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.minScore > amount ||
        this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].platform.maxScore < amount
      ) {
        // 金额不足
        errMsg = this.$t("The amount is err");
      } else if (!this.withdraw.pwd) {
        errMsg = this.$t("562");
      } else if (
        !this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1] ||
        !this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].wallet.walletId
      ) {
        errMsg = this.$t("574");
      }

      if (this.totalTurnover) {
        errMsg = this.$t("568");
      }

      if (errMsg) {
        if (store.state.webType > 1) {
          window.$toast.fail(errMsg);
        } else {
          window.cover.init({
            html: errMsg,
            icon: "error",
            timeOut: 2000,
          });
        }
        return;
      }
      debounce(() => {
        this.event_withdrawClick()
        const {advertiseType, bundleId} = this.event_Param()
        this.$protoApi(ROUTE_PLATFORM_WITHDRAWAL, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          walletId: this.walletList(this.withdrawalTypeIndex)[this.dotIndex - 1].wallet.walletId,
          transactionPasswd: MD5(this.withdraw.pwd).toString(),
          amount: amount,
          advertiseType: advertiseType,
          bundleId: bundleId,
        })
          .then((res) => {
            this.$store.commit("setBalance", res);
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });
            this.withdraw = {}
          })
          .catch(() => {});
      })();
    },
    validAddWallet() {
      let e = this;
      this.$validator.validateAll().then(function (t) {
        t && e.addWallet();
      });
    },
    addWallet() {
      debounce(() => {
        this.$protoApi(ROUTE_PLATFORM_ADDWALLET, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          token: this.$store.state.token.token,
          platformId:
            this.withdrawalPlatforms[this.withdrawalPlatIndex].platformId,
          cardName: this.form.cardName,
          cardAddress: this.withdrawalPlatIndex === 0 && this.withdrawalPlatforms[this.withdrawalPlatIndex].banks[this.bankIndex].bankCode === 'BR-PHONE' ? this.$store.state.phonePreApi + this.form.cardAddress : this.form.cardAddress,
          cardCode: this.withdrawalPlatforms[this.withdrawalPlatIndex].banks.length ? this.withdrawalPlatforms[this.withdrawalPlatIndex].banks[this.bankIndex].bankCode : '',
          transactionPasswd: MD5(this.form.transactionPasswd).toString(),
        })
          .then((res) => {
            $toast.success({
              message: this.$t("label_success"),
              icon: "passed",
            });
            if (this.$store.state.webType === 1) {
              this.show = "";
              this.withdrawaldetails(this.withdrawalTypeIndex);
            } else {
              let that = this
              setTimeout(()=>{
                that.$router.back()
              }, 2000)
            }

          })
          .catch(() => {});
      })();
    },
    btnAdd(withdrawalTypeIndex = 0) {
      // if (!this.bindPhone) {
      //   if (this.$store.state.webType === 1) {
      //     this.cover.init(
      //         {
      //           title: this.$t("in_popup_prompt"),
      //           html: this.$t("624"),
      //           icon: "info",
      //           btn: {
      //             cancel: this.$t("button_cancel"),
      //             confirm: this.$t("go_bind"),
      //           },
      //         },
      //         (e) => {
      //           if ("confirm" === e) {
      //             this.$store.commit('setPcMcPage', 'securityCenter')
      //             this.$store.commit('setOpenBind', true)
      //           }
      //         }
      //     );
      //   } else {
      //     window.$Dialog.confirm({
      //       title: this.$t("in_popup_prompt"),
      //       message: this.$t("624"),
      //       confirmButtonText: this.$t("go_bind"),
      //       cancelButtonText: this.$t("button_cancel"),
      //     })
      //         .then(() => {
      //           this.$router.push({
      //             path:'/m/myAccount/phone',
      //           })
      //         })
      //         .catch(() => {});
      //   }
      // } else {
        if (this.$store.state.webType === 1) {
          this.show = true
        } else {
          this.$router.push({
            path:'/m/securityCenter/addBankCardPix',
            query:{
              i: withdrawalTypeIndex
            }
          }).catch(()=>{})
        }
      // }
    }
  },
};
