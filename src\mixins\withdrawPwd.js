import { SHA256 } from "crypto-js";
import {TDTC_ROURE} from '@/api/tdtc'

export const withdrawPwd = {
  data() {
    return {
      showNewPassword: false,
      showConfirmPassword: false,
      form: {
        NewPassword: "",
        ConfirmPassword: "",
      },
    };
  },
  methods: {
    submitHandle() {
      if (!this.form.NewPassword) {
        window.$toast.fail("Mật khẩu không được để trống");
        return
      }
      if (this.form.NewPassword.length < 6 || this.form.NewPassword.length > 20) {
        window.$toast.fail("Độ dài mật khẩu không đáp ứng yêu cầu 6-20 ký tự");
        return
      }
      if (this.form.NewPassword !== this.form.ConfirmPassword) {
        window.$toast.fail("Hai mật khẩu không nhất quán");
        return
      }
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.SET_WITHDRAW_PASSWD, {
        passwd: SHA256(this.form.NewPassword).toString(),
      })
          .then((res) => {
            if (res.code === 200) {
              $toast.success({
                icon: "passed",
                message: "Đặt mật khẩu thành công",
              });
              setTimeout(()=>{
                this.$router.back();
              }, 1000)
            } else if (res.code === 1) {
              window.$toast.fail("Mật khẩu rút tiền đã được thiết lập");
            } else {
              window.$toast.fail("Thiết lập mật khẩu không thành công");
            }
          })
          .catch(() => {});
    },
  },
};
