import {debounce} from "@/utils/common";
import {ROUTE_RECORDER_QUERY_WITHDRAWS} from "@/api";
import {menu} from "@/mixins/menu";

export const withdrawReport = {
    mixins: [menu],
    data() {
      return {
          processing: false,
          records: [],
          form: {
              status: 0,
          },
          total: 0,
          statusActions: [
              this.$t("All"),
              this.$t("status_withdrawal_1"),
              this.$t("status_withdrawal_2"),
              this.$t("status_withdrawal_3"), // 锁定
              this.$t("status_withdrawal_4"),
          ],
          statusActions2: [
              this.$t("All"),
              this.$t("status_withdrawal_1"),
              this.$t("status_withdrawal_2"),
              this.$t("status_withdrawal_3"), // 锁定
              this.$t("status_withdrawal_4"),
              this.$t("status_withdrawal_1"),
              this.$t("status_withdrawal_1"),

              // "status_withdrawal_2",
              // "status_withdrawal_5",
          ],
      }
    },

    mounted() {
        this.paginate.pageSize = 13;
    },
    methods: {
        resetTotal() {
            this.total = 0;
        },
        search(paginate = false) {
            if (!paginate) {
                this.paginate.page = 1
                this.finished = false
                this.records = []
            }
            this.processing = true;
            this.resetTotal();
            debounce(() => {
                this.$protoApi(ROUTE_RECORDER_QUERY_WITHDRAWS, {
                    channel: this.$store.state.channel,
                    device: this.$store.state.device,
                    token: this.$store.state.token.token,
                    beginTime: this.form.beginTime,
                    endTime: this.form.endTime,
                    page: this.paginate.page,
                    pageSize: this.paginate.pageSize,
                    status: this.form.status,
                })
                    .then((res) => {
                        if (this.$store.state.webType === 1) {
                            this.paginate.total = res.counts
                            this.records = res.records;
                            // for (const row of res.records) {
                            //     this.total.received += row.comfirmAmount;
                            //     this.total.fee += (row.chargeAmount - row.comfirmAmount);
                            // }
                        } else {
                            this.records = this.records.concat(res.records);
                            this.paginate.page++

                            if (res.counts === 0 || this.records.length >= res.counts) {
                                this.finished = true;
                            }
                            this.loading = false;
                        }
                    })
                    .catch(() => {
                    })
                    .finally(() => {
                        this.processing = false;
                    });
            })();
        },
    }

}