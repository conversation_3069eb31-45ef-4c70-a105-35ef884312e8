<script>
import {menu} from "@/mixins/menu";

export default {
  name: "AppGameCategory",
  mixins: [menu],
  data() {
    return {
      swiperOptions: {
        slidesPerView: 5,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
    }
  }
}
</script>

<template>
  <section style="position: relative">
    <swiper :options="swiperOptions" style="height: 1.36rem;padding-top: .2rem;">
      <template v-for="(item, index) in menuOptions">
      <swiper-slide
          v-if="index < 0 && item.show"
          :key="index"
      >
        <div class="swiper-item" @click="setCategory(index); index === '-2' && loadGame()" :class="{
                      on: $store.state.platform.currentCategory === index,
                    }">
          <svg class="am-icon am-icon-icon-home am-icon-md"><use v-bind:xlink:href="`#${item.icon}`"></use></svg>
          <span>{{item.title }}</span>
        </div>
      </swiper-slide>
      </template>
      <swiper-slide
          v-for="item in $store.state.platform.categories"
          :key="item.categoryId"
      >
        <div class="swiper-item" @click="setCategory(item.categoryId)" :class="{
                    on:
                      $store.state.platform.currentCategory === item.categoryId,
                  }">
          <svg class="am-icon"><use v-bind:xlink:href="`#${menuOptions[item.categoryId].icon}`"></use></svg>
          <span>{{ menuOptions[item.categoryId].title }}</span>
        </div>
      </swiper-slide>

      <swiper-slide
          :key="-4"
      >
        <div @click="setCategory('-4'); $store.commit('setPlatformPlatform', 0);loadGame()" :class="{
                      on: $store.state.platform.currentCategory === '-4',
                    }"
            class="swiper-item">
          <svg class="am-icon"><use v-bind:xlink:href="`#${menuOptions['-4'].icon}`"></use></svg>
          <span>{{ menuOptions['-4'].title }}</span>
        </div>
      </swiper-slide>
    </swiper>
<!--    <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>-->
<!--    <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>-->
  </section>
</template>

<style scoped lang="scss">
.swiper-item {
  width: 100%;
  height: 100%;
  display         : flex;
  flex-direction  : column;
  justify-content : center;
  align-items     : center;
  font-weight: 400;
  font-size: 0.19rem;
  color: #FFFFFF;

  svg {
    height : .52rem;
    margin-bottom: .2rem;
  }
}

.swiper-item.on {
  color: rgb(255 182 39 / 1);
}
</style>
