<script>
import {play} from "@/mixins/play";

export default {
  name: "AppMemberInfo",
  mixins: [play],
  methods: {
    goPage(path) {
      // this.$store.commit("setShowMobileSide");
      let that = this
      setTimeout(()=>{
        if (that.$store.getters.isLogin) {
          that.$router.push(path);
        } else {
          that.$router.push("/m/login");
        }
      }, 300)
    },
    showAd() {
      if (!this.$store.getters.isLogin) {
        this.$router.push("/m/login");
      } else {
        this.$store.commit('setShowAd', true)
      }
    },
  }
}
</script>

<template>
  <section>
    <div style="height: 1.28rem;font-size: .23rem"  class="border-b-1 border-b-customized-bg-quaternary flex h-[63px] items-center justify-center">
      <div class="flex w-[39%] flex-col items-center justify-center text-center">
        <template v-if="$store.getters.isLogin">
          <div>
            <div class="text-customized-text-primary-800 amount" @click.stop="leaveThirdGame">
              <span>{{ this.$store.state.configs.currency_symbol }} {{ balance }}</span>
<!--              <i class="balance-button ml-1" style="scale: 1.5;" :class="{rotateFull: refresh}"></i>-->
              <span>
                <svg style="width: .28rem;height: .28rem;margin-left: .15rem;"
                    class="am-icon am-icon-refresh_9b98ac99 am-icon-md false refresh-balance"
                    :class="{ rotateFull: refresh }"
                >
                <use xlink:href="#icon-refresh"></use>
              </svg>
              </span>
            </div>
          </div>
          <p class="account">{{ $store.state.account.nickname }}</p>
        </template>
        <template v-else>
          <div style="color: white;font-size: .26rem">
            <span @click="$router.push('/m/login')" style="border-bottom: 1px solid #ffb627; padding: .1rem 0;">{{ $t('login') }}</span>
            or
            <span @click="$router.push('/m/register')" style="border-bottom: 1px solid #ffb627; padding: .1rem 0;">{{ $t('register') }}</span>
          </div>
        </template>
      </div>
            <ul  class="relative box-border flex h-full w-[61%] items-center justify-center" >
              <li class="discount text-customized-text-primary-400 mx-[2%] inline-block text-center align-top " @click="goPage('/m/inviteFriends')">
                <i class="text-customized-bg-tertiary  block "></i>
                <!---->
                <span translate="Footer_AnyTimeDiscount" class="whitespace-nowrap">{{ $t('agent') }}</span>
              </li>
              <li class="vip text-customized-text-primary-400 mx-[2%] inline-block text-center align-top " @click="goPage('/m/vip')">
                <div class="relative inline"><!----><i class="text-customized-bg-tertiary  block "></i>
                </div>
                <span>{{ $t('vip') }}</span>
              </li><!---->
              <li class="promotion text-customized-text-primary-400 mx-[2%] inline-block text-center align-top " @click="showAd">
                <i class="text-customized-bg-tertiary  block "></i>
                <span translate="Shared_ReceivePromotion">{{ $t('events') }}</span>
              </li>
            </ul>
    </div><!----></section>
</template>

<style scoped>

.visible {
  visibility : visible
}

.\!visible {
  visibility : visible !important
}

.invisible {
  visibility : hidden
}

.static {
  position : static
}

.fixed {
  position : fixed
}

.absolute {
  position : absolute
}

.relative {
  position : relative
}

.top-1 {
  top : .25rem
}

.right-9 {
  right : 2.25rem
}

.top-10 {
  top : 2.5rem
}

.right-3 {
  right : .75rem
}

.top-0 {
  top : 0px
}

.right-0 {
  right : 0px
}

.top-8 {
  top : 2rem
}

.right-10 {
  right : 2.5rem
}

.right-7 {
  right : 1.75rem
}

.right-2 {
  right : .5rem
}

.top-1\/2 {
  top : 50%
}

.top-2 {
  top : .5rem
}

.-left-3 {
  left : -.75rem
}

.-bottom-full {
  bottom : -100%
}

.left-2 {
  left : .5rem
}

.right-1 {
  right : .25rem
}

.-top-6 {
  top : -1.5rem
}

.left-1\/2 {
  left : 50%
}

.top-4 {
  top : 1rem
}

.bottom-0 {
  bottom : 0px
}

.left-0 {
  left : 0px
}

.top-\[10px\] {
  top : 10px
}

.right-4 {
  right : 1rem
}

.top-\[15px\] {
  top : 15px
}

.-top-1 {
  top : -.25rem
}

.right-\[15px\] {
  right : 15px
}

.right-\[-25px\] {
  right : -25px
}

.right-\[-20px\] {
  right : -20px
}

.bottom-\[5rem\] {
  bottom : 5rem
}

.top-\[5px\] {
  top : 5px
}

.right-\[-5px\] {
  right : -5px
}

.top-\[11vh\] {
  top : 11vh
}

.right-\[2\%\] {
  right : 2%
}

.left-\[-300px\] {
  left : -300px
}

.z-10 {
  z-index : 10
}

.z-\[1050\] {
  z-index : 1050
}

.z-20 {
  z-index : 20
}

.z-30 {
  z-index : 30
}

.float-right {
  float : right
}

.float-left {
  float : left
}

.m-1 {
  margin : .25rem
}

.m-0 {
  margin : 0
}

.my-2 {
  margin-top    : .5rem;
  margin-bottom : .5rem
}

.my-4 {
  margin-top    : 1rem;
  margin-bottom : 1rem
}

.mx-auto {
  margin-left  : auto;
  margin-right : auto
}

.mx-1 {
  margin-left  : .25rem;
  margin-right : .25rem
}

.my-5 {
  margin-top    : 1.25rem;
  margin-bottom : 1.25rem
}

.mx-5 {
  margin-left  : 1.25rem;
  margin-right : 1.25rem
}

.my-3 {
  margin-top    : .75rem;
  margin-bottom : .75rem
}

.my-0\.5 {
  margin-top    : .125rem;
  margin-bottom : .125rem
}

.my-0 {
  margin-top    : 0;
  margin-bottom : 0
}

.mx-2 {
  margin-left  : .5rem;
  margin-right : .5rem
}

.mx-\[2\%\] {
  margin-left  : 2%;
  margin-right : 2%
}

.mx-\[1\%\] {
  margin-left  : 1%;
  margin-right : 1%
}

.mx-1\.5 {
  margin-left  : .375rem;
  margin-right : .375rem
}

.mr-1 {
  margin-right : .25rem
}

.mt-1 {
  margin-top : .25rem
}

.ml-1 {
  margin-left : .25rem
}

.mb-4 {
  margin-bottom : 1rem
}

.mt-4 {
  margin-top : 1rem
}

.ml-\[1\%\] {
  margin-left : 1%
}

.ml-\[22px\] {
  margin-left : 22px
}

.mr-\[5px\] {
  margin-right : 5px
}

.ml-4 {
  margin-left : 1rem
}

.mb-5 {
  margin-bottom : 1.25rem
}

.mb-3\.5 {
  margin-bottom : .875rem
}

.mb-3 {
  margin-bottom : .75rem
}

.mr-2 {
  margin-right : .5rem
}

.mb-2 {
  margin-bottom : .5rem
}

.mr-3 {
  margin-right : .75rem
}

.mt-5 {
  margin-top : 1.25rem
}

.ml-2 {
  margin-left : .5rem
}

. {
  margin-bottom : .25rem
}

.6 {
  margin-bottom : 4rem
}

.ml-\[50\%\] {
  margin-left : 50%
}

.mt-2 {
  margin-top : .5rem
}

.mt-3 {
  margin-top : .75rem
}

.-ml-9 {
  margin-left : -2.25rem
}

.mb-6 {
  margin-bottom : 1.5rem
}

.0 {
  margin-bottom : 2.5rem
}

.mb-\[10px\] {
  margin-bottom : 10px
}

.mr-\[3\%\] {
  margin-right : 3%
}

.mb-\[3\%\] {
  margin-bottom : 3%
}

.mt-6 {
  margin-top : 1.5rem
}

.mr-5 {
  margin-right : 1.25rem
}

.mr-0\.5 {
  margin-right : .125rem
}

.mr-0 {
  margin-right : 0
}

.ml-8 {
  margin-left : 2rem
}

.ml-\[-30\%\] {
  margin-left : -30%
}

.mt-7 {
  margin-top : 1.75rem
}

.mr-\[10px\] {
  margin-right : 10px
}

.ml-\[5px\] {
  margin-left : 5px
}

.mt-\[-5px\] {
  margin-top : -5px
}

.mt-\[4px\] {
  margin-top : 4px
}

.mr-1\.5 {
  margin-right : .375rem
}

.box-border {
  box-sizing : border-box
}

.block {
  display : block
}

.inline-block {
  display : inline-block
}

.inline {
  display : inline
}

.flex {
  display : flex
}

.inline-flex {
  display : inline-flex
}

.table {
  display : table
}

.inline-table {
  display : inline-table
}

.table-cell {
  display : table-cell
}

.hidden {
  display : none
}

.h-\[52px\] {
  height : 52px
}

.h-full {
  height : 100%
}

.h-\[38px\] {
  height : 38px
}

.h-\[96\%\] {
  height : 96%
}

.h-8 {
  height : 2rem
}

.h-4\/5 {
  height : 80%
}

.h-7 {
  height : 1.75rem
}

.h-6 {
  height : 1.5rem
}

.h-\[300px\] {
  height : 300px
}

.h-\[83px\] {
  height : 83px
}

.h-9 {
  height : 2.25rem
}

.h-28 {
  height : 7rem
}

.h-3\/4 {
  height : 75%
}

.h-\[112px\] {
  height : 112px
}

.h-\[170px\] {
  height : 170px
}

.h-\[35px\] {
  height : 35px
}

.h-\[58px\] {
  height : 58px
}

.h-\[27px\] {
  height : 27px
}

.h-10 {
  height : 2.5rem
}

.h-\[34px\] {
  height : 34px
}

.h-\[70px\] {
  height : 70px
}

.h-\[20px\] {
  height : 20px
}

.h-screen {
  height : 100vh
}

.h-\[30px\] {
  height : 30px
}

.h-12 {
  height : 3rem
}

.h-\[28px\] {
  height : 28px
}

.h-5 {
  height : 1.25rem
}

.h-4 {
  height : 1rem
}

.h-\[100px\] {
  height : 100px
}

.h-\[63px\] {
  height : 63px
}

.h-\[40px\] {
  height : 40px
}

.h-\[90px\] {
  height : 90px
}

.h-\[54vh\] {
  height : 54vh
}

.h-\[26vh\] {
  height : 26vh
}

.h-14 {
  height : 3.5rem
}

.h-16 {
  height : 4rem
}

.h-\[18px\] {
  height : 18px
}

.h-\[13\%\] {
  height : 13%
}

.h-\[80\%\] {
  height : 80%
}

.max-h-full {
  max-height : 100%
}

.max-h-\[57vh\] {
  max-height : 57vh
}

.max-h-\[256px\] {
  max-height : 256px
}

.max-h-\[96vh\] {
  max-height : 96vh
}

.max-h-\[54vh\] {
  max-height : 54vh
}

.min-h-screen {
  min-height : 100vh
}

.min-h-\[20vh\] {
  min-height : 20vh
}

.min-h-full {
  min-height : 100%
}

.min-h-\[0\.01\%\] {
  min-height : .01%
}

.min-h-\[calc\(100vh-285px\)\] {
  min-height : calc(100vh - 285px)
}

.min-h-\[600px\] {
  min-height : 600px
}

.min-h-\[400px\] {
  min-height : 400px
}

.w-\[60\%\] {
  width : 60%
}

.w-\[100\%\] {
  width : 100%
}

.w-\[100px\] {
  width : 100px
}

.w-full {
  width : 100%
}

.w-\[34\%\] {
  width : 34%
}

.w-\[65\%\] {
  width : 65%
}

.w-7 {
  width : 1.75rem
}

.w-screen {
  width : 100vw
}

.w-6 {
  width : 1.5rem
}

.w-\[200px\] {
  width : 200px
}

.w-16 {
  width : 4rem
}

.w-\[95\%\] {
  width : 95%
}

.w-\[140px\] {
  width : 140px
}

.w-1\/2 {
  width : 50%
}

.w-\[300px\] {
  width : 300px
}

.w-\[360px\] {
  width : 360px
}

.w-\[75px\] {
  width : 75px
}

.w-\[175px\] {
  width : 175px
}

.w-\[146px\] {
  width : 146px
}

.w-\[20px\] {
  width : 20px
}

.w-\[1\%\] {
  width : 1%
}

.w-\[90\%\] {
  width : 90%
}

.w-\[120px\] {
  width : 120px
}

.w-\[30px\] {
  width : 30px
}

.w-1\/3 {
  width : 33.333333%
}

.w-4\/5 {
  width : 80%
}

.w-1\/5 {
  width : 20%
}

.w-\[27\%\] {
  width : 27%
}

.w-1\/4 {
  width : 25%
}

.w-3\/4 {
  width : 75%
}

.w-\[80\%\] {
  width : 80%
}

.w-\[calc\(100vw-160px\)\] {
  width : calc(100vw - 160px)
}

.w-auto {
  width : auto
}

.w-\[50\%\] {
  width : 50%
}

.w-\[39\%\] {
  width : 39%
}

.w-\[61\%\] {
  width : 61%
}

.w-\[90px\] {
  width : 90px
}

.w-9\/12 {
  width : 75%
}

.w-3\/12 {
  width : 25%
}

.w-\[31\%\] {
  width : 31%
}

.w-9 {
  width : 2.25rem
}

.min-w-\[124px\] {
  min-width : 124px
}

.min-w-\[125px\] {
  min-width : 125px
}

.min-w-\[111px\] {
  min-width : 111px
}

.min-w-\[30\%\] {
  min-width : 30%
}

.min-w-\[80px\] {
  min-width : 80px
}

.min-w-\[65px\] {
  min-width : 65px
}

.min-w-\[28px\] {
  min-width : 28px
}

.max-w-full {
  max-width : 100%
}

.max-w-\[308px\] {
  max-width : 308px
}

.max-w-\[5em\] {
  max-width : 5em
}

.max-w-\[92\.5px\] {
  max-width : 92.5px
}

.flex-1 {
  flex : 1 1 0%
}

.flex-initial {
  flex : 0 1 auto
}

.flex-shrink-0 {
  flex-shrink : 0
}

.flex-shrink {
  flex-shrink : 1
}

.flex-grow, .grow {
  flex-grow : 1
}

.origin-top-left {
  transform-origin : top left
}

.-translate-y-1\/2 {
  --tw-translate-y : -50%;
  transform        : translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-translate-x-1\/2 {
  --tw-translate-x : -50%;
  transform        : translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-rotate-45 {
  --tw-rotate : -45deg;
  transform   : translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.-rotate-\[16deg\] {
  --tw-rotate : -16deg;
  transform   : translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.rotate-180 {
  --tw-rotate : 180deg;
  transform   : translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform {
  transform : translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.cursor-pointer {
  cursor : pointer
}

.resize {
  resize : both
}

.list-disc {
  list-style-type : disc
}

.flex-col {
  flex-direction : column
}

.flex-wrap {
  flex-wrap : wrap
}

.items-start {
  align-items : flex-start
}

.items-center {
  align-items : center
}

.justify-start {
  justify-content : flex-start
}

.justify-center {
  justify-content : center
}

.justify-between {
  justify-content : space-between
}

.justify-around {
  justify-content : space-around
}

.gap-4 {
  gap : 1rem
}

.overflow-auto {
  overflow : auto
}

.overflow-hidden {
  overflow : hidden
}

.overflow-x-auto {
  overflow-x : auto
}

.overflow-y-auto {
  overflow-y : auto
}

.overflow-y-hidden {
  overflow-y : hidden
}

.overflow-y-scroll {
  overflow-y : scroll
}

.truncate {
  overflow      : hidden;
  text-overflow : ellipsis;
  white-space   : nowrap
}

.text-ellipsis {
  text-overflow : ellipsis
}

.whitespace-nowrap {
  white-space : nowrap
}

.break-words {
  overflow-wrap : break-word
}

.break-all {
  word-break : break-all
}

.rounded {
  border-radius : .25rem
}

.rounded-\[4px\] {
  border-radius : 4px
}

.rounded-2xl {
  border-radius : 1rem
}

.rounded-md {
  border-radius : .375rem
}

.rounded-full {
  border-radius : 9999px
}

.rounded-\[5px\] {
  border-radius : 5px
}

.rounded-\[20px\] {
  border-radius : 20px
}

.rounded-3xl {
  border-radius : 1.5rem
}

.rounded-sm {
  border-radius : .125rem
}

.rounded-lg {
  border-radius : .5rem
}

.rounded-\[34px\] {
  border-radius : 34px
}

.rounded-xl {
  border-radius : .75rem
}

.rounded-\[16px\] {
  border-radius : 16px
}

.rounded-\[10px\] {
  border-radius : 10px
}

.rounded-l-2xl {
  border-top-left-radius    : 1rem;
  border-bottom-left-radius : 1rem
}

.rounded-r-2xl {
  border-top-right-radius    : 1rem;
  border-bottom-right-radius : 1rem
}

.rounded-t-md {
  border-top-left-radius  : .375rem;
  border-top-right-radius : .375rem
}

.rounded-t {
  border-top-left-radius  : .25rem;
  border-top-right-radius : .25rem
}

.rounded-l-\[4px\] {
  border-top-left-radius    : 4px;
  border-bottom-left-radius : 4px
}

.rounded-r-\[4px\] {
  border-top-right-radius    : 4px;
  border-bottom-right-radius : 4px
}

.rounded-b-\[8px\] {
  border-bottom-right-radius : 8px;
  border-bottom-left-radius  : 8px
}

.border {
  border-width : 1px
}

.border-3 {
  border-width : 3px
}

.border-2 {
  border-width : 2px
}

.border-b {
  border-bottom-width : 1px
}

.border-t {
  border-top-width : 1px
}

.border-l {
  border-left-width : 1px
}

.border-r {
  border-right-width : 1px
}

.border-b-2 {
  border-bottom-width : 2px
}

.border-l-4 {
  border-left-width : 4px
}

.border-dashed {
  border-style : dashed
}

.border-dotted {
  border-style : dotted
}

.border-\[\#ccc\] {
  --tw-border-opacity : 1;
  border-color        : rgb(204 204 204 / var(--tw-border-opacity))
}

.border-\[\#ff6000\] {
  --tw-border-opacity : 1;
  border-color        : rgb(255 96 0 / var(--tw-border-opacity))
}

.border-\[\#e5e5e5\] {
  --tw-border-opacity : 1;
  border-color        : rgb(229 229 229 / var(--tw-border-opacity))
}

.border-\[\#204d74\] {
  --tw-border-opacity : 1;
  border-color        : rgb(32 77 116 / var(--tw-border-opacity))
}

.border-customized-bg-undenary {
  --tw-border-opacity : 1;
  border-color        : rgb(95 103 234 / var(--tw-border-opacity))
}

.border-\[\#f9f3e1\] {
  --tw-border-opacity : 1;
  border-color        : rgb(249 243 225 / var(--tw-border-opacity))
}

.border-\[\#bba57e\] {
  --tw-border-opacity : 1;
  border-color        : rgb(187 165 126 / var(--tw-border-opacity))
}

.border-\[\#fe0000\] {
  --tw-border-opacity : 1;
  border-color        : rgb(254 0 0 / var(--tw-border-opacity))
}

.border-\[\#bee5eb\] {
  --tw-border-opacity : 1;
  border-color        : rgb(190 229 235 / var(--tw-border-opacity))
}

.border-white {
  --tw-border-opacity : 1;
  border-color        : rgb(255 255 255 / var(--tw-border-opacity))
}

.border-\[\#c9c9c9\] {
  --tw-border-opacity : 1;
  border-color        : rgb(201 201 201 / var(--tw-border-opacity))
}

.border-\[\#fc3\] {
  --tw-border-opacity : 1;
  border-color        : rgb(255 204 51 / var(--tw-border-opacity))
}

.border-\[\#efefef\] {
  --tw-border-opacity : 1;
  border-color        : rgb(239 239 239 / var(--tw-border-opacity))
}

.border-\[\#ddd\] {
  --tw-border-opacity : 1;
  border-color        : rgb(221 221 221 / var(--tw-border-opacity))
}

.border-\[\#a94442\] {
  --tw-border-opacity : 1;
  border-color        : rgb(169 68 66 / var(--tw-border-opacity))
}

.border-customized-bg-septenary {
  --tw-border-opacity : 1;
  border-color        : rgb(207 209 249 / var(--tw-border-opacity))
}

.border-black\/25 {
  border-color : #00000040
}

.border-customized-text-senary {
  --tw-border-opacity : 1;
  border-color        : rgb(95 103 234 / var(--tw-border-opacity))
}

.border-customized-text-primary {
  --tw-border-opacity : 1;
  border-color        : rgb(255 255 255 / var(--tw-border-opacity))
}

.border-black\/10 {
  border-color : #0000001a
}

.border-\[\#ebccd1\] {
  --tw-border-opacity : 1;
  border-color        : rgb(235 204 209 / var(--tw-border-opacity))
}

.border-red-600 {
  --tw-border-opacity : 1;
  border-color        : rgb(220 38 38 / var(--tw-border-opacity))
}

.border-\[\#e3e3e3\] {
  --tw-border-opacity : 1;
  border-color        : rgb(227 227 227 / var(--tw-border-opacity))
}

.border-\[\#eee\] {
  --tw-border-opacity : 1;
  border-color        : rgb(238 238 238 / var(--tw-border-opacity))
}

.border-\[\#aaa\] {
  --tw-border-opacity : 1;
  border-color        : rgb(170 170 170 / var(--tw-border-opacity))
}

.border-customized-text-quaternary {
  --tw-border-opacity : 1;
  border-color        : rgb(255 65 123 / var(--tw-border-opacity))
}

.border-customized-bg-senary {
  --tw-border-opacity : 1;
  border-color        : rgb(214 219 255 / var(--tw-border-opacity))
}

.border-b-customized-bg-quaternary {
  --tw-border-opacity : 1;
  border-bottom-color : rgb(78 78 78 / var(--tw-border-opacity))
}

.bg-white {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-yellow-100 {
  --tw-bg-opacity  : 1;
  background-color : rgb(254 249 195 / var(--tw-bg-opacity))
}

.bg-\[\#ff6000\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 96 0 / var(--tw-bg-opacity))
}

.bg-black {
  --tw-bg-opacity  : 1;
  background-color : rgb(0 0 0 / var(--tw-bg-opacity))
}

.bg-\[\#b20000\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(178 0 0 / var(--tw-bg-opacity))
}

.bg-\[\#286090\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(40 96 144 / var(--tw-bg-opacity))
}

.bg-customized-bg-nonary {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-black\/30 {
  background-color : #0000004d
}

.bg-customized-bg-tertiary {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 144 0 / var(--tw-bg-opacity))
}

.bg-\[\#f8f8f8\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(248 248 248 / var(--tw-bg-opacity))
}

.bg-\[\#ca5759\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(202 87 89 / var(--tw-bg-opacity))
}

.bg-\[\#cfa772\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(207 167 114 / var(--tw-bg-opacity))
}

.bg-\[\#d1ecf1\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(209 236 241 / var(--tw-bg-opacity))
}

.bg-\[\#535353\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(83 83 83 / var(--tw-bg-opacity))
}

.bg-\[\#292929\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(41 41 41 / var(--tw-bg-opacity))
}

.bg-customized-bg-primary {
  --tw-bg-opacity  : 1;
  background-color : rgb(17 17 17 / var(--tw-bg-opacity))
}

.bg-\[\#c1c1c1\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(193 193 193 / var(--tw-bg-opacity))
}

.bg-\[\#e0e0e0\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(224 224 224 / var(--tw-bg-opacity))
}

.bg-\[\#eee\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(238 238 238 / var(--tw-bg-opacity))
}

.bg-customized-bg-octonary {
  --tw-bg-opacity  : 1;
  background-color : rgb(236 237 252 / var(--tw-bg-opacity))
}

.bg-\[\#efefef\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(239 239 239 / var(--tw-bg-opacity))
}

.bg-\[\#8f8f8f\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(143 143 143 / var(--tw-bg-opacity))
}

.bg-customized-bg-tertiary-600 {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 182 39 / var(--tw-bg-opacity))
}

.bg-customized-bg-tertiary\/50 {
  background-color : #ff900080
}

.bg-\[\#f5f5f5\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(245 245 245 / var(--tw-bg-opacity))
}

.bg-\[\#f2dede\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(242 222 222 / var(--tw-bg-opacity))
}

.bg-customized-bg-quaternary-100 {
  --tw-bg-opacity  : 1;
  background-color : rgb(252 143 0 / var(--tw-bg-opacity))
}

.bg-\[\#e4e4e4\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(228 228 228 / var(--tw-bg-opacity))
}

.bg-\[\#f00\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 0 0 / var(--tw-bg-opacity))
}

.bg-\[\#0c3\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(0 204 51 / var(--tw-bg-opacity))
}

.bg-\[\#f90\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(255 153 0 / var(--tw-bg-opacity))
}

.bg-\[\#ddd\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(221 221 221 / var(--tw-bg-opacity))
}

.bg-\[\#5cb85c\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(92 184 92 / var(--tw-bg-opacity))
}

.bg-\[\#d9534f\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(217 83 79 / var(--tw-bg-opacity))
}

.bg-\[\#337ab7\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(51 122 183 / var(--tw-bg-opacity))
}

.bg-red-600 {
  --tw-bg-opacity  : 1;
  background-color : rgb(220 38 38 / var(--tw-bg-opacity))
}

.bg-customized-bg-senary {
  --tw-bg-opacity  : 1;
  background-color : rgb(214 219 255 / var(--tw-bg-opacity))
}

.bg-black\/90 {
  background-color : #000000e6
}

.bg-\[\#484848\] {
  --tw-bg-opacity  : 1;
  background-color : rgb(72 72 72 / var(--tw-bg-opacity))
}

.bg-gradient-to-b {
  background-image : linear-gradient(to bottom, var(--tw-gradient-stops))
}

.from-\[\#e0c595\] {
  --tw-gradient-from  : #e0c595;
  --tw-gradient-stops : var(--tw-gradient-from), var(--tw-gradient-to, rgb(224 197 149 / 0))
}

.from-\[\#fc3\] {
  --tw-gradient-from  : #fc3;
  --tw-gradient-stops : var(--tw-gradient-from), var(--tw-gradient-to, rgb(255 204 51 / 0))
}

.to-\[\#eed8aa\] {
  --tw-gradient-to : #eed8aa
}

.to-\[\#f90\] {
  --tw-gradient-to : #f90
}

.object-cover {
  object-fit : cover
}

.p-3 {
  padding : .75rem
}

.p-2 {
  padding : .5rem
}

.p-6 {
  padding : 1.5rem
}

.p-3\.5 {
  padding : .875rem
}

.p-4 {
  padding : 1rem
}

.p-2\.5 {
  padding : .625rem
}

.p-1 {
  padding : .25rem
}

.p-5 {
  padding : 1.25rem
}

.py-2 {
  padding-top    : .5rem;
  padding-bottom : .5rem
}

.px-4 {
  padding-left  : 1rem;
  padding-right : 1rem
}

.px-2 {
  padding-left  : .5rem;
  padding-right : .5rem
}

.px-1 {
  padding-left  : .25rem;
  padding-right : .25rem
}

.py-1 {
  padding-top    : .25rem;
  padding-bottom : .25rem
}

.px-3 {
  padding-left  : .75rem;
  padding-right : .75rem
}

.py-4 {
  padding-top    : 1rem;
  padding-bottom : 1rem
}

.px-10 {
  padding-left  : 2.5rem;
  padding-right : 2.5rem
}

.py-5 {
  padding-top    : 1.25rem;
  padding-bottom : 1.25rem
}

.px-12 {
  padding-left  : 3rem;
  padding-right : 3rem
}

.px-\[7\.5px\] {
  padding-left  : 7.5px;
  padding-right : 7.5px
}

.py-\[10px\] {
  padding-top    : 10px;
  padding-bottom : 10px
}

.py-6 {
  padding-top    : 1.5rem;
  padding-bottom : 1.5rem
}

.py-\[6px\] {
  padding-top    : 6px;
  padding-bottom : 6px
}

.px-\[12px\] {
  padding-left  : 12px;
  padding-right : 12px
}

.py-8 {
  padding-top    : 2rem;
  padding-bottom : 2rem
}

.py-3 {
  padding-top    : .75rem;
  padding-bottom : .75rem
}

.py-7 {
  padding-top    : 1.75rem;
  padding-bottom : 1.75rem
}

.px-5 {
  padding-left  : 1.25rem;
  padding-right : 1.25rem
}

.px-7 {
  padding-left  : 1.75rem;
  padding-right : 1.75rem
}

.pt-5 {
  padding-top : 1.25rem
}

.pt-0 {
  padding-top : 0
}

.pb-6 {
  padding-bottom : 1.5rem
}

.pt-2 {
  padding-top : .5rem
}

.pt-14 {
  padding-top : 3.5rem
}

.pb-3 {
  padding-bottom : .75rem
}

.pt-6 {
  padding-top : 1.5rem
}

.pl-8 {
  padding-left : 2rem
}

.pl-2 {
  padding-left : .5rem
}

.pt-7 {
  padding-top : 1.75rem
}

.pr-7 {
  padding-right : 1.75rem
}

.pb-12 {
  padding-bottom : 3rem
}

.pl-9 {
  padding-left : 2.25rem
}

.pt-3 {
  padding-top : .75rem
}

.pb-2 {
  padding-bottom : .5rem
}

.pr-\[30px\] {
  padding-right : 30px
}

.pl-\[15px\] {
  padding-left : 15px
}

.pt-10 {
  padding-top : 2.5rem
}

.pt-4 {
  padding-top : 1rem
}

.pb-14 {
  padding-bottom : 3.5rem
}

.pb-4 {
  padding-bottom : 1rem
}

.pb-5 {
  padding-bottom : 1.25rem
}

.pt-16 {
  padding-top : 4rem
}

.pl-3 {
  padding-left : .75rem
}

.pl-4 {
  padding-left : 1rem
}

.text-left {
  text-align : left
}

.text-center {
  text-align : center
}

.text-right {
  text-align : right
}

.align-baseline {
  vertical-align : baseline
}

.align-top {
  vertical-align : top
}

.align-middle {
  vertical-align : middle
}

.align-bottom {
  vertical-align : bottom
}


.text-\[13px\] {
  font-size : 13px
}

.text-sm {
  font-size   : .875rem;
  line-height : 1.25rem
}

.text-8xl {
  font-size   : 6rem;
  line-height : 1
}

.text-base {
  font-size   : 1rem;
  line-height : 1.5rem
}

.text-lg {
  font-size   : 1.125rem;
  line-height : 1.75rem
}

.text-\[16px\] {
  font-size : 16px
}

.text-\[12px\] {
  font-size : 12px
}

.text-3xl {
  font-size   : 1.875rem;
  line-height : 2.25rem
}

.text-\[22px\] {
  font-size : 22px
}

.text-\[20px\] {
  font-size : 20px
}

.font-bold {
  font-weight : 700
}

.font-normal {
  font-weight : 400
}

.uppercase {
  text-transform : uppercase
}

.capitalize {
  text-transform : capitalize
}

.leading-8 {
  line-height : 2rem
}

.leading-6 {
  line-height : 1.5rem
}

.leading-\[22px\] {
  line-height : 22px
}

.leading-10 {
  line-height : 2.5rem
}

.leading-5 {
  line-height : 1.25rem
}

.leading-\[34px\] {
  line-height : 34px
}

.leading-7 {
  line-height : 1.75rem
}

.leading-9 {
  line-height : 2.25rem
}

.leading-none {
  line-height : 1
}

.leading-\[14px\] {
  line-height : 14px
}

.tracking-widest {
  letter-spacing : .1em
}

.text-green-500 {
  --tw-text-opacity : 1;
  color             : rgb(34 197 94 / var(--tw-text-opacity))
}

.text-red-500 {
  --tw-text-opacity : 1;
  color             : rgb(239 68 68 / var(--tw-text-opacity))
}

.text-yellow-500 {
  --tw-text-opacity : 1;
  color             : rgb(234 179 8 / var(--tw-text-opacity))
}

.text-black {
  --tw-text-opacity : 1;
  color             : rgb(0 0 0 / var(--tw-text-opacity))
}

.text-\[\#333\] {
  --tw-text-opacity : 1;
  color             : rgb(51 51 51 / var(--tw-text-opacity))
}

.text-\[\#a94442\] {
  --tw-text-opacity : 1;
  color             : rgb(169 68 66 / var(--tw-text-opacity))
}

.text-yellow-700 {
  --tw-text-opacity : 1;
  color             : rgb(161 98 7 / var(--tw-text-opacity))
}

.text-\[\#58b037\] {
  --tw-text-opacity : 1;
  color             : rgb(88 176 55 / var(--tw-text-opacity))
}

.text-\[\#3b76ed\] {
  --tw-text-opacity : 1;
  color             : rgb(59 118 237 / var(--tw-text-opacity))
}

.text-\[\#ff6000\] {
  --tw-text-opacity : 1;
  color             : rgb(255 96 0 / var(--tw-text-opacity))
}

.text-white, .text-customized-text-primary {
  --tw-text-opacity : 1;
  color             : rgb(255 255 255 / var(--tw-text-opacity))
}

.text-customized-bg-quaternary-100 {
  --tw-text-opacity : 1;
  color             : rgb(252 143 0 / var(--tw-text-opacity))
}

.text-\[\#008f36\] {
  --tw-text-opacity : 1;
  color             : rgb(0 143 54 / var(--tw-text-opacity))
}

.text-\[\#666\] {
  --tw-text-opacity : 1;
  color             : rgb(102 102 102 / var(--tw-text-opacity))
}

.text-\[\#204d74\] {
  --tw-text-opacity : 1;
  color             : rgb(32 77 116 / var(--tw-text-opacity))
}

.text-white\/50 {
  color : #ffffff80
}

.text-customized-text-tertiary {
  --tw-text-opacity : 1;
  color             : rgb(255 248 54 / var(--tw-text-opacity))
}

.text-customized-text-senary {
  --tw-text-opacity : 1;
  color             : rgb(95 103 234 / var(--tw-text-opacity))
}

.text-\[\#7d5252\] {
  --tw-text-opacity : 1;
  color             : rgb(125 82 82 / var(--tw-text-opacity))
}

.text-\[\#fe0000\] {
  --tw-text-opacity : 1;
  color             : rgb(254 0 0 / var(--tw-text-opacity))
}

.text-\[\#0c5460\] {
  --tw-text-opacity : 1;
  color             : rgb(12 84 96 / var(--tw-text-opacity))
}

.text-\[\#fedf00\] {
  --tw-text-opacity : 1;
  color             : rgb(254 223 0 / var(--tw-text-opacity))
}

.text-red-600 {
  --tw-text-opacity : 1;
  color             : rgb(220 38 38 / var(--tw-text-opacity))
}

.text-customized-bg-tertiary {
  --tw-text-opacity : 1;
  color             : rgb(255 144 0 / var(--tw-text-opacity))
}

.text-\[\#494949\] {
  --tw-text-opacity : 1;
  color             : rgb(73 73 73 / var(--tw-text-opacity))
}

.text-\[\#141413\] {
  --tw-text-opacity : 1;
  color             : rgb(20 20 19 / var(--tw-text-opacity))
}

.text-\[\#555\] {
  --tw-text-opacity : 1;
  color             : rgb(85 85 85 / var(--tw-text-opacity))
}

.text-\[\#8f8f8f\] {
  --tw-text-opacity : 1;
  color             : rgb(143 143 143 / var(--tw-text-opacity))
}

.text-\[\#999\] {
  --tw-text-opacity : 1;
  color             : rgb(153 153 153 / var(--tw-text-opacity))
}

.text-\[\#ccc\] {
  --tw-text-opacity : 1;
  color             : rgb(204 204 204 / var(--tw-text-opacity))
}

.text-\[\#444\] {
  --tw-text-opacity : 1;
  color             : rgb(68 68 68 / var(--tw-text-opacity))
}

.text-customized-text-quaternary {
  --tw-text-opacity : 1;
  color             : rgb(255 65 123 / var(--tw-text-opacity))
}

.text-customized-text-octonary {
  --tw-text-opacity : 1;
  color             : rgb(255 28 75 / var(--tw-text-opacity))
}

.text-\[\#797979\] {
  --tw-text-opacity : 1;
  color             : rgb(121 121 121 / var(--tw-text-opacity))
}

.text-customized-text-primary-900 {
  --tw-text-opacity : 1;
  color             : rgb(0 0 0 / var(--tw-text-opacity))
}

.text-\[\#777\] {
  --tw-text-opacity : 1;
  color             : rgb(119 119 119 / var(--tw-text-opacity))
}

.text-green-600 {
  --tw-text-opacity : 1;
  color             : rgb(22 163 74 / var(--tw-text-opacity))
}

.text-\[\#337ab7\] {
  --tw-text-opacity : 1;
  color             : rgb(51 122 183 / var(--tw-text-opacity))
}

.text-\[\#f60\] {
  --tw-text-opacity : 1;
  color             : rgb(255 102 0 / var(--tw-text-opacity))
}

.text-\[\#4286f5\] {
  --tw-text-opacity : 1;
  color             : rgb(66 134 245 / var(--tw-text-opacity))
}

.text-\[\#3c763d\] {
  --tw-text-opacity : 1;
  color             : rgb(60 118 61 / var(--tw-text-opacity))
}

.text-\[\#aaa\] {
  --tw-text-opacity : 1;
  color             : rgb(170 170 170 / var(--tw-text-opacity))
}

.text-customized-text-primary-300 {
  --tw-text-opacity : 1;
  color             : rgb(255 144 0 / var(--tw-text-opacity))
}

.text-customized-text-primary-800 {
  --tw-text-opacity : 1;
  color             : rgb(102 102 102 / var(--tw-text-opacity))
}

.text-customized-text-primary-400 {
  --tw-text-opacity : 1;
  color             : rgb(255 182 39 / var(--tw-text-opacity))
}

.text-\[\#ffca00\] {
  --tw-text-opacity : 1;
  color             : rgb(255 202 0 / var(--tw-text-opacity))
}

.text-customized-text-primary-600 {
  --tw-text-opacity : 1;
  color             : rgb(170 170 170 / var(--tw-text-opacity))
}

.text-\[\#e9e9e9\] {
  --tw-text-opacity : 1;
  color             : rgb(233 233 233 / var(--tw-text-opacity))
}

.text-customized-bg-primary {
  --tw-text-opacity : 1;
  color             : rgb(17 17 17 / var(--tw-text-opacity))
}

.text-customized-text-quinary {
  --tw-text-opacity : 1;
  color             : rgb(188 190 204 / var(--tw-text-opacity))
}

.text-customized-text-secondary {
  --tw-text-opacity : 1;
  color             : rgb(153 153 153 / var(--tw-text-opacity))
}

.text-\[\#f00\] {
  --tw-text-opacity : 1;
  color             : rgb(255 0 0 / var(--tw-text-opacity))
}

.underline {
  text-decoration-line : underline
}

.decoration-solid {
  text-decoration-style : solid
}

.underline-offset-auto {
  text-underline-offset : auto
}

.opacity-80 {
  opacity : .8
}

.opacity-20 {
  opacity : .2
}

.opacity-70 {
  opacity : .7
}

.opacity-100 {
  opacity : 1
}



.point {
  position         : absolute;
  top              : -3px;
  right            : 5px;
  width            : 16px;
  height           : 16px;
  background-color : red;
  opacity          : .7;
  border-radius    : 50%
}

.account {
  margin            : 6px 0 0;
  --tw-text-opacity : 1;
  color             : rgb(255 255 255 / var(--tw-text-opacity));
  font-size         : 12px
}


.amount {
  font-size         : 16px;
  --tw-text-opacity : 1;
  color             : rgb(255 196 129 / var(--tw-text-opacity))
}

.amount span {
  display        : inline-block;
  vertical-align : middle;
  white-space    : nowrap
}

.btns {
  --tw-text-opacity : 1;
  color             : rgb(255 255 255 / var(--tw-text-opacity))
}

.btns > button {
  width     : 50%;
  height    : 100%;
  font-size : 16px
}

.btn-register {
  background-image    : linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from  : #ff9000;
  --tw-gradient-stops : var(--tw-gradient-from), var(--tw-gradient-to, rgb(255 144 0 / 0));
  --tw-gradient-to    : #ad1403
}

.btn-login {
  background-image    : linear-gradient(to right, var(--tw-gradient-stops));
  --tw-gradient-from  : #fdc965;
  --tw-gradient-stops : var(--tw-gradient-from), var(--tw-gradient-to, rgb(253 201 101 / 0));
  --tw-gradient-to    : #6c4e13
}

@media (max-width : 375px) {
  .promotion span {
    display       : block;
    overflow      : hidden;
    white-space   : nowrap;
    text-overflow : ellipsis
  }
}

</style>