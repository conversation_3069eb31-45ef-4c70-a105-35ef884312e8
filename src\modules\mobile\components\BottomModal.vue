<script>
import {check} from '@/mixins/check'

export default {
  name:    "BottomModal",
  mixins:  [check],
  methods: {
    webApp() {
      this.$store.commit('setBottomModalStatus', 2)
      if (this.isIos) {
        this.$store.commit('setMobileModalDownloadBar', 2)
      }
    }
  }
}
</script>

<template>
  <div data-reactroot="" class="bottom-modal a2hs light on">
    <div class="bottom-modal--content popup-slide-enter-done">
      <div class="a2hs-close--wrap" @click="$store.commit('setBottomModalStatus', 2)">
        <svg class="a2hs-close" viewBox="0 0 1024 1024">
          <path d="M505.173 416.427L122.88 27.307C95.573 0 54.613 0 20.48 27.307c-27.307 27.306-27.307 68.266 0 102.4L409.6 512 20.48 901.12c-27.307 27.307-27.307 68.267 0 102.4 27.307 27.307 68.267 27.307 102.4 0L512 614.4l389.12 389.12c27.307 27.307 68.267 27.307 102.4 0 27.307-27.307 27.307-68.267 0-102.4L607.573 512l389.12-389.12c27.307-27.307 27.307-68.267 0-102.4-27.306-27.307-68.266-27.307-102.4 0l-389.12 395.947z"></path>
        </svg>
      </div>
      <div class="bottom-modal--content-main">
        <img alt="app" class="bottom-modal--icon" src="/img/icon/57.png">
        <div class="bottom-modal--desc">{{ $t('bottomModal.tip1') }}</div>
      </div>
      <div class="bottom-modal--footer">
        <div class="continue-use" @click="$store.commit('setBottomModalStatus', 2)">{{ $t('bottomModal.tip2') }}</div>
        <div class="bottom-btn-group">
          <div v-if="downUrl" class="bottom-btn bottom-btn--primary" @click="$store.commit('setBottomModalStatus', 2);goUrl(downUrl, false)">
            <div class="bottom-btn-tips common_animate__heartBeat">{{ $t('bottomModal.tip3') }}</div>
            <svg v-if="isIos" viewBox="0 0 40 40" fill="none" class="bottom-btn-icon">
              <g clip-path="url(#a2hs-ios_svg__clip0_98_44)">
                <path d="M38.287 31.172a21.694 21.694 0 01-2.17 3.867c-1.14 1.612-2.075 2.728-2.795 3.348-1.117 1.017-2.313 1.538-3.593 1.568-.92 0-2.028-.26-3.319-.786-1.295-.523-2.485-.782-3.573-.782-1.141 0-2.365.259-3.674.782-1.311.526-2.367.8-3.175.828-1.228.051-2.452-.484-3.674-1.61-.78-.674-1.755-1.83-2.924-3.467-1.253-1.748-2.284-3.775-3.092-6.086C5.434 26.338 5 23.92 5 21.58c0-2.68.585-4.992 1.755-6.93a10.25 10.25 0 013.677-3.685 9.958 9.958 0 014.97-1.39c.975 0 2.254.3 3.844.887 1.585.59 2.603.889 3.05.889.333 0 1.463-.35 3.38-1.047 1.813-.647 3.343-.914 4.596-.809 3.396.272 5.947 1.599 7.644 3.989-3.037 1.823-4.54 4.377-4.51 7.654.028 2.552.962 4.676 2.799 6.363A9.203 9.203 0 0039 29.318a29.7 29.7 0 01-.713 1.854zM30.5.8c0 2-.738 3.869-2.208 5.597-1.774 2.056-3.92 3.243-6.246 3.056a6.162 6.162 0 01-.047-.758c0-1.92.843-3.976 2.342-5.656.748-.851 1.699-1.559 2.852-2.123C28.343.36 29.432.053 30.456 0a7.2 7.2 0 01.043.8z"></path>
              </g>
              <defs>
                <clipPath id="a2hs-ios_svg__clip0_98_44">
                  <path fill="#fff" d="M0 0h40v40H0z"></path>
                </clipPath>
              </defs>
            </svg>
            <svg v-else viewBox="0 0 40 40" fill="none" class="bottom-btn-icon">
              <g clip-path="url(#a2hs-android_svg__clip0_32_511)">
                <path d="M21.678 37.548a2.074 2.074 0 01-.016-.347V31.915h-3.324v5.312a2.415 2.415 0 01-4.789.44 3.11 3.11 0 01-.026-.555v-4.825c0-.114-.011-.228-.018-.374-.13-.01-.243-.024-.357-.027-.211-.007-.422 0-.634 0a7.499 7.499 0 01-1.355-.071 2.507 2.507 0 01-2.037-2.5V14.029c0-.076.008-.151.014-.257h21.722c.*************.021.283v15.216a2.579 2.579 0 01-2.62 2.615h-1.433c-.1 0-.2.015-.326.026-.009.13-.022.242-.022.354v4.894a2.376 2.376 0 01-2.113 2.458 3.007 3.007 0 01-.256.011 2.393 2.393 0 01-2.431-2.081zM3.359 26.152a2.566 2.566 0 01-.026-.462v-4.962-4.961a2.367 2.367 0 012.128-2.42 2.4 2.4 0 012.672 2.033c.016.13.021.262.016.393v9.947a2.4 2.4 0 01-2.222 2.436c-.072.007-.143.01-.214.01a2.43 2.43 0 01-2.354-2.014zm28.516-.017a2.296 2.296 0 01-.021-.416v-9.947a2.41 2.41 0 014.781-.466c.026.16.036.322.03.484v9.924a2.4 2.4 0 01-2.423 2.45 2.427 2.427 0 01-2.367-2.029zM9.106 12.79a9.965 9.965 0 015.579-8.587l-1.282-2.288a53.23 53.23 0 00-.408-.731c-.1-.185-.123-.371.093-.477.2-.1.355-.013.464.182.525.941 1.052 1.88 1.581 2.818.04.073.089.141.144.227a11.92 11.92 0 019.453.005l.794-1.406c.308-.547.618-1.093.921-1.643a.329.329 0 01.466-.183c.217.106.2.292.094.476l-1.557 2.784c-.041.073-.079.148-.134.251a10.046 10.046 0 015.119 5.929c.225.871.405 1.753.539 2.643H9.106zm14.982-4.767a.946.946 0 001.891-.007.939.939 0 00-.925-.921h-.011a.937.937 0 00-.955.928zm-10.067-.018a.945.945 0 10.96-.91h-.012a.937.937 0 00-.948.91z"></path>
                <path d="M24.108 39.63c.084 0 .169-.003.255-.01 1.211-.105 2.112-1.15 2.114-2.459.002-1.631 0-3.263.002-4.894 0-.112.013-.225.022-.354.128-.01.227-.026.326-.026h.624c.27 0 .539 0 .809-.002 1.492-.016 2.62-1.138 2.62-2.616.002-5.072 0-10.144 0-15.216 0-.09-.014-.179-.022-.283H9.134c-.006.106-.015.181-.015.257 0 5.095-.002 10.19.001 15.286 0 1.214.834 2.287 2.038 2.5.442.077.898.074 1.354.07.212-.001.424-.003.634.004.113.003.226.017.357.027.007.145.018.26.018.373.001 1.609 0 3.217.002 4.825 0 .185-.008.375.026.555.232 1.256 1.34 2.063 2.672 1.952 1.164-.096 2.116-1.17 2.117-2.392.002-1.662 0-3.325 0-4.987v-.325h3.323v.322l.001 4.964c0 .115-.003.233.016.346.22 1.274 1.181 2.083 2.43 2.083zM5.709 28.166c.071 0 .143-.003.215-.01 1.294-.121 2.221-1.135 2.222-2.436.002-3.316 0-6.631 0-9.947 0-.131.005-.265-.016-.393-.218-1.314-1.293-2.13-2.669-2.033-1.195.085-2.125 1.14-2.127 2.42-.002 1.653 0 3.307 0 4.961v4.962c.001.154-.003.311.025.462.22 1.175 1.222 2.014 2.35 2.014zm28.533-.002c.061 0 .123-.002.185-.007 1.288-.1 2.236-1.131 2.238-2.443.002-1.661 0-3.323 0-4.985 0-1.646.002-3.293 0-4.939 0-.161.001-.326-.03-.484-.259-1.297-1.339-2.081-2.682-1.957-1.19.109-2.099 1.155-2.1 2.423v9.947c0 .139-.003.28.022.416.22 1.191 1.216 2.03 2.367 2.03zM30.976 12.79c-.182-.916-.279-1.804-.54-2.643-.835-2.688-2.646-4.593-5.122-5.933.055-.103.093-.178.134-.25l1.557-2.78c.103-.185.123-.371-.094-.477-.2-.097-.356-.012-.463.183-.303.55-.614 1.096-.922 1.643l-.794 1.406c-3.162-1.305-6.304-1.305-9.452-.005-.055-.086-.104-.154-.144-.227-.53-.938-1.06-1.876-1.585-2.817-.108-.195-.263-.28-.463-.182-.217.106-.196.292-.093.477.135.244.271.487.408.73l1.282 2.288C11.284 6.11 9.31 8.873 9.106 12.79h21.87zm-5.934-5.695h.012a.939.939 0 01.925.921c.002.518-.429.93-.967.925a.927.927 0 01-.924-.918c-.004-.513.424-.928.954-.928zm-10.073 0h.012c.518.006.945.437.93.94-.015.52-.427.91-.96.906a.933.933 0 01-.93-.936.937.937 0 01.948-.91zm9.139 33.535c-1.742 0-3.115-1.17-3.416-2.912-.031-.186-.03-.353-.03-.476v-.041-4.286h-1.323v.734l-.001 3.579c-.001.835-.311 1.645-.874 2.28a3.247 3.247 0 01-2.484 1.121c-1.708 0-3.112-1.143-3.415-2.78a3.414 3.414 0 01-.043-.653v-.082-2.657-1.574h-.003l-.288.002c-.39 0-.81-.011-1.247-.088-1.659-.293-2.863-1.758-2.864-3.483V28.2a3.457 3.457 0 01-2.41.966c-1.624 0-3.026-1.19-3.334-2.83a3.093 3.093 0 01-.041-.59v-.053c-.002-1.336-.002-2.67-.001-4.005V15.764c.003-1.792 1.345-3.292 3.056-3.414.098-.007.196-.01.292-.01.948 0 1.783.33 2.4.903l.025-.505c.11-2.094.7-3.96 1.757-5.549.847-1.273 2.014-2.394 3.475-3.342A3662.497 3662.497 0 0112.12 1.67c-.257-.464-.22-.854-.143-1.098.104-.33.342-.6.67-.762.194-.095.396-.143.6-.143.488 0 .917.268 1.178.736.427.765.864 1.541 1.29 2.297 1.423-.492 2.86-.74 4.282-.74 1.422 0 2.86.248 4.287.74l.371-.658.24-.424c.223-.397.454-.807.677-1.21.26-.471.69-.741 1.18-.741.204 0 .405.048.597.142.33.16.57.432.674.763.077.246.114.636-.145 1.1a3763.01 3763.01 0 01-1.217 2.173c2.369 1.503 3.958 3.52 4.73 6.005.198.636.308 1.282.415 1.908.047.275.096.56.15.838l.103.515a3.228 3.228 0 011.802-.758c.122-.011.246-.017.367-.017 1.695 0 3.056 1.115 3.388 2.774.048.245.048.469.048.649v.03c.002 1.244.002 2.489.002 3.733v6.194a3.47 3.47 0 01-.9 2.35 3.409 3.409 0 01-2.524 1.098 3.362 3.362 0 01-2.362-.976v1.082c0 .981-.371 1.892-1.044 2.563-.67.668-1.581 1.042-2.566 1.052a54.85 54.85 0 01-.513.002h-.279v1.575l-.001 2.7c-.003 1.82-1.304 3.305-3.028 3.453a4.01 4.01 0 01-.341.015z"></path>
              </g>
              <defs>
                <clipPath id="a2hs-android_svg__clip0_32_511">
                  <path fill="#fff" d="M0 0h40v40H0z"></path>
                </clipPath>
              </defs>
            </svg>
            <!-- react-text: 189 -->APP<!-- /react-text --></div>
          <button class="bottom-btn bottom-btn--secondary" @click="webApp" v-if="isIos && !standalone">
            <svg v-if="isIos" viewBox="0 0 40 40" fill="none" class="bottom-btn-icon">
              <g clip-path="url(#a2hs-ios_svg__clip0_98_44)">
                <path d="M38.287 31.172a21.694 21.694 0 01-2.17 3.867c-1.14 1.612-2.075 2.728-2.795 3.348-1.117 1.017-2.313 1.538-3.593 1.568-.92 0-2.028-.26-3.319-.786-1.295-.523-2.485-.782-3.573-.782-1.141 0-2.365.259-3.674.782-1.311.526-2.367.8-3.175.828-1.228.051-2.452-.484-3.674-1.61-.78-.674-1.755-1.83-2.924-3.467-1.253-1.748-2.284-3.775-3.092-6.086C5.434 26.338 5 23.92 5 21.58c0-2.68.585-4.992 1.755-6.93a10.25 10.25 0 013.677-3.685 9.958 9.958 0 014.97-1.39c.975 0 2.254.3 3.844.887 1.585.59 2.603.889 3.05.889.333 0 1.463-.35 3.38-1.047 1.813-.647 3.343-.914 4.596-.809 3.396.272 5.947 1.599 7.644 3.989-3.037 1.823-4.54 4.377-4.51 7.654.028 2.552.962 4.676 2.799 6.363A9.203 9.203 0 0039 29.318a29.7 29.7 0 01-.713 1.854zM30.5.8c0 2-.738 3.869-2.208 5.597-1.774 2.056-3.92 3.243-6.246 3.056a6.162 6.162 0 01-.047-.758c0-1.92.843-3.976 2.342-5.656.748-.851 1.699-1.559 2.852-2.123C28.343.36 29.432.053 30.456 0a7.2 7.2 0 01.043.8z"></path>
              </g>
              <defs>
                <clipPath id="a2hs-ios_svg__clip0_98_44">
                  <path fill="#fff" d="M0 0h40v40H0z"></path>
                </clipPath>
              </defs>
            </svg>
            <svg v-else viewBox="0 0 40 40" fill="none" class="bottom-btn-icon">
              <g clip-path="url(#a2hs-android_svg__clip0_32_511)">
                <path d="M21.678 37.548a2.074 2.074 0 01-.016-.347V31.915h-3.324v5.312a2.415 2.415 0 01-4.789.44 3.11 3.11 0 01-.026-.555v-4.825c0-.114-.011-.228-.018-.374-.13-.01-.243-.024-.357-.027-.211-.007-.422 0-.634 0a7.499 7.499 0 01-1.355-.071 2.507 2.507 0 01-2.037-2.5V14.029c0-.076.008-.151.014-.257h21.722c.*************.021.283v15.216a2.579 2.579 0 01-2.62 2.615h-1.433c-.1 0-.2.015-.326.026-.009.13-.022.242-.022.354v4.894a2.376 2.376 0 01-2.113 2.458 3.007 3.007 0 01-.256.011 2.393 2.393 0 01-2.431-2.081zM3.359 26.152a2.566 2.566 0 01-.026-.462v-4.962-4.961a2.367 2.367 0 012.128-2.42 2.4 2.4 0 012.672 2.033c.016.13.021.262.016.393v9.947a2.4 2.4 0 01-2.222 2.436c-.072.007-.143.01-.214.01a2.43 2.43 0 01-2.354-2.014zm28.516-.017a2.296 2.296 0 01-.021-.416v-9.947a2.41 2.41 0 014.781-.466c.026.16.036.322.03.484v9.924a2.4 2.4 0 01-2.423 2.45 2.427 2.427 0 01-2.367-2.029zM9.106 12.79a9.965 9.965 0 015.579-8.587l-1.282-2.288a53.23 53.23 0 00-.408-.731c-.1-.185-.123-.371.093-.477.2-.1.355-.013.464.182.525.941 1.052 1.88 1.581 2.818.04.073.089.141.144.227a11.92 11.92 0 019.453.005l.794-1.406c.308-.547.618-1.093.921-1.643a.329.329 0 01.466-.183c.217.106.2.292.094.476l-1.557 2.784c-.041.073-.079.148-.134.251a10.046 10.046 0 015.119 5.929c.225.871.405 1.753.539 2.643H9.106zm14.982-4.767a.946.946 0 001.891-.007.939.939 0 00-.925-.921h-.011a.937.937 0 00-.955.928zm-10.067-.018a.945.945 0 10.96-.91h-.012a.937.937 0 00-.948.91z"></path>
                <path d="M24.108 39.63c.084 0 .169-.003.255-.01 1.211-.105 2.112-1.15 2.114-2.459.002-1.631 0-3.263.002-4.894 0-.112.013-.225.022-.354.128-.01.227-.026.326-.026h.624c.27 0 .539 0 .809-.002 1.492-.016 2.62-1.138 2.62-2.616.002-5.072 0-10.144 0-15.216 0-.09-.014-.179-.022-.283H9.134c-.006.106-.015.181-.015.257 0 5.095-.002 10.19.001 15.286 0 1.214.834 2.287 2.038 2.5.442.077.898.074 1.354.07.212-.001.424-.003.634.004.113.003.226.017.357.027.007.145.018.26.018.373.001 1.609 0 3.217.002 4.825 0 .185-.008.375.026.555.232 1.256 1.34 2.063 2.672 1.952 1.164-.096 2.116-1.17 2.117-2.392.002-1.662 0-3.325 0-4.987v-.325h3.323v.322l.001 4.964c0 .115-.003.233.016.346.22 1.274 1.181 2.083 2.43 2.083zM5.709 28.166c.071 0 .143-.003.215-.01 1.294-.121 2.221-1.135 2.222-2.436.002-3.316 0-6.631 0-9.947 0-.131.005-.265-.016-.393-.218-1.314-1.293-2.13-2.669-2.033-1.195.085-2.125 1.14-2.127 2.42-.002 1.653 0 3.307 0 4.961v4.962c.001.154-.003.311.025.462.22 1.175 1.222 2.014 2.35 2.014zm28.533-.002c.061 0 .123-.002.185-.007 1.288-.1 2.236-1.131 2.238-2.443.002-1.661 0-3.323 0-4.985 0-1.646.002-3.293 0-4.939 0-.161.001-.326-.03-.484-.259-1.297-1.339-2.081-2.682-1.957-1.19.109-2.099 1.155-2.1 2.423v9.947c0 .139-.003.28.022.416.22 1.191 1.216 2.03 2.367 2.03zM30.976 12.79c-.182-.916-.279-1.804-.54-2.643-.835-2.688-2.646-4.593-5.122-5.933.055-.103.093-.178.134-.25l1.557-2.78c.103-.185.123-.371-.094-.477-.2-.097-.356-.012-.463.183-.303.55-.614 1.096-.922 1.643l-.794 1.406c-3.162-1.305-6.304-1.305-9.452-.005-.055-.086-.104-.154-.144-.227-.53-.938-1.06-1.876-1.585-2.817-.108-.195-.263-.28-.463-.182-.217.106-.196.292-.093.477.135.244.271.487.408.73l1.282 2.288C11.284 6.11 9.31 8.873 9.106 12.79h21.87zm-5.934-5.695h.012a.939.939 0 01.925.921c.002.518-.429.93-.967.925a.927.927 0 01-.924-.918c-.004-.513.424-.928.954-.928zm-10.073 0h.012c.518.006.945.437.93.94-.015.52-.427.91-.96.906a.933.933 0 01-.93-.936.937.937 0 01.948-.91zm9.139 33.535c-1.742 0-3.115-1.17-3.416-2.912-.031-.186-.03-.353-.03-.476v-.041-4.286h-1.323v.734l-.001 3.579c-.001.835-.311 1.645-.874 2.28a3.247 3.247 0 01-2.484 1.121c-1.708 0-3.112-1.143-3.415-2.78a3.414 3.414 0 01-.043-.653v-.082-2.657-1.574h-.003l-.288.002c-.39 0-.81-.011-1.247-.088-1.659-.293-2.863-1.758-2.864-3.483V28.2a3.457 3.457 0 01-2.41.966c-1.624 0-3.026-1.19-3.334-2.83a3.093 3.093 0 01-.041-.59v-.053c-.002-1.336-.002-2.67-.001-4.005V15.764c.003-1.792 1.345-3.292 3.056-3.414.098-.007.196-.01.292-.01.948 0 1.783.33 2.4.903l.025-.505c.11-2.094.7-3.96 1.757-5.549.847-1.273 2.014-2.394 3.475-3.342A3662.497 3662.497 0 0112.12 1.67c-.257-.464-.22-.854-.143-1.098.104-.33.342-.6.67-.762.194-.095.396-.143.6-.143.488 0 .917.268 1.178.736.427.765.864 1.541 1.29 2.297 1.423-.492 2.86-.74 4.282-.74 1.422 0 2.86.248 4.287.74l.371-.658.24-.424c.223-.397.454-.807.677-1.21.26-.471.69-.741 1.18-.741.204 0 .405.048.597.142.33.16.57.432.674.763.077.246.114.636-.145 1.1a3763.01 3763.01 0 01-1.217 2.173c2.369 1.503 3.958 3.52 4.73 6.005.198.636.308 1.282.415 1.908.047.275.096.56.15.838l.103.515a3.228 3.228 0 011.802-.758c.122-.011.246-.017.367-.017 1.695 0 3.056 1.115 3.388 2.774.048.245.048.469.048.649v.03c.002 1.244.002 2.489.002 3.733v6.194a3.47 3.47 0 01-.9 2.35 3.409 3.409 0 01-2.524 1.098 3.362 3.362 0 01-2.362-.976v1.082c0 .981-.371 1.892-1.044 2.563-.67.668-1.581 1.042-2.566 1.052a54.85 54.85 0 01-.513.002h-.279v1.575l-.001 2.7c-.003 1.82-1.304 3.305-3.028 3.453a4.01 4.01 0 01-.341.015z"></path>
              </g>
              <defs>
                <clipPath id="a2hs-android_svg__clip0_32_511">
                  <path fill="#fff" d="M0 0h40v40H0z"></path>
                </clipPath>
              </defs>
            </svg>
            <!-- react-text: 197 -->WEB-APP<!-- /react-text --></button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.bottom-modal {
  position                : fixed;
  top                     : 0;
  width                   : var(--theme-max-width);
  height                  : 100%;
  background              : rgba(0, 0, 0, .65);
  -webkit-backdrop-filter : blur(.05rem);
  backdrop-filter         : blur(.05rem);
  display                 : none;
  z-index                 : 2147483640;
  font-size               : .28rem;
  color                   : var(--a2hs-font-color, #111)
}

.bottom-modal.on {
  display : block
}

.bottom-modal--icon {
  width         : 1.28rem;
  height        : 1.28rem;
  border-radius : .08rem;
  flex          : 0 0 1.28rem
}

.bottom-modal--content {
  position         : fixed;
  bottom           : 0;
  padding          : .4rem;
  width            : 100%;
  background-color : var(--a2hs-bg-color, #fff);
  transition       : all .25s
}

.bottom-modal--content.ios-tg {
  bottom : 1.3rem
}

.bottom-modal--content-main {
  display         : grid;
  grid-column-gap : .18rem;
  flex-direction  : row;
  margin-bottom   : .188rem;
  grid-auto-flow  : column
}

.bottom-modal .btn-group {
  display         : flex;
  justify-content : flex-end
}

.bottom-modal .bottom-btn {
  all             : unset;
  display         : flex;
  align-items     : center;
  justify-content : center;
  font-size       : .24rem;
  border-radius   : .08rem;
  padding         : .2rem .5rem
}

.bottom-modal .bottom-btn--cancel {
  color : var(--a2hs-btn-cancel-font-color, #111)
}

.bottom-modal .bottom-btn--agree {
  background-color : var(--a2hs-btn-bg-color, #009d81);
  color            : var(--a2hs-btn-color, #fff)
}

.bottom-modal .popup-slide-enter {
  opacity   : 0;
  transform : translateY(100%)
}

.bottom-modal .popup-slide-enter-active, .bottom-modal .popup-slide-exit {
  opacity   : 1;
  transform : translateY(0)
}

.bottom-modal .popup-slide-exit-active {
  opacity   : 0;
  transform : translateY(100%)
}

.activity-goto-btn {
  display          : grid !important;
  place-items      : center;
  border-radius    : .3rem;
  min-height       : .6rem;
  margin           : 0 auto;
  padding          : 0 .2rem;
  width            : -webkit-fit-content;
  width            : fit-content;
  color            : var(--goto-color);
  background-color : var(--goto-bg-color);
  background-image : var(--goto-bg-img)
}

.bottom-modal.a2hs .bottom-btn-tips {
  position        : absolute;
  top             : -.26rem;
  right           : 0;
  display         : flex;
  align-items     : center;
  justify-content : center;
  color           : #fff;
  font-size       : .16rem;
  font-weight     : 400;
  border-radius   : .125rem .125rem .125rem 0;
  background      : #ea4e3d;
  line-height     : 1.4;
  padding         : .04rem .2rem
}

.bottom-modal.a2hs {
  background : unset
}

.bottom-modal.a2hs .bottom-modal--content-main {
  grid-template-columns : 1.2rem 1fr
}

.bottom-modal.a2hs .bottom-modal--content-main.one {
  grid-template-columns : 1fr
}

.bottom-modal.a2hs.dark .bottom-modal--content {
  background              : unset;
  background-color        : rgba(0, 0, 0, .8);
  box-shadow              : 0 -.04rem .1rem 0 hsla(0, 0%, 100%, .25);
  -webkit-backdrop-filter : blur(.05rem);
  backdrop-filter         : blur(.05rem)
}

.bottom-modal.a2hs.dark .continue-use {
  color : #fff60a
}

.bottom-modal.a2hs.dark .bottom-btn--primary {
  border           : .02rem solid #16b9ff;
  background-color : #16b9ff
}

.bottom-modal.a2hs.dark .bottom-btn--secondary {
  border : .02rem solid #16b9ff;
  color  : #16b9ff
}

.bottom-modal.a2hs.dark .bottom-modal--desc {
  color : #999
}

.bottom-modal.a2hs.light .bottom-modal--content {
  background              : unset;
  background              : hsla(0, 0%, 100%, .8);
  box-shadow              : 0 -.04rem .1rem 0 rgba(0, 0, 0, .25);
  -webkit-backdrop-filter : blur(.05rem);
  backdrop-filter         : blur(.05rem)
}

.bottom-modal.a2hs.light .continue-use {
  color : #16b9ff
}

.bottom-modal.a2hs.light .bottom-modal--desc {
  color : #323232
}

.bottom-modal.a2hs.light .bottom-modal--content-main {
  border-bottom : .02rem solid #cbcbcb
}

.bottom-modal.a2hs .a2hs-close--wrap {
  position         : absolute;
  top              : .3rem;
  right            : .2rem;
  width            : .5rem;
  height           : .5rem;
  background-color : #868686;
  border-radius    : 50%;
  display          : flex;
  align-items      : center;
  justify-content  : center
}

.bottom-modal.a2hs .a2hs-close {
  width  : 50%;
  height : 50%;
  fill   : #fff
}

.bottom-modal.a2hs .bottom-modal--content {
  border-radius : .15rem .15rem 0 0;
  padding       : .14rem .3rem .32rem
}

.bottom-modal.a2hs .bottom-modal--desc {
  font-size   : .26rem;
  font-weight : 700;
  line-height : 1.4;
  width       : 94%
}

.bottom-modal.a2hs .bottom-modal--icon {
  width      : 1.2rem;
  height     : 1.2rem;
  object-fit : contain
}

.bottom-modal.a2hs .bottom-modal--content-main {
  padding       : .09rem .14rem;
  border-bottom : .02rem solid #999;
  align-items   : center
}

.bottom-modal.a2hs .continue-use {
  text-align      : center;
  font-size       : .28rem;
  font-weight     : 600;
  margin-bottom   : .25rem;
  text-decoration : underline
}

.bottom-modal.a2hs .bottom-btn {
  position      : relative;
  line-height   : 0;
  font-size     : .24rem;
  height        : .7rem;
  width         : 2.96rem;
  padding       : 0;
  border-radius : .14rem;
  border        : .02rem solid #1678ff
}

.bottom-modal.a2hs .bottom-btn + .bottom-btn {
  margin-left : .15rem
}

.bottom-modal.a2hs .bottom-btn--primary {
  background-color : #1678ff;
  color            : #fff
}

.bottom-modal.a2hs .bottom-btn--secondary {
  background : hsla(0, 0%, 100%, 0);
  box-shadow : 0 .04rem 0 0 rgba(0, 0, 0, .02);
  color      : #1678ff
}

.bottom-modal.a2hs .bottom-btn-group {
  display         : flex;
  justify-content : center
}

.bottom-modal.a2hs .bottom-btn-icon {
  fill         : currentColor;
  width        : .4rem;
  height       : .4rem;
  margin-right : .12rem
}


</style>