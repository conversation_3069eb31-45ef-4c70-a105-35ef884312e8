<script>

import {earncash} from "@/mixins/activity/earncash";
import CountTo from "vue-count-to";

export default {
  name: "EarnWheel",
  components: {CountTo},
  mixins: [earncash],
  data() {
    return {
      blocksImgs : [
        {
          src: "img/activity/15/bg_6.png",
          height: '4.86rem',
          top: '.25rem',
          rotate: true,
        },
        {
          src: "img/activity/15/bg_5.png",
          height: '5.37rem',
        },

      ],
      blocksImgsSelected : [
        {
          src: "img/activity/15/bg_6.png",
          height: '4.86rem',
          top: '.25rem',
          rotate: true,
        },
        {
          src: "img/activity/15/bg_5.png",
          height: '5.37rem',
        },
        {
          src: "img/activity/15/bg_8.png",
          height: '2.3rem',
          top: '.2rem',
        },
        {
          src: "img/activity/15/bg_9.png",
          height: '2.1rem',
          top: '.44rem',
        },
      ],
      show: true,
      width: "5.37rem",
      height: "5.37rem",
      blocks: [
        {
          padding: ".76rem",
          imgs: [],
        },
      ],
    }
  },
  computed: {
    buttons() {
      return [
        {
          radius: "45%",
          pointer: true,
          fonts: [
            {
              text: this.btnText,
              top: this.upIng && this.rewardValue ? "-.22rem" : "-.36rem",
              fontColor: "white",
              fontSize: this.upIng && this.rewardValue ? ".4rem" : ".6rem",
              fontWeight: 800
            },
          ],
          imgs: [
            {
              src: "img/activity/15/bg_7.png",
              top: "-1.25rem",
              height: '2.3rem',
            },
          ],
        },
      ]
    },
    prizes() {
      let wheelConf = this.Details.wheelConf.length ? this.Details.wheelConf : [{
        "wheelId": 1,
        "rewardType": 1
      }, {"wheelId": 2, "rewardType": 2, "value": 1000}, {"wheelId": 3, "rewardType": 2, "value": 50}, {
        "wheelId": 4,
        "rewardType": 3
      }, {"wheelId": 5, "rewardType": 1}, {"wheelId": 6}, {"wheelId": 7, "rewardType": 4}, {
        "wheelId": 8,
        "rewardType": 2,
        "value": 1
      }];
      let list = [];
      let img = ""
      for (let i = 0; i < wheelConf.length; i++) {
        switch (wheelConf[i].rewardType) {
          case 0:
            img = "img/activity/15/thanks.png"
            break;
          case 1:
            img = "img/activity/15/cash.png"
            break;
          case 2:
            break;
          case 3:
            img = "img/activity/15/sacar.png"
            break;
          case 4:
            img = "img/activity/15/gold.png"
            break;

        }
        if (wheelConf[i].rewardType === 2) {
          list.push({
            fonts: [
              {
                text: wheelConf[i].value,
                fontColor: "white",
                fontSize: ".36rem",
                fontWeight: 700
              },
            ],
          });
        } else {
          list.push({
            imgs: [
              {
                src: img,
                height: ".7rem",
              },
            ],
          });
        }

      }
      return list;
    },
  },
}
</script>

<template>
  <div>
    <van-popup v-model="showWheel" :close-on-click-overlay="false" style="overflow: visible;background : transparent !important;">
      <div style="width: 6.99rem; height: 6.61rem;position: relative; overflow: visible">
        <img src="img/activity/15/bg_1.png" style="position: absolute; top:0; left:0; width: 6.99rem; height: 6.61rem;" alt="">
        <img src="img/activity/15/bg_2.png" style="position: absolute; top:-0.65rem; left:0.3rem; width: 6.47rem; height: 3.99rem;" alt="">
        <img src="img/activity/15/bg_3.png" style="position: absolute; top:-0.69rem; left:1.37rem; width: 4.26rem; height: 4.32rem;" alt="">
        <img @click="$store.commit('setActivity15Step', 0);$store.commit('setShowBannersNotice', true)" src="img/activity/15/close.png" style="position: absolute; top:-0.32rem; right:0.38rem; width: 0.46rem; height: 0.46rem;" alt="">
        <div style="position: absolute; top: 2.09rem; left: 0.77rem">
          <LuckyWheel
              ref="myLucky"
              :width="width"
              :height="height"
              :prizes="prizes"
              :blocks="blocks"
              :buttons="buttons"
              @start="valid"
              @end="endCallback(blocksImgs, blocksImgsSelected,true)"
              :default-config="{
       offsetDegree: 360/8/2
      }"
          />
        </div>
        <img src="img/activity/15/bg_4.png" style="position: absolute; bottom:-1.2rem; left:-0.2rem; width: 7.28rem; height: 2.01rem;" alt="">
        <div style="
                      position: absolute; bottom:-1.33rem;
                      width: 100%;

                      font-weight: 700;
                      font-size: 0.39rem;
                      color: #FFEDAD;
                      text-shadow: 0rem 0rem 0rem rgba(0,0,0,0.69);
                      text-stroke: 0.01rem #FF0A0A;

                      font-family: Segoe UI;
                      text-align: center;

                    ">
          <div style="transform-origin: bottom center; display: inline-block; transform: rotate(10deg) translateX(-2%) translateY(-38%);">REC</div>
          <div style="transform-origin: bottom center; display: inline-block; transform: rotate(2deg) translateX(7%) translateY(-26%);">EBA</div>
          <div style="transform-origin: bottom center; display: inline-block; transform: rotate(0deg) translateX(6%) translateY(-16%);
              font-weight: 900;
              font-size: 0.53rem;
              color: #FFCB16;">{{ $store.state.configs.currency_symbol }}</div>
          <div style="transform-origin: bottom center; display: inline-block; transform: rotate(0deg) translateX(-3%) translateY(-9%);
              font-weight: 900;
              font-size: 0.88rem;
              color: #FFCB16;">{{ Details.withdrawalAmount / 100 }}</div>
          <div style="transform-origin: bottom center; display: inline-block; transform: rotate(-2deg) translateX(-6%) translateY(-70%);">DE</div>
          <div style="transform-origin: bottom center; display: inline-block; transform: rotate(7deg) translateX(-10%) translateY(-58%);">GRCA</div>
        </div>
        <span style="
                      position: absolute; bottom:-1.4rem;; left:-0.1rem;
                      width: 100%;
                      text-align: center;
                      height: 0.22rem;
                      font-weight: 400;
                      font-size: 0.28rem;
                      color: #FFEDAD;
                      font-style: italic;">
          {{ $t('activity_15_tips') }}
        </span>
      </div>
    </van-popup>
    <van-popup closeable round v-model="showRes" :close-on-click-overlay="false" style="width: 6.82rem;
  height: 6.13rem;
  background: #2B3248;">
      <div style="margin-top: 0.19rem; margin-left: 0.32rem;display: flex;align-items: center">
        <img src="img/activity/15/cash_1.png" alt="" style="width: 0.41rem; height: 0.37rem">
        <span style="font-size: 0.23rem;
color: #FFFFFF;margin-left: .2rem ;">{{ $t('activity_15_title', {0: $store.state.configs.currency_symbol, 1: Details.withdrawalAmount / 100}) }}</span>
      </div>
      <div style="height: 90%;display: flex; flex-direction: column; justify-content: space-around; align-items: center;font-size: 0.23rem;">
        <div style="display: flex; align-items: center">
          <img src="img/activity/15/btn_coin.png" alt="" style="height: 0.33rem; width: 0.33rem">
          <span style="
color: #BCCCFF;
line-height: 0.25rem;margin-left: .13rem;">{{ $t('activity_15_text_1') }}</span>
        </div>
        <div style="color: #41DFED;">{{ $t('activity_15_text_7') }}</div>
        <div style="font-weight: bold;
font-size: 0.88rem;
color: #FFFFFF;
line-height: 1.13rem;">{{ $store.state.configs.currency_symbol }}
          <count-to :start-val='preCashAmount' :end-val="Details.currentCashAmount / 100" :duration='1000' :decimals='2' :autoplay=true />
        </div>

        <div style="text-align: center; width: 6.2rem;">
          <van-progress :percentage="Details.currentCashAmount / 100" stroke-width="10" text-color="white" color="#FFBA00" track-color="#F22626" :show-pivot="true"/>
          <div style="font-weight: 600; margin-top: .1rem;
color: #FFFFFF;" v-html="$t('activity_15_text_2', {0: cha})"></div>
        </div>

        <div style="width: 6rem;
height: .68rem;
background: #5FC975;line-height: .68rem;
border-radius: .18rem;text-align: center">
        <span style="font-weight: bold;
font-size: .28rem;
color: #FFFFFF;" @click="showRes = false;$router.push('/m/rewardCenter/EarnCashByPromote')">{{ $t('activity_15_text_8') }}</span>
        </div>
<!--        <div style="width: 6.2rem;-->
<!--height: 3rem;-->
<!--background: #191E2B;-->
<!--border-radius: .12rem;overflow: scroll; text-align: center">-->
<!--          <div v-for="item in 20" :key="item" :title="item" style="display: flex; justify-content: space-around; align-items: center; padding: .12rem 0;font-family: Arial;-->
<!--font-weight: 400;-->
<!--color: #7181A6;">-->
<!--            <div>5066**********</div>-->
<!--            <div>Acabou de sac</div>-->
<!--            <div style="color: #5FC975;">-->
<!--              + 100 <span style="background-color: #5FC975;color: white; border-radius: 50%; padding: .06rem">R$</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </div>-->
      </div>
    </van-popup>
  </div>
</template>

<style scoped>
</style>