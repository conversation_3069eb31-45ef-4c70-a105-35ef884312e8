<script>
import {FieldRenderType} from "@/utils/common"
export default {
  name: "FieldRender",
  computed: {
    FieldRenderType() {
      return FieldRenderType
    }
  },
  props: {
    field: {
      type: Object,
      required: true,
    },
    value: {
      required: true
    },
  }
}
</script>

<template>
  <span v-if="field['render'] === FieldRenderType.datetime">{{ value | datetimeFormat }}</span>
  <span v-else-if="field['render'] === FieldRenderType.datetime_s">{{ value*1000 | datetimeFormat }}</span>
  <span v-else-if="field['render'] === FieldRenderType.date">{{ value | dateFormat }}</span>
  <span v-else-if="field['render'] === FieldRenderType.hideStr">{{ value | hideStr }}</span>
  <span v-else-if="field['render'] === FieldRenderType.currency">{{ value | currency }}</span>
  <span v-else-if="field['render'] === FieldRenderType.formatGold">{{ value | formatGold }}</span>
  <span v-else-if="field['render'] === FieldRenderType.formatGoldWithK">{{ value | formatGoldWithK }}</span>
  <span v-else-if="field['render'] === FieldRenderType.customTemplate" >{{ field['customTemplate'](value) }}</span>
  <span v-else>
    <svg v-if="field['copy']" style="margin-right: .1rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="value">
          <use xlink:href="#icon-copy"></use>
    </svg>
    {{ value ? value: field['default'] }}
  </span>
</template>

<style scoped>

</style>