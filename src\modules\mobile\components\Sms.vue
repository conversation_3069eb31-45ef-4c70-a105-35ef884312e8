<script>
import {getRandomInt, isMobile} from '@/utils/common'
import {regex} from '@/mixins/regex'

export default {
  props: {
    phone: {

    }
  },
  name: "Sms",
  data() {
    return {
      tabIndex: 0,
      btnNum: [],


      countdown: 0,
      loading: false,
    }
  },
  mixins: [regex],
  computed: {
    smsContent() {
      return [this.$store.state.configs.sms_content, `1${this.$store.getters.tempAccount}`].join(" ");
    }
  },
  methods:  {
    onChange() {
      this.$emit("smsBtn", this.btnNum[this.tabIndex])
    },
    setInterval() {
      this.countdown = Math.round(
          (this.$store.state.smsExpiresTime - Date.now()) / 1000
      );
      if (this.countdown) {
        this.timer = setInterval(() => {
          if (this.countdown > 0) {
            this.countdown -= 1;
          } else {
            clearInterval(this.timer);
          }
        }, 1000);
      }
    },
    isMobile,
    send() {
      if (!isMobile()) return
      return window.open(`sms:${this.$store.state.configs.sms_phone}?&body=${this.smsContent}`, "_self")
    },
    authCode(voice) {
      this.loading = true;
      this.$tdtcApi.getAuth({
        method: 'post',
        url:    '/code',
        data:  {
          phone: this.phone,
          voice: voice
        }
      })
          .then((result) => {
            if (200 === result["code"]) {
              this.$emit("smsFocus", this.btnNum[this.tabIndex])
              //成功
              $toast.success({
                icon:    "passed",
                message: this.$t('bind_tip_code1'),
              });
              this.$store.commit("setSmsExpiresTime", Date.now() + 120 * 1000);
              setTimeout(() => {
                this.setInterval();
              });
            } else {
              window.$toast.fail(this.$t("error_" + result["code"]));
            }

          }).catch((result)=>{
            window.$toast.fail(this.$t(result["code"]));
      })
          .finally(()=>{
            this.loading = false;
          })
    },
  },
  mounted() {
    let smsType = this.$store.state.configs.sms_type
    // let smsType = 123
    let page_1 = Math.floor(smsType / 100)
    let page_2 = Math.floor(smsType % 100 / 10)
    let page_3 = Math.floor(smsType % 10)
    page_1 > 0 && this.btnNum.push(page_1)
    page_2 > 0 && this.btnNum.push(page_2)
    page_3 > 0 && this.btnNum.push(page_3)

    this.onChange()
    this.setInterval();
  }
}
</script>

<template>
  <div style="margin: 0 .2rem .3rem;padding: .2rem .2rem 0;border-radius: 0.2rem;
    border: 0.02px solid #CECDC9;">
    <van-tabs @change="onChange" v-model="tabIndex" animated line-width="20%" line-height="2px" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="1">
      <van-tab v-for="item in btnNum" :title="$t('SMS_TYPE_'+item)" :key="item">
      </van-tab>
    </van-tabs>

    <div v-if="btnNum[tabIndex] === 1">
      <div class="withdraw-tip">
        {{ $t('ACCOUNTLAYER.TIP_SEND_1_1') }}
        <span style="color:#FF8200;font-size: .26rem">{{ smsContent }}</span>
        <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="smsContent">
          <use xlink:href="#icon-copy"></use>
        </svg>
        <br/> {{ $t('ACCOUNTLAYER.TIP_SEND_2_1') }}:
        <span style="color:#FF8200;font-size: .28rem;margin: 0 .1rem">{{ $store.state.configs.sms_phone }}</span>{{ $t('ACCOUNTLAYER.TIP_SEND_2_2') }}
      </div>
      <div style="padding: 0 .2rem;" v-if="isMobile()">
        <van-button native-type="button" type="warning" block round plain  @click="send">{{ $t('go') }}</van-button>
      </div>
    </div>

    <div v-if="btnNum[tabIndex] === 2" style="padding: 0 .2rem;">
      <van-button :disabled="!patternPhone.test(phone.slice(4)) || countdown > 0" native-type="button" type="warning" block round plain @click="!(countdown > 0) && authCode(1)">{{ countdown > 0 ? countdown : (!loading ? $t('ACCOUNTLAYER.LAB_BTN_SMS'): '')}}</van-button>
    </div>
    <div v-if="btnNum[tabIndex] === 3" style="padding: 0 .2rem;">
      <van-button :disabled="!patternPhone.test(phone.slice(4))|| countdown > 0" native-type="button" type="warning" block round plain @click="!(countdown > 0) && authCode(0)">{{ countdown > 0 ? countdown : (!loading ? $t('ACCOUNTLAYER.LAB_BTN_SMS2'): '')}}</van-button>
    </div>
  </div>
</template>

<style scoped>
::v-deep .van-button {
  height: .68rem;
  border-radius: 0.1rem;
}
::v-deep .van-button__text {
  font-weight: 600;
  font-size: 0.26rem;
  color: #FFFFFF;
}
::v-deep .van-cell {
  font-weight: 600;
  font-size: 0.26rem;
  color: #312E2A;
}

::v-deep .van-tab__pane {
  height: auto !important;
}

.withdraw-tip {
  background    : #F0DEDC;
  border-radius : 0.08rem;
  border        : 0.02px solid #E6D3D7;
  text-align    : center;
  padding       : .2rem;
  font-size     : 0.23rem;
  color         : #646566;
  margin        : .2rem .2rem 0;
}

</style>