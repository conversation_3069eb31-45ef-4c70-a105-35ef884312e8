<script>
import {preloadImages} from '@/utils/common'

export default {
  name: "tabbar",
  data() {
    return {
      showActivity: false,
      showInOut: false,
      opened: false,
    };
  },
  methods: {
    changeTab(path) {
      if (path === '/') {
        this.$store.commit("setCurrentCategory", "-3");
      }
        this.$router.push(path).catch(()=>{})
    },
    openActivity() {
      if (this.opened) return
      this.opened = true
      const imageUrls = [
        "img/login-img.jpg",
        "img/activity/15/bg_1.png",
        "img/activity/15/bg_2.png",
        "img/activity/15/bg_3.png",
        "img/activity/15/bg_1_pc.png",
        "img/activity/15/bg_2_pc.png",
        "img/activity/15/bg_3_pc.png",
        "img/activity/15/bg_4.png",
        "img/activity/15/bg_5.png",
        "img/activity/15/bg_5_1.png",
        "img/activity/15/bg_6.png",
        "img/activity/15/bg_7.png",
        "img/activity/15/bg_7.png",
        "img/activity/15/bg_9.png",
        "img/activity/15/btn_coin.png",
        "img/activity/15/cash.png",
        "img/activity/15/cash_1.png",
        "img/activity/15/checked.png",
        "img/activity/15/close.png",
        "img/activity/15/gold.png",
        "img/activity/15/sacar.png",
        "img/activity/15/sun.png",
        "img/activity/15/thanks.png",
        "img/tdtc/events/banner.png",

        "img/tdtc/events/0.png",
        "img/tdtc/events/1.png",
        "img/tdtc/events/2.png",
        "img/tdtc/events/3.png",
        "img/tdtc/events/4.png",
        "img/tdtc/events/5.png",
        "img/tdtc/events/6.png",
        "img/tdtc/events/7.png",
        "img/tdtc/events/8.png",
        "img/tdtc/events/9.png",
        "img/tdtc/events/10.png",
        "img/tdtc/events/11.png",
        "img/tdtc/events/12.png",
        // "img/tdtc/events/13.png",
        "img/tdtc/events/14.png",
        "img/tdtc/events/15.png",
      ];
      preloadImages(imageUrls);
    },
    showAd() {
      this.showActivity = false
      if (!this.$store.getters.isLogin) {
        this.$router.push("/m/login");
      } else {
        this.$store.commit('setShowAd', true)
      }
    },
  }
};
</script>

<template>
  <div id="menu" style="z-index: 100" :style="{
      backgroundImage: `url(${require('@/assets/images/index/bg_menu.png')})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
    }">
    <a
        href="javascript:void(0)"
        :class="{ on: ['/'].includes($route.path) }"
        @click="changeTab('/')"
    >
      <div class="footer_icon">
        <svg class="am-icon am-icon-icon-home am-icon-md">
          <use xlink:href="#icon-homepage-"></use>
        </svg>
      </div>
      <span>{{ $t("home") }}</span>
    </a>
    <a
        href="javascript:void(0)"
        :class="{ on: ['/m/inviteFriends'].includes($route.path) }"
        @click="changeTab('/m/inviteFriends')"
    >
      <div class="footer_icon">
        <svg class="am-icon am-icon-icon-home am-icon-md">
          <use xlink:href="#icon-agency"></use>
        </svg>
      </div>
      <span>{{ $t('agent') }}</span>
    </a>
    <van-popover overlay v-if="$store.getters.isLogin" :offset="[0, 20]" placement="top" @open="openActivity" v-model="showActivity" trigger="click">
      <van-grid
          square
          clickable
          :border="false"
          column-num="5"
          style="width: 7rem;"
      >
        <van-grid-item
            :text="$t('events')"
            @click="changeTab('/m/events')"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-activity"></use>
            </svg>
          </template>
        </van-grid-item>
        <van-grid-item
            :text="$t('vip')"
            @click="changeTab('/m/vip')"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-vip"></use>
            </svg>
          </template>
        </van-grid-item>
        <van-grid-item
            :text="$t('notification')"
            @click="showAd()"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-advertising-"></use>
            </svg>
          </template>
        </van-grid-item>

        <van-grid-item
            :text="$t('tasks')"
            @click="changeTab('/m/events/6')"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-task"></use>
            </svg>
          </template>
        </van-grid-item>
        <van-grid-item
            text="Bonus"
            @click="changeTab('/m/bonus')"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-bonus"></use>
            </svg>
          </template>
        </van-grid-item>
      </van-grid>
      <template #reference>
        <a href="javascript:void(0)">
          <div class="footer_icon">
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-activity1"></use>
            </svg>
          </div>
          <span>{{ $t("events") }}</span>
        </a>
      </template>
    </van-popover>
    <a
        v-else
        href="javascript:void(0)"
        :class="{ on: $route.path === '/m/login' }"
        @click="changeTab('/m/login')"
    >
      <div class="footer_icon">
        <svg class="am-icon am-icon-icon-home am-icon-md">
          <use xlink:href="#icon-login"></use>
        </svg>
      </div>
      <span>{{ $t("login") }}</span>
    </a>


    <van-popover overlay v-if="$store.getters.isLogin" :offset="[0, 20]" placement="top" v-model="showInOut" trigger="click">
      <van-grid
          square
          clickable
          :border="false"
          column-num="2"
          style="width: 2.8rem;"
      >
        <van-grid-item
            :text="$t('recharge')"
            @click="changeTab('/m/voucherCenter')"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-deposit"></use>
            </svg>
          </template>
        </van-grid-item>
        <van-grid-item
            :text="$t('withdrawal')"
            @click="changeTab('/m/withdraw')"
        >
          <template #icon>
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-withdrawal"></use>
            </svg>
          </template>
        </van-grid-item>
      </van-grid>
      <template #reference>
        <a href="javascript:void(0)">
          <div class="footer_icon">
            <svg class="am-icon am-icon-icon-home am-icon-md">
              <use xlink:href="#icon-roll"></use>
            </svg>
          </div>
          <span>{{ $t("in-out") }}</span>
        </a>
      </template>
    </van-popover>
    <a
        v-else
        href="javascript:void(0)"
        :class="{ on: $route.path === '/m/register' }"
        @click="changeTab('/m/register')"
    >
      <div class="footer_icon">
        <svg class="am-icon am-icon-icon-home am-icon-md">
          <use xlink:href="#icon-register"></use>
        </svg>
      </div>
      <span>{{ $t("register") }}</span>
    </a>
    <a
        href="javascript:void(0)"
        :class="{ on: $route.path === '/m/member/home' }"
        @click="changeTab('/m/member/home')"
    >
      <div class="footer_icon">
        <svg class="am-icon am-icon-icon-home am-icon-md">
          <use xlink:href="#icon-profile"></use>
        </svg>
      </div>
      <span>{{ $t("mine") }}</span>
    </a>
  </div>
</template>

<style>
.van-popover--light .van-popover__content {
  background: linear-gradient(180deg, rgba(53, 53, 53, 1), rgba(24, 24, 24, 1));
  box-shadow: 0rem 0rem 0rem 0rem #FFFFFF;
}
.van-popover--light .van-popover__arrow {
  color: rgba(24, 24, 24, 1)
}
</style>

<style scoped lang="scss">
::v-deep .van-grid-item__content {
  background-color: unset ;
}
::v-deep .van-popover__wrapper {
  flex: 1;
}
::v-deep .van-grid-item__text {
  color: #705840;
  font-size: .2rem;
}
.van-grid-item__icon-wrapper svg {
  fill: #705840;
  height: .44rem;
  width: .44rem;
  margin-bottom: .16rem;
}




[data-theme=dark] #menu a .footer_icon svg {
  fill: #705840 !important;
  height: .44rem;
  width: .44rem;
}
#menu a span {
  margin-top: .05rem;
}

</style>
