<script>
import {check} from "@/mixins/check";

export default {
  name:     "download-bar",
  mixins:   [check],
  data() {
    return {
      showWeb: false
    }
  },
  methods:  {}
};
</script>

<template>
  <div>
    <div class="download-bar-model-shell" v-show="$store.state.mobile.modal.downloadBar === 1">
      <div class="bar-model-mask" @click="$store.commit('setMobileModalDownloadBar', 0)"></div>
      <div class="bar-model-contents">
        <div class="bar-model-bg">
          <div class="header-left"><img src="/m/download-img.deac3bf5.png" alt=""></div>
          <div class="header-right">{{$t('a6.game.galaxyGold.appDownload')}}</div>
          <div class="bar-model-closes" @click="$store.commit('setMobileModalDownloadBar', 0)">
            <svg class="am-icon am-icon-icon-close am-icon-md">
              <use xlink:href="#icon-close"></use>
            </svg>
          </div>
        </div>
        <div class="appdown-list">
          <div class="item-link main-home" v-if="isIos">
            <div class="app-info">
              <img class="app-icon" src="/img/icon/57.png" alt="homescreen">
              <div><h5>{{$t('install')}} {{devicePlatform}} WEB-APP</h5></div>
            </div>
            <span class="down-btn" @click="$store.commit('setMobileModalDownloadBar', 2)">{{$t('download')}}</span></div>
          <div class="item-link iphone-link1">
            <div class="app-info">
              <img class="app-icon" src="/img/icon/57.png" alt="homescreen">
              <div><h5>{{$t('install')}} {{devicePlatform}} LITE-APP</h5></div>
            </div>
            <div
                @click="goUrl(downUrl, false)"
                class="down-btn"
                rel="nofollow me noopener noreferrer"
                target="_self"
            >{{$t('download')}}
            </div
            >
          </div>
        </div>
      </div>
    </div>
    <footer class="footer-home-screen-shell undefined" :class="{'footer-home-screen-hide': $store.state.mobile.modal.downloadBar !== 2}">
      <div class="model-mask" @click="$store.commit('setMobileModalDownloadBar', 0)"></div>
      <div class="add-home-screen">
        <div class="home-screen-tips-second">
          <div class="home-screen-tips"><!-- react-text: 4353 -->{{$t('a6.saveShortcut.click')}}<!-- /react-text --><img class="icon_click" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAlBAMAAACjVpTCAAAAJFBMVEVHcEz///////////////////////////////////////////8Uel1nAAAAC3RSTlMAHofSEZkzzGR3q6ENvvcAAACBSURBVCjPY2AAA/cSBiTAUr3dAYnrvnt3CbJkswWStPsOZoNuhPTqBmYDjl1wbgYDswFDG5JZQC4DVm5aWloCiMsGZDAwsO/evXsLiOsNZBQwSK/o6GgAcTlnzpy+kUFaAaGXaZBxWQNQuBAweLmloXAQvoWBdTcSAHqExQUOHBgAi4BCNvG7dAoAAAAASUVORK5CYII=" alt=""><!-- react-text: 4355 -->
            {{$t('a6.saveShortcut.guideModalTitleAndriod')}}<!-- /react-text -->
            <img class="icon_add" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACEAAAAhBAMAAAClyt9cAAAAIVBMVEX///9HcEz///////////////////////////////////+jWTucAAAACnRSTlMQAO+R6CionC9gb58HpAAAAGhJREFUKM9jEGzzWoUAbo0CDJKrUMBKAYau5QEMCBC+qoEhq1AQGVQlMHgFoohEOTCsYkQR4VqAW0TYGF1EaNFAiQgrKSlprQISynARWFAsxq1mYN2MPQzxhjxmfGHGKWa8Y6YNjPQDAFF2eAu0iHVjAAAAAElFTkSuQmCC" alt="">
          </div>
        </div>
        <i class="icon_close" @click="$store.commit('setMobileModalDownloadBar', 0)"></i></div>
    </footer>
  </div>
</template>

<style scoped>
.footer-home-screen-shell {
  width: 100%;
  z-index: 99999;
  position: fixed;
  left: 0;
  bottom: .35rem!important;
  padding: 0;
  text-align: center;
  background-color: transparent;
  transition: all .5s
}

.footer-home-screen-shell.footer-home-screen-hide {
  bottom: -4rem!important
}

.footer-home-screen-shell.footer-home-screen-hide .model-mask {
  display: none
}

.footer-home-screen-shell .model-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,.3)
}

.footer-home-screen-shell .icon {
  width: .44rem;
  height: .44rem;
  fill: #fff
}

.footer-home-screen-shell .icon__share {
  width: .6rem;
  height: .6rem;
  margin: 0;
  transform: translateY(10%)
}

.footer-home-screen-shell .icon__close-button {
  position: absolute;
  top: .45em;
  right: .45em;
  width: .875em;
  height: .875em
}

.footer-home-screen-shell svg {
  position: relative;
  top: .1rem;
  margin-right: .05rem
}

.footer-home-screen-shell .add-home-screen {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  width: 6.2rem;
  height: 1rem;
  align-items: center;
  position: relative;
  background: linear-gradient(180deg,#71bdfa,#2090eb);
  margin: 0 auto;
  border-radius: .15rem
}

.footer-home-screen-shell .add-home-screen:after {
  content: "";
  width: 0;
  height: 0;
  position: absolute;
  left: 50%;
  bottom: -.19rem;
  transform: translateX(-50%);
  border-top: .2rem solid #2090eb;
  border-left: .2rem solid transparent;
  border-right: .2rem solid transparent
}

.footer-home-screen-shell .add-home-screen .icon_close {
  position: absolute;
  right: 0;
  top: -.7rem;
  width: .6rem;
  height: .6rem;
  opacity: .8;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAMAAABHPGVmAAAATlBMVEVHcEz////////////////////////5+fn///////////////////////8AAAAODg7Q0NAuLi5fX1+goKB4eHi5ubmSkpJPT0/X19fg4ODSgmORAAAADXRSTlMASi+vxgyY+OQef2bVNJ5FkgAAA/lJREFUaN69mumSgyAMgKuieAWqUI/3f9EF7XbVCgTB5c/ObB2+CTkISR4P5MrTps5IW1IAWrYkq5s0f8RcSZ2VcLLKrE7ikJJs3XF6yb7rOs4YV396+ZrW/2dJKKGqtQizkB07WZ0UsxaorgIQqRZiOgf8gbREWXr1nIjS8WAlvDmDsgZy5dRSjZCcoRaXGuN9aLUH4oOp/fTdArw8EAvmBdB6CJNQmDvmvboZKFYzeQEwcH8G41wAFCjvzAnQkV1cPQWCoFQlTB27vLoJoRjFeHEWsJT+SwclVQwWuBQldcgxsOAlrLLkJQjGolCM2s/b8LP6PTGTjSnbDdP5TvsGSgHPSAxFeUJxGkuARmMoCoWTCFNRGFnENQL9NjESw3i3awByZDQw87gQPkNzsCwKHYu8uqO3FHG8cC+K2FtYGtWyNhaW7rQu2Q1LbnWf3CLIQZTsHkG0KNnHD82C9NJpdFyav1GiVB/TksZoCi4fVVEKzOY/fAysBG6ydL2Ek2G+ITiUv2o3fMNXiI2yMizX0PMdJ82ntRyXjfJmWEKrfJ8XBe7aRNh/7i2iAl29/ek8jvNtEAx9XukSfwd2iYJiKPtqFk8c2RUKjqEur8yuEttmSMaqlApmxvwpWAZjs3L6xJ1rnWyIZyg3SBx6N2zpwVg0X2Ai8GFTHwbX7pihMqHdtj6M1bwILoPYbOzHUFGWPFpkmvLZ2pOhIK05zpsosx9DZ0ZOX/ym+DGWKwXAJ1X3ZywuD8D9KR4MBQGP49pABPOTpMRDNsflQdGKb9GZ9k7xwgPSYp3xy4QFHkKQYeXEGbEUHVYKXIp6ElaQFB0ga9Qz7jRA4ig61CeYAoEh1KMo+tJKYbp+aWEo+vpFeKPl+hUoX9SvrDEgkRAY43o4Ne9IiYRT7/WS1D+DkjsHZVpfdFalINJUYfd36nwxohJugXg1JmYjdtznCMr8fgRZSh6unMGVVKi3Gs1/H6aD7c1ofeM4nnN/D1NT0eP9ZuyvP0w3T2yz6oX7PtcU2hkFyRD1m9FdLLB8s6/gWAo4IUWXXQHnpgrOsdhJbiiqKY2SYy31hvIgVMdG2Q2Fzvq7Th+9ZFt+d04gbvG5B3rSRWnMPnVFIfRYFf71+3hqUQopTO2TWG0HFWva3Nw0iwOxNc+qeO2mytqcu71x9j8twP9pZioKCW3LYpq//9FgXhKLa61yxgeAOsc3/a8cmTqq0mMcQykGhO/4gvCexWgo0N5TG2XjO+6hhximHj1SMmEHF46OWfgMxxRXR32qAjnmQ4uQaaK8IZozjOd57DjoQSLSBM9fVTVZ0l0xyPFv9GqUg1gaHyRoJGorT1KcD5EVSdxptZBxuB/J7LC7y3uFmQAAAABJRU5ErkJggg==) no-repeat 50%/cover
}

.footer-home-screen-shell .app-icon {
  width: 1.28rem;
  height: 1.28rem;
  vertical-align: middle;
  border-radius: .8em;
  flex: 0 0 1.28rem
}

.footer-home-screen-shell .home-screen-tips {
  display: flex;
  align-items: center;
  font-size: .26rem;
  text-align: left;
  padding-left: 1em;
  color: #fff
}

.footer-home-screen-shell .home-screen-tips .icon_click {
  display: inline-block;
  width: .28rem;
  height: .37rem;
  margin: 0 .15rem
}

.footer-home-screen-shell .home-screen-tips .icon_add {
  display: inline-block;
  width: .33rem;
  height: .33rem;
  margin: 0 .15rem
}

.footer-home-screen-shell .home-screen-tips .label {
  display: inline-block;
  line-height: normal;
  transform: translateY(8%)
}

.footer-home-screen-shell .add-app-icon {
  display: inline-block;
  width: .1rem;
  height: .1rem
}

.footer-home-screen-shell .home-screen-tips-second {
  display: flex;
  align-items: center
}

.footer-home-screen-shell .home-screen-tips-first .title {
  display: block;
  font-weight: 700;
  text-align: left;
  color: #fff;
  font-size: .36rem;
  padding-bottom: .2rem
}

.footer-home-screen-shell .home-screen-tips-first .content {
  display: block;
  text-align: left;
  color: #fff;
  font-size: .24rem;
  padding-bottom: 1em;
  line-height: 1.5
}

.footer-home-screen-shell .home-screen-tips-first h4,.footer-home-screen-shell span {
  line-height: 1
}

.footer-home-screen-shell .btn-primary {
  height: auto;
  padding: .14rem;
  margin: 0 auto;
  font-size: .3rem
}

.footer-home-screen-shell .home-screen-tips-profile {
  width: 100%
}
.download-bar-model-shell {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10001
}

.download-bar-model-shell.hide-model {
  display: none
}

.download-bar-model-shell .bar-model-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,.4)
}

.download-bar-model-shell .bar-model-closes {
  position: absolute;
  top: -.6rem;
  right: 0
}

.download-bar-model-shell .bar-model-closes svg {
  width: .5rem;
  height: .5rem;
  fill: var(--theme_font_color)
}

.download-bar-model-shell .bar-model-contents {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-50%);
  width: 6.3rem;
  animation: zoomShow .2s both
}

.download-bar-model-shell .bar-model-contents .bar-model-bg {
  position: relative;
  width: 100%;
  height: 2rem;
  border-radius: .2rem .2rem 0 0;
  background: linear-gradient(180deg,var(--theme_gradient_color_1),var(--theme_gradient_color_2))
}

.download-bar-model-shell .bar-model-contents .bar-model-bg .header-left {
  position: absolute;
  left: .1rem;
  bottom: .2rem;
  height: 3rem;
  z-index: 1
}

.download-bar-model-shell .bar-model-contents .bar-model-bg .header-left img {
  height: 100%
}

.download-bar-model-shell .bar-model-contents .bar-model-bg .header-right {
  position: absolute;
  bottom: .2rem;
  right: .2rem;
  width: 3.5rem;
  font-size: .56rem;
  font-weight: 700;
  text-align: right;
  color: var(--theme_font_color);
  z-index: 2;
  filter: drop-shadow(0 .05rem .1rem rgba(0,0,0,.56))
}

.download-bar-model-shell .bar-model-contents .appdown-list {
  width: 100%;
  background: var(--main_background_color);
  padding: 0 .18rem .03rem;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-bottom-left-radius: .2rem;
  border-bottom-right-radius: .2rem
}

.download-bar-model-shell .bar-model-contents .item-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: .32rem .22rem .32rem .2rem;
  background: var(--login_pg_background_color);
  border-radius: .1rem;
  margin-bottom: .2rem;
  position: relative;
  top: -.2rem
}

.download-bar-model-shell .bar-model-contents .item-link .app-info {
  display: flex;
  align-items: center
}

.download-bar-model-shell .bar-model-contents .item-link .app-info h5 {
  font-size: .26rem;
  font-weight: 600;
  color: var(--theme_font_color)
}

.download-bar-model-shell .bar-model-contents .item-link .app-info p {
  font-size: .2rem;
  color: #a5a9b3;
  text-align: left;
  line-height: .32rem
}

.download-bar-model-shell .bar-model-contents .item-link .app-info .app-icon {
  width: .88rem;
  height: .88rem;
  margin-right: .15rem;
  border-radius: .1rem
}

.download-bar-model-shell .bar-model-contents .item-link .down-btn {
  min-width: 1.46rem;
  height: .54rem;
  line-height: .54rem;
  padding: 0 .2rem;
  background-image: conic-gradient(from 180deg at 50% 50%,var(--theme_gradient_color_1) 0deg,var(--theme_gradient_color_2) 1turn);
  border: none;
  outline: none;
  color: var(--theme_font_color);
  font-size: .23rem;
  font-weight: 600;
  border-radius: .27rem;
  display: flex;
  align-items: center;
  justify-content: center
}

.download-bar-model-shell .bar-model-contents .item-link img {
  width: .55rem;
  height: .55rem;
  display: inline-block;
  vertical-align: middle;
  margin-right: .5em;
  margin-top: -.06rem
}

._container_box .download-bar-shell {
  position: fixed;
  top: 0;
  left: 0;
  transition: all .2s;
  width: 100%;
  height: 1.2rem;
  z-index: 10001;
  background-size: 100% 100%;
  background-color: var(--sub_background_color)
}

._container_box .download-bar-shell.hide-bar {
  top: -1.7rem
}

._container_box .download-bar-shell .download-bar-wrap {
  height: 100%
}

._container_box .download-bar-shell .download-bar-icon {
  position: relative;
  height: 100%;
  display: flex;
  padding: .23rem 0 0 .26rem
}

._container_box .download-bar-shell .download-bar-icon .app-icon {
  display: block;
  width: .8rem;
  height: .8rem
}

._container_box .download-bar-shell .download-bar-icon .app-text {
  position: absolute;
  left: 1.39rem;
  top: 50%;
  transform: translateY(-50%)
}

._container_box .download-bar-shell .download-bar-icon .app-text h2 {
  font-size: .28rem;
  font-weight: 500;
  color: var(--theme_font_color,#fff)
}

._container_box .download-bar-shell .download-bar-icon .app-title {
  display: block;
  height: .34rem
}

._container_box .download-bar-shell .download-bar-icon .app-star {
  display: flex;
  margin-top: .06rem
}

._container_box .download-bar-shell .download-bar-icon .app-star .am-icon {
  width: .29rem;
  height: .28rem
}

._container_box .download-bar-shell .download-bar-icon .app-star .am-icon:not(:first-child) {
  margin-right: .05rem
}

._container_box .download-bar-shell .download-bar-btn {
  position: absolute;
  top: 50%;
  right: .92rem;
  transform: translateY(-50%);
  z-index: 10;
  min-width: 1.8rem;
  padding-inline:.1rem;height: .6rem;
  line-height: .6rem;
  background-image: conic-gradient(from 180deg at 50% 50%,var(--theme_gradient_color_1) 0deg,var(--theme_gradient_color_2) 1turn);
  font-size: .28rem;
  border-radius: .3rem;
  color: var(--theme_font_color);
  text-align: center;
  text-transform: capitalize
}

._container_box .download-bar-shell .download-bar-close {
  position: absolute;
  width: .32rem;
  height: .32rem;
  right: .4rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10
}

._container_box .download-bar-shell .download-bar-close svg {
  width: 100%;
  height: 100%;
  fill: var(--theme_font_color)
}

._container_box .download-bar-shell .download-bar-gift {
  display: none
}

._container_box .download-bar-shell .app-full-name-wrap {
  font-size: .28rem
}

._container_box .download-bar-shell .app-star-grade {
  margin: 0
}

._container_box .download-bar-shell .app-star-grade .grade-text {
  display: none
}

._container_box .download-bar-shell .app-star-grade .star-icon {
  width: .29rem;
  height: .28rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB0AAAAcCAMAAACqEUSYAAAAh1BMVEVHcEz/wgj/qgP/kgP/0wf/vgX/dQD/6gn/xQb/hAL/tgX/hAP/cQD/cQH/zAf/ggL/mQX/2Qf/jwL/rwX/tAb/5Ar/9Ar/owT/pAX/zgf/cAH/uQb/vwb/tAX/fAH/rwX/qgT/pAT/cgH/nwT/igL/hAL/jwP/lQP/mgT/2Qf/zAb/xAb/agCqOERMAAAAG3RSTlMAPzBVWxDQ/n8g5nDwk6CAIPDQoNCgcMDQ38Dz39oLAAAAz0lEQVQoz3WR2RKDIAxFkbUg6th93zdb///7iliniTXnKeE4FxIZg8zNnJEoY4wirQxWknYW7Iy0ZVVVJRlcNVDRebQ5FfxqWA1L/npHzeGMchSQSZLk75Y81O2hYm5L45jb0DjGpzuKaXiBmhyGmbQrF+74jxPdu+3y1GdhwVzFBVPgVYgnRPQWlT4gac+O75AxlvaKsTj4hsHReh/xmW8LjYK/Z5zx73cwOj03ZPGPZrGG0Tr0vhtS+NCtga3rWv+yrA49sJnHqyt8vIV9ADfbK6wGN45GAAAAAElFTkSuQmCC) no-repeat;
  background-size: 100% 100%
}

._container_box .app-win {
  display: none
}

.download-bar-shell.show-bar+.home-header .app-step-wrap .app-step-content {
  top: 2.25rem
}
</style>
