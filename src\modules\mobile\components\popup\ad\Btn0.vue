<script>
import {TDTC_ROURE} from "@/api/tdtc";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";

export default {
  components: {RecordBoard},
  data() {
    return {
      res: {
        conf: []
      },
      column: [
        {
          label: this.$t('ad.panel.11.th.0'),
          prop: "need_recharge",
          render: FieldRenderType.formatGold,
        },
        {
          label: this.$t('ad.panel.11.th.1'),
          prop: "award_score",
          render: FieldRenderType.formatGoldWithK,
        },
      ],
    }
  },
  mounted() {
    this.query34()
  },
  methods: {
    query34() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AD_TOTAL_RECHARGE)
          .then((res) => {
            this.res.conf = res['conf'] ?? []
          })
          .catch(() => {
          })
    },
  }
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/0.png" style="width: 6rem;margin-top: .8rem;" alt="">
    </div>
    <div>
      <div class="ad-title">{{$t('ad.tab.0')}}</div>

    </div>
    <div style="z-index: 1;">
      <div class="grade">
        <p style="
font-size: 0.32rem;
color: #FC9712;">Sau khi Đăng ký TK chính thức</p>
        <p style="
font-size: 0.5rem;
color: #FA3A8F;">Tặng ngay </p>
        <p style="
font-size: 1.2rem;
color: #F43F00;" class="element">{{ $store.state.configs.bindphone / 100 }}</p>
      </div>
      <div class="ad-btn" @click="$router.push('/m/myAccount/phone')">{{$t('GOLDANDREGISTER.BTN_1')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

/* 定义放大缩小的关键帧 */
@keyframes scaleAnimation {
  0%, 100% {
    transform: scale(.8); /* 初始和结束状态的缩放比例 */
  }
  50% {
    transform: scale(1.2); /* 放大状态的缩放比例 */
  }
}

/* 应用动画到目标元素 */
.element {
  animation: scaleAnimation 1.5s infinite alternate ease-in-out;
}


.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(250, 58, 143, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {
    width: 5.95rem;
    height: 3.3rem;
    background: #FFFFFF;
    border-radius: 0.2rem;
    margin: 0 auto;
    font-weight: 900;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: .5rem 0 .3rem;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>