<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";
import {TDTC_ROURE} from "@/api/tdtc";

export default {
  components: {RecordBoard},
  data() {
    return {
      column: [
        {
          label: this.$t('ad.panel.1.th.0'),
          prop: "level",

        },
        {
          label: this.$t('ad.panel.1.th.1'),
          prop: "bonus",
          render: FieldRenderType.currency,
        },
      ],
      conf: []
    }
  },
  mounted() {
    this.query23()
  },
  methods: {
    query23() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AD_VIP_ACTIVE_INFO)
          .then((res) => {
            // VipActiveInfo
            this.conf = []
            if (res['conf']) {
              res['conf'].forEach((item) => {
                this.conf.push({
                  level: item['viplevel'],
                  bonus: item['levelupaward'] + item['weekwage'] + item['monthwage']
                })
              })
            }
          })
          .catch(() => {
          })
    },
  }
}
</script>

<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/1.png" style="width: 5rem;margin-top: .8rem;" alt="">
    </div>
    <div class="ad-title">{{ $t('ad.tab.1') }}</div>
    <div>
      <RecordBoard :data="conf" :column="column"/>
      <div class="ad-btn" @click="$router.push('/m/vip')">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

::v-deep .record-board {
  .record-board-wrap {
    z-index: 1;
    width: 6.19rem;
    border-radius: 0.12rem;
    color: #CB542B;

    table {

      background: #FFFFFF;
      table-layout: unset;

      th {
        font-size: 0.26rem;
        color: #7A1620;
        background: #FFC63B;
        padding: unset;
      }

      tr {
        line-height: 0.6rem;
      }

      tbody tr:nth-child(odd) {
        background: #FFF9E7;
      }
    }
  }
}

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(140, 16, 255, 1), rgba(255, 217, 173, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>