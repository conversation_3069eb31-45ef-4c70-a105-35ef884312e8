<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";
import {TDTC_ROURE} from "@/api/tdtc";

export default {
  components: {RecordBoard},
  data() {
    return {
      column: [
        {
          label: this.$t('NOTICELAYER.ad_1.th_0'),
          prop: "level",

        },
        {
          label: this.$t('NOTICELAYER.ad_1.th_1'),
          prop: "bonus",
          render: FieldRenderType.currency,
        },
      ],
      res: {
        conf: []
      }
    }
  },
  mounted() {
    this.query12()
  },
  computed: {
    conf() {
      let c = [];
      c[0] = this.res.conf[0] ? this.res.conf[0]["award_money"] : 0
      c[1] = this.res.conf[1] ? this.res.conf[1]["award_money"] : 0
      c[2] = this.res.conf[0] ? this.res.conf[0]["award_money"] : 0
      c[3] = this.res.conf[1] ? this.res.conf[1]["award_money"] * 2 : 0
      return c
    }
  },
  methods: {
    query12() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_NEW_PEOPLE_AWARD)
          .then((res) => {
            this.res.conf = res['conf'] ?? [];
          })
          .catch(() => {
          })
    },
  }
}
</script>

<template>
  <div class="ad-wrap">
    <div class="ad-bg">
<!--      <img src="/img/tdtc/events/10.png" style="height: 1.5rem; margin-top: .77rem;margin-left: 4.6rem;" alt="">-->
    </div>
    <div class="ad-title">{{ $t('ad.tab.10') }}</div>
    <div class="clearfix">
      <div class="image-wrapper">
      <img src="/img/tdtc/events/10.png" alt="" style="height: 1.3rem;">
      </div>
      <p style="font-size: .2rem;text-align: start">
        {{ $t('ad.panel.10.tip.0') }}
      </p>
    </div>
    <div class="grade">
      <div class="grade-head">
        <div>VIP {{ $t('ad.panel.10.grade') }}</div>
        <div>VIP 1</div>
      </div>
      <div class="grade-content">
        <div>
          <div>{{ $t('ad.panel.10.tr.0.0') }}</div>
          <div>{{ $t('ad.panel.10.tr.0.1') }}</div>
          <div>{{ $t('ad.panel.10.tr.0.2') }}</div>
        </div>
        <div>
          <div>{{ $t('ad.panel.10.tr.1.0') }}</div>
          <div>{{ $t('ad.panel.10.tr.1.1') }}</div>
          <div>{{ $t('ad.panel.10.tr.1.2') }}</div>
        </div>
        <div>
          <div>{{ $t('ad.panel.10.tr.2.0') }}</div>
          <div>{{ conf[0] | formatGold }}</div>
          <div>{{ conf[1] | formatGold }}</div>
        </div>
      </div>
    </div>
    <div class="grade">
      <div class="grade-head" style="background: #FF6BD0;">
        <div>VIP {{ $t('ad.panel.10.grade') }}</div>
        <div>VIP 2</div>
      </div>
      <div class="grade-content">
        <div>
          <div>{{ $t('ad.panel.10.tr.0.0') }}</div>
          <div>{{ $t('ad.panel.10.tr.0.1') }}</div>
          <div>{{ $t('ad.panel.10.tr.0.2') }}</div>
        </div>
        <div>
          <div>{{ $t('ad.panel.10.tr.1.0') }}</div>
          <div>{{ $t('ad.panel.10.tr.1.1') }}</div>
          <div>{{ $t('ad.panel.10.tr.1.2') }}</div>
        </div>
        <div>
          <div>{{ $t('ad.panel.10.tr.2.0') }}</div>
          <div>{{ conf[2] | formatGold }}</div>
          <div>{{ conf[3] | formatGold }}</div>
        </div>
      </div>
    </div>
    <div class="ad-btn" @click="$router.push('/m/events/8')">{{ $t('go') }}</div>
  </div>
</template>

<style scoped lang="scss">
.image-wrapper {
  float: right;
}
.clearfix::after {
  content: "";
  clear: both;
  display: table;
}
.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(98, 62, 249, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;


  .grade {
    width: 100%;
    border-radius: .12rem;
    overflow: hidden;
    line-height: 1.2;

    &-head {
      height: .5rem;
      background: #FF6B6B;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 .3rem;
    }

    &-content {
      display: flex;
      flex-direction: column;
      color: #CB542B;
      font-size: 0.23rem;

      > div {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        align-items: center;
        height: 0.7rem;
        background: #FFF1F1;

        > div {
          flex: 1;
          padding: 0 .1rem;
        }

        &:nth-child(even) {
          background: #FFFFFF
        }
      }
    }
  }

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
    color: #FFF948;
  }

  .ad-rule {
    width: 5.2rem;
    height: 1.58rem;
    font-weight: 600;
    font-size: 0.2rem;
    color: #FFFAF2;
    line-height: 0.26rem;
    align-self: start;
    text-align: start;
    padding-left: .1rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>