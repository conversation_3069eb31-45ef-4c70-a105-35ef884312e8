<script>
import {TDTC_ROURE} from "@/api/tdtc";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";

export default {
  components: {RecordBoard},
  data() {
    return {
      res: {
        conf: []
      },
      column: [
        {
          label: this.$t('ad.panel.11.th.0'),
          prop: "need_recharge",
          render: FieldRenderType.formatGold,
        },
        {
          label: this.$t('ad.panel.11.th.1'),
          prop: "award_score",
          render: FieldRenderType.formatGoldWithK,
        },
      ],
    }
  },
  mounted() {
    this.query34()
  },
  methods: {
    query34() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AD_TOTAL_RECHARGE)
          .then((res) => {
            this.res.conf = res['conf'] ?? []
          })
          .catch(() => {
          })
    },
  }
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/11.png" style="width: 6rem;margin-top: .8rem;" alt="">
    </div>
    <div>
      <div class="ad-title">{{$t('ad.tab.11')}}</div>

    </div>
    <div style="z-index: 1;">
      <div class="grade">
        <RecordBoard :data="res.conf" :column="column"/>
      </div>
      <div style="margin-top: .2rem;
color: #3D8298;
font-weight: 400;
font-size: 0.2rem;">{{ $t('ad.panel.11.tip.1') }} <br/>{{ $t('ad.panel.11.tip.0') }}</div>
      <div class="ad-btn" @click="$router.push('/m/events/45')">{{$t('go')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">


::v-deep .record-board {
  .record-board-wrap {
    height: 3.5rem;
    table {
      th:nth-child(1) {
        background: #513DC9;
      }
      th:nth-child(2) {
        background: #C93D43;
      }
      th {
        color: #FFFFFF;
      }
      td:nth-child(1) {
        background: #F8F9FF;
        color: #513DC9;
      }
      td:nth-child(2) {
        background: #FFF3F4;
        color: #C93D43;
      }
    }
  }
}

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(58, 190, 77, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {
    background: #FFFFFF;
    border-radius: 0.12rem;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    > div > div:nth-child(2) {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>