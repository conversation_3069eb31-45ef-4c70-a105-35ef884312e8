<script>
import {TDTC_ROURE} from "@/api/tdtc";

export default {
  mounted() {
    this.query202()
  },
  methods: {
    query202() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_ACTIVE200_URL_Ad)
          .then((res) => {

          })
          .catch(() => {
          })
    },
  }
}
</script>


<template>
  <div class="ad-wrap">
    <div style="
font-weight: bold;
font-size: 0.32rem;
color: #3BFF4D;">{{ $t('ad.panel.13.text.0') }}
    </div>
    <div style="
font-weight: bold;
font-size: 0.23rem;
color: #FFF600;">{{ $t('ad.panel.13.text.1') }}
    </div>

    <div style="
font-weight: 400;
font-size: 0.2rem;
color: #FFFFFF;text-align: start" v-html="$t('ad.panel.13.tip.0')"></div>

    <table>
      <tr>
        <th style="width: 1.79rem;">{{ $t('ad.panel.13.th.0') }}</th>
        <th colspan="7">{{ $t('ad.panel.13.th.1') }}</th>
      </tr>
      <tr>
        <td>{{ $t('ad.panel.13.tr.0') }}</td>
        <td>300+</td>
        <td>500+</td>
        <td>1000+</td>
        <td>2000+</td>
        <td>3000+</td>
        <td>4000+</td>
        <td>5000+</td>
      </tr>
      <tr>
        <td>{{ $t('ad.panel.13.tr.1') }}</td>
        <td>28+</td>
        <td>58+</td>
        <td>108+</td>
        <td>158+</td>
        <td>588+</td>
        <td>788+</td>
        <td>1288+</td>
      </tr>
      <tr>
        <td>{{ $t('ad.panel.13.tr.2') }}</td>
        <td>1</td>
        <td>1</td>
        <td>1</td>
        <td>1</td>
        <td>1</td>
        <td>1</td>
        <td>1</td>
      </tr>
      <tr>
        <td>{{ $t('ad.panel.13.tr.3') }}</td>
        <td>1</td>
        <td>2</td>
        <td>3</td>
        <td>4</td>
        <td>5</td>
        <td>6</td>
        <td>7</td>
      </tr>
      <tr>
        <td>{{ $t('ad.panel.13.tr.4') }}</td>
        <td>88K</td>
        <td>128K</td>
        <td>188K</td>
        <td>268K</td>
        <td>388K</td>
        <td>588K</td>
        <td>1288K</td>
      </tr>

    </table>

    <div class="ad-btn" @click="$router.push('/m/events/200')">{{ $t('go') }}</div>
  </div>
</template>
<style scoped lang="scss">


.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(0, 68, 194, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  table {
    width: 6.13rem;
    border-radius: 0.12rem;
    overflow: hidden;
    background: #FFF9ED;
    font-size: 0.18rem;
    opacity: 0.9;

    th {
      height: .8rem;
      line-height: .8rem;
    }

    td {
      border: none !important;
      padding: .15rem 0;
    }


    tr:nth-child(odd) {
      background: #9322AF;

    }
    tr:nth-child(even) {
      background: #AB2DCA;
    }
    tr {
      td:nth-child(even) {
        background: rgba(130, 46, 185, 0.3);
      }
    }
  }


  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>