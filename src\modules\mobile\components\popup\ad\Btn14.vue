<script>
import {TDTC_ROURE} from "@/api/tdtc";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";

export default {
  components: {RecordBoard},
  data() {
    return {
      res: {
        agent_bet_score: 10000000,
        agent_recharge_amount: 5000000,
        code: 200,
        every_round_activity_validtime: 3,
        freegiving_lottery_count: 1,
        freegiving_time_interval: 1,
        goldcoin_to_cash_of_cash: 100,
        goldcoin_to_cash_of_goldcoin: 1000,
        promote_url: "https://td88.cc",
        withdrawal_amount: 50000000,
        withdrawal_validtime: 3,
      }
    }
  },
  mounted() {
    this.query35()
  },
  methods: {
    query35() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AD_EARNCASH_BY_PROMOTE)
          .then((res) => {
            Object.assign(this.res, res)
          })
          .catch(() => {
          })
    },
  }
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/14.png" style="width: 6.6rem" alt="">
    </div>
    <div class="ad-title">{{$t('ad.tab.14')}}</div>
    <div style="z-index: 1;">
      <div class="grade">
        <div>{{ $t('ad.panel.14.tip.0', {0:$options.filters['formatGold'](res.withdrawal_amount)}) }}</div>
        <div>{{ $t('ad.panel.14.tip.1', [$options.filters['formatGold'](res.agent_recharge_amount),$options.filters['formatGold'](res.agent_bet_score)]) }}</div>
        <div>{{ $t('ad.panel.14.tip.2', [res.every_round_activity_validtime, res.freegiving_lottery_count, res.freegiving_time_interval]) }}</div>
        <div>{{ $t('ad.panel.14.tip.3', [res.withdrawal_validtime]) }}</div>
        <div>{{ $t('ad.panel.14.tip.4') }}</div>
        <div>{{ $t('ad.panel.14.tip.5', [res.goldcoin_to_cash_of_goldcoin, $options.filters['currency'](res.goldcoin_to_cash_of_cash)]) }}</div>
      </div>
      <div class="ad-btn" @click="$router.push('/m/events/46')">{{$t('go')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(151, 0, 222, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {

    width: 6.19rem;
    height: 4.6rem;
    background: #FFFFFF;
    border-radius: 0.12rem;
    font-size: 0.23rem;
    color: #59358F;
    line-height: 1.4;
    text-align: start;
    padding: .1rem;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(209, 61, 231, 1), rgba(255, 114, 160, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>