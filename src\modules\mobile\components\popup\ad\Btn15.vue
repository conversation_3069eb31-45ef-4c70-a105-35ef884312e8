<script>
import {type202} from "@/mixins/tdtc/events/type202";

export default {
  mixins: [type202]
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/15.png" style="width: 5rem;margin-top: 1.39rem;" alt="">
    </div>
    <div>
      <div style="font-weight: bold;
font-size: 0.46rem;
color: #FFD74C;">{{$t('ad.tab.15')}}</div>
      <div style="font-weight: bold;
font-size: 0.32rem;
color: #FFFFFF;">{{$t('ad.panel.15.text.0')}}</div>
    </div>
    <div style="z-index: 1;">
      <div class="grade" style="font-size: 0.23rem;
    color: #59358F;
    line-height: 1.6;
    text-align: start;
    padding: .1rem;">
        <p>{{ $t('ad.panel.15.tip.0') }}</p>
        <p>{{ $t('ad.panel.15.tip.1', {0:$options.filters['formatGold'](res.recharge_gift_money)}) }}</p>
        <p>{{ $t('ad.panel.15.tip.2') }}</p>
        <p style="color: #FF0060;">{{ res.recharge_gift_result_url }}</p>
        <p>{{ $t('ad.panel.15.tip.3') }}</p>
      </div>
      <div class="ad-btn" @click="go">{{$t('go')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(79, 3, 194, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {

    width: 6.19rem;
    height: 4.6rem;
    background: #FFFFFF;
    border-radius: 0.12rem;



  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(209, 61, 231, 1), rgba(255, 114, 160, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>