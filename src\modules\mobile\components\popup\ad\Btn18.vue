<script>
import { type52 } from "@/mixins/tdtc/events/type52";

export default {
  mixins: [type52],
  methods: {
    formatter(day) {
      const month = day.date.getMonth() + 1;
      const date = day.date.getDate();
      this.res.receive_records.forEach((item)=>{

        if (month === item.month && date === item.day) {
          if (item.flag) {
            day.className = 'checkin_status_' + item.flag
          } else {
            day.className = 'checkin_status_0'
          }
        }
      })
      return day;
    },
  },

}
</script>
<template>
  <div class="ad-wrap" style="background-image: url('/img/tdtc/events/18.png');">
    <div class="ad-bg">
    </div>
    <div>
      <div style="font-weight: bold;
font-size: 0.46rem;
color: #FFD74C;"></div>
      <div style="font-weight: bold;
font-size: 0.32rem;
color: #FFFFFF;"></div>
    </div>
    <div style="z-index: 1;">
      <div class="grade" style="font-size: 0.2rem;overflow: scroll;
    color: #04692F;
    line-height: 1.6;
    text-align: start;
    padding: .1rem;">
        <p>{{ $t('ad.panel.18.tip.0', {0:res.config.tax_to_award_pro}) }}</p>
        <p>{{ $t('ad.panel.18.tip.1', {0:res.config.receive_time_duration / 24, 1:res.config.receive_time - res.config.receive_time_duration}) }}</p>
        <p>{{ $t('ad.panel.18.tip.2', {0:res.config.receive_time_duration / 24}) }}</p>
        <p>{{ $t('ad.panel.18.tip.3') }}</p>
        <p>{{ $t('ad.panel.18.tip.4') }}</p>
        <p>{{ $t('ad.panel.18.tip.5') }}</p>
      </div>
      <div class="ad-btn" @click="$router.push('/m/events/52')">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {

    width: 6.19rem;
    height: 2.76rem;
    background: #FFEAE8;
    border-radius: 0.12rem;



  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}
</style>