<script>
import { type53 } from "@/mixins/tdtc/events/type53";

export default {
  mixins: [type53],
}
</script>
<template>
  <div class="ad-wrap" style="background-image: url('/img/tdtc/events/19.png');">
    <div class="ad-bg">
    </div>
    <div class="ad-title">Hoàn thành nhiệm vụ giới thiệu có thể nhận thưởng lên đến 10M!</div>
    <div>
      <div style="font-weight: bold;
font-size: 0.46rem;
color: #FFD74C;"></div>
      <div style="font-weight: bold;
font-size: 0.32rem;
color: #FFFFFF;"></div>
    </div>
    <div style="z-index: 1;">
      <div class="grade" style="font-size: 0.2rem;overflow: scroll;
    color: #B15000;
    line-height: 1.6;
    text-align: start;
    padding: .1rem;">
        <p>{{ $t('ad.panel.19.tip.0', {0:res.checkin_max_day_id}) }}</p>
        <p>{{ $t('ad.panel.19.tip.1', {0:res.last_check_in_time, 1:res.checkin_max_day_id}) }}</p>
        <p>{{ $t('ad.panel.19.tip.2') }}</p>
      </div>
      <div class="ad-btn" @click="$router.push('/m/events/53')">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-title {
    font-weight: bold;
    font-size: 0.38rem;
    font-family: Segoe UI;
    color: #FFF88B;
    margin-top: .6rem;
  }

  .grade {

    width: 6.19rem;
    height: 2.76rem;
    background: #FFEAE8;
    border-radius: 0.12rem;



  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}
</style>