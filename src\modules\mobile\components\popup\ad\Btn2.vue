<script>
import {type6} from "@/mixins/tdtc/events/type6";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";

export default {
  components: {RecordBoard},
  mixins: [type6]
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/2.png" style="width: 5rem" alt="">
    </div>
    <div class="ad-title">{{ $t('ad.tab.2') }}</div>
    <div style="z-index: 1;">
      <div class="grade">
        <div class="grade-head">
          <div>{{ $t('ad.panel.2.th.0') }}</div>
          <div>{{ $t('ad.panel.2.th.1') }}</div>
        </div>
        <div class="grade-content">
          <div>
            <div>{{ $t('ad.panel.2.tr.0') }}</div>
            <div>{{ lab_progress[0] }}</div>
          </div>
          <div>
            <div>{{ $t('ad.panel.2.tr.1') }}</div>
            <div>{{ lab_progress[1] }}</div>
          </div>
          <div>
            <div>{{ $t('ad.panel.2.tr.2') }}</div>
            <div>{{ lab_progress[2] }}</div>
          </div>
          <div>
            <div>{{ $t('ad.panel.2.tr.3') }}</div>
            <div>{{ lab_progress[3] }}</div>
          </div>
          <div>
            <div>{{ $t('ad.panel.2.tr.4') }}</div>
            <div>{{ lab_progress[4] }}</div>
          </div>
        </div>
      </div>
      <div style="
height: .8rem;
font-weight: 600;
font-size: 0.23rem;
color: #2D82B3;margin: .3rem auto;">{{ $t('ad.panel.2.tip.0') }}
      </div>
      <div class="ad-btn" @click="$router.push({path:'/m/events/6',query:{index: 1}})">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(16, 168, 255, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {
    &-head > div, &-content > div > div {
      width: 50%;
    }

    &-head {
      width: 6.19rem;
      height: 0.5rem;
      background: #FF376B;
      border-radius: 0.12rem 0.12rem 0rem 0rem;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      align-items: center;
    }

    &-content {
      width: 6.19rem;
      height: 2.85rem;
      background: #FFFFFF;
      color: #CB542B;
      border-radius: 0rem 0rem 0.1rem 0.1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;

      > div:nth-child(even) {
        background: #FFF9E7;
      }

      > div {
        display: flex;
        flex-direction: row;
        width: 100%;
        flex: 1;

        > div {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>