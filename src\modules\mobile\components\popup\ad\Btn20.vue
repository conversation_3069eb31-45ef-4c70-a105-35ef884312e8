<script>


export default {
  data() {
    return {
    }
  },

}
</script>

<template>
  <div class="ad-wrap" style="background-image: url('/img/tdtc/events/20.png');">
    <div class="ad-bg">

    </div>
    <div class="ad-title"><PERSON><PERSON><PERSON> thành nhiệm vụ giới thiệu có thể nhận thưởng lên đến {{ $store.state.popups.configs[53].reward_amount | formatGold }}!</div>
    <div>
      <div style="font-size: .26rem; margin-top: .1rem;color:#fff">
        {{ $store.state.popups.configs[53].start_time }} - {{ $store.state.popups.configs[53].end_time }}.</div>
      <div class="ad-btn" @click="$router.push({path:'/m/inviteFriends',query:{index: 2}})">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-title {
    font-weight: bold;
    font-size: 0.38rem;
    font-family: Segoe UI;
    color: #FFF88B;
    margin-top: .6rem;
  }



  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, #FF5736, #FFA904);
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>