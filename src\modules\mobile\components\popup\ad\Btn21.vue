<script>
import {type51} from "@/mixins/tdtc/events/type51";

export default {
  mixins: [type51]
}
</script>
<template>
  <div class="ad-wrap" style="background-image: url('/img/tdtc/events/21.png');">
    <div class="ad-bg">
    </div>
    <div class="ad-title"><PERSON><PERSON> xì cá KOI đang vẫy tay chào bạn!</div>
    <div>
      <div style="font-weight: bold;
font-size: 0.46rem;
color: #FFD74C;"></div>
      <div style="font-weight: bold;
font-size: 0.32rem;
color: #FFFFFF;"></div>
    </div>
    <div style="z-index: 1;">
      <div class="grade" style="
    color: #B15000;
    text-align: start;
    padding: .1rem;">


        <div style="font-family: Segoe UI;
 text-align: center;
font-weight: 900;
font-size: 0.46rem;
color: #FC9712;">Phần thưởng cao nhất mỗi lần là</div>
        <div style="margin: 0 auto;
width: 5.68rem;
height: 1.36rem;
background: linear-gradient(0deg, rgba(255,230,60,0.72), rgba(232,25,44,0.72));
border-radius: 0.23rem;
border: 0.02px solid;
border-image: linear-gradient(90deg, #FFFC22, #FFFEB2, #FFFC22) 0.02 0.02;">
        <div style="font-family: Segoe UI;
font-weight: 900;
font-size: 0.8rem;
color: #F8FF2D;text-align: center;
text-shadow: 0rem 0rem 0rem rgba(0,0,0,0.39);"> {{ $options.filters['formatGold2'](res.config.highest_award, 6) }}</div>
        </div>
      </div>
      <div style="font-size: .2rem; margin-top: .1rem;color:#e77474">
        Thời gian sự kiện: {{res.config.active_start_time}} - {{res.config.active_end_time}}.</div>
      <div class="ad-btn" @click="$router.push('/m/events/51')">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-title {
    width: 4.14rem;
    height: 0.42rem;
    font-family: Segoe UI;
    font-weight: 900;
    font-size: 0.45rem;
    color: #FFFFFF;
  }

  .grade {

    width: 6.19rem;
    height: 3rem;
    background: #FFEAE8;
    border-radius: 0.12rem;



  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, #FF5536, #CA4F1C);
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}
</style>