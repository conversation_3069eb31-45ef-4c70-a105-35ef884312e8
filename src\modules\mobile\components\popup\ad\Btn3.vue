<script>
import {type18} from "@/mixins/tdtc/events/type18";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";

export default {
  components: {RecordBoard},
  mixins: [type18]
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/3.png" style="width: 5rem;margin-top: 3.5rem;" alt="">
    </div>
    <div>
      <div class="ad-title">{{ $t('ad.tab.3') }}</div>
      <div style="font-size: 0.2rem;text-align: start;margin-left: .02rem;z-index: 1;color: #CB542B;">
        <div>{{ $t('ad.panel.3.tip.0') }}</div>
        <div>{{ $t('ad.panel.3.tip.1') }}</div>
        <div>{{ $t('ad.panel.3.tip.2') }}</div>
      </div>
    </div>
    <div>
      <RecordBoard :data="conf" :column="column"/>
      <div class="ad-btn" @click="$router.push('/m/events/18')">{{ $t('go') }}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

::v-deep .record-board {
  .record-board-wrap {
    z-index: 1;
    width: 6.19rem;
    height: 3rem;
    border-radius: 0.12rem;
    color: #CB542B;

    table {
      table-layout: unset;

      th {
        font-size: 0.26rem;
        color: #FEFEFE;
        background: #F02B63;
        padding: unset;
        width: unset;
      }

      tr {
        background: #FFFFFF;
        line-height: 0.6rem;
      }
    }
  }
}

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(255, 211, 105, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>