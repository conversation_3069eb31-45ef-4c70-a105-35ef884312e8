<script>
import {TDTC_ROURE} from "@/api/tdtc";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";

export default {
  components: {RecordBoard},
  data() {
    return {
      conf: [],
    }
  },
  mounted() {
    this.query14()
  },
  methods: {
    query14() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AD_AGENT_SPREAD_INFO_A)
          .then((res) => {
            if (res['spread']) this.conf = res['spread']
          })
          .catch(() => {
          })
    },
  }
}
</script>


<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/4.png" style="width: 6rem;margin-top: .2rem;" alt="">
    </div>
    <div class="ad-title">{{$t('ad.tab.4')}}</div>
    <div style="z-index: 1;">
      <div style="
      width: 6.06rem;
height: 3.7rem;
font-weight: 400;
font-size: 0.2rem;
color: #FFFAF2;
line-height: 0.3rem;
  text-align: start">
        {{ $t('ad.panel.4.tip.0') }}
      </div>
      <div class="grade">
        <div>
          <div style="
height: 0.5rem;background: #FF2D4B;line-height: .5rem;">{{ $t('ad.panel.4.th.0') }}</div>
          <div style="width: 3.15rem;
height: 3.06rem;
background: #FFF8F9;font-size: 0.23rem;
color: #FF2D4B;">
            <div v-for="item in conf">{{ item.num }}</div>
          </div>

        </div>
        <div>
          <div style="
height: 0.5rem;background: #097FDA;line-height: .5rem;">{{ $t('ad.panel.4.th.1') }}</div>
          <div style="width: 3.05rem;
height: 3.06rem;
background: #EDF7FF;font-size: 0.23rem;
color: #097FDA;">
            <div v-for="item in conf">{{ item.score | formatGold }}</div>
          </div>

        </div>
      </div>

      <div class="ad-btn" @click="$router.push({path:'/m/inviteFriends',query:{index: 1}})">{{$t('go')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

::v-deep .record-board {
  .record-board-wrap {
    z-index: 1;
    width: 6.19rem;
    height: 4.3rem;
    border-radius: 0.12rem;
    color: #CB542B;

    table {
      table-layout: unset;

      th {
        font-size: 0.26rem;
        color: #FEFEFE;
        background: #F02B63;
        padding: unset;
      }

      tr {
        background: #FFFFFF;
        line-height: 0.6rem;
      }
    }
  }
}

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(237, 43, 120, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {
    background: #FFFFFF;
    border-radius: 0.12rem;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    > div > div:nth-child(2) {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>