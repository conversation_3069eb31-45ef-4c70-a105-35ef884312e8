<script>
import {TDTC_ROURE} from "@/api/tdtc";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";

export default {
  components: {RecordBoard},
  data() {
    return {
      res: {
        growfund_money: 0,
        conf: []
      },
      column: [
        {
          label: this.$t('ad.panel.5.th.0'),
          prop: "player_level",

        },
        {
          label: this.$t('ad.panel.5.th.1'),
          prop: "award_money",
          render: FieldRenderType.formatGoldWithK,
        },
      ],
    }
  },
  mounted() {
    this.query11()
  },
  methods: {
    query11() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_AD_GROW_FUND)
          .then((res) => {
            this.res.growfund_money = res['growfund_money'] ?? 0
            this.res.conf = res['conf'] ?? []
          })
          .catch(() => {
          })
    },
  }
}
</script>

<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/5.png" style="width: 6rem;" alt="">
    </div>
    <div class="ad-title">{{$t('ad.tab.5')}}</div>
    <div style="z-index: 1;">
      <div class="grade">
        <RecordBoard :data="res.conf" :column="column"/>
      </div>
      <div style="width: 6.06rem;
font-weight: 400;
font-size: 0.2rem;
color: #247548;
line-height: 0.3rem;text-align: start;padding-top: .2rem;">{{ $t('ad.panel.5.tip.0') }}</div>
      <div class="ad-btn" @click="$router.push('/m/events/7')">{{$t('go')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">


::v-deep .record-board {
  .record-board-wrap {
    height: 3.17rem;
    table {
      th:nth-child(1) {
        background: #F08A06;
      }
      th:nth-child(2) {
        background: #00AF63;
      }
      th {
        color: #FFFFFF;
      }
      td:nth-child(1) {
        color: #B66700;
        background: #FEFFF8;
      }
      td:nth-child(2) {
        color: #006F3F;
        background: #EDFFF0;
      }
    }
  }
}

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(0, 191, 139, 1), rgba(245, 255, 181, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {
    background: #FFFFFF;
    border-radius: 0.12rem;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    > div > div:nth-child(2) {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>