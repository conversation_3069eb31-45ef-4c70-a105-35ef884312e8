<script>
export default {
}
</script>

<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/7.png" style="width: 6rem;margin-top: .7rem;" alt="">
    </div>
    <div class="ad-title">{{$t('ad.tab.7')}}</div>
    <div style="z-index: 1;">
      <div class="grade">
        <div>
          <div style="width: 3.7rem;
height: 0.5rem;background: #3C2DFF;line-height: .5rem;">{{ $t('ad.panel.7.th.0') }}</div>
          <div style="width: 3.67rem;
height: 3.06rem;
background: #F8F9FF;font-size: 0.23rem;
color: #3C2DFF;">
            <div>200K</div>
            <div>1M</div>
            <div>3M</div>
            <div>10M</div>
            <div>30M</div>
            <div>100M</div>
            <div>500M</div>
          </div>

        </div>
        <div>
          <div style="width: 2.52rem;
height: 0.5rem;background: #DA0909;line-height: .5rem;">{{ $t('ad.panel.7.th.0') }}</div>
          <div style="width: 2.52rem;
height: 3.06rem;
background: #FFF3F4;font-size: 0.23rem;
color: #DA0909;">
            <div>8K</div>
            <div>28K</div>
            <div>58K</div>
            <div>188K</div>
            <div>588K</div>
            <div>1.89M</div>
            <div>8.89M</div>
          </div>

        </div>
      </div>
      <div style="width: 6.06rem;
font-weight: 400;
font-size: 0.2rem;
color: #3C4E87;
line-height: 0.3rem;text-align: start;margin-top: .2rem;">{{ $t('ad.panel.7.tip.0') }}</div>
      <div class="ad-btn" @click="$router.push('/m/events/5')">{{$t('go')}}</div>
    </div>
  </div>
</template>

<style scoped lang="scss">

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(0, 60, 255, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .grade {
    background: #FFFFFF;
    border-radius: 0.12rem;
    display: flex;
    flex-direction: row;
    overflow: hidden;
    > div > div:nth-child(2) {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>