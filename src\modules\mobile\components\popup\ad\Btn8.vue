<script>

export default {

  data() {
    return {
    }
  },
  methods: {
  }
}
</script>

<template>
  <div class="ad-wrap">
    <div class="ad-bg">
      <img src="/img/tdtc/events/8.png" style="width: 6.6rem" alt="">
    </div>
    <div class="ad-title">{{ $t('ad.tab.8') }}</div>
    <div style="height: 1.7rem;"></div>
    <div class="grade">
      <div class="grade-head">{{ $t('ad.panel.8.th.0') }}</div>
      <div class="grade-clip"></div>
      <div class="grade-content">
        <div>
          <span>18</span>
          <span style="margin-bottom: .2rem;">18%</span>
        </div>
        <div>
          <span>38</span>
          <span style="margin-bottom: .4rem;">38%</span>
        </div>
        <div>
          <span>88</span>
          <span style="margin-bottom: .6rem;">88%</span>
        </div>
      </div>
      <div class="grade-footer">{{ $t('ad.panel.8.th.1') }}</div>
    </div>
    <div style="font-weight: 600;
font-size: 0.26rem;
color: #FE2F56;
line-height: 0.34rem;">{{ $t('ad.panel.8.tip.0') }}</div>
    <div style="font-size: 0.23rem;
color: #833E68;
line-height: 0.36rem;">{{ $t('ad.panel.8.tip.1') }}</div>
    <div class="ad-btn" @click="$router.push('/m/events/10')">{{ $t('go') }}</div>
  </div>
</template>

<style scoped lang="scss">

.ad-wrap {
  border-radius: 0.18rem;
  height: 8.8rem;
  margin-top: .05rem;
  background: linear-gradient(180deg, rgba(255, 113, 28, 1), rgba(255, 255, 255, 1));
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .15rem .2rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;


  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
    color: #FFF948;
  }

  .grade {
    z-index: 1;
    width: 6.19rem;
    border-radius: 0.12rem;
    background: linear-gradient(0deg, #FF6BA6, #FD4C5D);
    position: relative;

    &-head, &-footer {
      height: 0.5rem;
      line-height: .5rem;
    }


    &-clip {
      height: 2.37rem;
      background: white;
      clip-path: polygon(
              0 0, 0.69rem 0,
              0.69rem 0.5rem, 0.99rem 0.5rem,1.15rem 0.67rem,1.29rem 0.5rem, 1.61rem 0.5rem,
              1.61rem 0, 2.61rem 0,
              2.61rem 0.5rem, 2.91rem 0.5rem, 3.07rem 0.67rem, 3.21rem 0.5rem, 3.53rem 0.5rem,
              3.53rem 0, 4.52rem 0,
              4.52rem 0.5rem, 4.82rem 0.5rem, 4.98rem 0.67rem, 5.12rem 0.5rem, 5.44rem 0.5rem,
              5.44rem 0, 100% 0,
              100% 100%, 5.44rem 100%,
              5.44rem 0.76rem, 4.52rem 0.76rem,
              4.52rem 100%, 3.53rem 100%,
              3.53rem 1.3rem, 2.61rem 1.3rem,
              2.61rem 100%, 1.61rem 100%,
              1.61rem 1.62rem, 0.69rem 1.62rem,
              0.69rem 100%, 0 100%,
      );
    }

    &-content {
      position: absolute;
      top: 0.5rem;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      height: 2.37rem;
      width: 100%;
      background: transparent;
      padding: 0 .75rem 0 .66rem;
      > div {
        width: 0.92rem;
        padding-top: .08rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
      }
    }

  }

  .ad-bg {
    position: fixed;
    top: 0;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>