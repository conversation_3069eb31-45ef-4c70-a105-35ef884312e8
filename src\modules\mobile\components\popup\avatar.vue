<script>
import {avatar} from "@/mixins/avatar";

export default {
  name: "avatar",
  mixins: [avatar],
  methods: {
    close(){
      iconsole($('#avatar'))
      // window.jquery('#avatar').modal('hide')
    }
  }
};
</script>

<template>
  <div class="modal fade" id="avatar" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="false">
<!--    <div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="false">-->
<!--      <div class="modal-dialog">-->
<!--        <div class="modal-content">-->
<!--          <div class="modal-header">-->
<!--            <h1 class="modal-title fs-5" id="exampleModalLabel">Modal title</h1>-->
<!--            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>-->
<!--          </div>-->
<!--          <div class="modal-body">-->
<!--            ...-->
<!--          </div>-->
<!--          <div class="modal-footer">-->
<!--            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>-->
<!--            <button type="button" class="btn btn-primary">Save changes</button>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
<!--    </div>-->
    <div data-reactroot="" class="modal-dialog">
<!--      <div class="am-modal-mask"></div>-->
      <div
        tabindex="-1"
        class="am-modal-wrap"
        role="dialog"
        aria-labelledby="rcDialogTitle0"
      >
        <div
          role="document"
          class="am-modal profile-swiper-container am-modal-transparent"
          style="width: 5.4rem; height: auto"
        >
          <div class="am-modal-content modal-content" style="overflow: visible">
            <div style="position: absolute; bottom: -1rem; left: 44%" data-bs-dismiss="modal">
              <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
            </div>
            <div class="am-modal-header">
              <div class="am-modal-title" id="rcDialogTitle0">
                {{ $t('choose_avatar') }}
              </div>
            </div>
            <div class="am-modal-body">
              <div class="profile-swiper-drop-header">
<!--                <div class="close-modal" data-bs-dismiss="modal"></div>-->
              </div>
              <div class="profile-swiper-drop">
                <img
                    :src="`img/profile/icon_${$store.state.account.icon}.png`"
                  alt=""
                  class="default-thumb"
                />
<!--                <input type="file" class="default-change" />-->
              </div>
              <!-- react-text: 14 -->
              <!-- /react-text -->
              <div class="profile-swiper-drop-tips">
                {{ $t('click_change_avatar') }}
              </div>
              <div class="profile-swiper-drop-title">{{ $t('system_avatar') }}</div>
              <div
                class="react-swipe-container carousel"
                style="
                  overflow: hidden;
                  visibility: visible;
                  position: relative;
                "
              >
                <div style="overflow: hidden; position: relative;">
                  <div
                    data-index="0"
                    style="
                      float: left;
                      position: relative;
                      transition-property: transform;

                      transition-duration: 0ms;
                      transform: translate(0px, 0px) translateZ(0px);
                    "
                  >
                    <div class="mc-profile-item" style="font-size: 0px" v-for="index in 12" :key="index" @click="iconIndex = index - 1" :class="{active: index - 1 === iconIndex}">
                      <img
                          :src="`img/profile/icon_${index-1}.png`"
                        alt=""
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="mc-profile-swiper-nav hide">
                <div>
                  <span
                    ><svg class="am-icon am-icon-left am-icon-md dis">
                      <use xlink:href="#left"></use></svg></span
                  ><span class="mc-profile-swiper-nav-text"
                    ><!-- react-text: 42 -->1
                    <!-- /react-text --><!-- react-text: 43 -->
                    &nbsp;/ &nbsp;<!-- /react-text --><!-- react-text: 44 -->1
                    <!-- /react-text --></span
                  ><span
                    ><svg class="am-icon am-icon-right am-icon-md dis">
                      <use xlink:href="#right"></use></svg
                  ></span>
                </div>
              </div>
            </div>
            <div class="am-modal-footer" >
              <div
                class="am-modal-button-group-v am-modal-button-group-normal"
                role="group"
              >
                <a class="am-modal-button" role="button" @click="changeIcon" data-bs-dismiss="modal">{{ $t('ok') }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
