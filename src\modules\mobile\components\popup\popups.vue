<script>
import {setAdTabIndex} from '@/utils/storage'

export default {
  name: "popups",
  data() {
    return {
      index:  0,
      popup:  {
        switch_49: false,
        switch_51: false,
        switch_52: false,
        switch_53: false,
      }
    }
  },
  mounted() {
    if (this.$store.state.popups.active_types.length > 0) {
      this.index = 0;
      this.showPopup(this.$store.state.popups.active_types[this.index]);
    }
  },
  methods: {

    click49() {
      this.closePopupById(49);
      this.$router.push({path:'/m/inviteFriends',query:{index: 2}})
      // this.$store.commit('setShowAd', true)
      // setAdTabIndex(18)
    },
    click52() {
      this.closePopupById(52);
      this.$store.commit('setShowAd', true)
      setAdTabIndex(18)
    },
    click53() {
      this.closePopupById(53)
      setTimeout(()=>{
        this.$router.push('/m/events/53').catch(()=>{})
      }, 300)
    },
    showPopup(typeId) {
      this.popup['switch_' + typeId] = true;
    },
    closePopupById(typeId, next = true) {
      this.popup['switch_' + typeId] = false;
      if (next && this.$store.state.popups.active_types[this.index + 1]) {
        this.index += 1;
        this.showPopup(this.$store.state.popups.active_types[this.index]);
      }
    },
  }
}
</script>

<template>
  <div>
    <van-popup
        v-model="popup.switch_49"
        lock-scroll
        style="width: 7.14rem;height: 7.31rem;background-image: url('/img/activity/49/popup.png');"
        :close-on-click-overlay="false"
    >
      <div style="position: fixed; bottom: 0; left: 44%" @click="closePopupById(49, true)">
        <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
      </div>
      <div class="coin49">{{ $store.state.popups.configs[49].reward_amount | formatGold }}</div>
      <div style="width: 3.93rem;
    height: .85rem;
    position: absolute;
    background-image: url('/img/activity/49/btn.png');
    background-size: contain;
    background-repeat: no-repeat;
    bottom: 1rem;
    left: 1.6rem;display: flex;justify-content: center;align-items: center;font-size: 0.35rem;
color: #FBFFD2;" @click="click49">Bắt đầu</div>
    </van-popup>
    <van-popup
        v-model="popup.switch_52"
        lock-scroll
        style="width: 7.23rem;height: 7.91rem;background-image: url('/img/activity/52/popup.png');"
        :close-on-click-overlay="false"
    >
      <div style="position: fixed; bottom: 0; left: 44%" @click="closePopupById(52, true)">
        <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
      </div>
      <div style="width: 3.5rem;
    height: .7rem;
    position: absolute;
    bottom: 1.2rem;
    left: 1.6rem;" @click="click52"></div>
    </van-popup>
    <van-popup
        v-model="popup.switch_53"
        lock-scroll
        style="width: 7.23rem;height: 8rem;background-image: url('/img/activity/53/popup.png');"
        :close-on-click-overlay="false"
    >
      <div style="position: fixed; bottom: 0.8rem; left: 44%" @click="closePopupById(53, true)">
        <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
      </div>
      <div style="width: 3.2rem;
    height: 0.8rem;
    position: absolute;
    bottom: 1.9rem;
    left: 2.1rem;" @click="click53">
      </div>
      <span class="gold-text">
        {{ $store.state.popups.configs[53].reward_amount | formatGold }}
      </span>
    </van-popup>
  </div>
</template>

<style scoped>
.coin49 {
  position: absolute;
  top: 3.3rem;
  left: 2.2rem;
  font-family: Segoe UI;
  font-weight: bold;
  font-size: 1.1rem;
  color: #EADBDA;
  //text-shadow: 0rem 0rem 0rem #FFE71C, 0rem 0rem 0rem #B3199F, 0rem 0rem 0rem rgba(0,0,0,0.38);
  background: linear-gradient(180deg, #8AFFD2 17.919921875%, #4B40EB 77.8076171875%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.gold-text {
  position: absolute;
  top: 1.05rem;
  right: 3.9rem;
  font-family: fantasy;
  font-size: 0.68rem;
  font-weight: bold;
  background: radial-gradient(#EABF28, #FEFF4B, #FFFF4B, #EABF28, #9A5C21);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transform: rotate(-1deg);
}

.van-popup {
  background-size   : contain;
  background-repeat : no-repeat;
  background-color  : unset;
}

</style>