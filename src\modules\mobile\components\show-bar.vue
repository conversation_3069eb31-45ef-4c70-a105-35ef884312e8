<script>
export default {
  name: "show-bar",
  data(){
    return {
      showDownloadBar: true
    }
  }
};
</script>

<template>
  <div class="shell-download-bar show-bar" v-if="showDownloadBar">
    <div class="download-bar-icon" style="height: auto; width: 1.2rem">
      <img
          src="img/logo.png"
          alt="app-icon"
      />
    </div>
    <div class="download-bar-btn" @click="$store.commit('setMobileModalDownloadBar', 1)">Download</div>
    <div class="download-bar-close" @click="showDownloadBar=false">
      <svg class="am-icon am-icon-icon-close2 am-icon-md">
        <use xlink:href="#icon-close2"></use>
      </svg>
    </div>
    <div class="app-full-name-wrap">
      <span class="app-full-name">55G</span>
      <div class="app-star-grade">
        <span class="star-icon"></span><span class="star-icon"></span
      ><span class="star-icon"></span><span class="star-icon"></span
      ><span class="star-icon"></span>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
