<script>
import { lang } from "@/mixins/lang";
import SimpleOption from "@/modules/pc/components/select/SimpleOption.vue";
import NationFlag from "@/modules/pc/components/NationFlag.vue";
import {check} from "@/mixins/check";

export default {
  name: "sidebar",
  components: { NationFlag, SimpleOption },
  mixins: [lang, check],
  data() {
    return {
      show: false,
      showSelectLang: false,
    };
  },
  methods: {
    goActivity() {
      this.$store.commit("setShowMobileSide");
      let that = this
      setTimeout(()=>{
        that.$router.push("/m/activity");
      }, 300)
    },
    goPage(path) {
      this.$store.commit("setShowMobileSide");
      let that = this
      setTimeout(()=>{
        if (that.$store.getters.isLogin) {
          that.$router.push(path);
        } else {
          that.$router.push("/m/login");
        }
      }, 300)
    },
  },
};
</script>

<template>
  <div
    class="sidebar"
    :class="{ 'main-nav-open': $store.state.showMobileSide }"
  >
    <div class="left-side-menu">
      <div class="side-menu-content hide-scrollbar">
        <div class="main-nav">
<!--          <div class="main-nav-item vip" v-if="$store.getters.isLogin" @click="goPage('/m/gameRecord')">-->
<!--            <div class="nav-icon-wrap">-->
<!--              <img class="nav-icon" src="/m/icon-vip.3a85ae4f.png" alt="">-->
<!--            </div>-->
<!--            <span class="main-nav-text">{{ $t("nav_vip") }}</span>-->
<!--          </div>-->
          <div class="main-nav-item invite" @click="goPage('/m/inviteFriends')">
            <div class="nav-icon-wrap">
              <img class="nav-icon" src="m/icon-invite.f8e91372.png" alt="" />
            </div>
            <span class="main-nav-text">{{ $t("nav_invite") }}</span>
          </div>
          <div class="main-nav-item bonus" @click="goPage('/m/gameRecord')">
            <div class="nav-icon-wrap">
              <img class="nav-icon" src="m/icon-bonus.4fa02b65.png" alt="" />
            </div>
            <span class="main-nav-text">{{ $t("nav_bet") }}</span>
          </div>
          <div class="main-nav-item vip" @click="goPage('/m/rewardCenter')">
            <div class="nav-icon-wrap">
              <img class="nav-icon" src="img/entry_icon9.png" alt="">
            </div>
            <span class="main-nav-text">{{ $t("in_reward_center") }}</span>
          </div>
        </div>
        <div class="promo-nav">
          <div class="am-accordion collapse-accordion">
            <div
              class="am-accordion-item promo-nav-item"
              :class="{ 'am-accordion-item-active': show }"
              role="tablist"
            >
              <div
                class="am-accordion-header"
                role="tab"
                :aria-expanded="show"
                @click="show = !show"
              >
                <i class="arrow"></i>
                <div class="vendor-header">
                  <svg
                    class="am-icon am-icon-icon-promo-decorate am-icon-md icon-promo-decorate"
                  >
                    <use xlink:href="#icon-promo-decorate"></use>
                  </svg>
                  <span>{{ $t("in_promotional_map") }}</span>
                  <svg
                    class="am-icon am-icon-side-arrow-down am-icon-md accordion-arrow"
                  >
                    <use xlink:href="#side-arrow-down"></use>
                  </svg>
                </div>
              </div>
              <div
                class="am-accordion-content"
                :class="
                  show
                    ? 'am-accordion-content-active'
                    : 'am-accordion-content-inactive'
                "
                role="tabpanel"
                style=""
              >
                <div class="am-accordion-content-box">
                  <div class="sub-nav-list">
                    <div
                      class="sub-nav-item"
                      v-for="(item, index) in $store.state.banners"
                      :key="index"
                      @click="goDetail(item.id)"
                    >
                      <span class="sub-nav-item-text">{{
                        changeLang(item.messages).title
                      }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="main-nav-item all-promo" @click="goActivity()">
              <span>{{ $t("nav_more") }}</span>
            </div>
          </div>
        </div>
        <div
          class="download-wrap"
          @click="$store.commit('setMobileModalDownloadBar', 1)"
        >
          <svg class="am-icon am-icon-icon-nav-download am-icon-md" style="width: .4rem">
            <use xlink:href="#icon-nav-download"></use>
          </svg>
          <span class="download-text">{{ $t("in_app_download") }}</span>
        </div>
        <div
            v-if="$store.state.configs.customer_web"
          class="side-service"
            @click="goUrl($store.state.configs.customer_web)"
        >
          <svg class="am-icon am-icon-icon-service2 am-icon-md service-icon">
            <use xlink:href="#icon-service2"></use>
          </svg>
          <span class="download-text">{{ $t("in_customer_service") }}</span>
        </div>
        <div
            class="side-service"
            @click="$router.push('/m/help')"
        >

          <svg class="am-icon am-icon-icon-service2 am-icon-md service-icon" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="13px" height="22px" viewBox="0 0 13 22" enable-background="new 0 0 13 22" xml:space="preserve">  <image id="image0" width="13" height="22" x="0" y="0"
              href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAWCAMAAAAsJOYWAAAABGdBTUEAALGPC/xhBQAAACBjSFJN
AAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABU1BMVEX/////w0b/xEb/wkX/
xEb/vkT/u0L/t0D/wkX/w0b/xEb/sz//vkT/wkX/rz3/sj7/tD//u0L/wET/uUH/vEP/sz//qTr/
rDz/pDj/ukL/vEP/nzb/sT7/tD//mjT/qTr/rDz/lTH/pTj/kC//nzb/hSr/iCz/iy3/ji7/kTD/
lTH/fyj/kC//eib/ii3/dSP/hSv/cCL/gCj/bCD/biH/ciL/dCP/dyT/eib/xEb/w0b/wET/wkX/
vUP/v0T/wUX/uUH/vEL/vkP/tkD/uEH/u0L/t0D/vkT/rz3/sj7/tD//pzn/qjv/rTz/sD3/sz7/
ojf/pTn/qTr/qzv/rj3/nTX/oDb/pDj/pjn/mDP/mzT/nzb/oTf/kjD/ljL/mTP/nDX/gin/hiv/
iSz/jC7/fSf/gSn/hCr/hyv/eCX/fCb/fij/cyP/dyT/eSX/fCf///9cvBNHAAAAOXRSTlMAEcyq
d7u7u9273bt3d4i77nd3Zu6IEcxEmRFE7mZE3SJEu0S7M7u7u7uIRLtEu0S7RLsid3d3d2Y6Cjs0
AAAAAWJLR0QAiAUdSAAAAAd0SU1FB+cMDQsfCD/b3QsAAAC+SURBVBjTY2BksoQBJgYGZis4z5KF
gdXaxgrBY7O1s7eB89gdHJ2sOTi5IDxuZxdXWx4GXiswj49fwM1BkEHI3grEgwBhEXegaVCeKEi5
uz0viC0m7uHp5ebgaisE4kl4+/j6+Tu7SEqBeNIBgUHBITKyEF1yoWHhEfIKUAMVI6OiY5SgHAbl
2Lj4BBUGrEBVTV1DUwvG005MSk7RgfF0U9PSM/RgPP3MrOxEAxjPMCc3L98IxjM2MTUztwCxAOTI
J+0oAeBDAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIzLTEyLTEzVDEwOjMxOjA4KzAxOjAw6iVM/wAA
ACV0RVh0ZGF0ZTptb2RpZnkAMjAyMy0xMi0xM1QxMDozMTowOCswMTowMJt49EMAAAAZdEVYdFNv
ZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAAAAElFTkSuQmCC" />
</svg>
          <span class="download-text">{{ $t("help_center") }}</span>
        </div>
        <div class="side-language">
          <div class="language-select">
            <div class="simple-select">
              <div
                class="select-wrap"
                :class="{ on: showSelectLang }"
                @click="showSelectLang = !showSelectLang"
              >
                <div class="selected-lang">
                  <NationFlag type="2" :lang="currentLanguage" class="icon-lang"/>
                  <span>{{ $t(currentLanguage) }}</span>
                </div>
              </div>
              <div class="option-wrap" :class="{ on: showSelectLang }">
                <div
                  class="option-item"
                  v-for="(item, index) in supportLanguages"
                  :key="'lang_' + index"
                  :class="className"
                  @click="()=>{
                    showSelectLang = false;
                    selectLanguage(item);
                    $store.commit('setShowMobileSide');
                  }"
                >
                  <div class="lang-title">
                    <NationFlag type="2" :lang="item" class="icon-lang"/>
                    <span> {{ $t(item) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sidebar-mask" @click="$store.commit('setShowMobileSide')"></div>
  </div>
</template>

<style scoped>
.sidebar .left-side-menu .download-wrap, .sidebar .left-side-menu .side-service {
  justify-content: start;
  padding-left: .23rem;
  gap: 0.2rem;
}
</style>