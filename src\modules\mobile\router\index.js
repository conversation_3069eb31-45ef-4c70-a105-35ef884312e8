export const routes = [
  {
    path: "/:path(|m)",
    component: () => import("../views/index/index.vue"),
  },
  {
    path: "/m/test",
    component: () => import("../views/test.vue"),
  },
  {
    path: "/m/login",
    component: () => import("../views/login.vue"),
  },
  {
    path: "/m/register",
    component: () => import("../views/register.vue"),
  },
  {
    path: "/m/notice",
    component: () => import("../views/notice.vue"),
  },
  {
    path: "/m/seamless",
    component: () => import("../views/seamless/index.vue"),
  },
  {
    path: "/m/seamless/go",
    component: () => import("../views/seamless/url.vue"),
  },
  {
    path: "/m/activity",
    component: () => import("../views/activity/index.vue"),
  },
  {
    path: "/m/activityDetail",
    component: () => import("../views/activity/detail.vue"),
  },
  {
    path: "/m/rewardCenter/report",
    component: () => import("../views/rewardCenter/report.vue"),
  },
  {
    path: "/m/rewardCenter/signIn",
    component: () => import("../views/rewardCenter/signin.vue"),
  },
  {
    path: "/m/events/9",
    component: () => import("../views/tdtc/events/Type9.vue"),
  },
  {
    path: "/m/help",
    component: () => import("../views/help/index.vue"),
  },
  {
    path: "/m/voucherCenter",
    component: () => import("../views/tdtc/recharge/Index.vue"),
  },
  {
    path: "/m/vouReport",
    component: () => import("../views/tdtc/recharge/Page4.vue"),
  },
  {
    path: "/m/withdraw",
    component: () => import("../views/tdtc/withdraw/Index.vue"),
  },
  {
    path: "/m/withdrawReport",
    component: () => import("../views/tdtc/withdraw/history.vue"),
  },
  {
    path: "/m/bonus",
    component: () => import("../views/tdtc/Bonus.vue"),
  },
  {
    path: "/m/myAccount/index",
    component: () => import("../views/account/index.vue"),
  },
  {
    path: "/m/member/home",
    component: () => import("../views/account/home.vue"),
  },
  {
    path: "/m/member/benefits",
    component: () => import("../views/account/benefits.vue"),
  },
  {
    path: "/m/member/manualRebate",
    component: () => import("../views/account/manualRebate.vue"),
  },
  {
    path: "/m/member/rebateReport",
    component: () => import("../views/account/rebateReport.vue"),
  },
  {
    path: "/m/securityCenter",
    component: () => import("../views/account/securityCenter.vue"),
  },
  {
    path: "/m/gameRecord",
    component: () => import("../views/game/record.vue"),
  },
  {
    path: "/m/rewardCenter",
    component: () => import("../views/rewardCenter/index.vue"),
  },
  {
    path: "/m/events",
    component: () => import("../views/tdtc/events/index.vue"),
  },
  {
    path: "/m/receivingCenter",
    component: () => import("../views/receivingCenter.vue"),
  },
  {
    path: "/m/profitandloss",
    component: () => import("../views/profitandloss.vue"),
  },
  {
    path: "/m/transaction/record",
    component: () => import("../views/transaction/record.vue"),
  },
  {
    path: "/m/webEmail",
    component: () => import("../views/web/email.vue"),
  },
  {
    path: "/m/inviteFriends",
    component: () => import("../views/invite/index.vue"),
  },
  {
    path: "/m/inviteOverview",
    component: () => import("../views/invite.vue"),
  },
  {
    path: "/m/member/manualRebate",
    component: () => import("../views/account/manualRebate.vue"),
  },
  {
    path: "/m/myVCards",
    component: () => import("../views/account/myVCards.vue"),
  },
  {
    path: "/m/idVerification",
    component: () => import("../views/account/idVerification.vue"),
  },
  {
    path: "/m/myEWallet",
    component: () => import("../views/account/myEWallet.vue"),
  },
  {
    path: "/m/securityCenter/loginChange",
    component: () => import("../views/account/loginChange.vue"),
  },
  {
    path: "/m/securityCenter/FundsPasswordBox",
    component: () => import("../views/account/withdrawChange.vue"),
  },
  {
    path: "/m/securityCenter/bindGA",
    component: () => import("../views/account/bindGA.vue"),
  },
  {
    path: "/m/myAccount/phone",
    component: () => import("../views/account/phone.vue"),
  },
  {
    path: "/m/securityCenter/addBankCard",
    component: () => import("../views/account/addBankCard.vue"),
  },
  {
    path: "/m/securityCenter/addBankCardPix",
    component: () => import("../views/account/addBankCardPix.vue"),
  },
  {
    path: "/m/rewardCenter/Luckwheel",
    component: () => import("../views/rewardCenter/Luckwheel.vue"),
  },
  {
    path: "/m/events/6",
    component: () => import("../views/tdtc/events/Type6.vue"),
  },
  {
    path: "/m/events/8",
    component: () => import("../views/tdtc/events/Type8.vue"),
  },
  {
    path: "/m/events/10",
    component: () => import("../views/tdtc/events/Type10.vue"),
  },
  {
    path: "/m/events/11",
    component: () => import("../views/tdtc/events/Type11.vue"),
  },
  {
    path: "/m/events/26",
    component: () => import("../views/tdtc/events/Type26.vue"),
  },
  {
    path: "/m/events/45",
    component: () => import("../views/tdtc/events/Type45.vue"),
  },
  {
    path: "/m/events/46",
    component: () => import("../views/tdtc/events/Type46.vue"),
  },
  {
    path: "/m/events/200",
    component: () => import("../views/tdtc/events/Type200.vue"),
  },
  {
    path: "/m/events/201",
    component: () => import("../views/tdtc/events/Type201.vue"),
  },
  {
    path: "/m/events/202",
    component: () => import("../views/tdtc/events/Type202.vue"),
  },
  {
    path: "/m/rewardCenter/Dailybetreward",
    component: () => import("../views/rewardCenter/Dailybetreward.vue"),
  },
  {
    path: "/m/rewardCenter/Dailylogonredpacket",
    component: () => import("../views/rewardCenter/Dailylogonredpacket.vue"),
  },
  {
    path: "/m/rewardCenter/Dailyrelieffund",
    component: () => import("../views/rewardCenter/Dailyrelieffund.vue"),
  },
  {
    path: "/m/events/5",
    component: () => import("../views/tdtc/events/Type5.vue"),
  },
  {
    path: "/m/rewardCenter/Dailywinnerreward",
    component: () => import("../views/rewardCenter/Dailywinnerreward.vue"),
  },
  {
    path: "/m/rewardCenter/Firstchargereward",
    component: () => import("../views/rewardCenter/Firstchargereward.vue"),
  },
  {
    path: "/m/events/18",
    component: () => import("../views/tdtc/events/Type18.vue"),
  },
  {
    path: "/m/events/7",
    component: () => import("../views/tdtc/events/Type7.vue"),
  },
  {
    path: "/m/rewardCenter/Monthlyreward",
    component: () => import("../views/rewardCenter/Monthlyreward.vue"),
  },
  {
    path: "/m/rewardCenter/Redpacketrain",
    component: () => import("../views/rewardCenter/Redpacketrain.vue"),
  },
  {
    path: "/m/rewardCenter/Vipwelfare",
    component: () => import("../views/rewardCenter/Vipwelfare.vue"),
  },
  {
    path: "/m/vip",
    component: () => import("../views/Vip.vue"),
  },
  {
    path: "/m/rewardCenter/Weeklybetreward",
    component: () => import("../views/rewardCenter/Weeklybetreward.vue"),
  },
  {
    path: "/m/rewardCenter/SaturdayCarnival",
    component: () => import("../views/rewardCenter/SaturdayCarnival.vue"),
  },
  {
    path: "/m/rewardCenter/DailyRebate",
    component: () => import("../views/rewardCenter/DailyRebate.vue"),
  },
  {
    path: "/m/rewardCenter/EarnCashByPromote",
    component: () => import("../views/rewardCenter/EarnCashByPromote.vue"),
  },
  {
    path: "/m/statement",
    component: () => import("../views/tdtc/statement/Index.vue"),
  },
  {
    path: "/m/history",
    component: () => import("../views/tdtc/history/Index.vue"),
  },
  {
    path: "/m/rank",
    component: () => import("../views/tdtc/rank/Index.vue"),
  },
  {
    path: "/m/events/52",
    component: () => import("../views/tdtc/events/Type52.vue"),
  },
  {
    path: "/m/events/53",
    component: () => import("../views/tdtc/events/Type53.vue"),
  },
  {
    path: "/m/events/51",
    component: () => import("../views/tdtc/events/Type51.vue"),
  },
];