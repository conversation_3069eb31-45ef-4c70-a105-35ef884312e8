<script>
import {TDTC_ROURE} from "@/api/tdtc";

export default {
  data() {
    let that = this
    return {
      btns_list: [],
      color: [
        "#F8A772",
        "#82C9E5",
        "#F6A47B",
        "#7BBBF6",
        "#E37BF6",
        "#FD5858",
        "#57BCCC",
        "#A776E9",
      ],
      trackColor: [
        "#F1D8CB",
        "#C1DBE6",
        "#EDD49E",
        "#9AE4ED",
        "#D9AFFF",
        "#F1A0A4",
        "#ADEADF",
        "#CEABF5",
      ],
      index: this.$store.state.account.vip > 1 ? this.$store.state.account.vip-1 : 0,
      swiperOptions: {
        initialSlide: this.$store.state.account.vip > 1 ? this.$store.state.account.vip-1 : 0,
        slidesPerView: 'auto',
        centeredSlides: true,
        on: {
          slideChange: function () {
            that.index = this.activeIndex
          },
        },
      },
      res: {
        nowrecharge: 0,
        levelupbonus: [],
        weekwageviplevel: 0,
        weekwagestatus: 0,
        monthwageviplevel: 0,
        monthwagestatus: 0,
        conf: [],
        vipconf: [],
      }
    }
  },
  mounted() {
    this.btns_list = []
    this.query()
  },
  methods: {
    setBtn() {
      let msg = this.res
      let level_configs = msg["conf"]
      for (let i = 1; i <= level_configs.length; i++) {
        let config = level_configs[i - 1];
        let btns = []
        for (let j = 1; j <= 4; j++) {
          let lab_num  = 0
          let btn_check = false
          let active = false
          let lab_checked = ""

          if (j === 1) {
            lab_num = this.$options.filters['formatGoldWithK'](config["levelupaward"])
            if (i > this.$store.state.account.vip || this.$store.state.account.vip === 0) {
              active = true
              btn_check = false
              lab_checked = this.$t(`VIP.STATUS_1`)
            } else {
              active = msg["levelupbonus"][i - 1] === 0
              btn_check = msg["levelupbonus"][i - 1] === 0
              if (msg["levelupbonus"][i - 1] === 0) {
                lab_checked = this.$t(`VIP.STATUS_2`)
              } else {
                lab_checked = this.$t(`VIP.STATUS_3`)
              }
            }
          } else if (j === 2) {
            if (i !== this.$store.state.account.vip || this.$store.state.account.vip === 0 || (msg["weekwageviplevel"] ? msg["weekwageviplevel"] : 0) === 0) {
              active = true
              btn_check = false
              lab_checked = this.$t(`VIP.STATUS_1`)
              lab_num = this.$options.filters['formatGoldWithK'](config["weekwage"])
            } else {
              if ((msg["weekwagestatus"] ? msg["weekwagestatus"] : 0) === 0) {
                active = true
                btn_check = false
                lab_checked = this.$t(`VIP.STATUS_1`)
                lab_num = this.$options.filters['formatGoldWithK'](level_configs[msg["weekwageviplevel"] - 1 < 0 ? 0 : msg["weekwageviplevel"] - 1]["weekwage"])
              } else if (msg["weekwagestatus"] && msg["weekwagestatus"] == 1) {
                active = true
                btn_check = true
                lab_checked = this.$t(`VIP.STATUS_2`)
                lab_num = this.$options.filters['formatGoldWithK'](level_configs[msg["weekwageviplevel"] - 1 < 0 ? 0 : msg["weekwageviplevel"] - 1]["weekwage"])
              } else if (msg["weekwagestatus"] && msg["weekwagestatus"] == 2) {
                active = false
                btn_check = false
                lab_checked = this.$t(`VIP.STATUS_3`)
                lab_num = this.$options.filters['formatGoldWithK'](level_configs[msg["weekwageviplevel"] - 1 < 0 ? 0 : msg["weekwageviplevel"] - 1]["weekwage"])
              }
            }
          } else if (j === 3) {
            if (i !== this.$store.state.account.vip || this.$store.state.account.vip === 0 || !msg["monthwageviplevel"]) {
              active = true
              btn_check = false
              lab_checked = this.$t(`VIP.STATUS_1`)
              lab_num = this.$options.filters['formatGoldWithK'](config["monthwage"] ? config["monthwage"] : 0, 0)
            } else {
              if ((msg["monthwagestatus"] ? msg["monthwagestatus"] : 0) === 0) {

                active = true
                btn_check = false
                lab_checked = this.$t(`VIP.STATUS_1`)
                lab_num = this.$options.filters['formatGoldWithK'](level_configs[msg["monthwageviplevel"] - 1 < 0 ? 0 : msg["monthwageviplevel"] - 1]["monthwage"])

              } else if (msg["monthwagestatus"] && msg["monthwagestatus"] === 1) {
                active = true
                btn_check = true
                lab_checked = this.$t(`VIP.STATUS_2`)
                lab_num = this.$options.filters['formatGoldWithK'](level_configs[msg["monthwageviplevel"] - 1 < 0 ? 0 : msg["monthwageviplevel"] - 1]["monthwage"])

              } else if (msg["monthwagestatus"] && msg["monthwagestatus"] === 2) {
                active = false
                btn_check = false
                lab_checked = this.$t(`VIP.STATUS_3`)
                lab_num = this.$options.filters['formatGoldWithK'](level_configs[msg["monthwageviplevel"] - 1 < 0 ? 0 : msg["monthwageviplevel"] - 1]["monthwage"])
              }
            }
          } else if (j === 4) {
            lab_num = config["signtimes"] ? config["signtimes"] : 0
            if (i !== this.$store.state.account.vip || this.$store.state.account.vip === 0) {
              active = true
              btn_check = false
              lab_checked = this.$t(`VIP.STATUS_1`)
            } else {
              active = false
              btn_check = false
              lab_checked = this.$t(`VIP.STATUS_3`)
            }
          }
          btns[j-1] = {lab_num,btn_check,lab_checked, active}
        }
        this.btns_list[i-1] = btns
      }
    },
    percentage(needmoney) {
      let p = parseInt(this.res.nowrecharge/needmoney*100)
      return p >= 100 ? 100 : p
    },
    query() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_VIP_ACTIVE_INFO)
          .then((res) => {
            // VipActiveInfo
            Object.assign(this.res, res)
            this.setBtn()
          })
          .catch(() => {
          })
    },
    award(vip) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_VIP_UP_AWARD, {
        'viplevel': vip,
      })
          .then((res) => {
            // GetVipActiveResponse
            if (!res["code"]) {
              if (res["bonusScore"] > 0) {
                $toast.success({
                  icon: "passed",
                  message: this.$t("BONUS.AWARDSUCCESS", { money: this.$options.filters['formatGoldWithK'](res["bonusScore"]) })
                });

              } else {
                $toast.success({
                  icon: "passed",
                  message: this.$t("VIP.SUCCESS", { money: this.$options.filters['formatGoldWithK'](res["awardMoney"]) }),
                });
              }
              this.query()
            } else {
              window.$toast.fail(this.$t("VIP.ERROR"));
            }
          })
          .catch(() => {})
    },
    wage(awardtype) {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_VIP_UP_WAGE, {
        'awardtype': awardtype,
      })
          .then((res) => {
            // GetVipActiveResponse
            if (!res["code"]) {
              if (res["bonusScore"] > 0) {
                $toast.success({
                  icon: "passed",
                  message: this.$t("BONUS.AWARDSUCCESS", { money: this.$options.filters['formatGoldWithK'](res["bonusScore"]) })
                });

              } else {
                $toast.success({
                  icon: "passed",
                  message: this.$t("VIP.SUCCESS", { money: this.$options.filters['formatGoldWithK'](res["awardMoney"]) }),
                });
              }
              this.query()
            } else if (res["code"] === 7) {
              window.$toast.fail(this.$t(awardtype ? "VIP.ERROR_2" : "VIP.ERROR_1", { limite: this.$options.filters['formatGoldWithK'](res["leftRech"]) }))
            } else {
              window.$toast.fail(this.$t("VIP.ERROR"));
            }
          })
          .catch(() => {})
    }
  }
};
</script>

<template>
  <div class="td-vip" :class="`td-vip${index}`" style="position: relative;height: 100vh;background: #F9F9F9;">
    <div class="vip-header">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-home am-navbar am-navbar-light" style="position: fixed !important;width: 100%; z-index: 1; top: 0;"
        >
          <div class="am-navbar-left" role="button">
          <span class="am-navbar-left-content" @click="$router.back()">
            <span class="return_icon">
              <svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg>
            </span>
          </span>
          </div>
          <div class="am-navbar-title" style="font-size: .4rem !important;font-weight: 600;">{{ $t('vip') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>

      <swiper :options="swiperOptions">
        <swiper-slide
            v-for="(item, index) in res.vipconf"
            :key="'banner_' + index"
        >
          <div class="vip-left">
            <p class="vip-title" style="font-weight: bold;font-size: 0.68rem;">VIP{{ item['viplevel'] }}</p>
            <div>


              <p class="vip-sub-title">{{ $t('VIP.NEEDCHARGE') }}
                <i>{{ index >= $store.state.account.vip ? res.nowrecharge : item['needmoney'] | formatGold }}</i>&nbsp;/&nbsp;<i style="color: #D72323;">{{ item['needmoney'] | formatGold }}</i>
              </p>
              <van-progress style="width: 100%;margin: 0.2rem auto;" :percentage="percentage(item['needmoney'])" stroke-width="6" text-color="white" :color="color[index]" :track-color="trackColor[index]"/>
            </div>
          </div>
          <div class="vip-right">
            <img :src="'/img/tdtc/vip/vip_' + item['viplevel'] + '.png'" width="80" alt="">
            <div class="vip-btn" @click="$router.push('/m/voucherCenter')">{{ $t('VIP.BTN_CHARGE') }}</div>
          </div>
        </swiper-slide>

      </swiper>
      <div style="color: white; font-size: .23rem; width: 100%;text-align: center; margin-top: .15rem;">
        {{ $t('label_proportion_tip1') }}: {{ $t('NEWHALL.TO_BONUS') }}
      </div>
<!--      <div>
        <div class="pao-header">{{ $t('VIP.EXCLUSIVE_PAO') }}</div>
        <div class="pao-content">
          <div style="display: flex; align-items: center">
            <img :src="'/img/tdtc/vip/cannon_gun_' + index + '.png'" width="60" alt="">
            <div style="margin-left: .3rem;
font-size: 0.26rem;
color: #2F0C0C;">{{ $t('VIP.TITLE_AWARDS_UP') }}
            </div>
          </div>

          <div class="vip-btn">{{ $t('VIP.EXCLUSIVE_SIMPLE') }}</div>

        </div>
      </div>-->


    </div>


    <ul v-if="res.conf.length">
      <li>
        <div>
          <img src="/img/tdtc/vip/li_1.png" width="38" alt="">
          <div>{{ $t('VIP.TITLE_AWARDS_UP') }}</div>
        </div>
        <div>
          <span>{{ btns_list[index][0]['lab_num']}}</span>
          <div class="li-btn" @click="btns_list[index][0]['btn_check'] && award(res.conf[index]['viplevel'])" :class="{'sb-disable': !btns_list[index][0]['btn_check'], 'bg_none': !btns_list[index][0]['active']}">{{ btns_list[index][0]['lab_checked']}}</div>
        </div>
      </li>
      <li>
        <div>
          <img src="/img/tdtc/vip/li_2.png" width="38" alt="">
          <div>{{ $t('VIP.TITLE_AWARDS_WEEK') }}</div>
        </div>
        <div>
          <span>{{ btns_list[index][1]['lab_num']}}</span>
          <div class="li-btn" @click="btns_list[index][1]['btn_check'] && wage(0)" :class="{'sb-disable': !btns_list[index][1]['btn_check'], 'bg_none': !btns_list[index][1]['active']}">{{ btns_list[index][1]['lab_checked']}}</div>
        </div>
      </li>
      <li>
        <div>
          <img src="/img/tdtc/vip/li_3.png" width="38" alt="">
          <div>{{ $t('VIP.TITLE_AWARDS_MONTH') }}</div>
        </div>
        <div>
          <span>{{ btns_list[index][2]['lab_num']}}</span>
          <div class="li-btn" @click="btns_list[index][2]['btn_check'] && wage(1)" :class="{'sb-disable': !btns_list[index][2]['btn_check'], 'bg_none': !btns_list[index][2]['active']}">{{ btns_list[index][2]['lab_checked']}}</div>
        </div>
      </li>
      <li>
        <div>
          <img src="/img/tdtc/vip/li_4.png" width="38" alt="">
          <div>{{ $t('VIP.TITLE_AWARDS_RETROACTIVE') }}</div>
        </div>
        <div>
          <span>{{ btns_list[index][3]['lab_num']}}</span>
          <div class="li-btn" :class="{'sb-disable': !btns_list[index][3]['btn_check'], 'bg_none': !btns_list[index][3]['active']}">{{ btns_list[index][3]['lab_checked']}}</div>
        </div>
      </li>
    </ul>
  </div>
</template>
<style scoped lang="scss">
.bg_none {
  background: none !important;
}
.td-vip {


  .vip-header {
    width: 100%;
    height: 6.97rem;
  }


  .swiper-slide {
    width: 6.77rem;
    height: 2.69rem;
    border-radius: 0.3rem;
    transition: 300ms;
    transform: perspective(1px) scale(1);

    display: flex;
    padding: 0.1rem .3rem;

    font-weight: 600;

    > div {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }

    .vip-left {
      width: 60%;
    }

    .vip-right {
      width: 40%;
      align-items: center;
    }
  }

  .swiper-slide-prev, .swiper-slide-next {
    transform: scale(0.93);
  }


  .vip-btn {
    width: 1.5rem;
    height: 0.42rem;
    border-radius: 0.21rem;
    line-height: 0.46rem;
    text-align: center;
    font-size: 0.23rem;
    box-shadow: 0 0 0 0 rgba(167, 125, 76, 0.24);
  }

  .vip-sub-title {
    font-size: 0.26rem;
  }
}

.td-vip {
  .vip-btn {
    box-shadow: 0 0 0 0 rgba(167, 125, 76, 0.24);
  }

  &0 {
    .van-progress {
      .van-progress__portion {

      }
    }
    .vip-header {
      background: linear-gradient(180deg, rgba(233, 182, 135, 1), rgba(197, 130, 85, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(255, 252, 249, 1), rgba(250, 224, 211, 1));
    }

    .vip-title {
      color: #B96634;
    }

    .vip-sub-title {
      color: #874920;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(250, 236, 233, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(250, 236, 233, 1));
      color: #7B4F34;
    }

  }

  &1 {
    .vip-header {
      background: linear-gradient(180deg, rgba(135, 206, 233, 1), rgba(85, 156, 197, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(255, 252, 249, 1), rgba(207, 240, 255, 1));
    }

    .vip-title {
      color: #4186AD;
    }

    .vip-sub-title {
      color: #2B6585;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(236, 249, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(236, 249, 255, 1));
      color: #305F79;
    }
  }

  &2 {
    .vip-header {
      background: linear-gradient(180deg, rgba(255, 181, 108, 1), rgba(197, 100, 53, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(255, 251, 230, 1), rgba(255, 223, 160, 1));
    }

    .vip-title {
      color: #AC4414;
    }

    .vip-sub-title {
      color: #612F18;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(255, 248, 218, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(255, 248, 218, 1));
      color: #784F2F;
    }
  }

  &3 {
    .vip-header {
      background: linear-gradient(180deg, rgba(108, 191, 255, 1), rgba(53, 144, 197, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(230, 254, 255, 1), rgba(160, 243, 255, 1));
    }

    .vip-title {
      color: #174B81;
    }

    .vip-sub-title {
      color: #174B81;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(214, 254, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(214, 254, 255, 1));
      color: #2D547A;
    }
  }

  &4 {
    .vip-header {
      background: linear-gradient(180deg, rgba(112, 104, 211, 1), rgba(134, 59, 204, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(252, 244, 255, 1), rgba(209, 160, 255, 1));
    }

    .vip-title {
      color: #231B80;
    }

    .vip-sub-title {
      color: #231B80;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(235, 212, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(235, 212, 255, 1));
      color: #2C248C;
    }
  }

  &5 {
    .vip-header {
      background: linear-gradient(180deg, rgba(238, 86, 86, 1), rgba(210, 43, 43, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(252, 244, 255, 1), rgba(255, 160, 160, 1));
    }

    .vip-title {
      color: #9B1627;
    }

    .vip-sub-title {
      color: #460701;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(255, 223, 227, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(255, 223, 227, 1));
      color: #7E1E15;
    }
  }

  &6 {
    .vip-header {
      background: linear-gradient(180deg, rgba(46, 196, 148, 1), rgba(27, 147, 177, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(244, 255, 254, 1), rgba(160, 255, 238, 1));
    }

    .vip-title {
      color: #095452;
    }

    .vip-sub-title {
      color: #022221;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(203, 255, 246, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(203, 255, 246, 1));
      color: #0D5C5C;
    }
  }

  &7 {
    .vip-header {
      background: linear-gradient(180deg, rgba(84, 92, 255, 1), rgba(142, 51, 245, 1));
    }

    .swiper-slide {
      background: linear-gradient(90deg, rgba(254, 244, 255, 1), rgba(212, 178, 255, 1));
    }

    .vip-title {
      color: #4826AA;
    }

    .vip-sub-title {
      color: #220044;
    }

    .vip-btn {
      background: linear-gradient(90deg, rgba(226, 201, 255, 1), rgba(255, 255, 255, 1), rgba(255, 255, 255, 1), rgba(226, 201, 255, 1));
      color: #501A86;
    }
  }


}

.pao-header {
  font-weight: 600;
  font-size: 0.26rem;
  text-align: center;
  color: #FCEBE2;
  margin: .15rem 0;
}

.pao-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  background: #FFFFFF;

  padding: 0 .2rem;
  font-weight: 600;

  margin: 0 auto;
  width: 6.77rem;
  height: 1.21rem;
  background: rgba(255, 241, 240, 0.1);
  box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.1);
  border-radius: 0.3rem;
}


ul {
  position: absolute;
  top: 4.3rem;
  width: 100%;
  background: #F9F9F9;
  border-top-left-radius: .2rem;
  border-top-right-radius: .2rem;
  padding: .24rem .14rem 0;

  font-weight: 600;
  font-size: 0.23rem;

  li {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 1.18rem;
    background: #FFFFFF;
    border-radius: 0.28rem;
    padding: 0 .2rem;
    margin-bottom: .2rem;

    > div {
      display: flex;
      flex-direction: row;
      align-items: center;

      > div {
        margin: 0 .2rem;
      }
    }

    span {
      font-size: 0.32rem;
      color: #495064;
    }

    img + div {
      color: #874920;
    }

    .li-btn {
      width: 3rem;
      height: 0.48rem;
      line-height: .48rem;
      text-align: center;
      color: #FFFFFF;
      background: linear-gradient(-67deg, rgba(175, 106, 218, 1), rgba(137, 194, 240, 1));
      border-radius: 0.1rem;
    }
  }
}
</style>
