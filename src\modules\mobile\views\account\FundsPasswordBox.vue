<script>

import {paymentPwd} from "@/mixins/paymentPwd";

export default {
  mixins: [paymentPwd],
};
</script>
<template>
  <div class="funds-password">
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-FundsPasswordBox am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">{{ $t('label_pwd_pay') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <form>
      <div class="am-whitespace am-whitespace-lg white-space"></div>
      <div class="flex-container">
        <div class="account-form no-mgbtm">
          <div class="account-form-group">
            <div class="mc-account-list">
              <svg class="am-icon am-icon-passwordBetter_0bd7a252 am-icon-md txt-svg">
                <use xlink:href="#passwordBetter_0bd7a252"></use>
              </svg>
              <input
                  name="password"
                  autocomplete="off"
                  :placeholder="$t('set_new_pwd')"
                  :type="[showPassword ? 'text' : 'password']"
                  v-validate="{ required: true }"
                  v-model.trim="user.password"
                  ref="password"
                   class="input-base" style="transition: all 0.15s linear 0s;">
            </div>
            <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" :class="showPassword ? 'open-eye' : 'close-eye'" @click="showPassword=!showPassword">
              <use xlink:href="#eyes_27cd1e8b"></use>
            </svg>
          </div>
          <div class="am-flexbox am-flexbox-align-middle errors"><div class="am-flexbox-item"><span>{{ errors.first("password") }}</span></div></div>
        </div>
        <div class="account-form no-mgbtm">
          <div class="account-form-group">
            <div class="mc-account-list">
              <svg class="am-icon am-icon-passwordAgain_4b9460bf am-icon-md txt-svg">
                <use xlink:href="#passwordAgain_4b9460bf"></use>
              </svg>
              <input
                  name="confirmPassword"
                  autocomplete="off"
                  :placeholder="$t('confirm_pwd')"
                  :type="[showConfirmPassword ? 'text' : 'password']"
                  v-validate="'required|confirmed:password'"
                  v-model.trim="user.confirmPassword"
                  class="input-base" style="transition: all 0.15s linear 0s;">
            </div>
            <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" :class="showConfirmPassword ? 'open-eye' : 'close-eye'" @click="showConfirmPassword=!showConfirmPassword">
              <use xlink:href="#eyes_27cd1e8b"></use>
            </svg>
          </div>
          <div class="am-flexbox am-flexbox-align-middle errors"><div class="am-flexbox-item"><span>{{ errors.first("confirmPassword") }}</span></div></div>
        </div>
<!--        <p class="txt-gray">&lt;!&ndash; react-text: 4760 &ndash;&gt;* &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 4761 &ndash;&gt;Insira 6 - 16 caracteres alfanuméricos.&nbsp;não diferencia maiúsculas de minúsculas.&nbsp;(caracteres chineses não permitidos)&lt;!&ndash; /react-text &ndash;&gt;</p>-->
<!--        <div class="am-whitespace am-whitespace-lg new-space-bet"></div>-->
<!--        <p class="txt-gray red" style="color: red;"></p>-->
<!--        <div class="am-whitespace am-whitespace-lg new-space-bet"></div>-->
        <a role="button" class="btn-success enterButton  am-button" @click="submitTransactionpasswd"><span>{{ $t('button_submit') }}</span></a></div>
    </form>
  </div>
</template>

<style scoped>
.errors {
  min-height: unset;
}
</style>