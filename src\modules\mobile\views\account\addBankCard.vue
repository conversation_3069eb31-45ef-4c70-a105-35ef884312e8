<template>
  <div class="PT addBank_root">
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-addBankCard am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">Adicionar carteira</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="account-form"><br>
      <div class="account-form-group">
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcPayeeName_d3e50d87 am-icon-md txt-svg">
            <use xlink:href="#mcPayeeName_d3e50d87"></use>
          </svg>
          <input type="text" class="input-base" placeholder="＊ Por favor, insira o nome completo do beneficiário" name="payee" style="transition: all 0.15s linear 0s;"><span class="tips">Certifique-se de que o nome corresponda às informações de retirada para evitar falhas.</span>
        </div>
        <div class="error "></div>
      </div>
    </div>
    <div class="flex-container account-form bank-card-container">
      <div class="choose-bank-type">
        <div class="am-flexbox am-flexbox-align-middle flex-money-contentbank-select s">
          <div class="am-flexbox-item">
            <div class="">
              <div class="inputCon">
                <div class="inputSelect">
                  <div type="text" class="inputBase" placeholder="＊ Tipo de carteira" disabled="">＊ Tipo de carteira</div>
                </div>
                <div class="inputIcon icon-down">
                  <svg class="am-icon am-icon-downArrows_cff85593 am-icon-md">
                    <use xlink:href="#downArrows_cff85593"></use>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="account-form-group">
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcBankCard_b967cbbc am-icon-md txt-svg">
            <use xlink:href="#mcBankCard_b967cbbc"></use>
          </svg>
          <input type="text" class="input-base" placeholder="＊ Endereço da carteira" name="walletAddress" maxlength="100">
        </div>
      </div>
      <div class="error"></div>
      <div class="account-form-group">
        <div class="tip">Por favor, configure sua senha de pagamento.</div>
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcPwd_c9953ab8 am-icon-md txt-svg">
            <use xlink:href="#mcPwd_c9953ab8"></use>
          </svg>
          <input type="password" class="input-base" placeholder="＊ Definir senha de pagamento" name="withdraw" style="transition: all 0.15s linear 0s;">
          <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" id="payment" style="color: rgb(165, 165, 165);">
            <use xlink:href="#eyes_27cd1e8b"></use>
          </svg>
        </div>
        <div class="error "></div>
      </div>
      <div class="account-form-group">
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcPwd_c9953ab8 am-icon-md txt-svg">
            <use xlink:href="#mcPwd_c9953ab8"></use>
          </svg>
          <input type="password" class="input-base" placeholder="＊ Confirme a Senha" name="withdrawT" style="transition: all 0.15s linear 0s;">
        </div>
        <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" id="validate" style="color: rgb(165, 165, 165);">
          <use xlink:href="#eyes_27cd1e8b"></use>
        </svg>
        <div class="error "></div>
      </div>
      <span class="am-button  btn-success">Enviar</span></div>
  </div>
</template>