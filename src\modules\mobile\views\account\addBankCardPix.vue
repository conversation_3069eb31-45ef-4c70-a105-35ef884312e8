<template>
  <div class="PT addBank_root">
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-addBankCardPix am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">{{ $t('bind_ewallet') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="account-form"><br>
      <div class="account-form-group">
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcPayeeName_d3e50d87 am-icon-md txt-svg">
            <use xlink:href="#mcPayeeName_d3e50d87"></use>
          </svg>
          <input type="text" v-validate="{ required: true, max: 255 }"
              name="payeeNames" v-model.trim="form.cardName" class="input-base" :placeholder="'＊ ' + $t('enter_real_name')" style="transition: all 0.15s linear 0s;">
        </div>
        <div class="error">{{ errors.first("payeeNames") }}</div>
        <span class="tips">{{ $t('bind_card_name_tips') }}</span>
      </div>
    </div>
    <form class="flex-container account-form bank-card-container">
      <div class="banks-wrapper">
        <div class="bank-title">{{ $t('label_select') }}</div>
        <div class="bank-list-wrap">
          <div class="bank-list">
            <div class="bank-item"
                v-for="(item, index) in withdrawalPlatforms" :key="index"
                :class="{'bank-active': index === withdrawalPlatIndex}"
                v-show="item.platformType === withdrawalTypes[withdrawalTypeIndex].platformType"
                @click="withdrawalPlatIndex = index"
            >
              <div class=" bank-icon"></div>
              <div class="bank-text">{{ item.platformName }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="banks-wrapper" v-if="withdrawalPlatforms[withdrawalPlatIndex] && withdrawalPlatforms[withdrawalPlatIndex].banks.length">
        <div class="bank-title">{{ $t('label_bank_select') }}</div>
        <div class="bank-list-wrap">
          <div class="bank-list">
            <div class="bank-item" @click="bankIndex = index"
                v-for="(item, index) in withdrawalPlatforms[withdrawalPlatIndex].banks" :key="index"
                :class="{'bank-active': index === bankIndex}"
            >
              <div class=" bank-icon"></div>
              <div class="bank-text">{{ item.bankName }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="error"></div>
      <div></div>
      <div v-if="!withdrawalPlatIndex" class="account-form-group">

        <div class="mc-account-list" v-if="withdrawalPlatforms[withdrawalPlatIndex] && 'banks' in withdrawalPlatforms[withdrawalPlatIndex]">
          <svg class="am-icon am-icon-mcUser_f24280fd am-icon-md txt-svg">
            <use xlink:href="#mcUser_f24280fd"></use>
          </svg>
          <input type="text" :placeholder="'＊ ' + $t('enter_bank_card')" class="input-base" v-if="withdrawalPlatforms[withdrawalPlatIndex].banks[bankIndex].bankCode === 'BR-CPF'"
              v-validate="{ required: true, regex: /^[0-9]{11}$/ }"
              v-model.trim="form.cardAddress" name="bankNumber"
              style="transition: all 0.15s linear 0s;">
          <template v-else-if="withdrawalPlatforms[withdrawalPlatIndex].banks[bankIndex].bankCode === 'BR-PHONE'">
            <span style="position: absolute;top: 0.27rem;left: 3em;z-index: 999">
              {{ this.$store.state.phonePreShow }}
            </span>
            <input type="text" :placeholder="'＊ ' + $t('enter_bank_card')" class="input-base"
                v-validate="{ required: true, regex: /^[0-9]{9}$/ }"
                v-model.trim="form.cardAddress" name="bankNumber"
                style="transition: all 0.15s linear 0s;padding-left: 5em;">
          </template>
          <input type="text" :placeholder="'＊ ' + $t('enter_bank_card')" class="input-base" v-else-if="withdrawalPlatforms[withdrawalPlatIndex].banks[bankIndex].bankCode === 'BR-CNPJ'"
              v-validate="{ required: true, regex: /^[0-9]{14}$/ }"
              v-model.trim="form.cardAddress" name="bankNumber"
              style="transition: all 0.15s linear 0s;">
          <input type="text" :placeholder="'＊ ' + $t('enter_bank_card')" class="input-base" v-else-if="withdrawalPlatforms[withdrawalPlatIndex].banks[bankIndex].bankCode === 'BR-EMAIL'"
              v-validate="{ required: true, email: true }"
              v-model.trim="form.cardAddress" name="bankNumber"
              style="transition: all 0.15s linear 0s;">
          <input type="text" :placeholder="'＊ ' + $t('enter_bank_card')" class="input-base" v-else
              v-validate="{ required: true, max: 255 }"
              v-model.trim="form.cardAddress" name="bankNumber"
              style="transition: all 0.15s linear 0s;">
          <div class="error">{{ errors.first("bankNumber") }}</div>
        </div>
      </div>
      <div v-else class="account-form-group">
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcUser_f24280fd am-icon-md txt-svg">
            <use xlink:href="#mcUser_f24280fd"></use>
          </svg>
          <input type="text" :placeholder="'＊ ' + $t('enter_bank_card')" class="input-base"
              v-validate="{ required: true, max: 255 }"
              v-model.trim="form.cardAddress" name="bankNumber"
              style="transition: all 0.15s linear 0s;">
          <div class="error">{{ errors.first("bankNumber") }}</div>
        </div>
      </div>
      <div class="account-form-group">
        <div class="tip">{{ $t('payment_password') }}</div>
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcPwd_c9953ab8 am-icon-md txt-svg">
            <use xlink:href="#mcPwd_c9953ab8"></use>
          </svg>
          <input :type="[showPassword ? 'text' : 'password']" class="input-base" :placeholder="'＊ ' + $t('set_pay_pwd')"
              name="password" v-model.trim="form.transactionPasswd"
              autocomplete="false"
              v-validate="{ required: true }"
              style="transition: all 0.15s linear 0s;">
          <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" id="payment" :class="showPassword ? 'open-eye' : 'close-eye'" @click="showPassword=!showPassword">
            <use xlink:href="#eyes_27cd1e8b"></use>
          </svg>
        </div>
        <div class="error">{{ errors.first("password") }}</div>
      </div>
      <div class="account-form-group" v-if="!transactionPasswd">
        <div class="mc-account-list">
          <svg class="am-icon am-icon-mcPwd_c9953ab8 am-icon-md txt-svg">
            <use xlink:href="#mcPwd_c9953ab8"></use>
          </svg>
          <input :type="[showConfirmPassword ? 'text' : 'password']" class="input-base" :placeholder="'＊ ' + $t('label_pwd_confirm')"
              name="confirmPassword" v-model.trim="form.confirmTransactionPasswd"
              autocomplete="false"
              v-validate="{ required: true }"
              style="transition: all 0.15s linear 0s;">
        </div>
        <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" :class="showConfirmPassword ? 'open-eye' : 'close-eye'" @click="showConfirmPassword=!showConfirmPassword" id="validate">
          <use xlink:href="#eyes_27cd1e8b"></use>
        </svg>
        <div class="error">{{ errors.first("confirmPassword") }}</div>
      </div>
      <span class="am-button  btn-success" @click="validAddWallet">{{ $t('submit') }}</span>
      <div class="brl-tip-info">
        <div class="tip-item">{{ $t('brl_tip_0') }}</div>
        <div class="tip-item">{{ $t('brl_tip_1') }}</div>
        <div class="tip-item">{{ $t('brl_tip_2') }}</div>
        <div class="tip-item">{{ $t('brl_tip_3') }}</div>
        <div class="tip-item">{{ $t('brl_tip_4') }}</div>
        <div class="tip-item">{{ $t('brl_tip_5') }}</div>
      </div>
    </form>
  </div>
</template>
<script>
import {withdraw} from "@/mixins/withdraw";
export default {
  mixins:[withdraw]
};
</script>

<style scoped>

</style>