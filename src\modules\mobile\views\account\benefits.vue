<script>
export default {
  name: "benefits",
};
</script>

<template>
  <div
      class="member-benefits customize"
      data-color="rgba(179,188,200,1)"
      id="controlColor"
  >
    <canvas class="top-bg" style="    background-color: #c0c9d4;"></canvas>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-benefits am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">Benefícios de classificação</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="member-level-info">
      <div class="cur-level-text">VIP0</div>
      <div class="level-up-bar">
        <div class="next-condition">0 / 1</div>
        <div class="level-up-count">
          <div class="level-up-progress" style="width: 0%"></div>
        </div>
      </div>
      <div class="level-icon">
        <img alt="" src="mobile/mc/lv3.d0257f97.png"/>
      </div>
      <div class="check-level-info">
        <router-link class="check-rule-item" to="/m/member/TermsConditions?k=1">
          <div class="">
            <svg
                class="am-icon am-icon-04.rule_80ed54e0 am-icon-md"
                style="color: rgb(179, 188, 200)"
            >
              <use xlink:href="#04.rule_80ed54e0"></use>
            </svg>
          </div>
          <!-- react-text: 2281 -->Regras<!-- /react-text --></router-link
        >
        <div class="check-rule-item false">
          <div>
            <svg
                class="am-icon am-icon-01.king_37c0e7f7 am-icon-md"
                style="color: rgb(179, 188, 200)"
            >
              <use xlink:href="#01.king_37c0e7f7"></use>
            </svg>
          </div>
          <!-- react-text: 2286 -->Próxima Classificação<!-- /react-text -->
        </div>
      </div>
    </div>
    <div class="hide member-level-condition save-level">
      <div class="condition-title" style="color: rgb(179, 188, 200)">
        Cumpra qualquer um para manter a classificação
      </div>
      <div class="condition-item hide false done-task">
        <div class="task-icon">
          <div
              class="bg-radius"
              style="background-color: rgb(179, 188, 200)"
          ></div>
          <svg
              class="am-icon am-icon-07.money-bag_35e02c2c am-icon-md"
              style="color: rgb(179, 188, 200)"
          >
            <use xlink:href="#07.money-bag_35e02c2c"></use>
          </svg>
        </div>
        <div class="task-text">Aposta Mensal</div>
        <div class="task-view-info">R$0.00/-</div>
        <div class="task-progress-bar">
          <div
              class="progress-animate"
              style="background-color: rgb(179, 188, 200); width: 100%"
          ></div>
          <div
              class="progress-count opacity-item"
              style="background-color: rgb(179, 188, 200)"
          ></div>
          <div class="progress-number" style="color: rgb(179, 188, 200)">
            100%
          </div>
        </div>
        <div
            class="btn-task-status"
            style="
            box-shadow: rgb(179, 188, 200) 0px 0px 0.18rem;
            background-color: rgb(179, 188, 200);
          "
        >
          Completo
        </div>
      </div>
      <div class="condition-item hide false done-task">
        <div class="task-icon">
          <div
              class="bg-radius"
              style="background-color: rgb(179, 188, 200)"
          ></div>
          <svg
              class="am-icon am-icon-08.coupon_2ac216dd am-icon-md"
              style="color: rgb(179, 188, 200)"
          >
            <use xlink:href="#08.coupon_2ac216dd"></use>
          </svg>
        </div>
        <div class="task-text">Depósito Mensal</div>
        <div class="task-view-info">R$0.00/-</div>
        <div class="task-progress-bar">
          <div
              class="progress-animate"
              style="background-color: rgb(179, 188, 200); width: 100%"
          ></div>
          <div
              class="progress-count opacity-item"
              style="background-color: rgb(179, 188, 200)"
          ></div>
          <div class="progress-number" style="color: rgb(179, 188, 200)">
            100%
          </div>
        </div>
        <div
            class="btn-task-status"
            style="
            box-shadow: rgb(179, 188, 200) 0px 0px 0.18rem;
            background-color: rgb(179, 188, 200);
          "
        >
          Completo
        </div>
      </div>
    </div>
    <div class="member-level-condition">
      <div class="condition-title" style="color: rgb(179, 188, 200)">
        Requisitos para subir de nível
      </div>
      <div class="level-up-brief">
        <div class="level-up-need">
          <span>Melhoria</span
          ><span class="task-member-need"
        >Complete qualquer um para subir de nível</span
        >
        </div>
      </div>
      <div class="condition-item false">
        <div class="task-icon">
          <div
              class="bg-radius"
              style="background-color: rgb(179, 188, 200)"
          ></div>
          <svg
              class="am-icon am-icon-08.coupon_2ac216dd am-icon-md"
              style="color: rgb(179, 188, 200)"
          >
            <use xlink:href="#08.coupon_2ac216dd"></use>
          </svg>
        </div>
        <div class="task-text">Aposta Total</div>
        <div class="task-view-info">R$0.00/30,000.00</div>
        <div class="task-progress-bar">
          <div
              class="progress-animate"
              style="background-color: rgb(179, 188, 200); width: 0%"
          ></div>
          <div
              class="progress-count opacity-item"
              style="background-color: rgb(179, 188, 200)"
          ></div>
          <div class="progress-number" style="color: rgb(179, 188, 200)">
            0%
          </div>
        </div>
        <div
            class="btn-task-status"
            style="
            box-shadow: rgb(179, 188, 200) 0px 0px 0.18rem;
            background-color: rgb(179, 188, 200);
          "
        >
          <router-link to="/m/home">IR</router-link>
        </div>
      </div>
    </div>
    <svg
        aria-hidden="true"
        focusable="false"
        style="width: 0px; height: 0px; position: absolute"
    >
      <linearGradient id="benefit-gradient" x2="1" y2="1">
        <stop offset="0%" stop-color="rgba(241,249,255,1)"></stop>
        <stop offset="100%" stop-color="rgba(179,188,200,1)"></stop>
      </linearGradient>
    </svg>
  </div>
</template>

<style scoped></style>