<script>
import {bindGA} from "@/mixins/bindGA";

export default {
  mixins:[ bindGA]
};
</script>
<template>
  <div class="ga-auth">
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-bindGA am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">{{ $t('ga_ver_code') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="am-whitespace am-whitespace-lg white-space"></div>
    <div class="flex-container">
      <div class="am-whitespace am-whitespace-lg white-space"></div>
      <div>
        <VueQr
            :logoMargin="2"
            :text="authUrl"
            :size="200"
            :margin="5"
        />
        <div class="copy-key">
          <span class="ms-code">{{ $t('secret_key') }}: {{ Details.oauthSecret }}</span><span class="btn-copy-code copy-btn" :data-clipboard-text="Details.oauthSecret">{{ $t('label_copy') }}</span>
        </div>
      </div><!-- react-text: 4447 --> <!-- /react-text -->
      <div class="account-form no-mgbtm">
        <div class="account-form-group">
          <div class="mc-account-list">
            <svg class="am-icon am-icon-mcGA_0a8ad9ea am-icon-md txt-svg">
              <use xlink:href="#mcGA_0a8ad9ea"></use>
            </svg>
            <input type="number" class="input-base" name="code" v-validate="{ digits:6, required: true }" v-model.trim="code">
          </div><!-- react-text: 4454 --><!-- /react-text --></div>
      </div>
      <p class="txt-gray" style="color: red" v-if="errors.first('code')">*{{$t('ga_text')}}</p>
      <div class="submit" @click="showActionCaptcha('bindGaHandler')">
        <a role="button" class="btn-success enterButton am-button" aria-disabled="false"><span>{{$t('submit')}}</span></a></div>
      <div class="show-tips prompt" style="margin-top: 180px; width: 100%; text-align: center;">
        <!-- react-text: 4462 -->{{$t('google_auth_none')}}<!-- /react-text --><!-- react-text: 4463 --> ?<!-- /react-text -->
        <a @click="openDownUrl" href="#" style="line-height: 40px; height: 40px; color: rgb(59, 121, 243); width: 100px; text-decoration: underline;">{{$t('download_app')}}</a>
      </div>
    </div>
  </div>
</template>