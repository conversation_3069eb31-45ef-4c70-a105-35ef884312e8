<script>
import {play} from "@/mixins/play";
import {check} from "@/mixins/check";
import {TDTC_ROURE} from "@/api/tdtc";
import CountTo from 'vue-count-to'
import Avatar from '@/modules/mobile/components/popup/avatar.vue'

export default {
  name: "home",
  components: {
    Avatar,
    CountTo},
  mixins: [play, check],
  data() {
    return {
      showService: false,
      showEditNickname: false,
      nickname: "",
      percentage: 0,
      form: this.$form.createForm(this),
      QueryGrowLevelResponse: {
        growlevel: 0,
        experience: 0,
        upgrade_experience: 0,
      }
    };
  },
  beforeCreate() {
    document.getElementById('css_forgot_ant').disabled = false;
  },
  beforeDestroy() {
    document.getElementById('css_forgot_ant').disabled = true;
  },
  mounted() {
    this.leaveThirdGame()
    this.queryGrowLevel()
  },
  methods: {
    percentageTo(start, end) {
      if (end < 0.01) return
      let rate = 30;
      let time = 1000;
      let step = end / (time / rate);
      let count = Number(start);
      let timer = setInterval(() => {
        count = count + step;
        this.percentage = count.toFixed(0)

        if (count > end || !end) {
          count = end;
          this.percentage = count
          clearInterval(timer);
          timer = null;
        }
      }, rate);
    },
    queryGrowLevel() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_GROW_LEVEL)
          .then((res) => {
            Object.assign(this.QueryGrowLevelResponse, res)
            this.percentageTo(0, this.QueryGrowLevelResponse.experience / this.QueryGrowLevelResponse.upgrade_experience * 100)
          })
          .catch(() => {
          })
    },
    handleSubmit(e) {
      e.preventDefault();
      this.form.validateFields((err, values) => {
        if (!err) {
          this.$tdtcApi.getQueryInfo(TDTC_ROURE.HALL_MODIFY_NAME, {
            nickname: values.nickname
          })
              .then((res) => {
                if (res['code'] === 200) {
                  this.$store.commit("setNickname", values.nickname);
                  $toast.success({
                    icon: "passed",
                    message: this.$t("label_success"),
                  });
                } else {
                  window.$toast.fail(this.$t('1040'));
                }
              })
              .catch(() => {
              })
              .finally(() => {
                this.showEditNickname = false;
                this.form.resetFields()
              })
        }
      });
    },
  }
};
</script>

<template>
  <div id="mc-app-home-root" class="mc dark" style="height: 100vh;background-color: rgb(246, 242, 229)">
    <div id="page_bg" class="common"></div>
    <avatar />
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-home am-navbar am-navbar-light" style="position: fixed !important;width: 100%; z-index: 1;
          background: linear-gradient(180deg, rgba(34, 34, 34, 1), rgba(16, 16, 16, 1)) !important;

    top: 0;"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content">
            <span class="return_icon">
              <svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg>
            </span>
          </span>
        </div>
        <div class="am-navbar-title" style="font-size: .4rem !important;font-weight: 600;">{{ $t('mine') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="n-home-container">
      <div class="service-modal-container" v-if="showService">
        <div class="smc-content">
          <div style="position: absolute; bottom: -1rem; left: 44%" @click="showService = false">
            <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
          </div>
          <h2>{{ $t('choose_service_link') }}</h2>
          <ul>
            <li @click="showService=false; goUrl($store.state.configs.customer_web)" v-if="$store.state.configs.customer_web">
              <span
                  style="color: #666"
              > <img src="mobile/mc/icon1.11e46ae7.png" alt=""/>
                <div>
                  {{ $t('in_customer_service') }}
                </div>
              </span>
            </li>
            <li @click="showService=false; goUrl($store.state.configs.customer_telegram, false)" v-if="$store.state.configs.customer_telegram">
              <span
                  style="color: #666"
              > <img src="mobile/mc/icon2.1116b59b.png" alt=""/>
                <div>
                  <!-- react-text: 4838 -->
                  <!-- /react-text --><!-- react-text: 4839 -->TELEGRAM
                  <!-- /react-text -->
                </div>
              </span>
            </li>
          </ul>
        </div>
      </div>
      <div class="n-home-header" style="padding: 0.1rem 0.2rem; height: 3.3rem;">
        <div style="display: flex; flex-direction: row;margin-left: .3rem;margin-top: .1rem;">
<!--          <img data-bs-toggle="modal" data-bs-target="#avatar" style="width: 1.12rem;height: 1.12rem;border-radius: 50%;" :src="`img/profile/icon_${$store.state.account.icon}.png`" alt=""/>-->

          <div data-bs-toggle="modal" data-bs-target="#avatar" style="display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center">
            <img style="width: 0.98rem;
height: 0.98rem;
background: #EFF6FF;
border-radius: 50%;
border: 0.03px solid #8AC5D2;" :src="`img/profile/icon_${$store.state.account.icon}.png`">
            <div style="background-image: url('/img/tdtc/rank/0.png');  background-size: cover;height: .3rem;width: 1.2rem;margin-top: -.2rem;
      padding-top: .01rem;
      text-align: center;
      font-weight: bold;
      font-size: 0.2rem;
      color: #FFFFFF;">
              VIP {{this.$store.state.account.vip}}
            </div>
          </div>
          <div style="font-size: 0.26rem;
color: #FFF7E9;width: 2.61rem;margin-left: .2rem;margin-top: .15rem;">
            <div style="display: flex;justify-content: space-between;align-items: end;margin-bottom: .06rem;">
                <span>
                  ID:
                </span>
              <div class="copy-account">
                <span>{{ this.$store.state.account.userId && this.$store.state.account.userId.toString() | hideStr }}</span>
                <svg style="margin-left: .07rem;width: .36rem;height: .36rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="$store.state.account.userId">
                  <use xlink:href="#icon-copy_8fbca030"></use>
                </svg>
              </div>
            </div>
            <div style="display: flex;justify-content: space-between;align-items: end" @click="showEditNickname = true">
                <span>
                  {{ $t('USERINFO.LAB_NAME') }}:
                </span>
              <div class="copy-account">
                <span>{{ this.$store.state.account.nickname && this.$store.state.account.nickname.toString() | hideStr }}</span>
                <van-icon name="edit" color="white" size=".36rem" style="margin-left: .08rem;"/>
                <van-popup get-container="#mc-app-home-root" v-model="showEditNickname" round style="width: 6.8rem; height: 3.6rem;font-size: .3rem;
                  color:#47404d;padding: .36rem .8rem 0;text-align: center;overflow: visible;
             ">
                  <div style="position: absolute; bottom: -1rem; left: 44%" @click="showEditNickname = false">
                    <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
                  </div>
                  <div>{{ $t('CHANGENICKNAME.LAYER_TITLE') }}</div>
                  <a-form :form="form" @submit="handleSubmit" style="margin: .5rem 0 0;">
                    <a-form-item>
                      <a-input style="height: .66rem"
                          :placeholder="$t('CHANGENICKNAME.TIP_1')"
                          v-decorator="['nickname', { rules: [{ required: true, message: $t('1038')},{pattern: /^[a-zA-Z0-9_]{4,20}$/, message: $t('NoticeNameLength')}] }]"
                      />
                    </a-form-item>

                    <a-form-item>
                      <van-button block round type="warning" html-type="submit" style="margin-top: .2rem;width: 100% !important;height: .66rem;border-radius: .08rem !important;">
                        {{ $t('ok') }}
                      </van-button>
                    </a-form-item>
                  </a-form>
                  <div style="font-size: .22rem;"><i style="color: red">* </i>{{ $t('CHANGENICKNAME.TIP_2') }}</div>
                </van-popup>
              </div>
            </div>
          </div>
        </div>
        <div style="width: 7rem;height: 2.02rem;margin-top: .2rem;margin-left: .06rem;">
          <div style="display:flex;justify-content: space-between; align-items: center;padding: .1rem .3rem 0;">
            <span class="number-mc" style="font-weight: bold;
font-size: 0.58rem;
color: #FCB541;">
              {{ balance }}
            </span>
            <div style="display: flex;text-align: end;">
              <span>VND</span>
              <svg style="width: .34rem;height: .34rem;margin-left: .1rem;"
                  class="am-icon am-icon-refresh_9b98ac99 am-icon-md false refresh-balance"
                  :class="{ rotateFull: refresh }"
                  @click="leaveThirdGame"
              >
                <use xlink:href="#icon-refresh"></use>
              </svg>
            </div>
          </div>
          <div class="common-center-top" style="padding-top: unset;margin: -.1rem;min-height: unset">
            <div class="common-member-level">
              <div class="" style="color: #d32f2f; display: flex;flex-direction: row; align-items: end;">
                <svg class="am-icon icon svg-icon" aria-hidden="true" style="fill: #FCB541;">
                  <use xlink:href="#icon-crown"></use>
                </svg>
                <div style="margin-left: .1rem;height: .36rem;font-size: .26rem;font-weight: 600;">Lv.{{ QueryGrowLevelResponse.growlevel }}</div>
              </div>
              <div class="quanyi-go" style="background: unset; padding-right: 0;font-weight: 600;">
                <div class="level-progress-text">
                  <i style="color: #d32f2f">
                    <count-to :start-val='0' :end-val="QueryGrowLevelResponse.experience" :duration='1000' :autoplay=true />
                   </i>
                  /
                  <i style="color: #d32f2f">{{ QueryGrowLevelResponse.upgrade_experience }}</i>
                </div>
              </div>
              <div class="member-progress-warp">
                <van-progress style="width: 100%;" :percentage="percentage" stroke-width="5" text-color="white" color="#d32f2f" track-color="#6666664d" :show-pivot="false"/>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="member-home-root" style="background: rgba(246, 242, 229, 1) !important;">
        <div class="mine">
          <router-link to="/m/voucherCenter" class="mine-btn" style="margin-right: .69rem;">
            <img src="/m/menu/deposit.svg" style="width: .38rem" alt="" />
            <div>{{$t('recharge')}}</div>
          </router-link>
          <router-link to="/m/withdraw" class="mine-btn">
            <img src="/m/menu/withdraw.svg"  style="width: .38rem" alt="" />
            <div>{{$t('withdrawal')}}</div>
          </router-link>
        </div>
        <div>
          <div class="n-nav-list-title"><span>{{ $t('myself') }}</span></div>
          <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="background: rgba(246, 242, 229, 1) !important">
<!--            <router-link
                to="/m/rewardCenter"
                class="REWCEN3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-AGETEAMPNL3_a62d89c9 am-icon-lg">
                    <use xlink:href="#AGETEAMPNL3_a62d89c9"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('in_reward_center') }}</span>
            </router-link>-->
            <router-link
                to="/m/rank"
                class="REWCEN3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-AGETEAMPNL3_a62d89c9 am-icon-lg">
                    <use xlink:href="#AGETEAMPNL3_a62d89c9"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('rank') }}</span>
            </router-link>

            <router-link
                to="/m/events"
                class="REWCEN3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-AGETEAMPNL3_a62d89c9 am-icon-lg">
                    <use xlink:href="#icon-activity"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('events') }}</span>
            </router-link>
            <router-link
                to="/m/vip"
                class="REWCEN3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-AGETEAMPNL3_a62d89c9 am-icon-lg">
                    <use xlink:href="#icon-vip"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('vip') }}</span>
            </router-link>

            <router-link
                to="/m/statement"
                class="TRANSBETREPMEM3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg
                      class="am-icon am-icon-TRANSBETREPMEM3_0d83fc3f am-icon-lg"
                  >
                    <use xlink:href="#TRANSBETREPMEM3_0d83fc3f"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('statement') }}</span>
            </router-link>
            <router-link
                to="/m/history"
                class="PERSPNL3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-PERSREP_9e354430 am-icon-lg">
                    <use xlink:href="#PERSREP_9e354430"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('label_history_log') }}</span>
            </router-link>
<!--            <router-link
                to="/m/gameRecord"
                class="TRANSBETREPMEM3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg
                      class="am-icon am-icon-TRANSBETREPMEM3_0d83fc3f am-icon-lg"
                  >
                    <use xlink:href="#TRANSBETREPMEM3_0d83fc3f"></use>
                  </svg>
                </div>
              </div>
              <br/>
              <span>{{ $t('in_betting_record') }}</span>
            </router-link>
            <router-link
                to="/m/profitandloss"
                class="PERSPNL3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-PERSREP_9e354430 am-icon-lg">
                    <use xlink:href="#PERSREP_9e354430"></use>
                  </svg>
                </div>
              </div>
              <br/>
              <span>{{ $t('label_profit_loss') }}</span>
            </router-link>-->
            <router-link
                to="/m/vouReport"
                class="DEPRECM3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg
                      class="am-icon am-icon-deposit_record_b99e1f01 am-icon-lg"
                  >
                    <use xlink:href="#deposit_record_b99e1f01"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('RECHARGEINFO4.TEXT_SUM1') }}</span>
            </router-link>
            <router-link
                to="/m/withdrawReport"
                class="WITHREC3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg
                      class="am-icon am-icon-withdraw_record_c986adfb am-icon-lg"
                  >
                    <use xlink:href="#withdraw_record_c986adfb"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('EXCHANGELOG.LAYER_TITLE') }}</span>
            </router-link>
            <router-link
                to="/m/events/6"
                class="TRANSREC3 main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-TRANSREC3_fcac856d am-icon-lg">
                    <use xlink:href="#icon-task"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('tasks') }}</span>
            </router-link>
            <!--            <router-link-->
            <!--                to="/m/myAccount/index"-->
            <!--                class="MYACC main-nav-style mcm-teampandect"-->
            <!--            >-->
            <!--              <div class="n-linear-border">-->
            <!--                <div>-->
            <!--                  <svg class="am-icon am-icon-MYACC_a7ef046a am-icon-lg">-->
            <!--                    <use xlink:href="#MYACC_a7ef046a"></use>-->
            <!--                  </svg>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              <br/>&lt;!&ndash; react-text: 108 &ndash;&gt;-->
            <!--              {{ $t('in_my_account') }}&lt;!&ndash; /react-text &ndash;&gt;</router-link-->
            <!--            >-->
            <router-link
                to="/m/securityCenter"
                class="SECPRIV main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-SECPRIV_3f9a598c am-icon-lg">
                    <use xlink:href="#SECPRIV_3f9a598c"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('security_center') }}</span>
            </router-link>
            <router-link
                to="/m/bonus"
                class="REFERRAL main-nav-style mcm-teampandect"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-REFERRAL_9586116f am-icon-lg">
                    <use xlink:href="#icon-bonus"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>Bonus</span>
            </router-link>
            <!--            <router-link-->
            <!--                to="/m/member/rebateReport"-->
            <!--                class="PLYRBT main-nav-style mcm-teampandect"-->
            <!--            >-->
            <!--              <div class="n-linear-border">-->
            <!--                <div>-->
            <!--                  <svg class="am-icon am-icon-PLYRBT_49870211 am-icon-lg">-->
            <!--                    <use xlink:href="#PLYRBT_49870211"></use>-->
            <!--                  </svg>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              <br/>&lt;!&ndash; react-text: 129 &ndash;&gt;Meu Desconto&lt;!&ndash; /react-text &ndash;&gt;</router-link-->
            <!--            >-->
            <!--            <router-link-->
            <!--                to="/m/webEmail"-->
            <!--                class="MAILCEN main-nav-style mcm-teampandect"-->
            <!--            >-->
            <!--              <div class="n-linear-border">-->
            <!--                <div>-->
            <!--                  <svg class="am-icon am-icon-MAILCEN_4962dbe3 am-icon-lg">-->
            <!--                    <use xlink:href="#MAILCEN_4962dbe3"></use>-->
            <!--                  </svg>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              <br/>&lt;!&ndash; react-text: 136 &ndash;&gt;Correspondência&lt;!&ndash; /react-text &ndash;&gt;</router-link-->
            <!--            >-->
            <!--            <router-link-->
            <!--                to="/m/member/manualRebate"-->
            <!--                class="MANPLAYREB3 main-nav-style mcm-teampandect"-->
            <!--            >-->
            <!--              <div class="n-linear-border">-->
            <!--                <div>-->
            <!--                  <svg class="am-icon am-icon-MANPLAYREB3_da78e50e am-icon-lg">-->
            <!--                    <use xlink:href="#MANPLAYREB3_da78e50e"></use>-->
            <!--                  </svg>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              <br/>&lt;!&ndash; react-text: 143 &ndash;&gt;Desconto manual&lt;!&ndash; /react-text &ndash;&gt;</router-link-->
            <!--            >-->
            <div
                class="OPENHOURSSERVE main-nav-style mcm-teampandect"
                @click="showService = true"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-open_hours_141ce365 am-icon-lg">
                    <use xlink:href="#open_hours_141ce365"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('in_customer_service') }}</span>
            </div>
            <div
                class="OPENHOURSSERVE main-nav-style mcm-teampandect"
                @click="logout"
            >
              <div class="n-linear-border">
                <div>
                  <svg class="am-icon am-icon-open_hours_141ce365 am-icon-lg">
                    <use xlink:href="#icon-switch-"></use>
                  </svg>
                </div>
              </div>
              <br/> <span>{{ $t('logout') }}</span>
            </div>
          </div>
          <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.mcm-teampandect span {
  padding : .1rem;
}

::v-deep .ant-form-item {
  margin-bottom : unset;
}

::v-deep .ant-form-explain, .ant-form-extra {
  font-size  : .23rem;
  margin-top : .05rem;
}


#root #mc-app-home-root.dark .mc-flex.mc-flex-between.mc-flex-row.main-nav-list > .main-nav-style.mcm-teampandect {
  background: #FFFFFF !important;
  border-radius: 0.2rem;
  border: 0.02px solid #CECDC9 !important;
  margin-bottom : .2rem;

}
#root #mc-app-home-root.dark .n-linear-border:before, .n-home-container .main-nav-style > .n-linear-border:before {
  background : unset !important;
}
#root #mc-app-home-root.dark .n-linear-border svg {
  fill: #989898 !important;
}
.v-base .main-nav-style {
  font-size: 0.26rem;
  color: #989898;
  width : 32%;
  height : 2.2rem;
  //line-height: 0.1rem;
}
#root #mc-app-home-root.dark .main-nav-style {
  //font-weight: 600;
  font-size: 0.23rem;
  color: #989898 !important;
}
.mc-flex {
  justify-content: space-between;
}

.n-home-container .main-nav-style > div {
  margin-bottom : 0 !important;
}
#mc-app-home-root.mc:after {
  background : rgb(246, 242, 229) !important;
}

.mine {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: .23rem 0;


  &-btn {
    width: 2.26rem;
    height: 0.58rem;
    background: linear-gradient(90deg, rgba(248, 176, 65, 1), rgba(173, 41, 20, 1));
    border-radius: 0.29rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    font-size: 0.26rem;
    color: #FFFFFF;
    > div {
      margin-left: .2rem;
    }
  }

}
</style>
