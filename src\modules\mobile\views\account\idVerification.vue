<template>
  <div class="vrification-container">
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">
          <div>CPF Validar</div>
        </div>
        <div class="am-navbar-right"></div>
      </div>
    </div>
    <div class="vc-content">
      <div class="vc-title">Carregue a sua imagem de identificação, certifique-se de que o texto está claro e se ajusta à imagem</div>
      <div class="vc-upload">
        <div class=" up-load-positive"><input type="file" id="file" class="inputfile" accept="image/*">
          <div class="camera"><span><img src="mobile/mc/camera.fad27902.png" alt=""></span>
            <p>Faça o upload na frente do seu documento de identidade</p></div>
        </div>
      </div>
      <div class="vc-upload">
        <div class=" up-load-negative"><input type="file" id="file1" class="inputfile" accept="image/*">
          <div class="camera"><span><img src="mobile/mc/camera.fad27902.png" alt=""></span>
            <p>Faça o upload do seu documento de identidade</p></div>
        </div>
      </div>
      <div class="ver-btn"><a role="button" class="btn-gray am-button" aria-disabled="false"><span>Enviar</span></a>
        <p>As informações serão usadas apenas para verificação de conta.</p></div>
    </div>
  </div>
</template>