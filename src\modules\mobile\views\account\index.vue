<script>
import moment from "moment";
import {social} from "@/mixins/social";

export default {
  mixins: [social],
  data() {
    return {
      show: false,
      minDate: new Date(1901, 1, 1),
      maxDate: new Date(),
      currentDate: new Date(),
    };
  },
  methods: {
    onChange() {
      this.info.birthday = moment(new Date(this.currentDate)).format('YYYY-MM-DD');
      this.show = false
    }
  },
};
</script>
<template>
  <div class="personalCenter">
    <div class="mc-header-wrap">
      <div
        id="mc-header"
        class="mc-navbar-blue mc-index am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
            ><span class="return_icon"
              ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('label_setting_data') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-action-sheet get-container=".personalCenter" v-model="show" :title="$t('enter_birthday')">
      <van-datetime-picker
          show-toolbar
          v-model="currentDate"
          type="date"
          :min-date="minDate"
          :max-date="maxDate"
          @confirm="onChange"
          @cancel="show=false"
      />
    </van-action-sheet>
    <van-form colon @submit="submit">
      <van-field
          :readonly="res.telegram_status === 2"
          input-align="right"
          v-model.trim="info.telegram"
          name="telegram"
          :placeholder="res.telegram ? $options.filters['hideStr'](res.telegram) : $t('enter_telegram')"
          :class="{'disabled-field': res.telegram_status === 2}"
          :style="res.telegram && res.telegram_status !== 2 ? {'--placeholder-color': '#333333'} : {}"
          label="Telegram"
          :rules="[{ validator: val => !val || val.length >= 3 }]"
      />
      <van-field
          :readonly="res.signal_status === 2"
          input-align="right"
          v-model.trim="info.signal"
          name="signal"
          :placeholder="res.signal ? $options.filters['hideStr'](res.signal) : $t('enter_signal')"
          :class="{'disabled-field': res.signal_status === 2}"
          :style="res.signal && res.signal_status !== 2 ? {'--placeholder-color': '#fff'} : {}"
          label="Signal"
          :rules="[{ validator: val => !val || val.length >= 3 }]"
      />
      <van-field
          :readonly="res.zalo_status === 2"
          input-align="right"
          v-model.trim="info.zalo"
          name="zalo"
          :placeholder="res.zalo ? $options.filters['hideStr'](res.zalo) : $t('enter_zalo')"
          :class="{'disabled-field': res.zalo_status === 2}"
          :style="res.zalo && res.zalo_status !== 2 ? {'--placeholder-color': '#333333'} : {}"
          label="Zalo"
          :rules="[{ validator: val => !val || val.length >= 3 }]"
      />
      <van-field
          :readonly="res.email_status === 2"
          input-align="right"
          v-model.trim="info.email"
          name="email"
          :placeholder="res.email ? $options.filters['hideStr'](res.email) : $t('enter_email')"
          :class="{'disabled-field': res.email_status === 2}"
          :style="res.email && res.email_status !== 2 ? {'--placeholder-color': '#333333'} : {}"
          label="E-Mail"
          :rules="[{ validator: val => !val || (val.length >= 3 && val.includes('@')) }]"
      />
      <van-field
          :readonly="res.whatsapp_status === 2"
          input-align="right"
          v-model.trim="info.whatsapp"
          name="whatsapp"
          :placeholder="res.whatsapp ? $options.filters['hideStr'](res.whatsapp) : $t('enter_whatsapp')"
          :class="{'disabled-field': res.whatsapp_status === 2}"
          :style="res.whatsapp && res.whatsapp_status !== 2 ? {'--placeholder-color': '#333333'} : {}"
          label="Whatsapp"
          :rules="[{ validator: val => !val || val.length >= 3 }]"
      />
      <van-field
          readonly input-align="right"
          @click="res.birthday_status !== 2 ? show=true : ''"
          :value="info.birthday"
          name="birthday"
          :placeholder="res.birthday ? $options.filters['hideStr'](res.birthday) : $t('enter_birthday')"
          :class="{'disabled-field': res.birthday_status === 2}"
          :style="res.birthday && res.birthday_status !== 2 ? {'--placeholder-color': '#333333'} : {}"
          label="Ngày sinh"
      />
      <div style="padding: 0 .2rem;">
        <van-button round block type="warning" native-type="submit">{{$t('button_submit')}}</van-button>
      </div>
    </van-form>
    <div class="withdraw-tip">{{ $t('account_tip_content') }}</div>
    <div>
      <div class="withdraw-bkinfo">
        <div>
          <span>Telegram:</span>
          <span :class="{
            'status-green': res.telegram_status === 2,
            'status-red': res.telegram_status === 3
          }">{{ $t("bindSocialInfoStatus_" + res.telegram_status) }}</span>
        </div>
        <div>
          <span>Signal:</span>
          <span :class="{
            'status-green': res.signal_status === 2,
            'status-red': res.signal_status === 3
          }">{{ $t("bindSocialInfoStatus_" + res.signal_status) }}</span>
        </div>
        <div>
          <span>Zalo:</span>
          <span :class="{
            'status-green': res.zalo_status === 2,
            'status-red': res.zalo_status === 3
          }">{{ $t("bindSocialInfoStatus_" + res.zalo_status) }}</span>
        </div>
        <div>
          <span>E-Mail:</span>
          <span :class="{
            'status-green': res.email_status === 2,
            'status-red': res.email_status === 3
          }">{{ $t("bindSocialInfoStatus_" + res.email_status) }}</span>
        </div>
        <div>
          <span>WhatsApp:</span>
          <span :class="{
            'status-green': res.whatsapp_status === 2,
            'status-red': res.whatsapp_status === 3
          }">{{ $t("bindSocialInfoStatus_" + res.whatsapp_status) }}</span>
        </div>
        <div>
          <span>Ngày sinh:</span>
          <span :class="{
            'status-green': res.birthday_status === 2,
            'status-red': res.birthday_status === 3
          }">{{ $t("bindSocialInfoStatus_" + res.birthday_status) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
::v-deep .van-cell {
  font-weight: 600;
  font-size: 0.26rem;
  color: #312E2A;
}

::v-deep .van-cell {
  align-items: center !important;
}

::v-deep .disabled-field {
  .van-field__control {
    color: #999999 !important;
  }
}

::v-deep .van-field__placeholder {
  color: #999999;
}

/* 使用CSS变量控制placeholder颜色 */
::v-deep .van-field {
  --placeholder-color: #999999;
}

::v-deep .van-field .van-field__placeholder {
  color: var(--placeholder-color, #999999) !important;
}

/* 直接修改input的placeholder颜色 */
::v-deep input::placeholder {
  color: var(--placeholder-color, #999999) !important;
}

.status-green {
  color: #4CAF50 !important;
}

.status-red {
  color: #F44336 !important;
}

.withdraw-tip {
  width: 7.2rem;
  background: #F0DEDC;
  border-radius: 0.08rem;
  border: 0.02px solid #E6D3D7;margin: 0 auto;text-align: center;padding: .1rem;font-size: 0.23rem;
  color: #AF3841;
}

.withdraw-bkinfo {
  > div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: .1rem 0;
    font-size: 0.26rem;
  }
}
</style>
