<script>
import {loginPwd} from "@/mixins/loginPwd";

export default {
  mixins: [loginPwd],
}
</script>
<template>
  <div>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-loginChange am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title"> {{ $t("reset_login_pwd") }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-form colon @submit="showActionCaptcha('captchaPwdHandler')" style="margin-top: .1rem;">
<!--      &lt;!&ndash;        :rules="[{ required: true, message: '请填写用户名' }]"&ndash;&gt;-->

      <van-field
          :rules="[{ required: true, message: this.$t('old_password') }]"
          clearable
          autocomplete="off"
          label-width="3rem"
          input-align="right"
          :right-icon="!showCurrentPassword?'closed-eye':'eye'"
          :type="!showCurrentPassword?'password':'text'"
          @click-right-icon="showCurrentPassword=!showCurrentPassword"

          v-model.trim="form.CurrentPassword"
          name="email"
          :label="$t('label_pwd_old')"
      />
      <van-field
          :rules="[{ required: true, message: this.$t('enter_new_password') }]"
          clearable
          autocomplete="off"
          label-width="3rem"
          input-align="right"
          :right-icon="!showNewPassword?'closed-eye':'eye'"
          :type="!showNewPassword?'password':'text'"
          @click-right-icon="showNewPassword=!showNewPassword"
          @input="checkPasswordStrength(form.NewPassword)"

          v-model.trim="form.NewPassword"
          name="facebook"
          :label="$t('label_pwd_new')"
      />
      <div class="password-strength-indicator" style="padding: 0 .2rem" v-show="form.NewPassword">
        <div class="strength-level" :style="{backgroundColor: strength >= item ? levels[strength] : levels[0]}" v-for="item in 4" :key="item"></div>
      </div>
      <van-field
          :rules="[{ validator, message: this.$t('confirm_password') }]"
          clearable
          autocomplete="off"
          label-width="3.2rem"
          input-align="right"
          :right-icon="!showConfirmPassword?'closed-eye':'eye'"
          :type="!showConfirmPassword?'password':'text'"
          @click-right-icon="showConfirmPassword=!showConfirmPassword"

          v-model.trim="form.ConfirmPassword"
          name="telegram"
          :label="$t('label_pwd_confirm')"
      />
      <div style="padding: 0 .2rem;">
        <van-button round block type="warning" native-type="submit">{{$t('button_submit')}}</van-button>
      </div>
    </van-form>
<!--    <form class="flex-container security-center-container">
      <div class="am-whitespace am-whitespace-lg white-space"></div>
      <div class="account-form no-mgbtm">
        <div class="account-form-group">
          <div class="mc-account-list">
            <svg class="am-icon am-icon-lock_b49219f4 am-icon-md txt-svg">
              <use xlink:href="#lock_b49219f4"></use>
            </svg>
            <input
                autocomplete="off"
                :placeholder="$t('old_password')"
                :type="[showCurrentPassword ? 'text' : 'password']"
                v-validate="{ required: true }"
                v-model.trim="form.CurrentPassword"
                class="input-base"
                name="CurrentPassword" style="transition: all 0.15s linear 0s;">
          </div>
          <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" :class="showCurrentPassword ? 'open-eye' : 'close-eye'" @click="showCurrentPassword=!showCurrentPassword">
            <use xlink:href="#eyes_27cd1e8b"></use>
          </svg>
        </div>
        <div class="am-flexbox am-flexbox-align-middle errors">
          <div class="am-flexbox-item"><span>{{ errors.first("CurrentPassword") }}</span></div>
        </div>
      </div>
      <div class="account-form no-mgbtm">
        <div class="account-form-group">
          <div class="mc-account-list">
            <svg class="am-icon am-icon-passwordBetter_0bd7a252 am-icon-md txt-svg">
              <use xlink:href="#passwordBetter_0bd7a252"></use>
            </svg>
            <input autocomplete="off"
                :placeholder="$t('enter_new_password')"
                :type="[showNewPassword ? 'text' : 'password']"
                v-validate="{ required: true}"
                v-model.trim="form.NewPassword"
                @input="checkPasswordStrength(form.NewPassword)"
                ref="NewPassword"
                class="input-base" name="NewPassword" style="transition: all 0.15s linear 0s;">
          </div>
          <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" :class="showNewPassword ? 'open-eye' : 'close-eye'" @click="showNewPassword=!showNewPassword">
            <use xlink:href="#eyes_27cd1e8b"></use>
          </svg>
        </div>

        <div class="am-flexbox am-flexbox-align-middle errors">
          <div class="am-flexbox-item"><span>{{ errors.first("NewPassword") }}</span></div>
        </div>
      </div>
      <div class="account-form no-mgbtm">
        <div class="account-form-group">
          <div class="mc-account-list">
            <svg class="am-icon am-icon-passwordAgain_4b9460bf am-icon-md txt-svg">
              <use xlink:href="#passwordAgain_4b9460bf"></use>
            </svg>
            <input autocomplete="off"
                :placeholder="$t('confirm_pwd')"
                :type="[showConfirmPassword ? 'text' : 'password']"
                v-validate="'required|confirmed:NewPassword'"
                v-model.trim="form.ConfirmPassword"
                class="input-base" name="ConfirmPassword" style="transition: all 0.15s linear 0s;">
          </div>
          <svg class="am-icon am-icon-eyes_27cd1e8b am-icon-md am-icon-eyes" :class="showConfirmPassword ? 'open-eye' : 'close-eye'" @click="showConfirmPassword=!showConfirmPassword">
            <use xlink:href="#eyes_27cd1e8b"></use>
          </svg>
        </div>
        <div class="am-flexbox am-flexbox-align-middle errors">
          <div class="am-flexbox-item"><span>{{ errors.first("ConfirmPassword") }}</span></div>
        </div>
      </div>
      <div class="account-form no-mgbtm" v-if="false">
        <div class="account-form-group">
          <div class="mc-account-list">
            <svg class="am-icon am-icon-passwordAgain_4b9460bf am-icon-md txt-svg">
              <use xlink:href="#form-icon-invitation"></use>
            </svg>
            <input autocomplete="off"
                type="text"
                name="captcha"
                :placeholder="$t('in_increase_captcha')"
                v-model.trim="form.captcha"
                v-validate="{ required: true, alpha_num:true, min: 4 }"
                class="input-base" style="transition: all 0.15s linear 0s;">
          </div>
          <span style="position: absolute; top: 0; right: 1px;" @click="getCaptcha"
          >
                  <img :src="captcha.image" alt="" style="height: 1.03rem;"/>
                </span>
        </div>
        <div class="am-flexbox am-flexbox-align-middle errors">
          <div class="am-flexbox-item"><span>{{ errors.first("captcha") }}</span></div>
        </div>
      </div>
      &lt;!&ndash;      <p class="txt-gray">&lt;!&ndash; react-text: 5070 &ndash;&gt;* &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 5071 &ndash;&gt;Insira 6 - 12 caracteres alfanuméricos.&nbsp;não diferencia maiúsculas de minúsculas.&nbsp;(caracteres chineses não permitidos)&lt;!&ndash; /react-text &ndash;&gt;</p>&ndash;&gt;
      &lt;!&ndash;      <div class="am-whitespace am-whitespace-lg new-space-bet"></div>&ndash;&gt;
      <a role="button" class="btn-success enterButton  am-button" aria-disabled="false" @click="showActionCaptcha()"><span>{{ $t("button_submit") }}</span></a>
    </form>-->
  </div>
</template>
<style scoped>
.errors {
  min-height : unset;
}

.password-strength-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top : .2rem;
}

.strength-level {
  height: .1rem;  flex: 1; margin-left: .1rem;border-radius: .1rem
}
</style>