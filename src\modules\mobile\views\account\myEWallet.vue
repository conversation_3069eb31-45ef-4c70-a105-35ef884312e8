<template>
  <div>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-myEWallet am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">Vincule carteira eletrônica</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div>
      <div class="my-banks-main">
        <div>
          <div class="withdraw-bank my-banks vc pix">
            <div class="cards-title"><!-- react-text: 5355 -->carteira eletrônica registrada<!-- /react-text --><!-- react-text: 5356 -->(<!-- /react-text --><!-- react-text: 5357 -->0<!-- /react-text --><!-- react-text: 5358 -->/<!-- /react-text --><!-- react-text: 5359 -->2<!-- /react-text --><!-- react-text: 5360 -->)<!-- /react-text --></div>
            <div class="am-wingblank am-wingblank-lg">
              <div class="withdraw-nbank"><span>Carteira virtual vazia</span></div>
            </div>
            <div class="add-card-btn" @click="$router.push('/m/securityCenter/addBankCardPix')">
              <svg class="am-icon am-icon-bankadd_7c674007 am-icon-md">
                <use xlink:href="#bankadd_7c674007"></use>
              </svg><!-- react-text: 5367 -->E-wallet<!-- /react-text --></div>
            <p class="cards-tips">Máximo 2 permitido</p></div>
        </div>
      </div>
    </div>
  </div>
</template>