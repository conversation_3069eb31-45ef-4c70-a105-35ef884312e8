<template>
  <div>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-myVCards am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()"><span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">Adicionar carteira</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div>
      <div class="my-banks-main">
        <div>
          <div class="withdraw-bank my-banks-vc">
            <div class="cards-title"><!-- react-text: 5958 --><PERSON><PERSON> carteiras<!-- /react-text --><!-- react-text: 5959 -->(<!-- /react-text --><!-- react-text: 5960 -->0<!-- /react-text --><!-- react-text: 5961 -->/<!-- /react-text --><!-- react-text: 5962 -->1<!-- /react-text --><!-- react-text: 5963 -->)<!-- /react-text --></div>
            <div class="am-wingblank am-wingblank-lg">
              <div class="withdraw-nbank"><span>Lista de carteira vazia</span></div>
            </div>
            <div class="vc-add-container">
              <div class="add-card-btn" @click="$router.push('/m/securityCenter/addBankCard')">
                <svg class="am-icon am-icon-bankadd_7c674007 am-icon-md">
                  <use xlink:href="#bankadd_7c674007"></use>
                </svg><!-- react-text: 5971 -->Adicionar carteira<!-- /react-text --></div>
              <p class="cards-tips">Máximo 1 permitido</p></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>