<template>
  <div>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-phone am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title">{{ $t('label_phone_verify') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div>
      <van-form @submit="showActionCaptcha('submitCaptcha')">
        <van-field
            :readonly="!!hasSend"
            :rules="[{ required: true, pattern: patternPhone , message: this.$t('bind_tip_inputPhone') }]"
            clearable
            :error="false"
            autocomplete="off"
            label-width="3rem"
            input-align="right"
            v-model="form.phone"
            name="phone"
            :label="$store.state.phonePreShow"
            :placeholder="$t('ENTERPHONE.TIP_ENTER')"
        />
        <sms v-if="patternPhone.test(form.phone)" :phone="$store.state.phonePreApi+''+form.phone" @smsBtn="onSmsBtn" @smsFocus="onSmsFocus"/>
        <van-field
            v-show="hasSend === smsBtnNum"
            ref="smsCode"
            :rules="[{ required: true, pattern: patternCode, message: this.$t('ACCOUNTLAYER.TIP_BOTYZM') }]"
            clearable
            :error="false"
            autocomplete="off"
            label-width="3rem"
            input-align="right"

            v-model="form.phone_code" :placeholder="$t('ACCOUNTLAYER.TIP_BOTYZM')"
            name="phone_code"
            :label="$t('ACCOUNTLAYER.LAB_REP_PWD')"
        />
        <div style="margin: 0 .2rem">
          <van-button round block type="warning" native-type="submit">{{$t('button_submit')}}</van-button>
        </div>
      </van-form>
    </div>
  </div>
</template>
<script>
import {bindPhone} from "@/mixins/bindPhone";
import Sms from '@/modules/mobile/components/Sms.vue'
import {regex} from '@/mixins/regex'

export default {
  components: {Sms},
  mixins: [bindPhone, regex],
}
</script>
<style scoped>
.account-form .account-form-group {
  position: relative;
  margin-bottom: 0.2rem;
}
</style>