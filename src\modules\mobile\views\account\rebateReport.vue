<script>

import {mdate} from "@/mixins/mdate";

export default {
  name: "rebateReport",
  mixins: [mdate],
  data() {
    return {
      active: 0,
      showPicker: false
    }
  }
};
</script>

<template>
  <div class="rebateReport time-zone-space">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-rebateReport am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">Renda de desconto</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-tabs v-model="active" animated line-width="50%" line-height="2px" color="#108ee9">
      <van-tab title="Meu Desconto">
        <div class="myRebate time-zone-space">
          <div class="nodata-container" style="height: 573.5px;">
            <svg class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon">
              <use xlink:href="#nodata_f4c19c2d"></use>
            </svg>
            <p class="">Sem dados</p></div>
        </div>
      </van-tab>
      <van-tab title="Renda de desconto">
        <div class="rebateIncome">
          <div class="am-flexbox am-flexbox-align-middle mc-trans-filter  mc-filter ">
            <div class="am-flexbox am-flexbox-align-middle mc-trans-filter "><!-- react-text: 6758 --><!-- /react-text --><!-- react-text: 6759 --><!-- /react-text --><!-- react-text: 6760 --><!-- /react-text --><a role="button" class="am-button am-button-primary am-button-small am-button-inline am-button-icon" aria-disabled="false">
              <svg class="am-icon am-icon-check-circle-o am-icon-xxs" aria-hidden="true">
                <use xlink:href="#check-circle-o"></use>
              </svg>
              <span>Hoje</span></a><!-- react-text: 6765 --><!-- /react-text --><!-- react-text: 6766 --><!-- /react-text --><!-- react-text: 6767 --><!-- /react-text -->
              <div class="flex-shrink1 filter-time-btn" @click="show = true">
                <a role="button" class="am-button am-button-ghost am-button-small am-button-inline am-button-icon" aria-disabled="false" style="flex: 0 0 50%;">
                  <svg class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs" aria-hidden="true">
                    <use xlink:href="#calendar_c4db3b67"></use>
                  </svg>
                  <span>10/11- 10/11</span></a></div>
              <a @click="showPicker = true" role="button" class="am-button am-button-ghost am-button-small am-button-inline am-button-icon" aria-disabled="false" style="margin-left: 0.15rem;">
                <svg class="am-icon am-icon-down am-icon-xxs" aria-hidden="true">
                  <use xlink:href="#down"></use>
                </svg>
                <span>Tudo</span></a><!-- react-text: 6777 --><!-- /react-text --><!-- react-text: 6778 --><!-- /react-text --><!-- react-text: 6779 --><!-- /react-text --><!-- react-text: 6780 --><!-- /react-text --><!-- react-text: 6781 --><!-- /react-text --><!-- react-text: 6782 --><!-- /react-text --><!-- react-text: 6783 --><!-- /react-text -->
            </div><!-- react-text: 6784 --><!-- /react-text --><!-- react-text: 6785 --><!-- /react-text --><!-- react-text: 6786 --><!-- /react-text --><!-- react-text: 6787 --><!-- /react-text --><!-- react-text: 6788 --><!-- /react-text --><!-- react-text: 6789 --><!-- /react-text --><!-- react-text: 6790 --><!-- /react-text --><!-- react-text: 6791 --><!-- /react-text --><!-- react-text: 6792 --><!-- /react-text --><!-- react-text: 6793 --> <!-- /react-text --><!-- react-text: 6794 --><!-- /react-text -->
          </div>
          <div class="time-zone-space-p__small" style="height: 535px; overflow: auto;">
            <div>
              <div class="rebateReport-item-root contract">
                <div class="rebateReport-item-content">
                  <div class="card-header rebate"><!-- react-text: 6804 -->Data<!-- /react-text --><!-- react-text: 6805 -->：<!-- /react-text --><!-- react-text: 6806 -->2023-10-11<!-- /react-text --></div>
                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-top" style="padding: 0.2rem 0.4rem; border-top: 0.01rem solid rgb(235, 235, 235); line-height: 0.5rem;">
                    <div>
                      <div style="margin-right: 1rem;">
                        <span class="time"><!-- react-text: 6811 -->Valor da aposta<!-- /react-text --><!-- react-text: 6812 -->：<!-- /react-text --></span><span style="font-size: 0.32rem; font-weight: 700;">0</span>
                      </div>
                      <div style="margin-right: 1rem;">
                        <span class="time"><!-- react-text: 6816 -->Notas<!-- /react-text --><!-- react-text: 6817 -->：<!-- /react-text --></span><span style="font-size: 0.32rem; font-weight: 700;">0</span>
                      </div>
                    </div>
                    <div>
                      <div style="margin-right: 1rem;">
                        <span class="time"><!-- react-text: 6822 -->Desconto<!-- /react-text --><!-- react-text: 6823 -->：<!-- /react-text --></span><span style="font-size: 0.32rem; font-weight: 700;">0</span>
                      </div>
                    </div>
                  </div>
                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="padding: 0.2rem 0.4rem; border-top: 0.01rem solid rgb(235, 235, 235); line-height: 0.5rem;">
                    <div style="margin-right: 1rem;">
                      <span class="time"><!-- react-text: 6828 -->P&amp;L Total<!-- /react-text --><!-- react-text: 6829 -->：<!-- /react-text --></span><span style="font-weight: 700;"><span style="color: rgb(251, 91, 91);">0</span></span>
                    </div>
                    <div style="margin-right: 1rem;">
                      <span class="time"><!-- react-text: 6834 -->Ganhar perder<!-- /react-text --><!-- react-text: 6835 -->：<!-- /react-text --></span><span style="font-weight: 700;"><span style="color: rgb(251, 91, 91);">0</span></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </van-tab>
    </van-tabs>
     <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
        :max-date="maxDate" :allowSameDay="true"  />
    <van-popup v-model="showPicker" position="bottom">
      <div class="mc-popup-txtype"><div class="am-list"><div class="am-list-header"><div style="position: relative;"><!-- react-text: 13 -->produtos<!-- /react-text --><span style="position: absolute; right: 3px; top: -5px;"><svg class="am-icon am-icon-cross am-icon-md"><use xlink:href="#cross"></use></svg></span></div></div><div class="am-list-body"><div class="am-list-item am-list-item-middle"><div class="am-list-line"><div class="am-list-content"><svg class="am-icon am-icon-dice_02706417 am-icon-md svg-icon-dice"><use xlink:href="#dice_02706417"></use></svg><span>Tudo</span></div></div><div class="am-list-ripple" style="display: none;"></div></div><div class="am-list-item am-list-item-middle"><div class="am-list-line"><div class="am-list-content"><svg class="am-icon am-icon-dice_02706417 am-icon-md svg-icon-dice"><use xlink:href="#dice_02706417"></use></svg><span>Slot</span></div></div><div class="am-list-ripple" style="display: none;"></div></div><div class="am-list-item am-list-item-middle"><div class="am-list-line"><div class="am-list-content"><svg class="am-icon am-icon-dice_02706417 am-icon-md svg-icon-dice"><use xlink:href="#dice_02706417"></use></svg><span>Ao vivo</span></div></div><div class="am-list-ripple" style="display: none;"></div></div><div class="am-list-item am-list-item-middle"><div class="am-list-line"><div class="am-list-content"><svg class="am-icon am-icon-dice_02706417 am-icon-md svg-icon-dice"><use xlink:href="#dice_02706417"></use></svg><span>Pesca</span></div></div><div class="am-list-ripple" style="display: none;"></div></div><div class="am-list-item am-list-item-middle"><div class="am-list-line"><div class="am-list-content"><svg class="am-icon am-icon-dice_02706417 am-icon-md svg-icon-dice"><use xlink:href="#dice_02706417"></use></svg><span>Esportes</span></div></div><div class="am-list-ripple" style="display: none;"></div></div></div></div></div></van-popup>
  </div>
</template>

<style scoped></style>
