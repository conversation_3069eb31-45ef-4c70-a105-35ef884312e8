<script>
import {play} from "@/mixins/play";

export default {
  name: "securityCenter",
  mixins: [play],
  data() {
    return {
      safe: {
        score: this.$t("label_low"),
        img: '',
        star: 1,
      }
    }
  },
  mounted() {
    if (this.$store.state.account.registermobile) {
      this.safe.score = this.$t("label_high")
      this.safe.img = 'high grade-high'
      this.safe.star = 5
    } else {
      this.safe.score = this.$t("label_mid")
      this.safe.img = 'mid grade-mid'
      this.safe.star = 3
    }
  }
};
</script>

<template>
  <div class="security-center">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-securityCenter am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('security_center') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="card-list-item">
      <div class="security-circle">
        <div class="origin">
          <div class="circle-root">
            <img style="width: 1.6rem;" src="/img/safe.png"/>
            <!--            <canvas id="canvas_1" width="160" height="160"></canvas>-->
            <!--            <canvas id="canvas_2" width="160" height="160"></canvas>-->
          </div>
        </div>
      </div>
      <div class="flex-center">
        <div class="circleR">
          <div class="center-header">{{ $t('info_safety_score') }}:&nbsp;<i :class="`star_${safe.star}`">{{ safe.score }}</i>
          </div>
          <br>
          <div class="center-svgBox">
            <div>
              <template v-for="i in 5">
                <svg class="am-icon am-icon-lightning_cdf0a8f8 am-icon-xs center-svgL" color="#cdcdcd" v-if="i > safe.star">
                  <use xlink:href="#lightning_cdf0a8f8"></use>
                </svg>
                <svg class="am-icon am-icon-lightning_cdf0a8f8 am-icon-xs center-svgL" v-else>
                  <use xlink:href="#lightning_cdf0a8f8"></use>
                </svg>
              </template>
            </div>
          </div>
        </div>
        <div>
          <div class="flex-tips">{{ $t('login_ip') }}: {{
              $store.state.account.lastLogonIp
            }}
          </div>
          <div class="flex-tips">{{ $t('label_login_lastTime') }}: {{
              $store.state.account.lastLogonTime*1000 | datetimeFormat
            }}
          </div>
        </div>
      </div>
    </div>
    <div class="am-flexbox am-flexbox-align-middle" >
      <div class="flex-text-tips" v-if="safe.star < 5">{{ $t('info_account_grade') }} {{ safe.score }}, {{ $t('perfect_information') }}
      </div>
    </div>
    <div class="am-flexbox am-flexbox-align-middle">
      <div class="card-btm security-list">
        <div class="am-list">
          <div class="">
            <div
                v-if="$store.state.activitySwitchDetails && $store.state.activitySwitchDetails.some(item => item.activityType === 22 && item.status === 1)"
                @click="$router.push('/m/myAccount/index')"
                class="flex-list-item am-list-item am-list-item-middle"
            >
                          <div class="am-list-line">
                            <div class="am-list-content">
                              <div class="flex-grad-one">
                                <svg
                                  class="am-icon am-icon-profile_91540dcb am-icon-lg flex-list-icon1 security-icon"
                                  color="#cccccc"
                                >
                                  <use xlink:href="#icon-personage"></use>
                                </svg>
                              </div>
                              <div class="flex-list-con">
                                <p class="flex-list-header">
                                  <span class="flex-list-text">{{ $t('label_setting_data') }}</span>
                                  <span class="flex-list-icon">
                                </span>
                                </p>
                                <p class="flex-list-title flex-list-desc">
                                  {{ $t('profile_description') }}
                                </p>
                              </div>
                              <div class="flex-grad-arrows">
                                <van-icon name="arrow" color="#FFB627"/>
                              </div>
                            </div>
                          </div>
                          <div class="am-list-ripple" style="display: none"></div>
                        </div>
            <!--            <div @click="$router.push('/m/myVCards')" class="flex-list-item am-list-item am-list-item-middle">-->
            <!--              <div class="am-list-line">-->
            <!--                <div class="am-list-content">-->
            <!--                  <div class="flex-grad-one">-->
            <!--                    <svg-->
            <!--                      class="am-icon am-icon-bindvcCard_on_df9c0737 am-icon-lg flex-list-icon1 security-icon"-->
            <!--                      color="#cccccc"-->
            <!--                    >-->
            <!--                      <use xlink:href="#bindvcCard_on_df9c0737"></use>-->
            <!--                    </svg>-->
            <!--                  </div>-->
            <!--                  <div class="flex-list-con">-->
            <!--                    <p class="flex-list-header">-->
            <!--                      <span class="flex-list-text">{{ $t('bind_vc_card') }}</span-->
            <!--                      ><span class="flex-list-icon"-->
            <!--                        >&lt;!&ndash; react-text: 7303 &ndash;&gt;&lt;!&ndash; /react-text &ndash;&gt;<svg-->
            <!--                          class="am-icon am-icon-edit_0f6eb9f1 am-icon-md edit-icon"-->
            <!--                        >-->
            <!--                          <use xlink:href="#edit_0f6eb9f1"></use></svg-->
            <!--                      ></span>-->
            <!--                    </p>-->
            <!--                    <p class="flex-list-title flex-list-desc">-->
            <!--                      {{ $t('info_setting_virtual') }}-->
            <!--                    </p>-->
            <!--                  </div>-->
            <!--                  <div class="flex-grad-arrows">-->
            <!--                    <svg-->
            <!--                      class="am-icon am-icon-rightarrows_e029c01f am-icon-md"-->
            <!--                      color="#A6A6A6"-->
            <!--                    >-->
            <!--                      <use xlink:href="#rightarrows_e029c01f"></use>-->
            <!--                    </svg>-->
            <!--                  </div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              <div class="am-list-ripple" style="display: none"></div>-->
            <!--            </div>-->
            <!--            <div @click="$router.push('/m/idVerification?ing=1')" class="flex-list-item am-list-item am-list-item-middle">-->
            <!--              <div class="am-list-line">-->
            <!--                <div class="am-list-content">-->
            <!--                  <div class="flex-grad-one ivi-icon">-->
            <!--                    <span class="id-verification-icon"></span>-->
            <!--                    <svg-->
            <!--                      class="am-icon am-icon-verification_a14b9171 am-icon-lg security-icon"-->
            <!--                      color="#cccccc"-->
            <!--                    >-->
            <!--                      <use xlink:href="#verification_a14b9171"></use>-->
            <!--                    </svg>-->
            <!--                  </div>-->
            <!--                  <div class="flex-list-con">-->
            <!--                    <p class="flex-list-header">-->
            <!--                      <span class="flex-list-text"-->
            <!--                        >{{ $t('verify_cpf') }}&lt;!&ndash; react-text: 7451 &ndash;&gt;-->
            <!--                        &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 7452 &ndash;&gt;&lt;!&ndash; /react-text &ndash;&gt;</span-->
            <!--                      >-->
            <!--                    </p>-->
            <!--                    <p class="flex-list-title flex-list-desc">-->
            <!--                      {{ $t('info_setting_idverification') }}-->
            <!--                    </p>-->
            <!--                  </div>-->
            <!--                  <div class="flex-grad-arrows">-->
            <!--                    <svg-->
            <!--                      class="am-icon am-icon-rightarrows_e029c01f am-icon-md"-->
            <!--                      color="#A6A6A6"-->
            <!--                    >-->
            <!--                      <use xlink:href="#rightarrows_e029c01f"></use>-->
            <!--                    </svg>-->
            <!--                  </div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              <div class="am-list-ripple" style="display: none"></div>-->
            <!--            </div>-->
            <!--                        <div @click="$router.push('/m/myEWallet')" class="flex-list-item am-list-item am-list-item-middle">-->
            <!--                          <div class="am-list-line">-->
            <!--                            <div class="am-list-content">-->
            <!--                              <div class="flex-grad-one">-->
            <!--                                <svg-->
            <!--                                  class="am-icon am-icon-bindEwallet_ee5bf137 am-icon-lg flex-list-icon1 security-icon"-->
            <!--                                  color="#cccccc"-->
            <!--                                >-->
            <!--                                  <use xlink:href="#bindEwallet_ee5bf137"></use>-->
            <!--                                </svg>-->
            <!--                              </div>-->
            <!--                              <div class="flex-list-con">-->
            <!--                                <p class="flex-list-header">-->
            <!--                                  <span class="flex-list-text"-->
            <!--                                    >{{ $t('bind_ewallet') }}</span-->
            <!--                                  ><span class="flex-list-icon"-->
            <!--                                    >&lt;!&ndash; react-text: 7335 &ndash;&gt;&lt;!&ndash; /react-text &ndash;&gt;<svg-->
            <!--                                      class="am-icon am-icon-edit_0f6eb9f1 am-icon-md edit-icon"-->
            <!--                                    >-->
            <!--                                      <use xlink:href="#edit_0f6eb9f1"></use></svg-->
            <!--                                  ></span>-->
            <!--                                </p>-->
            <!--                                <p class="flex-list-title flex-list-desc">-->
            <!--                                  {{ $t('bind_ewallet_card_title') }}-->
            <!--                                </p>-->
            <!--                              </div>-->
            <!--                              <div class="flex-grad-arrows">-->
            <!--                                <svg-->
            <!--                                  class="am-icon am-icon-rightarrows_e029c01f am-icon-md"-->
            <!--                                  color="#A6A6A6"-->
            <!--                                >-->
            <!--                                  <use xlink:href="#rightarrows_e029c01f"></use>-->
            <!--                                </svg>-->
            <!--                              </div>-->
            <!--                            </div>-->
            <!--                          </div>-->
            <!--                          <div class="am-list-ripple" style="display: none"></div>-->
            <!--                        </div>-->
            <div @click="$router.push('/m/securityCenter/loginChange')" class="flex-list-item am-list-item am-list-item-middle">
              <div class="am-list-line">
                <div class="am-list-content">
                  <div class="flex-grad-one">
                    <svg
                        class="am-icon am-icon-otherLock_79d1fd21 am-icon-lg flex-list-icon1 security-icon"
                        color="#cccccc"
                    >
                      <use xlink:href="#icon-lock"></use>
                    </svg>
                  </div>
                  <div class="flex-list-con">
                    <p class="flex-list-header">
                      <span class="flex-list-text">{{ $t('reset_login_pwd') }}</span
                      > <span class="flex-list-icon"
                    >
<!--                                            <svg class="am-icon am-icon-cancal_462cf10f am-icon-xs flex-list-icon1 icon-complete" style="top: 0">-->
<!--                                                                    <use xlink:href="#cancal_462cf10f"></use>-->
<!--                                                                  </svg>-->
<!--                      <svg
                          class="am-icon am-icon-edit_0f6eb9f1 am-icon-md edit-icon t0"
                      >
                                                                      <use xlink:href="#edit_0f6eb9f1"></use></svg
                      >-->
                    </span>
                    </p>
                    <p class="flex-list-title flex-list-desc">
                      {{ $t('login_password_title') }}
                    </p>
                  </div>
                  <div class="flex-grad-arrows">
                    <van-icon name="arrow" color="#FFB627"/>
                  </div>
                </div>
              </div>
              <div class="am-list-ripple" style="display: none"></div>
            </div>
<!--            <div @click="!info.transactionPasswd && $router.push('/m/securityCenter/FundsPasswordBox')" class="flex-list-item am-list-item am-list-item-middle">
              <div class="am-list-line">
                <div class="am-list-content">
                  <div class="flex-grad-one">
                    <svg
                        class="am-icon am-icon-otherMoneyPassword_c96dfc31 am-icon-lg flex-list-icon1 security-icon"
                        color="#cccccc"
                    >
                      <use xlink:href="#otherMoneyPassword_c96dfc31"></use>
                    </svg>
                  </div>
                  <div class="flex-list-con">
                    <p class="flex-list-header">
                      <span class="flex-list-text">{{ $t('label_pwd_pay') }}</span>
                      <span class="flex-list-icon">
                      <svg class="am-icon am-icon-cancal_462cf10f am-icon-xs flex-list-icon1 icon-complete t0" v-if="info.transactionPasswd">
                                           <use xlink:href="#cancal_462cf10f"></use>
                       </svg>
                                                                  <svg
                                                                      class="am-icon am-icon-edit_0f6eb9f1 am-icon-md edit-icon t0" v-else
                                                                  >
                                                                      <use xlink:href="#edit_0f6eb9f1"></use></svg
                                                                  >
                    </span>
                    </p>
                    <p class="flex-list-title flex-list-desc">{{ $t('funds_password_title') }}
                    </p>
                  </div>
                  <div class="flex-grad-arrows" v-if="!info.transactionPasswd">
                    <svg
                        class="am-icon am-icon-rightarrows_e029c01f am-icon-md"
                        color="#A6A6A6"
                    >
                      <use xlink:href="#rightarrows_e029c01f"></use>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="am-list-ripple" style="display: none"></div>
            </div>-->
<!--            <div @click="!info.oauth && $router.push('/m/securityCenter/bindGA')" class="flex-list-item am-list-item am-list-item-middle">
              <div class="am-list-line">
                <div class="am-list-content">
                  <div class="flex-grad-one">
                    <svg
                      class="am-icon am-icon-google_6f0f58d5 am-icon-lg flex-list-icon1 security-icon"
                      color="#cccccc"
                    >
                      <use xlink:href="#google_6f0f58d5"></use>
                    </svg>
                  </div>
                  <div class="flex-list-con">
                    <p class="flex-list-header">
                      <span class="flex-list-text">{{ $t('google_verification') }}</span
                      ><span class="flex-list-icon"
                        >
                      <svg class="am-icon am-icon-cancal_462cf10f am-icon-xs flex-list-icon1 icon-complete t0" v-if="info.oauth">
                                           <use xlink:href="#cancal_462cf10f"></use>
                       </svg>
                                                                  <svg
                                                                      class="am-icon am-icon-edit_0f6eb9f1 am-icon-md edit-icon t0" v-else
                                                                  >
                                                                      <use xlink:href="#edit_0f6eb9f1"></use></svg
                                                                  >
                    </span
                      >
                    </p>
                    <p class="flex-list-title flex-list-desc">{{ $t('google_verification') }}
                    </p>
                  </div>
                  <div class="flex-grad-arrows" v-if="!info.oauth">
                    <svg
                      class="am-icon am-icon-rightarrows_e029c01f am-icon-md"
                      color="#A6A6A6"
                    >
                      <use xlink:href="#rightarrows_e029c01f"></use>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="am-list-ripple" style="display: none"></div>
            </div>-->
            <div @click="!$store.state.account.registermobile && $router.push('/m/myAccount/phone')" class="flex-list-item am-list-item am-list-item-middle" :class="{grayscale: $store.state.account.registermobile}">
              <div class="am-list-line">
                <div class="am-list-content">
                  <div class="flex-grad-one">
                    <svg
                        class="am-icon am-icon-mobile_35904f6b am-icon-lg flex-list-icon1 security-icon"
                        color="#cccccc"
                    >
                      <use xlink:href="#icon-phone"></use>
                    </svg>
                  </div>
                  <div class="flex-list-con">
                    <p class="flex-list-header">
                      <span class="flex-list-text">{{ $t('label_phone_verify') }}</span
                      ><span class="flex-list-icon"
                    >

<!--                      <svg class="am-icon am-icon-cancal_462cf10f am-icon-xs flex-list-icon1 icon-complete t0" v-if="info.bindPhone">
                                           <use xlink:href="#cancal_462cf10f"></use>
                       </svg>
                                                                  <svg
                                                                      class="am-icon am-icon-edit_0f6eb9f1 am-icon-md edit-icon t0" v-else
                                                                  >
                                                                      <use xlink:href="#edit_0f6eb9f1"></use></svg
                                                                  >-->
                    </span
                    >
                    </p>
                    <p class="flex-list-title flex-list-desc">
                      {{ $t('label_phone_verify') }}
                    </p>
                  </div>
                  <div class="flex-grad-arrows">
                    <van-icon name="arrow" color="#FFB627"/>
                  </div>
                </div>
              </div>
              <div class="am-list-ripple" style="display: none"></div>
            </div>
<!--            <div @click="logout" class="flex-list-item am-list-item am-list-item-middle">
              <div class="am-list-line">
                <div class="am-list-content">
                  <div class="flex-grad-one">
                    <svg
                        class="am-icon am-icon-logout_aae7786e am-icon-lg flex-list-icon1 security-icon"
                        color="#cccccc"
                    >
                      <use xlink:href="#icon-switch-"></use>
                    </svg>
                  </div>
                  <div class="flex-list-con">
                    <p class="flex-list-header">
                      <span class="flex-list-text">{{ $t('logout') }}</span>
                    </p>
                    <p class="flex-list-title flex-list-desc">
                      {{ $t('logout_safely') }}
                    </p>
                  </div>
                  <div class="flex-grad-arrows"></div>
                </div>
              </div>
              <div class="am-list-ripple" style="display: none"></div>
            </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

.t0{
 top: 0;
}

.flex-list-item {
  border-radius: 0.2rem;
  border: 0.02px solid #CECDC9;
  margin-bottom: .1rem;
}
.am-list-content {
  padding-left: .2rem;
}

.center-header {

    .star_1 {
      color: #FF0000;
    }
    .star_3 {
      color: #FF9C00;
    }
    .star_5 {
      color: #00BF3A;
    }

}

.grayscale {
  filter: grayscale(1);
}
</style>
