<script>
import {withdrawPwd} from "@/mixins/withdrawPwd";

export default {
  mixins: [withdrawPwd],
}
</script>
<template>
  <div>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-loginChange am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg></span></span>
        </div>
        <div class="am-navbar-title"> {{ $t("label_pwd_pay") }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-form colon @submit="submitHandle" style="margin-top: .1rem;">
      <van-field
          clearable
          autocomplete="off"
          label-width="3rem"
          input-align="right"
          :right-icon="!showNewPassword?'closed-eye':'eye'"
          :type="!showNewPassword?'password':'text'"
          @click-right-icon="showNewPassword=!showNewPassword"
          v-model.trim="form.NewPassword"
          name="NewPassword"
          label="Mật khẩu rút tiền"
      />
      <van-field
          clearable
          autocomplete="off"
          label-width="3rem"
          input-align="right"
          :right-icon="!showConfirmPassword?'closed-eye':'eye'"
          :type="!showConfirmPassword?'password':'text'"
          @click-right-icon="showConfirmPassword=!showConfirmPassword"
          v-model.trim="form.ConfirmPassword"
          name="ConfirmPassword"
          label="Xác nhận mật khẩu"
      />
      <div style="padding: 0 .2rem;">
        <van-button round block type="warning" native-type="submit">Rút tiền</van-button>
      </div>
    </van-form>
  </div>
</template>
<style scoped>
.errors {
  min-height : unset;
}

.password-strength-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top : .2rem;
}

.strength-level {
  height: .1rem;  flex: 1; margin-left: .1rem;border-radius: .1rem
}
</style>