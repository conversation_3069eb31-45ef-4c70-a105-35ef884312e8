<script>
import { lang } from "@/mixins/lang";

export default {
  name: "detail",
  mixins: [lang],
  computed: {
    item() {
      return this.banner;
    },
  },
  mounted() {
    this.bannerDetail(this.$route.query.id)
  }
};
</script>

<template>
  <div class="activity_container">
    <div id="page_bg" class="common"></div>
    <van-sticky>
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-withdrawReport am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
          </div>
          <div class="am-navbar-title">{{ $t('label_details') }}</div>
          <div class="am-navbar-right">
            <div class="to-record">
            </div>
          </div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
    </van-sticky>
    <div style="height: calc(100vh - 1rem - var(--safe-area-inset-bottom));background-color: #0e131b;">
    <div class="activity-detail-container">
      <div class="activity-detail-content">
        <div class="activity-info">
          <div class="activity-img">
            <img :src="changeLang(item.messages).imageUrl" alt="" />
          </div>
        </div>
        <div class="activity-desc">
          <div>
            <div
              class="wysiwyg"
              v-html="changeLang(item.messages).contents"
            ></div>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<style scoped></style>
