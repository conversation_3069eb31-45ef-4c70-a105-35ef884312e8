<script>
import {lang} from "@/mixins/lang";

export default {
  name: "index",
  mixins: [lang],
  mounted() {
    this.event_enterTask()
  }
};
</script>

<template>
  <div class="activity_container" style="background: #0e131b;">
    <div id="page_bg" class="common"></div>
    <div class="prev-route-container" style="    width: 100%;
    flex-shrink: 0;
    transition: all .5s;">
      <div class="prev-route-content" style="position: fixed;width: 100%; z-index: 1;
    top: 0;">
        <div class="header-left">
<!--          <svg class="am-icon am-icon-btn-arrow-left am-icon-md left-arrow">-->
<!--            <use xlink:href="#btn-arrow-left"></use>-->
<!--          </svg>-->
        </div>
        <div class="route-nav-item current">{{ $t('popularize') }}</div>
      </div>
    </div>
    <div class="activity-preview" style="padding-top: calc(1.3rem + (var(--safe-area-inset-bottom)))">
<!--      <div class="activity_title">-->
<!--        <div class="promo_menu hide-scrollbar">-->
<!--          <div class="promo_menu_list">-->
<!--            <div class="promo_menu_item on"><span>Tudo</span></div>-->
<!--            <div class="promo_menu_item"><span>Novo jogador</span></div>-->
<!--            <div class="promo_menu_item"><span>Depósito</span></div>-->
<!--            <div class="promo_menu_item"><span>Outros</span></div>-->
<!--          </div>-->
<!--        </div>-->
<!--      </div>-->
      <div class="activity_content">
        <div class="promo_item" v-for="(item, index) in $store.state.banners" :key="index" @click="$router.push({path: '/m/activityDetail', query:{id: item.id}})">
          <div class="activity">
            <div class="activity_img">
              <img
                  :src="changeLang(item.messages).imageUrl"
                  :alt="changeLang(item.messages).title"
                  style="object-fit: fill"
              />
            </div>
<!--            <div class="activity_info">
              <div class="item-title">{{ changeLang(item.messages).title }}</div>
              <svg class="am-icon am-icon-icon-more am-icon-md icon-more">
                <use xlink:href="#icon-more"></use>
              </svg>
            </div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
