<script>
import {deposit} from "@/mixins/deposit";

export default {
  name: "index",
  mixins: [deposit]
};
</script>

<template>
  <div class="my-home">
    <div class="ios-version" id="v2-vc-container">
      <!-- react-text: 9502 --><!-- /react-text -->
      <div class="mc-header-wrap">
        <div
          id="mc-header"
          class="mc-navbar-blue mc-voucherCenter am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
            <span class="am-navbar-left-content"
              ><span class="return_icon"
                ><svg class="am-icon am-icon-left am-icon-lg">
                  <use xlink:href="#left"></use></svg></span
            ></span>
          </div>
          <div class="am-navbar-title">{{ $t('label_deposit') }}</div>
          <div class="am-navbar-right">
            <div>
              <svg @click="$router.push('/m/vouReport')"
                class="am-icon am-icon-voucher_list_b99e1f01 am-icon-md am-navbar-title"
              >
                <use xlink:href="#voucher_list_b99e1f01"></use>
              </svg>
<!--              <svg-->
<!--                class="am-icon am-icon-information_b8db8191 am-icon-md am-navbar-title help-center-icon"-->
<!--              >-->
<!--                <use xlink:href="#information_b8db8191"></use>-->
<!--              </svg>-->
            </div>
          </div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <div class="vc-scroll-container" style="background-color: white;">
        <div class="vc-v2-container">
          <div class="pd-btm-20 ranndom-container">
            <div class="vc-v2-title">{{ $t('platform_type') }}</div>
            <div class="min-list vc-v2-method show-more-container">
              <ul>
                <li class="change-item-animate"
                    :class="{ ck: currentPlatformType === 1}"
                    @click="changePlatformType(1)"
                >
                  <svg v-if="currentPlatformType === 1"
                      class="am-icon am-icon-checked_0a488f7d am-icon-md deposit-list-ck"
                  >
                    <use xlink:href="#checked_0a488f7d"></use>
                  </svg>
                  <div
                      class="imageloader loaded deposit-img-new"
                  >
                    <img
                        src="img/pay/EN_2.png"
                    />
                  </div>
                  <div class="desc-content">
                    <div class="desc-info">
                      <div class="vcn-list-text"><p>PIX</p></div>
                    </div>
                  </div>
                </li>
                <li class="change-item-animate"
                    :class="{ ck: currentPlatformType === 2}"
                    @click="changePlatformType(2)"
                >
                  <svg v-if="currentPlatformType === 2"
                      class="am-icon am-icon-checked_0a488f7d am-icon-md deposit-list-ck"
                  >
                    <use xlink:href="#checked_0a488f7d"></use>
                  </svg>
                  <div
                      class="imageloader loaded deposit-img-new"
                  >
                    <img
                        src="img/pay/EN_1.png"
                    />
                  </div>
                  <div class="desc-content">
                    <div class="desc-info">
                      <div class="vcn-list-text"><p>USDT</p></div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
<!--          <div class="pd-btm-20 ranndom-container">-->
<!--            <div class="vc-v2-title">{{ $t('payment_mode') }}</div>-->
<!--            <div class="min-list vc-v2-method show-more-container">-->
<!--              <ul>-->
<!--                <li class="change-item-animate"-->
<!--                    :class="{ ck: currentPaymentIndex === index, recommendation: item.recommend}"-->
<!--                    @click="changePayment(index)"-->
<!--                    v-for="(item, index) in payments"-->
<!--                    :key="item.paymentId"-->
<!--                >-->
<!--                  <svg v-if="currentPaymentIndex === index"-->
<!--                    class="am-icon am-icon-checked_0a488f7d am-icon-md deposit-list-ck"-->
<!--                  >-->
<!--                    <use xlink:href="#checked_0a488f7d"></use>-->
<!--                  </svg>-->
<!--                  <div-->
<!--                    class="imageloader loaded deposit-img-new"-->
<!--                  >-->
<!--                    <img-->
<!--                      :src="item.icon"-->
<!--                    />-->
<!--                  </div>-->
<!--                  <div class="desc-content">-->
<!--                    <div class="desc-info">-->
<!--                      <div class="vcn-list-text"><p>{{ item.paymentName }}</p></div>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </li>-->
<!--              </ul>-->
<!--            </div>-->
<!--          </div>-->
          <div class="vc-v2-space-content vc-v2-space-content"></div>
          <div class="" v-if="channels.length">
            <div class="ranndom-container pay-way-container">
              <div class="vc-v2-title pay-way">{{ $t('pay_method') }}</div>
              <div class="vc-v2-method-list">
                <ul style="overflow: unset">
                  <li
                      :class="{ ck: currentChannelIndex === index, recommendation: item.recommend }"
                      v-for="(item, index) in channels"
                      :key="index"
                      @click="changeChannel(index)"
                      style="overflow: visible"
                  >
                    <span class="method-list-info">{{ item.title }}</span>
                    <div class="uh6csli2MHzFz87__Phx tool-tips eXOyUJ1Q7qC8CgKXC53w" v-if="paymentsExtraRate(item.paymentId)" style="--cu-top: -0.13rem; --cu-right: -.08rem; --cu-left: unset;">
                      <p class="tool-tips-box sNgyWmhVEZVrbSGKDBra">{{ paymentsExtraRate(item.paymentId) | toRate }}</p>
                      <p class="tool-tips-tail YiNQnpSJUes7u5sqBZhp"></p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <div v-if="channels.length">
              <div id="payAmount" class="ranndom-container vc-v2-amount">
                <div class="vc-v2-title pay-amount">{{ $t('deposit_amount') }}</div>
                <div class="vc-v2-input">
                  <div class="deposit-amount-con" style="overflow:visible;">
                    <div class="fixed-money">
                      <div class="fixed-money-item number-mc"
                           @click="chargeAmount = amount"
                           :class="{ 'money-active': chargeAmount == amount }"
                           v-for="(amount, index) in amounts"
                      >
                        {{ amount/100 }}
                        <svg v-if="chargeAmount == amount"
                            class="am-icon am-icon-checked_0a488f7d am-icon-md deposit-list-ck"
                        >
                          <use xlink:href="#checked_0a488f7d"></use>
                        </svg>
                        <div class="uh6csli2MHzFz87__Phx tool-tips eXOyUJ1Q7qC8CgKXC53w" v-if="channels[currentChannelIndex].channelExtraRate[index]" style="--cu-top: -0.13rem; --cu-right: -.06rem; --cu-left: unset;">
                          <p class="tool-tips-box sNgyWmhVEZVrbSGKDBra"> {{ paymentsExtraRate(channels[currentChannelIndex].paymentId) ? $options.filters['toRate'](paymentsExtraRate(channels[currentChannelIndex].paymentId)) + "+" : "" }}{{ channels[currentChannelIndex].channelExtraRate[index] | toRate }}</p>
                          <p class="tool-tips-tail YiNQnpSJUes7u5sqBZhp"></p>
                        </div>
                      </div>
                    </div>
                    <div>
                      <div class="inputCon">
                        <i>{{ $store.state.configs.currency_symbol }}</i
                        ><input
                          type="number"
                          inputmode="decimal"
                          maxlength="11"
                          autocomplete="false"
                          v-model="showAmount"
                          :placeholder="$t('deposit_single_amount', {0: paymentMin, 1:paymentMax})"
                          class="inputBase"
                          name="deposit"
                        />
                      </div>
                      <span class="error-tips">* {{ $t('deposit_single_amount', {0: paymentMin, 1:paymentMax}) }}</span>
                    </div>
                  </div>
                </div>
                <div class="bank-virtual-tips" v-if="payments[currentPaymentIndex] && payments[currentPaymentIndex].platformType !== 1">
                  <span>{{ convertedAmount }}</span>
                  {{ channels[currentChannelIndex].currencySymbol }}
                  <div>{{ $t('label_reference_rate') }}
                    <span>1 {{ channels[currentChannelIndex].currencySymbol }} = {{ $store.state.configs.currency_symbol }} {{ channels[currentChannelIndex].channelRate | currency(false, true) }}</span></div></div>
              </div>
              <div class="am-flexbox am-flexbox-align-middle errors">
                <div class="am-flexbox-item">
                  <span class="hide"
                    ><!-- react-text: 9558 -->Taxa de manuseio
                    <!-- /react-text --><!-- react-text: 9559 -->:
                    <!-- /react-text --><i>0</i></span
                  >
                </div>
              </div>
<!--              <div-->
<!--                class="ranndom-container vc-v2-space vc-v2-active"-->
<!--                id="vc-v2-active"-->
<!--              >-->
<!--                <div class="vc-v2-title pay-active">Atividade</div>-->
<!--                <div class="vc-v2-active-container">-->
<!--                  <div-->
<!--                    class="am-flexbox am-flexbox-align-middle deposit-promo-v2 flex-con"-->
<!--                  >-->
<!--                    <div class="am-flexbox-item">-->
<!--                      <div class="act-container">-->
<!--                        <div class="act-item">-->
<!--                          <div-->
<!--                            class="act-prom-name act-prom-remark-v2-container"-->
<!--                          >-->
<!--                            &lt;!&ndash; react-text: 9657 &ndash;&gt;Bônus de primeiro depósito&lt;!&ndash; /react-text &ndash;&gt;-->
<!--                            <div class="act-mount">-->
<!--                              &lt;!&ndash; react-text: 9659 &ndash;&gt;&gt;&lt;!&ndash; /react-text &ndash;&gt;<i-->
<!--                                >R$10.00</i-->
<!--                              >-->
<!--                            </div>-->
<!--                          </div>-->
<!--                          <div class="act-prom-remark-v2">-->
<!--                            <div class="wysiwyg">-->
<!--                              <h2>-->
<!--                                A partir de agora, todo novo membro do 55G que-->
<!--                                depositar pelo menos R$ 10 pela primeira vez-->
<!--                                pode ganhar um bônus de até R$ 5555! Não há-->
<!--                                limites! Convide seus amigos para participar!-->
<!--                              </h2>-->
<!--                              <h2><br /></h2>-->
<!--                            </div>-->
<!--                          </div>-->
<!--                        </div>-->
<!--                        <div class="act-item on">-->
<!--                          <svg-->
<!--                            class="am-icon am-icon-act_ck_56962fdb am-icon-md"-->
<!--                          >-->
<!--                            <use xlink:href="#act_ck_56962fdb"></use>-->
<!--                          </svg>-->
<!--                          <div class="act-prom-name">-->
<!--                            Não participe de nenhuma promoção-->
<!--                          </div>-->
<!--                        </div>-->
<!--                      </div>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
<!--              <div class="ranndom-container hide">-->
<!--                <div class="vc-v2-title pay-dc vc-v2-space">-->
<!--                  <div-->
<!--                    class="am-flexbox am-flexbox-align-middle flex-con-new flex-another-pay flex-pay-showborder hide"-->
<!--                  >-->
<!--                    &lt;!&ndash; react-text: 9567 &ndash;&gt;Depositar em nome&lt;!&ndash; /react-text &ndash;&gt;<span-->
<!--                      >&lt;!&ndash; react-text: 9569 &ndash;&gt;-->
<!--                      &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 9570 &ndash;&gt;Clique para-->
<!--                      depositar para outro usuário.-->
<!--                      &lt;!&ndash; /react-text &ndash;&gt;</span-->
<!--                    >-->
<!--                    <div-->
<!--                      class="agrg-downline-type switch-box setting_sort_right"-->
<!--                    >-->
<!--                      <span class="agrg-downline-cname"></span>-->
<!--                      <div class="mc-switch mc-qutas-bg">-->
<!--                        <div class="checkbox"></div>-->
<!--                      </div>-->
<!--                      <span class="agrg-downline-cname other-size"-->
<!--                        >Depositar em nome</span-->
<!--                      >-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
<!--              <div class="vc-v2-other vc-v2-input">-->
<!--                <div-->
<!--                  class="am-flexbox am-flexbox-align-middle hide flex-con flex-con-other"-->
<!--                >-->
<!--                  <div class="am-flexbox-item">-->
<!--                    <div class="inputCon">-->
<!--                      <input-->
<!--                        type="text"-->
<!--                        inputmode="text"-->
<!--                        maxlength="15"-->
<!--                        class="inputBase"-->
<!--                        placeholder="Por favor, insira o nome de usuário"-->
<!--                        name="targetName"-->
<!--                      />-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </div>-->
            </div>
          </div>
        </div>
      </div>
      <div class="vc-v2-submit"  @click="doCharge">
        <i></i>
        <div>{{ $t('button_confirm')}}</div>
      </div>
      <!-- react-empty: 9586 -->
    </div>
  </div>
</template>

<style scoped>
.uh6csli2MHzFz87__Phx {
  height: 0.32rem;
  left: var(--cu-left);
  position: absolute;
  right: var(--cu-right);
  top: var(--cu-top);
  width: fit-content;
  z-index: 100;
}
 .r1QnBDk2QnzYN6t7hweu p:first-child {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uh6csli2MHzFz87__Phx .sNgyWmhVEZVrbSGKDBra {
  background-color: red;
  border-radius: 0.125rem 0.125rem 0.125rem 0;
  color: #fff;
  width: 100%;
  font-size: .18rem;
  height: 0.28rem;
  line-height: .28rem;
  padding: 0 .07rem .02rem 0.07rem;
}

.uh6csli2MHzFz87__Phx .YiNQnpSJUes7u5sqBZhp {
  border-bottom: 0.07rem solid transparent;
  border-left: 0.07rem solid red;
  border-right: 0.07rem solid transparent;
  height: 0;
  width: 0;
}
.e8siic_ICXmLcAr12pgU dl, .e8siic_ICXmLcAr12pgU ol, .e8siic_ICXmLcAr12pgU p, .e8siic_ICXmLcAr12pgU ul {
  margin-bottom: 0;
}
</style>
