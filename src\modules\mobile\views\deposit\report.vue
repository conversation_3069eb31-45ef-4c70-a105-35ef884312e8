<script>
import {mdate} from "@/mixins/mdate";
import {deposit} from "@/mixins/deposit";
import GameListItem from "@/modules/mobile/views/index/components/GameListItem.vue";

export default {
  name: "report",
  components: {GameListItem},
  mixins: [mdate, deposit],
  data() {
    return {
      showChooseType: false,
    }
  },
  methods: {
    actionChange(picker, value, index) {
      this.form.paymentIndex = index
    },
    typeChange(picker, value, index) {
      this.form.status = index
    },
  }
};
</script>

<template>
  <div class="pop_root" style="background-color: white; height: 100vh">
    <van-sticky>

      <div class="mc-header-wrap">
        <div id="mc-header" class="mc-navbar-blue mc-vouReport am-navbar am-navbar-light">
          <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use>
              </svg></span></span>
          </div>
          <div class="am-navbar-title">{{ $t('load_record') }}</div>
          <div class="am-navbar-right">
            <div class="to-record">
              <router-link to="/m/withdrawReport">
                <svg class="am-icon am-icon-withdraw_record_c986adfb am-icon-md am-navbar-title">
                  <use xlink:href="#withdraw_record_c986adfb"></use>
                </svg>
              </router-link>
            </div>
          </div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <div class="tabPane-tips mc-filter-container" style="top: calc(1.88rem + (var(--safe-area-inset-bottom)));">
        <div style="width: 100%">
          <div class="am-flexbox am-flexbox-align-middle" style="width: 100%">
            <span class="tabPane-span filter-tabPane-btn" @click="showPicker = true">{{ form.paymentIndex !== 0 && paymentActions[form.paymentIndex] ? paymentActions[form.paymentIndex].paymentName : $t('All') }}</span>
            <span class="tabPane-span filter-tabPane-btn" @click="showChooseType = true">{{ form.status === 0 ? $t('label_type') : statusActions[form.status] }}</span>
            <div class="flex-shrink1 filter-time-btn" @click="show = true">
              <a role="button" class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
                  aria-disabled="false" style="flex: 0 0 50%">
                <svg class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs" aria-hidden="true">
                  <use xlink:href="#calendar_c4db3b67"></use>
                </svg>
                <span>{{ date }}</span></a>
            </div>
            <div translate="button_search" class="button button-submit tabPane-span" :class="{ processing: processing }" @click="search(false)">
              {{ $t("button_search") }}
            </div>
            <!--          <span class="tabPane-span filter-tabPane-btn" @click="search">-->
            <!--            {{ $t('button_search') }}</span>-->
            <van-action-sheet get-container=".pop_root" v-model="showPicker" :title="$t('deposit_method')">
              <van-picker :columns="this.paymentActions.map(item => item.paymentName)" @change="actionChange"/>
            </van-action-sheet>
            <van-action-sheet get-container=".pop_root" v-model="showChooseType" :title="$t('label_type')">
              <van-picker :columns="statusActions" @change="typeChange"/>
            </van-action-sheet>
             <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" get-container=".pop_root" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
                :max-date="maxDate" :allowSameDay="true"/>
          </div>
        </div>
      </div>
      <div class="am-tabs am-tabs-top">
        <div class="am-tabs-bar" role="tablist">
          <div class="am-tabs-tab active" data-bs-toggle="tab" data-bs-target="#tab-0" @click="setDate(0)">
            <span class="am-badge">{{ $t('today') }}</span>
          </div>
          <div class="am-tabs-tab" data-bs-toggle="tab" data-bs-target="#tab-1" @click="setDate(1)">
          <span class="am-badge">{{ $t('yesterday') }}
          </span>
          </div>
          <div class="am-tabs-tab" data-bs-toggle="tab" data-bs-target="#tab-2" @click="setDate(7)">
          <span class="am-badge">{{ $t('week') }}
          </span>
          </div>
        </div>
      </div>
    </van-sticky>

    <div class="am-tabs-content am-tabs-content-animated">
      <div class="am-tabs-tabpane" style="margin-top: 1.18rem; overflow: unset">
        <div class="mc-trans-record-container voucher">
          <div class="deposit-record-root">
            <div v-if="records.length" class="list-wrapper">
              <ul class="">

                <van-list
                    v-model="loading"
                    :finished="finished"
                    @load="search(true)"
                    offset="100"
                    :immediate-check="false"
                >
                  <li v-for="(item, index) in records" :key="index" style="margin-bottom: .15rem">
                    <div class="records-root other vou">
                      <div class="am-card">
                        <div class="am-card-body">
                          <div class="card-header">
                            <div><span class="card-header-span">{{ item.paymentName }}</span></div>
                            <div class="time" style="white-space: nowrap;">{{ item.chargeTime | datetimeFormat }}</div>
                          </div>
                          <div class="card-body">
                            <p class="clearfix deposit-id">{{ $t('deposit_id') }}：{{ item.sequenceId }}
                              <svg class="am-icon am-icon-copy_2a930dd0 am-icon-md">
                                <use xlink:href="#copy_2a930dd0"></use>
                              </svg>
                            </p>
                            <!--                              <p class="clearfix">{{ $t('load_message') }}：&lt;!&ndash; /react-text &ndash;&gt;</p>-->
                            <p class="clearfix">{{ $t('arrival_time') }}：{{ item.comfirmTime | datetimeFormat }}</p>
                            <p class="clearfix">{{ $t('fees') }}：{{ item.chargeAmount - item.comfirmAmount | currency }}</p>
                            <!--                              <p class="clearfix">{{ $t('activity') }}：</p>-->
                            <p class="clearfix">{{ $t('remarks') }}：{{ item.remark }}</p>
                          </div>
                          <div class="card-footer">
                            <ul>
                              <li><p>{{ $t('application_amount') }}</p>
                                <p class="requestedAmount">{{ item.chargeAmount | currency }}</p></li>
                              <li>
                                <p>{{ $t('amount_received') }}</p>
                                <p>{{ item.comfirmAmount | currency }}</p></li>
                              <li><p>{{ $t('label_status') }}</p>
                                <p class="col2">{{ statusActions[item.status] }}</p></li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                  <template #loading>
                    <div class="scroll-loading">
                      <svg
                          class="loading-icon"
                          x="0px"
                          y="0px"
                          width="40px"
                          height="40px"
                          viewBox="0 0 40 40"
                      >
                        <path
                            opacity="0.2"
                            d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                        ></path>
                        <path
                            d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                        >
                          <animateTransform
                              attributeType="xml"
                              attributeName="transform"
                              type="rotate"
                              from="0 20 20"
                              to="360 20 20"
                              dur="0.5s"
                              repeatCount="indefinite"
                          ></animateTransform>
                        </path>
                      </svg>
                    </div>
                  </template>
                </van-list>

                <!--                <li><div class="records-root other vou"><div class="am-card"><div class="am-card-body"><div class="card-header"><div><span class="card-header-span">USDT</span></div><div class="time" style="white-space: nowrap;">2023-08-10 14:14:01</div></div><div class="card-body"><p class="clearfix deposit-id">&lt;!&ndash; react-text: 1816 &ndash;&gt;Ref # do depósito&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1817 &ndash;&gt;：&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1818 &ndash;&gt;21357492748&lt;!&ndash; /react-text &ndash;&gt;<svg class="am-icon am-icon-copy_2a930dd0 am-icon-md"><use xlink:href="#copy_2a930dd0"></use></svg></p><p class="clearfix">&lt;!&ndash; react-text: 1822 &ndash;&gt;PostScript&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1823 &ndash;&gt;：&lt;!&ndash; /react-text &ndash;&gt;</p><p class="clearfix">&lt;!&ndash; react-text: 1825 &ndash;&gt;Tempo recebido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1826 &ndash;&gt;：&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1827 &ndash;&gt;2023-08-10 14:17:03&lt;!&ndash; /react-text &ndash;&gt;</p><p class="clearfix">&lt;!&ndash; react-text: 1829 &ndash;&gt;Taxa de manuseio&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1830 &ndash;&gt;：&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1831 &ndash;&gt;0.00&lt;!&ndash; /react-text &ndash;&gt;</p><p class="clearfix">&lt;!&ndash; react-text: 1833 &ndash;&gt;Atividade&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1834 &ndash;&gt;：&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1835 &ndash;&gt;-&lt;!&ndash; /react-text &ndash;&gt;</p><p class="clearfix">&lt;!&ndash; react-text: 1837 &ndash;&gt;Observações&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1838 &ndash;&gt;：&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1839 &ndash;&gt;2.040000USDT*4.89=9.97BRL&lt;!&ndash; /react-text &ndash;&gt;</p></div><div class="card-footer"><ul><li><p>Solicitar</p><p class="requestedAmount">10.00</p></li><li><p>&lt;!&ndash; react-text: 1847 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 1848 &ndash;&gt;Valor recebido&lt;!&ndash; /react-text &ndash;&gt;</p><p>9.97</p></li><li><p>Status</p><p class="col2">Aprovado</p></li></ul></div></div></div></div></li>-->

              </ul>
              <!--              <div class="pullup-wrapper"></div>-->
            </div>
            <div class="nodata-container" v-else>
              <svg class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon">
                <use xlink:href="#nodata_f4c19c2d"></use>
              </svg>
              <p class="">{{ $t('no_data') }}</p>
            </div>
          </div>
          <!--          <div class="records-footer hide">-->
          <!--            <p>-->
          <!--              <span>Montante total-->
          <!--              </span><span>-</span>-->
          <!--            </p>-->
          <!--          </div>-->
        </div>
      </div>
    </div>

  </div>
</template>

<style scoped>
.am-tabs-bar .active {
  color         : #108ee9;
  box-shadow    : none;
  border        : 0;
  border-bottom : .04rem solid #108ee9;
}

.am-list-header {
  line-height : 1;
}

.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}
</style>
