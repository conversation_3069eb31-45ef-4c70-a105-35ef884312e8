<script>
import {mdate} from "@/mixins/mdate";
import {bet} from "@/mixins/bet";

export default {
  mixins: [mdate, bet],
  data() {
    return {
      showPicker: false,
      showChooseStatus: false,
    }
  },
  methods: {
    typeChange(picker, value, index) {
      this.platformIndex = index;
      !index && (this.form.status = 0)
    },
    statusChange(picker, value, index) {
      this.form.status = index
    },
  }
};
</script>
<template>
  <div class="mc-game-record-root">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-gameRecord am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('in_betting_record') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
<!--    <div>-->
<!--      <div>-->
        <div class="am-tabs am-tabs-top i18n-fix mc-tabs-scroll">
<!--          <div class="mc-tab-container" id="mc-tab-container">-->
<!--            <div class="mc-tab-item"-->
<!--                :class="{ 'tab-active': categoryId === item.categoryId }"-->
<!--                v-for="(item, index) in $store.state.platform.categories"-->
<!--                :key="index"-->
<!--                @click="refreshVendor(item.categoryId)">-->
<!--              <span>{{ $t(item.categoryName) }}</span>-->
<!--            </div>-->
<!--          </div>-->
          <div
              class="am-tabs-content am-tabs-content-animated"
              style="transform: translateX(0%) translateZ(0px)"
          >
            <div
                role="tabpanel"
                aria-hidden="false"
                class="am-tabs-tabpane am-tabs-tabpane-active"
            >
<!--              <div>-->
                <div
                    class="am-flexbox am-flexbox-align-middle mc-trans-filter mc-filter">
                  <div class="am-flexbox am-flexbox-align-middle mc-trans-filter">

                    <div class="flex-shrink1 filter-time-btn" @click="show = true">
                      <a
                          role="button"
                          class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
                          aria-disabled="false"
                          style="flex: 0 0 50%">
                        <svg
                            class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs"
                            aria-hidden="true">
                          <use xlink:href="#calendar_c4db3b67"></use>
                        </svg>
                        <span>{{ date }}</span> </a>
                    </div>
                    <a
                        @click="showPicker = true"
                        v-if="$store.state.platform.platforms[platformIndex]"
                        role="button"
                        class="am-button am-button-ghost am-button-small am-button-inline"
                        aria-disabled="false"> <span>{{ $store.state.platform.platforms[platformIndex].platformName }}</span> </a>
                    <span class="tabPane-span filter-tabPane-btn" v-if="platformIndex" @click="showChooseStatus = true">{{ types[form.status] }}</span>
                    <div translate="button_search" class="button button-submit tabPane-span" :class="{ processing: processing }" @click="search(false)">
                      {{ $t("button_search") }}
                    </div>
                     <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" get-container=".mc-game-record-root" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
                        :max-date="maxDate" :allowSameDay="true"/>
                    <van-action-sheet get-container=".pop_root" v-model="showChooseStatus" :title="$t('label_type')">
                      <van-picker :columns="types" @change="statusChange"/>
                    </van-action-sheet>
                    <van-action-sheet get-container=".mc-game-record-root" v-model="showPicker" :title="$t('choose_vendors')">
                      <van-picker :columns="this.$store.state.platform.platforms.map(item => item.platformName)" @change="typeChange"/>
                    </van-action-sheet>
                  </div>
                </div>
                <div>
                  <div class="" style="overflow: hidden">
                    <div
                        class="list-wrapper"
                        style="height: calc(100 * var(--vh, 1vh) - 0.3rem - var(--safe-area-bottom)); overflow: scroll"
                    >
                      <ul
                          class="scroll-inner-container"
                      >
                        <template v-if="records.length">
                          <van-list
                              v-model="loading"
                              :finished="finished"
                              @load="search(true)"
                              :immediate-check="false"

                          >
                            <li v-for="(item, index) in records" :key="index">
                              <div class="lott-records-root">
                                <div class="records-item-header-sy">
                                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 0.5rem; width: 100%;">
                                    <div class="am-flexbox am-flexbox-dir-row am-flexbox-align-middle">
                                      <i style="width: 0.2rem; height: 0.2rem; margin-right: 0.2rem; display: inline-block; border-radius: 50%; background: rgb(16, 142, 233);"></i>
                                      <div class="mc_common_overflow" style="font-weight: 600;">{{ item.gameName }}</div>
                                    </div>
                                    <div class="record_date" style="font-size: 0.26rem;">{{ item.createTime | datetimeFormat }}</div>
                                  </div>
                                </div>
                                <div class="records-item-content-sy">
                                  <div class="am-flexbox am-flexbox-justify-between am-flexbox-align-top"></div>
                                  <div class="am-flexbox am-flexbox-justify-between am-flexbox-align-top">
                                  <span>{{ $t('order_number') }}:</span>
                                    <span class="order-number-group">
                                    <span class="title">{{ item.orderId }}</span>
                                    <svg class="am-icon am-icon-icon-copy_f562461f am-icon-md copy-btn" :data-clipboard-text="item.orderId"><use xlink:href="#icon-copy"></use></svg>
                                  </span>
                                  </div>
                                  <div class="am-flexbox am-flexbox-justify-between am-flexbox-align-middle">
                                    <span>{{ $t('label_bet_money') }}: {{ item.betAmount | currency }}</span>
                                    <span>{{ $t("label_awarded") }}: {{ item.settleAmount | currency }}</span>
                                  </div>
                                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle">
                                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-between am-flexbox-align-top">
                                      <span class="time">{{ $t('label_profit_loss') }}</span><span class="amount-negative-number record-amount">{{ (item.settleAmount - item.betAmount) | currency }}</span>
                                    </div>
                                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top">
                                      <span class="time">{{ $t("label_company") }}</span><span class="amount-zero-number record-amount">{{ item.PlatformName }}</span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </li>
                            <template #loading>
                              <div class="scroll-loading">
                                <svg
                                    class="loading-icon"
                                    x="0px"
                                    y="0px"
                                    width="40px"
                                    height="40px"
                                    viewBox="0 0 40 40"
                                >
                                  <path
                                      opacity="0.2"
                                      d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                                  ></path>
                                  <path
                                      d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                                  >
                                    <animateTransform
                                        attributeType="xml"
                                        attributeName="transform"
                                        type="rotate"
                                        from="0 20 20"
                                        to="360 20 20"
                                        dur="0.5s"
                                        repeatCount="indefinite"
                                    ></animateTransform>
                                  </path>
                                </svg>
                              </div>
                            </template>
                          </van-list>

                        </template>
                        <div class="nodata-container" v-else>
                          <svg
                              class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon"
                          >
                            <use xlink:href="#nodata_f4c19c2d"></use>
                          </svg>
                          <p class="">{{ $t('no_data') }}</p>
                        </div>
                      </ul>
                      <div class="pullup-wrapper"></div>
                    </div>
                  </div>
                </div>
<!--              </div>-->
            </div>
          </div>
        </div>
        <div>
          <div class="record-footer hide" v-if="!(show || showPicker)">
            <div
                class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle total-money"
            >
              <div
                  class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top total-money-item"
              >
                <span class="time">{{ $t('label_bet_money') }}</span
                ><span
                  style="
                    font-weight: 700;
                    font-size: 0.24rem;
                    color: rgb(255, 0, 0);
                  "
              >{{ total.bet | currency }}</span
              >
              </div>
              <div
                  class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top total-money-item"
              >
                <span class="time">{{ $t('label_prize_amount') }}</span
                ><span
                  style="
                    font-weight: 700;
                    font-size: 0.24rem;
                    color: rgb(255, 0, 0);
                  "
              >{{ total.award | currency }}</span
              >
              </div>
              <div
                  class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top total-money-item"
              >
                <span class="time">{{ $t('label_profit_loss') }}</span
                ><span
                  style="
                    font-weight: 700;
                    font-size: 0.24rem;
                    color: rgb(255, 0, 0);
                  "
              >{{ total.profit | currency }}</span
              >
              </div>
            </div>
          </div>
        </div>
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<style scoped>

.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}
</style>
