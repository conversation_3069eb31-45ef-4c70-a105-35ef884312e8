<script>
import { ROUTE_PLATFORM_HELPDETAILS } from "@/api";
import {lang} from "@/mixins/lang";

export default {
  name: "index",
  mixins: [lang],
  data() {
    return {
      currentHelp: {},
      content: "",
    };
  },
  computed: {
    currentData() {
      let row = this.$store.state.helper[this.$store.state.helpIndex];
      let item = this.changeLang(row.details);
      if (item.contents) {
        return item;
      }
      return this.getDetails();
    },
  },
  methods: {
    async getDetails() {
      let res = await this.$protoApi(ROUTE_PLATFORM_HELPDETAILS, {
        channel: this.$store.state.channel,
        device: this.$store.state.device,
        category:
          this.$store.state.helper[this.$store.state.helpIndex].category,
      }).catch(() => {
        return {};
      });
      this.$store.commit("updateHelp", res.help);
      return this.changeLang(res.help.details);
    },
  },
};
</script>

<template>
  <div class="help-container">
    <div id="page_bg" class="common"></div>
    <van-sticky>
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-withdrawReport am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
          </div>
          <div class="am-navbar-title">{{ $t('help_center') }}</div>
          <div class="am-navbar-right">
            <div class="to-record">
            </div>
          </div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
    </van-sticky>
    <div class="help-content" style="padding-top: .3rem">
      <div class="help-menu">
        <template v-for="(item, index) in this.$store.state.helper">
          <div
              class="menu-item"
              @click="setHelpIndex(index)"
              :key="index"
              :class="{ on: index === $store.state.helpIndex }"

          >
            <span>{{ changeLang(item.details).title }}</span>
          </div>
        </template>
      </div>
      <div class="help-main" ref="helpContent">
        <div class="main-item on" v-html="currentData.contents"></div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
::v-deep h1  {
  font-size: 1.6em;
  color: #da394f;
  font-weight: 700;
  margin: .2rem 0;
}
::v-deep h2  {
  font-size: 1.4em;
  font-weight: 600;
  color: #da394f;
  margin: .2rem 0;
}
::v-deep h3  {
  font-size: 1.2em;
  font-weight: 500;
  color: #da394f;
  margin: .2rem 0;
}

::v-deep li {
  list-style: inside;
}
::v-deep img {
  width: 100%;
}
</style>
