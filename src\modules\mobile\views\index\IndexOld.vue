<script>
import DownloadBar from "@/modules/mobile/components/modal/download-bar.vue";
import HomeItem from "./components/HomeItem.vue";
import HotItem from "@/modules/mobile/views/index/components/HotItem.vue";
import Winner from "./components/Winner.vue";
import VenderItem from "@/modules/mobile/views/index/components/VenderItem.vue";
import { lang } from "@/mixins/lang";
import { menu } from "@/mixins/menu";
import GameListCenter from "@/modules/mobile/views/index/components/GameListCenter.vue";
import PlatformListCenter from "@/modules/mobile/views/index/components/PlatformListCenter.vue";
import PlatformVenderItem from "@/modules/mobile/views/index/components/PlatformVenderItem.vue";
import PlatformHomeItem from "@/modules/mobile/views/index/components/PlatformHomeItem.vue";
import AppGameCategory from "@/modules/mobile/components/AppGameCategory.vue";
import AppMemberInfo from "@/modules/mobile/components/AppMemberInfo.vue";

export default {
  components: {
    AppMemberInfo,
    AppGameCategory,
    PlatformHomeItem,
    PlatformVenderItem,
    PlatformListCenter,
    GameListCenter,
    VenderItem,
    Winner,
    HotItem,
    HomeItem,
    DownloadBar,
  },
  mixins: [lang, menu],
  data() {
    return {
      iconVendors:   [
        "PG-GRAY",
        // "EG2-GRAY",
        // "PT-GRAY",
        // "JDB-GRAY",
        "CQ9-GRAY",
        // "FC-GRAY",
        "JL-GRAY",
        // "BGS-GRAY",
          "AG",
          "BBIN",
          "CMD",
          "DG",
          "PP",
          "SABA",
          "WM",
          "AE",
      ],
      swiperOptions: {
        loop: true,
        speed: 1000,
        autoplay: {
          delay: 5000,
          disableOnInteraction: false,
        },
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        //
        observer: true,
        observeParents: !0,



        centeredSlides : true,
        watchSlidesProgress : true,
        on: {
          click: (e) => {
            let banner_id = e.target.dataset.id;
            this.goDetail(banner_id);
            this.event_bannerClick(banner_id)
          },
        }
      },
    };
  },
  computed: {
    banners: function () {
      return this.$store.state.banners.filter(function (e) {
        return e.banner;
      });
    },
  },
  mounted() {
    if (this.$store.state.mobile.header.showDrop) {
      this.$store.commit("setMobileHeadShowDrop");
    }
    // this.$refs.scrollContainer.scrollLeft = this.$store.state.menu.scrollPosition
  },
  methods: {
    goHelp(index) {
      this.setHelpIndex(index);
      this.$router.push("/m/help");
    },
   /* recordScrollPosition() {
      this.$store.commit("setMenuScrollPosition", this.$refs.scrollContainer.scrollLeft);
    },*/
    noticeModal(index) {
      this.$store.commit("setNoticeIndex", index);
      this.$router.push({
        path: "/m/notice",
      });
    },
  },
};
</script>

<template>
  <div id="homePage" class="home-container">
    <div id="mc-app-home-root">
      <div class="mc_app_box">
        <div class="banner_notice">
<!--          <div class="notice_root">
            <div class="notice_bg">
              <i class="notice_icon"></i>
              <div class="marquee">
                <div
                    class="marquee"
                    style="
                    &#45;&#45;play: running;
                    &#45;&#45;direction: normal;
                    &#45;&#45;duration: 48.1891s;
                    &#45;&#45;delay: 0s;
                    &#45;&#45;iteration-count: infinite;
                  "
                >
                  <div class="marquee_content">
                    <span
                        class="content-item"
                        v-for="(item, index) in $store.state.notices"
                        :key="index"
                        @click="noticeModal(index)"
                    >{{ changeLang(item.messages).title }}</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>-->
          <div class="home-banner">
            <div class="page-banner" v-if="banners.length">
              <swiper
                key="mhome"
                ref="mySwiper"
                class="swiper-wrap"
                :options="swiperOptions"
              >
                <swiper-slide
                  v-for="(item, index) in banners"
                  :key="'banner_' + index"
                >
                  <div class="swiper-inner">
                    <img
                      :src="changeLang(item.messages).imageUrl"
                      :data-id="item.id"
                      alt=""
                      style="height: unset"
                    />
                  </div>
                </swiper-slide>
                <div
                  class="swiper-pagination"
                  slot="pagination"
                  v-if="banners.length > 1"
                ></div>
              </swiper>
            </div>
          </div>
        </div>
        <div class="home-content">
          <div class="game-enter-container">
            <div class="game-enter-content">
<!--              <div style="background-image: url('/m/icons/nen-info.bf0ea25e2da22157.png');">-->
              <div style="background: linear-gradient(0deg, rgba(77, 77, 77, 0.63), rgba(24,24,24,0.63))">
<!--                <app-member-info />-->
                <app-game-category />
              </div>
              <div class="game-content-wrap">
                <div class="game-tabs-content">
                  <div class="content-bg home">
                    <div
                      v-if="
                        ['-3'].includes($store.state.platform.currentCategory)
                      "
                    >
                        <HotItem/>
                        <template
                          v-for="(item, index) in $store.state.platform.games"
                        >
                          <VenderItem
                            :key="item.categoryId"
                            v-if="
                              'vendor' === menuOptions[item.categoryId].dataType
                            "
                            :params="item"
                          />
                          <HomeItem
                            v-else
                            :key="item.categoryId"
                            :params="item"
                          />
                        </template>
                      <!--                      <winner />-->
                      <div class="home-footer-container">
                        <div class="home-footer-content">
                          <div class="footer-help hide">
                            <div class="help-content">
                              <div class="help-nav">
                                <template
                                  v-for="(item, index) in $store.state.helper"
                                >
                                  <div
                                    class="nav-item"
                                    @click="goHelp(index)"
                                    :key="index"
                                    v-if="
                                      ['deposit', 'withdraw'].includes(
                                        item.category
                                      )
                                    "
                                  >
                                    <span class="nav-name">{{
                                      changeLang(item.details).title
                                    }}</span>
                                  </div>
                                </template>
                              </div>
                            </div>
                          </div>
                          <div class="game-provider">
                            <img
                              class="vendor-img"
                              v-for="(item, index) in iconVendors"
                              :key="index"
                              :src="'img/vendor/' + item + '.png'"
                              alt=""
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                        v-else-if="
                        ['-4'].includes($store.state.platform.currentCategory)
                      "
                    >
                      <PlatformListCenter :key="$store.state.platform.currentCategory + $store.state.playing"/>
                      <template v-if="$store.state.platform.platformGames.length">
                        <template
                            v-for="(item, index) in $store.state.platform.platformGames"
                        >
                          <PlatformVenderItem
                              :key="item.categoryId"
                              v-if="
                              'vendor' === menuOptions[item.categoryId].dataType
                            "
                              :params="item"
                          />
                          <PlatformHomeItem
                              v-else
                              :key="item.categoryId"
                              :params="item"
                          />
                        </template>
                        <div style="height: calc(1rem + (var(--safe-area-inset-bottom)))"></div>
                      </template>
                      <van-empty image="img/no-data.png" v-else />
                    </div>
                    <GameListCenter v-else :key="$store.state.platform.currentCategory + $store.state.playing"/>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- react-empty: 63 -->
    <download-bar v-if="$store.state.mobile.modal.downloadBar" />
    <div id="page_bg" class="home-bg"></div>
  </div>
</template>

<style scoped></style>