<script>
import {menu} from "@/mixins/menu";
import {events} from '@/mixins/tdtc/events'

export default {
  mixins: [menu, events],
  data() {
    return {
      swiperOptions: {
        slidesPerView: 4,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
    }
  }
}
</script>

<template>
  <section style="position: relative; margin-top: .1rem;">
    <div style="background-image: url(/img/activity/titile_bg.png); background-repeat: no-repeat;
    background-size: contain; margin-left: .2rem; width: 2.07rem;height: 0.39rem;
    font-size: 0.22rem;color: #FFFADD;line-height: .39rem;padding-left: .2rem;">
      Thưởng Chính
    </div>
    <swiper :options="swiperOptions" style="padding-top: .2rem;">
      <swiper-slide
          v-for="(item, i) in activityTypes" :key="i"
      >
        <div class="swiper-item" @click="go(item)">
          <img style="height: .8rem" :src="`/img/activity/swiper/${item}.png`" alt="">
          <span style="color: #ffdf7f; padding: 0 .2rem; text-align: center; font-size: .17rem">{{ $t(`events_page.type.${item}`) }}</span>
        </div>
      </swiper-slide>

    </swiper>
    <!--    <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>-->
    <!--    <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>-->
  </section>
</template>

<style scoped lang="scss">
.swiper-item {
  width: 100%;
  height: 100%;
  display         : flex;
  flex-direction  : column;
  justify-content : center;
  align-items     : center;
  font-weight: 400;
  font-size: 0.19rem;
  color: #FFFFFF;

  svg {
    height : .52rem;
    margin-bottom: .2rem;
  }
}

.swiper-item.on {
  color: rgb(255 182 39 / 1);
}

.gradient-text {
  background: linear-gradient(0deg, #FFC768 31.201171875%, #FFF4AB 54.7607421875%, #FFF4AB 100%);
  -webkit-background-clip: text; /* 背景裁剪为文字 */
  background-clip: text;
  color: transparent; /* 文字颜色透明，显示背景渐变 */
  background-attachment: fixed;
}
</style>
