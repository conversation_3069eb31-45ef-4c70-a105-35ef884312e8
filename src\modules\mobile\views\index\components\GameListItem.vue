<script>
import { game } from "@/mixins/game";
import { fav } from "@/mixins/fav";
import { play } from "@/mixins/play";
import { check } from "@/mixins/check";

export default {
  name: "GameListItem",
  props: { game: { type: Object, required: true } },
  mixins: [game, fav, play, check],
  data() {
    return {
      loaded: false,
    }
  },
  mounted() {
    // iconsole(this.props.game)
  }
};
</script>

<template>
  <li
    class="game-list-item"
    v-if="checkPlatAndGameStatus(game)"
    @click="startMobileGame(game)"
  >
    <div class="game-list-item">
      <div class="game-background">
        <span
          class="lazy-load-image-background blur" :class="{'lazy-load-image-loaded': loaded}"
          style="color: transparent; display: inline-block"
          >
          <img class="img-loading" fetchpriority="low" :src="game.icon" :key="game.gameId+''+game.platformId" @load="loaded = true"/>
        </span>
        <div class="game-fav" v-if="maintenanceCheck(game)">
          <img src="img/wh.png" alt="">
        </div>
        <div class="game-fav" v-else @click.stop="toggleFav()">
          <svg
            class="am-icon am-icon-game-fav-active am-icon-md favorite_icon on"
            v-if="isFav"
          >
            <use xlink:href="#game-fav-active"></use>
          </svg>
          <svg
            class="am-icon am-icon-game-fav-normal am-icon-md favorite_icon"
            v-else
          >
            <use xlink:href="#game-fav-normal"></use>
          </svg>
        </div>
      </div>
      <div class="game-item-name">
        {{ game.gameName }}
      </div>
    </div>
  </li>
</template>

<style scoped></style>
