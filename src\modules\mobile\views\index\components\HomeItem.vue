<script>
import { play } from "@/mixins/play";
import { check } from "@/mixins/check";
import GameListItem from "./GameListItem.vue";
import VenderItem from "@/modules/mobile/views/index/components/VenderItem.vue";
import {menu} from "@/mixins/menu";
import {scrollToTop} from '@/utils/common'

export default {
  name: "HomeItem",
  methods: {scrollToTop},
  components: { VenderItem, GameListItem },
  props: {
    params: {
      type: Object,
    },
  },
  mixins: [play, check, menu],
};
</script>

<template>
  <div class="home-game-list">
    <div class="game-title">
      <div class="title-content">
        <div class="title-text">
<!--          <img
            class="game-icon"
            style="width: auto"
            :src="menuItem(params.categoryId).icon"
            alt=""
          />-->
          <span>{{ menuItem(params.categoryId).title }}</span>
        </div>
        <span
          class="see-all"
          @click="$store.commit('setCurrentCategory', params.categoryId);scrollToTop()"
          >{{ $t("more") }}</span
        >
      </div>
    </div>
    <ul class="game-list-wrap">
      <template v-for="(item, index) in params.games.slice(0, 6)">
        <GameListItem  :game="item" :key="index" />
      </template>
    </ul>
  </div>
</template>

<style scoped></style>