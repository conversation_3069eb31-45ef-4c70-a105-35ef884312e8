<script>
import flvjs from 'flv.js'

export default {
  name: "LiveTab",
  data() {
    return {
      active: 2,
      players: {},
    };
  },
  computed: {

    games() {
      return this.$store.state.platform.hotGames.games.filter(item => item.liveUrl);
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initPlayers();
    });
  },
  beforeDestroy() {
    // 销毁所有播放器实例
    Object.values(this.players).forEach(player => {
      if (player) {
        player.destroy();
      }
    });
  },
  watch: {
    active(newVal, oldVal) {
      // 切换tab时暂停其他播放器，播放当前播放器
      this.handleTabChange(newVal, oldVal);
    }
  },
  methods: {
    initPlayers() {
      if (!flvjs.isSupported()) {
        console.error('FLV.js is not supported in this browser');
        return;
      }

      this.games.forEach((game, index) => {
        const videoElement = this.$refs[`video_${index}`]?.[0];
        if (videoElement && game.liveUrl) {
          const player = flvjs.createPlayer({
            type: 'flv',
            url: game.liveUrl,
            isLive: true
          });

          player.attachMediaElement(videoElement);
          player.load();

          // 只播放当前激活的tab
          if (index === this.active) {
            player.play().catch(err => {
              console.error('播放失败:', err);
            });
          }

          this.players[index] = player;
        }
      });
    },

    handleTabChange(newIndex, oldIndex) {
      // 暂停旧的播放器
      if (this.players[oldIndex]) {
        this.players[oldIndex].pause();
      }

      // 播放新的播放器
      if (this.players[newIndex]) {
        this.players[newIndex].play().catch(err => {
          console.error('播放失败:', err);
        });
      }
    }
  }
}
</script>

<template>
  <div class="tabs-bottom" v-if="games.length">
    <van-tabs v-model="active" swipeable>
      <van-tab v-for="(item, index) in games" :key="index" :title="item.gameName">
<div class="live-container">
          <video
            :ref="`video_${index}`"
            class="live-video"
            controls
            muted
            playsinline
            :poster="item.livePic"
          >
            Trình duyệt của bạn không hỗ trợ video
          </video>
        </div>
      </van-tab>
    </van-tabs>
  </div>

</template>

<style scoped>
.tabs-bottom .van-tabs {
  display: flex;
  flex-direction: column-reverse;
  padding: 0 .15rem .2rem;

}

.tabs-bottom .van-tabs .van-tab__pane {
  height: auto;
  min-height: 2.2rem;
  border-radius: 0.15rem;
  overflow: hidden;
  background: unset;
}

.live-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
    border-radius: 0.15rem;
    background: unset;
    overflow: hidden;
}

.live-video {
  width: 100%;
  height: 2.2rem;
  object-fit: cover;
  display: block;
}

.tabs-bottom ::v-deep .van-tabs__line {
  bottom: unset;
}

.tabs-bottom ::v-deep .van-tab {
  color: rgb(201,198,203);
  font-size: .2rem;
}
.tabs-bottom ::v-deep .van-tab.van-tab--active {
  color: #DF782B;
}

.tabs-bottom ::v-deep .van-tabs__line {
  background-color: #DF782B;
}



</style>