<script>
import { play } from "@/mixins/play";
import { check } from "@/mixins/check";
import GameListItem from "./GameListItem.vue";
import VenderItem from "@/modules/mobile/views/index/components/VenderItem.vue";
import {menu} from "@/mixins/menu";

export default {
  components: { VenderItem, GameListItem },
  props: {
    params: {
      type: Object,
    },
  },
  data() {
    return {
      more: false,
    }
  },
  mixins: [play, check, menu],
  computed: {
    games() {
      if (this.more) {
        return this.params.games;
      } else {
        return this.params.games.slice(0, 9);
      }
    }
  }
};
</script>

<template>
  <div class="home-game-list" v-if="games.length">
    <div class="game-title">
      <div class="title-content">
        <div class="title-text">
<!--          <img
            class="game-icon"
            style="width: auto"
            :src="menuItem(params.categoryId).icon"
            alt=""
          />-->
          <span>{{ menuItem(params.categoryId).title }}</span>
        </div>
        <span class="see-all" @click="more = !more">{{ !more ? $t("more") : $t("less") }}</span>
      </div>
    </div>
    <ul class="game-list-wrap">
      <template v-for="(item, index) in games">
        <GameListItem  :game="item" :key="index" />
      </template>
    </ul>
  </div>
</template>

<style scoped></style>