<script>
import {menu} from "@/mixins/menu";
export default {
  mixins: [menu],
};
</script>

<template>
  <div class="game-list-center-container" style="padding-bottom: 0;">
    <div class="game-select" style="position: static">
      <div class="select-bar hide-scrollbar">
        <div
            class="select-btn"
            v-for="(item, index) in $store.state.platform.gamePlatforms"
            :key="item.platformId"
            :class="{ on: $store.state.platform.currentPlatformIndex === index }"
            @click="$store.commit('setPlatformPlatform', index);loadGame()"
        >
          {{ item.platformName }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
::v-deep li {
  list-style: none;
}
</style>