<script>
import {menu} from "@/mixins/menu";
import {check} from "@/mixins/check";
import {game} from "@/mixins/game";
import VenderListItem from '@/modules/mobile/views/index/components/VenderListItem.vue'
import {scrollToTop} from '@/utils/common'

export default {
  name: "VenderItem",
  methods: {scrollToTop},
  components: {VenderListItem},
  mixins: [menu, check, game],
  props: {
    params: {
      type: Object,
    },
  },
}
</script>

<template>
  <div class="home-game-container">
    <div class="vendor-game-wrap">
      <div class="game-title">
        <div class="title-content">
          <div class="title-text">
<!--            <img
                class="game-icon sports"
                :src="menuItem(params.categoryId).icon"
                alt=""
            />-->
            <span>{{ menuItem(params.categoryId).title }}</span>
          </div>
          <span class="see-all" @click="$store.commit('setCurrentCategory', params.categoryId);scrollToTop()">{{ $t("more") }}</span>
        </div>
      </div>
      <div class="vendor-game-scroll hide-scrollbar">
        <div class="vendor-game-list home-page">
          <template v-for="(item, index) in params.games.slice(0, 6)">
            <VenderListItem :game="item" :key="index" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.game-fav {
  position: absolute;
  right: 0;
  top: 0;
  width: 0.45rem;
  height: 0.45rem;
  border-radius: 0 0.16rem 0 0.16rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>