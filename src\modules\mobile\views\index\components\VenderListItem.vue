<script>
import {game} from "@/mixins/game";
import {play} from "@/mixins/play";
import {check} from "@/mixins/check";

export default {
  props:  {
    game: {
      type:     Object,
      required: true
    }
  },
  mixins: [game, play, check],
  data() {
    return {
      loaded: false,
    }
  },
};
</script>

<template>
  <div class="vendor-game-item" v-if="checkPlatAndGameStatus(game)" @click="startMobileGame(game)">
    <div class="vendor-game-img">
      <span class="lazy-load-image-background blur" :class="{ 'lazy-load-image-loaded': loaded }" style="
          color: transparent;
          display: inline-block;
        ">
        <img fetchpriority="low" :src="game.icon" class="img-loading" :key="game.gameId + '' + game.platformId"
          @load="loaded = true" />
        <div class="game-fav" v-if="maintenanceCheck(game)"><img src="img/wh.png" alt=""></div>
      </span>
    </div>
  </div>
</template>

<style scoped></style>
