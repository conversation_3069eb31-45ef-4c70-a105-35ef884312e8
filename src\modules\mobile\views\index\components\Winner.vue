<script>
import VueSeamlessScroll from "vue-seamless-scroll";
export default {
  name: "Winner",
  components: {
    vueSeamless: VueSeamlessScroll,
  },
  data() {
    return {
      classOption: {
        step: 0.5,
        limitMoveNum: 3,
      },
      menuSwiperOption: {
        loop: !0,
        direction: "vertical",
        speed: 1500,
        slidesPerView: "auto",
        freeMode: !0,
        autoplay: {
          delay: 1000,
          disableOnInteraction: !1,
        },
      },
    };
  },
  computed: {
    winner() {
      let list = [];
      for (let i = 0; i < 20; i++) {
        list[i] = {
          customerName: "****sfs",
          winAmount: "1,600.00",
        };
      }
      return list;
    },
  },
};
</script>

<template>
  <div class="winner-board">
    <div class="winner-content">
      <span class="winner-title">{{ $t("br_tuikje_lbeuhf") }}</span>
      <div class="winner-head">
        <p>{{ $t("rank_promo") }}</p>
        <p>{{ $t("winner_game") }}</p>
        <p>{{ $t("winner_money") }}</p>
      </div>
      <div class="winner-wrap">
        <div class="winner-list">
          <div
            class="winner-item"
            v-for="(item, index) in winner.slice(0, 3)"
            :key="index"
          >
            <div class="swiper-inner">
              <span class="winner-rank" :class="'rank-' + (index + 1)">{{
                index + 1
              }}</span>
              <div class="winner-info">
                <div class="win-game">
                  <div class="winner-name">{{ item.customerName }}</div>
                </div>
              </div>
              <div class="winner-amount">
                {{ $store.state.configs.currency_symbol }} {{ item.winAmount }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="winner-swiper winner-wrap">
        <vueSeamless
            class="winner-list"
            :data="winner"
            :classOption="classOption"
        >
          <div
              class="winner-item"
              v-for="(item, index) in winner.slice(3, winner.length)"
              :key="index"
          >
            <div class="swiper-inner">
              <span class="winner-rank">{{ index + 4 }}</span>
              <div class="winner-info">
                <div class="win-game">
                  <div class="winner-name">{{ item.customerName }}</div>
                </div>
              </div>
              <div class="winner-amount">
                {{ $store.state.configs.currency_symbol }} {{ item.winAmount }}
              </div>
            </div>
          </div>
        </vueSeamless>
        </div>


    </div>
  </div>
</template>