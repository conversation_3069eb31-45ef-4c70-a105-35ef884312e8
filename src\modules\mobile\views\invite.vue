<template>
  <div class="invite-friends-root" style="background-color: white;">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-inviteFriends am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('Invite_friends') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>

    <div class="invite-friends-container">
      <div class="container-wrap">
        <div class="invite-friends-info">
          <div class="info-item">
            <div class="item-title">{{ $t('friend_overview_total_amount_today') }}</div>
            <div class="item-number">
              <span class="currency">--</span>
            </div>
          </div>
          <div class="info-item">
            <div class="item-title">{{ $t('friend_overview_total_amount_yesterday') }}</div>
            <div class="item-number">
                    <span class="currency">--</span>
            </div>
          </div>
          <div class="info-item">
            <div class="item-title">{{ $t('friend_overview_member_count') }}</div>
            <div class="item-number">--</div>
          </div>
          <div class="info-item">
            <div class="item-title">{{ $t('friend_overview_qualified_count') }}</div>
            <div class="item-number">--</div>
          </div>
        </div>
        <div class="bonus-list hide" style="
                margin-top: .2rem;
                padding: 15px 7px;
                height: 4.5rem;
                border-radius: .2rem;
                opacity: .8;
                background-image: linear-gradient(112deg, #43cbff 0, #9708cc);
                background-color: rgba(0, 0, 0, .6);
">
          <div class="bonus-board"></div>
          <div class="content-bottom">
            <div style="display: flex;justify-content: space-between">
              <div class="links-bg address-wrap pa">
                          <span class="p6" style="font-size: .3rem">
                            {{ $t('Total Rebate') }}
                          </span> <span class="p6">
                            --
                          </span>
              </div>
              <div class="links-bg address-wrap pa">
                          <span class="p6" style="font-size: .3rem">
                            {{ $t('Can withdraw') }}
                          </span> <span class="p6">
                            --
                          </span>
              </div>
            </div>

            <span class="recieve-title" style="font-weight: 400;font-size: .35rem; line-height: .6rem" translate="recent_bonus">
                        {{ $t('Min Withdraw') }}: --
          </span>

            <div style="display:flex; justify-content: space-around;align-items: center; width: 100%; height: .66rem; line-height: .66rem">
              <div class="withdraw-btn" style="width: 46%;padding: unset">{{ $t('withdraw')}}</div>
              <div class="withdraw-btn" style="width: 46%;padding: unset">{{ $t('show log')}}</div>
            </div>

          </div>
          <div class="content-bottom">

          </div>
        </div>
        <div class="invite-friends-link">
          <div class="link-top-title">{{ $t('invite_share') }}
            <div class="share">
              <svg class="am-icon am-icon-share_a8ff5806 am-icon-md">
                <use xlink:href="#share_a8ff5806"></use>
              </svg>
            </div>
          </div>
          <VueQr
              :logoMargin="2"
              :text="showUrl"
              :size="200"
              :margin="5"
          />
          <div class="link-subtitle">{{ $t('invite_link') }}
            <svg class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="showUrl">
              <use xlink:href="#icon-copy_8fbca030"></use>
            </svg>
          </div>

          <div class="invite-get-link">
            <div class="link-url">{{ showUrl }}</div>
          </div>
        </div>
        <div class="invite-friends-goals">
          <div class="goals-top">
            <img
                class="goals-img"
                src="mobile/mc/goals.72382a55.png"
                alt="goals"
            />
            <div class="goals-title">{{ $t('invite_goals') }}</div>
          </div>
          <div class="goals-bottom has-icon">
            <div class="amount-detail">
              {{ $t('invitation_tip') }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import {mdate} from "@/mixins/mdate";
import VueQr from "vue-qr";
export default {
  mixins: [mdate],
  components: {
    VueQr,
  },
  data() {
    return {
      showPicker: false,
    }
  },
  computed: {
    showUrl() {
      return window.location.origin;
    },
  },
}
</script>

<style scoped>
.pa {
  width            : 47.9%;
  height           : 2rem;
  display          : flex;
  align-items      : center;
  justify-content  : center;
  flex-direction   : column;
  margin           : .3rem 0;
  padding          : 0 .3rem 0 .3rem;
  border-radius    : .1rem;
  background-color : #fff;
  font-weight      : 600;
  color            : #000;
  font-size        : 18px !important;
  background-image : linear-gradient(to bottom, #f3f7fb 0, #e0e9f1 100%)

}

.p6 {
  padding : .1rem
}

.withdraw-btn {
  background-color : #428dfc;
  text-align       : center;
  border-radius    : .1rem;
  color            : white !important;
  padding: .2rem;
  font-size: .3rem;
}
</style>