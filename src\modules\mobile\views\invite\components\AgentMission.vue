<script>
import {agentMission} from "@/mixins/agent/agentMission";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
export default {
  components: {RecordBoard},
  mixins: [agentMission],
}
</script>
<template>
  <section>
    <div style="padding: .2rem; width: 100%">
      <div style="display:flex;flex-direction: row; align-items: center;justify-content: space-between">
        <div style="display:flex;align-items: center;  font-weight: 600;
          margin: .1rem 0;
font-size: 0.23rem;
color: #495064;
line-height: 0.35rem;">
          <span style="margin-left: .1rem;">{{ $t('AGENT_PAGELAYER7.TEXT_TITLE1') }} </span> &nbsp;
          <span style="color: #FF8200;font-size: .26rem"> {{ res['num'] ? res['num'] : 0 }}</span>
        </div>
        <div style="display:flex;align-items: center;  font-weight: 600;
          margin: .1rem 0;
font-size: 0.23rem;
color: #495064;
line-height: 0.35rem;">
          <span style="margin-left: .1rem;">{{ $t('AGENT_PAGELAYER7.TEXT_TITLE') }} </span> &nbsp;
          <span style="color: #FF8200;font-size: .26rem"> {{ res['totalnum'] ? res['totalnum'] : 0 }}</span>
        </div>
      </div>
      <swiper :options="swiperOptions">
        <swiper-slide
            v-for="item in res['spread']"
            :key="item['id']"
        >
          <div @click="getAward(item['id'])">
            <div :class="{'received': item['flag']}" style="width: 1.71rem;
height: 2.1rem;background: linear-gradient(180deg,rgba(255, 255, 255, 1), rgba(255, 251, 244, 1));
border-radius: 0.08rem;display: flex;flex-direction: column;align-items: center; justify-content: center">
              <div style="font-size: 0.26rem;color: #495064;">{{ item['score'] | formatGoldWithK}}</div>
              <div :style="{backgroundImage: 'url(/img/tdtc/tasks/cash_3.png)'}" style="width:100%;background-size: .93rem;background-repeat: no-repeat;background-position:center;height: .93rem;">
              </div>
              <div style="font-weight: 600;display: flex;align-items: end;justify-content: center;
font-size: 0.26rem;
color: #495064;">
                <svg class="am-icon am-icon-form-icon-name" style="width: .26rem;"><use xlink:href="#form-icon-name"></use></svg>
                <span style="margin-left: .05rem;">{{ item['num']}}</span>
              </div>
            </div>
            <div style="height: .42rem;line-height: .42rem;background: #FFB627;border-radius: .08rem;margin: .15rem .05rem .1rem;font-size: 0.23rem;text-align: center;
color: #FFFFFF;">
              {{ item['flag'] ? $t('events_page.6.BTN_FINISH') : $t('events_page.6.BTN_GET') }}
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <div style="background: #F0DEDC;
border-radius: 0.08rem;
border: 0.02px solid #E6D3D7;font-weight: 400;
font-size: 0.23rem;
color: #B24048;padding: .1rem">
        {{ $t('AGENT_PAGELAYER7.TEXT_1', {money: $options.filters['formatGold'](res['need_recharge'])}) }}
      </div>
    </div>
    <van-tabs type="card" background="#F9F9F9" v-model="index" animated line-width="20%" line-height="2px" color="#FFB627" title-active-color="#312E2A" title-inactive-color="#A9A9A9" swipe-threshold="1">
      <van-tab :title="$t('AGENT_PAGELAYER7.TEXT_TITLE2')">
        <RecordBoard :data="res.spread2" :column="column0" :has-more="hasMore && (res.spread2 ? res.spread2.length : 0)" @loadingMore="infoB"/>
      </van-tab>
      <van-tab :title="$t('AGENT_PAGELAYER7.TEXT_BTN_ALL')">
        <RecordBoard :data="rank[0]" :column="column1" />
      </van-tab>
      <van-tab :title="$t('AGENT_PAGELAYER7.TEXT_BTN_WEEK')">
        <RecordBoard :data="rank[1]" :column="column1" />
      </van-tab>
    </van-tabs>
    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
  </section>
</template>
<style scoped lang="scss">

.swiper-slide {
  width: unset !important
}
::v-deep .van-tab__pane {
  height: unset !important
}

.text-ellipsis {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.received {
  background: linear-gradient(180deg, rgba(51, 159, 238, 1), rgba(176, 131, 207, 1)) !important;
  div {
    color: #FFFFFF !important;
  }
}
</style>