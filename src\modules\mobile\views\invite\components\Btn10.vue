<script>
import {btn10} from "@/mixins/agent/btn10";
import RecordBoard from '@/modules/mobile/components/RecordBoard.vue'

export default {
  components: {RecordBoard},
  mixins: [btn10],
}
</script>

<template>
  <div class="invite-friends-container">
    <van-cell-group style="border-radius: 0.1rem;overflow: hidden">
      <van-field input-align="right" readonly label-width="5rem" label="Xếp hạng hiện tại của bạn" :value="res.now_rank"/>
      <van-field input-align="right" readonly label-width="5rem" label="Xếp hạng của bạn ngày hôm qua" :value="res.yesterday_rank"/>
      <van-field input-align="right" readonly label-width="5rem" label="Xếp hạng cao nhất của bạn" :value="res.max_rank"/>
      <van-field input-align="right" readonly label-width="5rem" label="Số lượng giới thiệu" :value="res.spread_num"/>
      <van-field input-align="right" readonly label-width="5rem" label="Thưởng giới thiệu của bạn" :value="userNowAward"/>
    </van-cell-group>

    <div style="display:flex; justify-content: space-around;align-items: center; width: 100%; height: .66rem; line-height: .66rem;margin: .2rem 0;">
      <van-button @click="get1030" type="warning" block :disabled="res.flag === 0 || res.flag === 2 || !userNowAward">{{ res.flag === 2 ? $t("AGENT_PROMOTION_AWARD_STATUS_2") : $t("AGENT_PROMOTION_AWARD_STATUS_1") }}</van-button>
    </div>
    <div @click="help = !help" style="text-align: left;
    padding: .05rem;
                width: 6.95rem;
background: #F0DEDC;
border-radius: 0.08rem;
border: 0.02px solid #E6D3D7;
font-size: 0.23rem;
color: #AF3841;
">
      <div>{{ $t('AGENT_PROMOTION_ACTIVETIME', {startTime:res.begin_time, endTime: res.end_time}) }}</div>
      <div style="margin: .1rem 0;">{{ lab_tips }}</div>
      <div>
        <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
        <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
        <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
      </div>
      <div style="margin: .1rem 0;" v-if="help">
        <p>{{ $t('AGENT_PROMOTION_RULES_1', {needRecharge: this.$options.filters['formatGold'](res52.need_recharge), needBet:this.$options.filters['formatGold'](res52.need_bet)}) }}</p>
        <p>{{ $t('AGENT_PROMOTION_RULES_2', {startTime:res.begin_time, endTime: res.end_time}) }}</p>
        <p>{{ $t('AGENT_PROMOTION_RULES_3') }}</p>
        <p>{{ $t('AGENT_PROMOTION_RULES_4') }}</p>
        <p>{{ $t('AGENT_PROMOTION_RULES_5') }}</p>
        <p>{{ $t('AGENT_PROMOTION_RULES_6') }}</p>
      </div>
      <div :class="{'close_help': help}" style="display:flex;justify-content: center">
        <img class="scaling-element" style="width: .38rem;height: .35rem;margin: .1rem 0" src="/img/activity/52/arrow.png" alt="">
      </div>
    </div>
    <div>

    </div>
    <div class="invite-friends-link">
      <div class="link-top-title">{{ $t('invite_share') }}</div>
      <VueQr
          :logoMargin="2"
          :text="showUrl"
          :size="160"
          :margin="5"
      />
      <div class="link-subtitle">{{ $t('invite_link') }}
        <svg style="fill: #8c8a8a !important;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="showUrl">
          <use xlink:href="#icon-copy"></use>
        </svg>
      </div>

      <div class="invite-get-link">
        <div class="link-url">{{ showUrl }}</div>
      </div>
    </div>
    <van-tabs type="card" background="#F9F9F9" v-model="index" animated line-width="20%" line-height="2px" color="#FFB627" title-active-color="#312E2A" title-inactive-color="#A9A9A9" swipe-threshold="1">
      <van-tab title="Bảng xếp hạng">
        <RecordBoard :data="res.rank_info" :column="column0"/>
      </van-tab>
      <van-tab title="Phần thưởng xếp hạng">
        <RecordBoard :data="res.confs" :column="column1" />
      </van-tab>
    </van-tabs>
<!--    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(&#45;&#45;safe-area-inset-bottom))"></div>-->
  </div>
</template>

<style scoped>

::v-deep .van-cell {
  font-weight: 600;
  font-size: 0.26rem;
  color: #312E2A;
}

::v-deep .van-button__text {
  font-weight: 600;
  font-size: 0.26rem;
  color: #FFFFFF;
}

.scaling-element {
  animation: scaleLoop 1.5s ease-in-out infinite;
  display: inline-block; /* 确保缩放效果居中 */
}

@keyframes scaleLoop {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}
.close_help {
  transform: rotate(180deg);
}
</style>