<script>
import {btn11} from "@/mixins/agent/btn11";
import RecordBoard from '@/modules/mobile/components/RecordBoard.vue'
import {getWebGameTitle} from '@/utils/common'

export default {
  methods:    {getWebGameTitle},
  components: {RecordBoard},
  mixins:     [btn11],
}
</script>

<template>
  <div style="padding: 0 .2rem">
    <div style="background: #F0DEDC;margin-top: .2rem;
    border-radius: 0.08rem;
    border: 0.02px solid #E6D3D7;
    font-size: 0.23rem;
    color: #B24048;
    padding: 0.1rem;" v-html="$t('AGENT_THIRD_REBATE_RATE_RELUS1', {gameName: getWebGameTitle(res.data[0]['kind_id']), rate1: res['data'][0].rate * 100, rate2: res['data'][0].rate * 100, secondRate: res.second_rate * 100, rate3: res['data'][0].rate * 100 })">
    </div>
    <div style="font-size: .23rem; padding: .2rem .1rem;line-height: 2;">
      <p style="font-weight: 600;font-size: .26rem;">{{ $t('AGENT_THIRD_REBATE_RATE_RELUS2') }}</p>
      <p>{{ $t('AGENT_THIRD_REBATE_RATE_RELUS3') }}</p>
      <p>{{ $t('AGENT_THIRD_REBATE_RATE_RELUS4') }}</p>
      <p>{{ $t('AGENT_THIRD_REBATE_RATE_RELUS5', {rate1: res['data'][0].rate * 100, rate2: res['data'][0].rate * 100, secondRate: res.second_rate * 100, rate3: res['data'][0].rate * 100 }) }}</p>
    </div>
    <record-board :column="column" :data="res.data"/>
<!--    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(&#45;&#45;safe-area-inset-bottom))"></div>-->
  </div>
</template>

<style scoped>
</style>