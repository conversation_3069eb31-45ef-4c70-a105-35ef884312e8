<template>
  <div class="agent-btn2" style="padding: 0.2rem .2rem">
    <div class="title" style="background: url(/img/tdtc/agent/btn2.png) no-repeat center;background-size: contain; width: 100%;height: 2.5rem">
      <span>{{$t('AGENT_PAGELAYER2.TEXT_1')}}</span>
      <span>{{$t('AGENT_PAGELAYER2.TEXT_2')}}</span>
      <span>{{$t('AGENT_PAGELAYER2.TEXT_3')}}</span>
      <span>{{$t('AGENT_PAGELAYER2.TEXT_4')}}</span>
      <span>{{$t('AGENT_PAGELAYER2.TEXT_5')}}</span>
      <span>{{$t('AGENT_PAGELAYER2.TEXT_6')}}</span>
    </div>
    <div style="background: #F0DEDC;margin-top: .2rem;
    border-radius: 0.08rem;
    border: 0.02px solid #E6D3D7;
    font-size: 0.23rem;
    color: #B24048;
    padding: 0.1rem;">{{$t('AGENT_PAGELAYER2.TEXT_7')}}</div>
    <div style="font-size: .23rem; padding: .2rem .1rem;line-height: 2;">
      <p style="font-weight: 600;font-size: .26rem;">{{$t('AGENT_PAGELAYER2.TEXT_8')}}</p>
      <p>{{$t('AGENT_PAGELAYER2.TEXT_9')}}</p>
      <p>{{$t('AGENT_PAGELAYER2.TEXT_10')}}</p>
      <p>{{$t('AGENT_PAGELAYER2.TEXT_11')}}</p>
    </div>
    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
  </div>
</template>


<style scoped lang="scss">
.agent-btn2 {
  .title {
    position: relative;
    span {
      font-size: .16rem;
      position: absolute;

      &:nth-child(1) {
        left: 1.8rem;
        top: .4rem;
      }
      &:nth-child(2) {
        left: .8rem;
        top: .8rem;
      }
      &:nth-child(3) {
        left: 0;
        top: 1.15rem;
      }
      &:nth-child(4) {
        right: 1.8rem;
        top: .4rem;
      }
      &:nth-child(5) {
        right: .8rem;
        top: .8rem;
      }
      &:nth-child(6) {
        right: 0;
        top: 1.15rem;
      }
    }
  }

}
</style>
<script>
</script>