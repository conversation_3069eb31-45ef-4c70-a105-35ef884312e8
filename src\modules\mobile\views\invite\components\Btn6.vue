<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {btn6} from "@/mixins/agent/btn6";

export default {
  components: {RecordBoard},
  mixins: [btn6],
}
</script>
<template>
  <div>
  <record-board :column="column" :data="records"/>
  <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))">
  </div>
  </div>
</template>
<style scoped>
</style>