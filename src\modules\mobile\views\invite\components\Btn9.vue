<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {btn9} from "@/mixins/agent/btn9";

export default {
  components: {RecordBoard},
  mixins: [btn9],
  data() {
    return {
      showPicker: false,
    }
  },
};
</script>

<template>
  <div class="invite-friends-records" style="padding: unset; margin: unset;">

    <div class="mc-transaction-record-root mtrr-container">
      <div class="mc-filter-container name-filter" style="position: fixed;
    top: 0;">
        <div class="am-flexbox am-flexbox-align-middle mc-trans-filter mc-filter">
          <div class="am-flexbox am-flexbox-align-middle mc-trans-filter">
            <a
              @click="showPicker = true"
              role="button"
              class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
              aria-disabled="false"

          >
            <svg class="am-icon am-icon-down am-icon-xxs" aria-hidden="true">
              <use xlink:href="#down"></use>
            </svg>
            <span>{{ types[form.type - 1] }}</span></a
          >
            <span v-if="!res.self_rank" style="font-size: .26rem">{{ $t('AGENT_PAGELAYER9.agent_rank_awardErr_2') }}</span>
            <div translate="button_search" class="button button-submit tabPane-span" @click="award" style="width: 2rem !important;">
              {{ $t("events_page.6.BTN_GET") }}
            </div>
            <van-action-sheet get-container=".invite-friends-root" v-model="showPicker" :title="$t('AGENT_PAGELAYER8.TEXT_TITLE2')">
              <van-picker show-toolbar :columns="types" @confirm="typeChange" @cancel="showPicker=false"/>
            </van-action-sheet>
          </div>
        </div>
      </div>


      <div class="mc-trans-record-container" style="margin-top: .8rem;">
        <RecordBoard :data="res.data" :column="column"/>
      </div>
      <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
    </div>
  </div>
</template>


<style scoped>

.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}


::v-deep .van-tab__pane {
  height: unset !important;
}
.number-mc {
  padding : .2rem 0 !important;
}
</style>
