<script>
import {agentincomes} from "@/mixins/agent/agentincomes";

export default {
  name: "Incomes",
  mixins: [agentincomes]
}
</script>

<template>
  <div class="invite-friends-income">
    <div class="container-wrap">
      <div class="income-tip">
<!--        {{ $t('income_tip') }}-->
      </div>
      <div class="income-wrap">
        <div class="income-top">
          <div class="income-title">{{ $t('AGENT.BTN_5') }}:
          </div>
        </div>
        <div class="income-list">
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_1') }}</div>
            <div class="item-num">{{ res.selfactivenum }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_4') }}</div>
            <div class="item-num">{{ res.selfaddspreadnum }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_5') }}</div>
            <div class="item-num">{{ res.selftotalspreadnum }}</div>
          </div>
        </div>
      </div>
      <div class="income-wrap">
        <div class="income-top">
          <div class="income-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_2') }}
          </div>
        </div>
        <div class="income-list">
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_1') }}</div>
            <div class="item-num">{{ res.nextactivenum }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_4') }}</div>
            <div class="item-num">{{ res.nextaddspreadnum }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_5') }}</div>
            <div class="item-num">{{ res.nexttotalspreadnum }}</div>
          </div>
        </div>
      </div>
      <div class="income-wrap">
        <div class="income-top">
          <div class="income-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_3') }}
          </div>
        </div>
        <div class="income-list">
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_1') }}</div>
            <div class="item-num">{{ res.otheractivenum }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_4') }}</div>
            <div class="item-num">{{ res.otheraddspreadnum }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYDATA_5') }}</div>
            <div class="item-num">{{ res.othertotalspreadnum }}</div>
          </div>
        </div>
      </div>
      <div class="income-wrap">
        <div class="income-top" style="border-bottom: 0.02rem solid hsla(0, 0%, 40%, .1) !important;margin: .2rem 0 !important;">
        </div>
        <div class="income-list">
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYBANKMONEY_1') }}</div>
            <div class="item-num">{{ res.totalrebate | formatGold }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYBANKMONEY_2') }}</div>
            <div class="item-num">{{ res.nextrebate | formatGold }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_MYBANKMONEY_3') }}</div>
            <div class="item-num">{{ (res.totalrebate - res.nextrebate) | formatGold }}</div>
          </div>
        </div>
        <div class="income-list">
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_TEAM_1') }}</div>
            <div class="item-num">{{ res.totalmoney | formatGold }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_TEAM_2') }}</div>
            <div class="item-num">{{ res.nexttotalmoney | formatGold }}</div>
          </div>
          <div class="list-item">
            <div class="item-title">{{ $t('AGENT_PAGELAYER3.TEXT_TEAM_3') }}</div>
            <div class="item-num">{{ (res.totalmoney - res.nexttotalmoney) | formatGold }}</div>
          </div>
        </div>
      </div>
      <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
    </div>
  </div>
</template>

<style scoped>
.invite-friends-income .income-wrap .income-top {
  padding: unset !important;
  border-bottom: unset !important;
}
</style>