<script>
import {agentoverview} from "@/mixins/agent/agentoverview";

export default {
  name: "Overview",
  mixins: [agentoverview],
}
</script>

<template>
  <div class="invite-friends-container">
    <van-cell-group style="border-radius: 0.1rem;overflow: hidden">
      <van-field input-align="right" readonly label-width="3rem" :label="$t('AGENT_PAGELAYER1.TEXT_NEWPEOPLE')" :value="res.newadd"/>
      <van-field input-align="right" readonly label-width="3rem" :label="$t('AGENT_PAGELAYER1.TEXT_TAX')" :value="res.rate+'%'"/>
      <van-field input-align="right" readonly label-width="3rem" :label="$t('AGENT_PAGELAYER1.TEXT_TA')" :value="$options.filters['currency'](res.todayrebat)"/>
      <van-field input-align="right" readonly label-width="5rem" :label="$t('AGENT_PAGELAYER1.TEXT_TAWARD')" :value="$options.filters['formatGold'](res.allrebate - res.drawmoney)"/>
      <van-field input-align="right" readonly label-width="5rem" :label="$t('AGENT_PAGELAYER1.TEXT_BTN_TQJL')" :value="$options.filters['formatGold'](res.drawmoney)"/>
    </van-cell-group>

    <div style="display:flex; justify-content: space-around;align-items: center; width: 100%; height: .66rem; line-height: .66rem;margin: .2rem 0;">
      <div class="withdraw-btn" style="width: 46%;padding: unset" @click="getagentdraw">{{ $t('AGENT_PAGELAYER1.TEXT_BTN_TQFX')}}</div>
      <div class="withdraw-btn" style="width: 46%;padding: unset" @click="$emit('showLog')">{{ $t('AGENT_PAGELAYER1.TEXT_BTN_TQJL')}}</div>
    </div>
    <div style="
                width: 6.95rem;
height: 0.58rem;
line-height: .58rem;
background: #F0DEDC;
border-radius: 0.08rem;
border: 0.02px solid #E6D3D7;font-weight: 600;
font-size: 0.26rem;
color: #AF3841;
">
      {{ $t('AGENT_PAGELAYER1.TEXT_PERIOD') }} {{ $t('AGENT_PAGELAYER1.TEXT_PERIOD_TXT') }}
    </div>
    <div class="invite-friends-link">
      <div class="link-top-title">{{ $t('invite_share') }}</div>
      <VueQr
          :logoMargin="2"
          :text="showUrl"
          :size="160"
          :margin="5"
      />
      <div class="link-subtitle">{{ $t('invite_link') }}
        <svg style="fill: #8c8a8a !important;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="showUrl">
          <use xlink:href="#icon-copy"></use>
        </svg>
      </div>

      <div class="invite-get-link">
        <div class="link-url">{{ showUrl }}</div>
      </div>
    </div>
    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
  </div>
</template>

<style scoped>

::v-deep .van-cell {
  font-weight: 600;
  font-size: 0.26rem;
  color: #312E2A;
}

.withdraw-btn {
  background: #FFB627;
  text-align       : center;
  border-radius    : .08rem;
  color            : white !important;
  padding: .2rem;
  font-size: .3rem;
}
</style>