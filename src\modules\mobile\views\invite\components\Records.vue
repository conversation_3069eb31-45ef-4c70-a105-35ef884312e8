<script>
import {mdate} from "@/mixins/mdate";
import {records} from "@/mixins/agent/records";

export default {
  name: "record",
  mixins: [mdate, records],
  data() {
    return {
      showPicker: false
    }
  },
};
</script>

<template>
  <div class="invite-friends-records" style="padding: unset">
  <div class="mc-transaction-record-root mtrr-container" style="background-color: #fff; height: 100%">
    <div class="mc-filter-container name-filter" style="position: fixed;
    top: 0;">
        <div class="am-flexbox am-flexbox-align-middle mc-trans-filter mc-filter">
          <div class="am-flexbox am-flexbox-align-middle mc-trans-filter">
            <div class="flex-shrink1 filter-time-btn" @click="show = true">
              <a
                  role="button"
                  class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"

              >
                <svg
                    class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs"
                    aria-hidden="true"
                >
                  <use xlink:href="#calendar_c4db3b67"></use>
                </svg>
                <span>{{ date }}</span></a
              >
            </div>
            <div translate="button_search" class="button button-submit tabPane-span" :class="{ processing: processing }" @click="search(false)">
              {{ $t("button_search") }}
            </div>
             <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" get-container=".invite-friends-root" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
                :max-date="maxDate" :allowSameDay="true"/>
          </div>
        </div>
      </div>
    <div class="mc-trans-record-container" style="margin-top: .6rem;">
      <div class="records-list-root">
        <div
            class="list-wrapper" style="overflow: unset"
        >
          <ul
              class="scroll-inner-container"

          >
            <van-list
                v-if="datas.length"
                v-model="loading"
                :finished="finished"
                @load="search(true)"
                :immediate-check="false"

            >
              <li v-for="(item, index) in datas" :key="index">
                <div class="rr-container">
                  <div class="tr-item-header">
                    <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;">
                      <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        <span class="record-icon-ticket"></span>
                        <span class="tr-item-title"></span>
                      </div>
                      <span class="tr-item-time">{{ item.createTime | dateFormat }}</span></div>
                  </div>
                  <div class="tr-item-content">
                    <div class="tr-item-detail">
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('casino') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.casinoBetRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('con_rng') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.slotBetRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('con_fish') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.fishBetRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('Cards') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.cardsBetRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('con_live') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.liveBetRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('con_sports') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.sportsBetRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('con_lottery') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.lotteryBetRebate | currency }}</div>
                      </div>
<!--                      <div class="tr-item-content-item">-->
<!--                        <div class="tic-code">{{ $t('compete_game') }} Rebate: </div>-->
<!--                        <div class=" tic-remark false">{{ item.esportsBetRebate | currency }}</div>-->
<!--                      </div>-->
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('deposit') }} Rebate: </div>
                        <div class=" tic-remark false">{{ item.rechargeRebate | currency }}</div>
                      </div>
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('Total Rebate') }}: </div>
                        <div class=" tic-remark false">{{ item.totalRebate | currency }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <template #loading>
                <div class="scroll-loading">
                  <svg
                      class="loading-icon"
                      x="0px"
                      y="0px"
                      width="40px"
                      height="40px"
                      viewBox="0 0 40 40"
                  >
                    <path
                        opacity="0.2"
                        d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                    ></path>
                    <path
                        d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                    >
                      <animateTransform
                          attributeType="xml"
                          attributeName="transform"
                          type="rotate"
                          from="0 20 20"
                          to="360 20 20"
                          dur="0.5s"
                          repeatCount="indefinite"
                      ></animateTransform>
                    </path>
                  </svg>
                </div>
              </template>
            </van-list>
            <div class="nodata-container" v-else>
              <svg class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon">
                <use xlink:href="#nodata_f4c19c2d"></use>
              </svg>
              <p class="">{{ $t('no_data') }}</p>
            </div>
          </ul>

          <!--          <div class="pullup-wrapper"></div>-->
        </div>
      </div>

    </div>
    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
  </div>
  </div>
</template>


<style scoped>

.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}
</style>
