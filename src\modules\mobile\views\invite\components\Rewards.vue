<script>
import {rewards} from "@/mixins/agent/rewards";

export default {
  name: "Rewards",
  mixins: [rewards],
}
</script>

<template>
  <div class="invite-friends-rewards">
    <div class="container-wrap">
      <div class="rewards-list">
        <div class="rewards-time">
          <div class="time-title">
            <!-- react-text: 1751 --><!-- /react-text --><span
              class="time"
          >{{ $t('rewards_time_forever') }}</span
          >
          </div>
        </div>
        <div class="rewards-item" :class="{disable: !(!item.flag && spreadawardinfo.spreadNum >= item.spreadNum)}" v-for="(item, index) in spreadawardinfo.data" :key="index">
          <div class="item-content">
            <div class="item-title">
              <div class="wysiwyg">
                {{ $t('rewards_title', {0: item.spreadNum}) }}
              </div>
            </div>
            <div class="item-info">
              <svg class="am-icon am-icon-coin_a9a0c66b am-icon-md">
                <use xlink:href="#coin_a9a0c66b"></use>
              </svg>
              <div class="item-num">{{ item.awardScore | currency }}</div>
            </div>
          </div>
          <div class="btn-wrap">
            <div class="btn-info">
              <div class="current">{{ spreadawardinfo.spreadNum }}</div>
              <div class="goals">{{ item.spreadNum }}</div>
            </div>
            <div class="get-btn" @click="getagentspreadaward(item)">{{ item.flag === 1 ? $t('button_receive_already') : spreadawardinfo.spreadNum >= item.spreadNum ? $t('button_receive') : $t('label_available') }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="mc-flex mc-flex-between mc-flex-row main-nav-list" style="height: calc(1.39rem + var(--safe-area-inset-bottom))"></div>
  </div>
</template>

<style scoped>

</style>