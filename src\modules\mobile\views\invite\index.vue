<template>
  <div class="invite-friends-root" ref="inviteRoot">
    <div class="mc-header-wrap" ref="search">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-inviteFriends am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('AGENT.LAYER_TITLE') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-tabs v-draggable style="margin-top: .15rem;" background="#F9F9F9" v-model="index" animated line-width="20%" line-height="2px" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="3">
     <van-tab :title="$t('AGENT.BTN_1')" >
        <Overview @showLog="index = 2"/>
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_2')">
        <Btn2 />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_10')">
        <Btn10 />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_11')">
        <Btn11 />
      </van-tab>
      <van-tab :title="$t('AGENT_PAGELAYER1.TEXT_BTN_TQJL')">
        <Withdrawrecords />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_9')">
        <Btn9 />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_5')">
        <Incomes />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_8')">
        <ReportOfTheLower />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_4')">
        <Btn4 />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_6')">
        <Btn6 />
      </van-tab>
      <van-tab :title="$t('AGENT.BTN_7')">
        <AgentMission />
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import Overview from "@/modules/mobile/views/invite/components/Overview.vue";
import Rewards from "@/modules/mobile/views/invite/components/Rewards.vue";
import Incomes from "@/modules/mobile/views/invite/components/Incomes.vue";
import Records from "@/modules/mobile/views/invite/components/Records.vue";
import Withdrawrecords from "@/modules/mobile/views/invite/components/Withdrawrecords.vue";
import {dot} from "@/mixins/dot";
import AgentMission from "@/modules/mobile/views/invite/components/AgentMission.vue";
import ReportOfTheLower from "@/modules/mobile/views/invite/components/ReportOfTheLower.vue";
import Btn9 from "@/modules/mobile/views/invite/components/Btn9.vue";
import Btn4 from "@/modules/mobile/views/invite/components/Btn4.vue";
import Btn6 from "@/modules/mobile/views/invite/components/Btn6.vue";
import Btn2 from '@/modules/mobile/views/invite/components/Btn2.vue'
import Btn10 from '@/modules/mobile/views/invite/components/Btn10.vue'
import Btn11 from '@/modules/mobile/views/invite/components/Btn11.vue'


export default {
  components: {
    Btn11,
    Btn10,
    Btn2,
    Btn6, Btn4, Btn9, ReportOfTheLower, AgentMission, Withdrawrecords, Records, Incomes, Rewards, Overview},
  mixins: [dot],
  data() {
    return {
      index: 0,
      showPicker: false,
    }
  },
  mounted() {
    // this.event_enterPromote()
    this.$nextTick(()=>{
      let index = this.$route.query.index ?? 0
      this.index = parseInt(index)
    })
  }
}
</script>
<style scoped>
::v-deep .van-tabs__nav {
  background-color: #F9F9F9 !important;
}
</style>

