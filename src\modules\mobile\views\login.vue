<script>
import {debounce} from "@/utils/common";
import {ROUTE_CAPTCHA, ROUTE_LOGON_USERNAME} from "@/api";
import {MD5} from "crypto-js";
import {logon} from "@/mixins/logon";
import {actionCaptcha} from "@/mixins/actionCaptcha";
import ForgotModal from "@/components/ForgotModal.vue";

export default {
  name: "login",
  components: {ForgotModal},
  mixins:[logon, actionCaptcha],
  data(){
    return {
      oauth: false,
      showPassword: !1,
      showConfirmPassword: !1,
      form:{
        username: '',
        phone: '',
        password: '',
        captcha: '',
        id: 0,
        code: ''
      },
      agree: false,
      captcha:{}
    }
  },
  beforeCreate() {
    document.getElementById('css_forgot_main').disabled = false
    document.getElementById('css_forgot_ant').disabled = false
    document.getElementById('css_forgot_3077').disabled = false
    document.getElementById('css_forgot_test').disabled = false
    document.documentElement.setAttribute('data-device', 'mobile')
    if (this.$store.state.webType === 1) {
      document.documentElement.style.fontSize = '57px';
    }
  },
  destroyed() {
    document.documentElement.setAttribute('data-device', '')
    document.getElementById('css_forgot_main').disabled = true
    document.getElementById('css_forgot_ant').disabled = true
    document.getElementById('css_forgot_3077').disabled = true
    document.getElementById('css_forgot_test').disabled = true
    if (this.$store.state.webType === 1) {
      document.documentElement.style.fontSize = '';
    }
  },
  mounted() {
    this.initActionCaptcha(this.captchaLogonHandler, "captchaLogonHandler")
    // this.getCaptcha()
  },
  methods: {
    changeCaptcha() {
      this.getCaptcha()
    },
    getCaptcha() {
      debounce(() => {
        this.$protoApi(ROUTE_CAPTCHA).then((res) => {
          this.captcha = res
        }).catch(() => { });
      }, 500)()
    },
    submitRegister: function () {
      var e = this;
      // if (!this.termsUserPass) return this.termsCheck = !0;
      // this.termsCheck = !1;
      this.$validator.validateAll().then((function (t) {
        t && e.registerMethod()
      }))
    },
    registerMethod: function () {
      debounce(() => {
        this.$protoApi(ROUTE_LOGON_USERNAME, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          username: this.form.username,
          passwd: MD5(this.form.password).toString(),
          id: this.captcha.id,
          captcha: this.form.captcha,
          oauth: this.form.code
        }).then((res) => {
          this.$store.commit("setLogon", res)
          this.event_login(this.$store.state.account.userId)
          this.platformInit()
          this.$router.replace("/")
        }).catch((res) => {
          this.form.captcha = ''
          this.getCaptcha()
          if (res.code === 516) {
            this.oauth = true
          }
        })
      }, 500)()

    }
  }
};
</script>

<template>
    <div class="login_wrap" v-show="!$store.state.showForgotModal" style="height: 100vh; background: rgba(1,1,1,1);">
      <div id="page_bg" class="login-bg" style="top: 0; height: 100vh"></div>
      <div style="display: flex;justify-content: center;align-items: center;padding: 1rem 0 0.2rem;">
        <img src="/img/logo.png" alt="" style="width: 4.96rem;
height: 3.04rem">
      </div>
      <div class="form-menu">
        <div class="menu-links">
          <router-link class="menu-link" to="/m/login" :class="{on: $route.path === '/m/login'}">{{ $t("login") }}</router-link>
          <router-link class="menu-link" to="/m/register" :class="{on: $route.path === '/m/register'}">{{ $t("register") }}</router-link>
        </div>
      </div>
      <div class="form_banner"><!-- react-empty: 5310 --></div>
      <div class="form_wrap" style="position: relative">
        <div style="position: absolute; bottom: -1rem; left: 44%" @click="$router.push('/')">
          <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
        </div>
        <form class="form_inputs login_height" autocomplete="off" @submit.prevent="submitRegister">
          <div class="outter_form login_form">
            <div class="login-form-bg">
              <div class="method_select">
                <div class="select-wrap">
                  <div class="method_toggle" :class="{on: loginMethod === 0}" @click="loginMethod = 0"><span>{{ $t("username") }}</span></div>
                  <div class="method_toggle" :class="{on: loginMethod === 1}" @click="loginMethod = 1"><span>{{ $t("login") }} SDT</span></div>
                </div>
              </div>
<!--              <div class="no-method-select"></div>-->
              <div class="form-group" v-show="!oauth" v-if="!loginMethod">
                <div class="fixed_list">
                  <i class="input_icon icon_username">
                    <svg class="am-icon am-icon-form-icon-name am-icon-md">
                      <use xlink:href="#form-icon-name"></use>
                    </svg>
                  </i
                  ><input
                    type="text"
                    class="form-control username undefined"
                    name="username"
                    :placeholder="$t('username')"
                    autocomplete="off"
                    v-model.trim="form.username"
                    key="username"
                    v-validate="{ regex: /^[a-zA-Z0-9_\u00C0-\u00D6\u00D8-\u00f6\u00f8-\u02AF]{4,48}$/, required: true}"
                /><span class="right-icon remove-btn" @click="form.username = ''">
                  <svg class="am-icon am-icon-remove am-icon-md">
                      <use xlink:href="#icon-close"></use>
                  </svg>
                </span>
                </div>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                      errors.first("username") ? $t('regex.username') : ""
                    }}</strong>
                </div>
              </div>
              <div class="form-group" v-show="!oauth" v-if="loginMethod">
                <div class="fixed_list">
                  <i class="input_icon icon_username" style="font-size: .23rem;top:.28rem">
<!--                    <svg class="am-icon am-icon-form-icon-name am-icon-md">
                      <use xlink:href="#form-icon-name"></use>
                    </svg>-->
                    {{this.$store.state.phonePreShow}}
                  </i
                  ><input
                    type="text"
                    class="form-control username undefined"
                    name="phone"
                    :placeholder="$t('bind_tip_inputPhone')"
                    autocomplete="off"
                    v-model.trim="form.phone"
                    key="phone"
                    v-validate="{ required: true, regex: /^[0-9]{9}$/ }"
                /><span class="right-icon remove-btn" @click="form.phone = ''">
                  <svg class="am-icon am-icon-remove am-icon-md">
                      <use xlink:href="#icon-close"></use>
                  </svg>
                  </span>
                </div>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                      errors.first("phone")
                    }}</strong>
                </div>
              </div>
              <div class="form-group" v-show="!oauth">
                <div class="fixed_list">
                  <i class="input_icon icon_password">
                    <svg class="am-icon am-icon-form-icon-password am-icon-md">
                      <use xlink:href="#form-icon-password"></use>
                    </svg>
                  </i
                  ><input
                    :type="[showPassword ? 'text' : 'password']"
                    autocomplete="off"
                    class="form-control password undefined"
                    name="password"
                    :placeholder="$t('password')"
                    v-model.trim="form.password"
                    key="pwd"
                    v-validate="{
                    required: true,
                  }"
                    ref="password"
                />
                  <span class="right-icon" @click="showPassword = !showPassword">
                    <svg class="am-icon am-icon-md" :class="!showPassword ? 'am-icon-eye-open' : 'am-icon-eye-closed'">
                      <use xlink:href="#eye-open" v-if="showPassword"></use>
                      <use xlink:href="#eye-closed" v-else></use>
                    </svg>
                  </span>
                </div>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                      errors.first("password")
                    }}</strong>
                </div>
              </div>
              <div class="form-group" v-if="oauth">
                <div class="fixed_list">
                  <i class="input_icon"
                  ><svg class="am-icon am-icon-form-icon-invitation am-icon-md">
                    <use xlink:href="#form-icon-invitation"></use></svg></i
                  ><input
                    type="text"
                    autocomplete="off"
                    class="form-control"
                    name="code"
                    :placeholder="$t('enter_google_verifyNum')"
                    v-model.trim="form.code"
                    key="code"
                    v-validate="{ required: true, alpha_num:true, min: 4, max: 8 }"
                />
                  <div class="panel panel-default">
                    <strong class="undefined">{{
                        errors.first("code")
                      }}</strong>
                  </div>
                </div>
              </div>
              <div class="form-group" v-if="false">
                <div class="fixed_list">
                  <i class="input_icon"
                  ><svg class="am-icon am-icon-form-icon-invitation am-icon-md">
                    <use xlink:href="#form-icon-invitation"></use></svg></i
                  ><input
                    type="text"
                    autocomplete="off"
                    class="form-control"
                    name="captcha"
                    :placeholder="$t('in_increase_captcha')"
                    v-model.trim="form.captcha"
                    key="captcha"
                    v-validate="{ required: true, alpha_num:true, min: 4 }"
                />
                  <span class="right-icon remove-btn" style="width: auto"  @click="changeCaptcha"
                  >
                  <img :src="captcha.image" />
                </span>
                  <div class="panel panel-default">
                    <strong class="undefined">{{
                        errors.first("captcha")
                      }}</strong>
                  </div>
                </div>
              </div>
              <div class="checkbox-container">
<!--                <div class="sm-checkbox-item sm-checkbox-item-select">-->
<!--                  <i class="sm-checkbox-item-bg">-->
<!--                    <svg-->
<!--                        class="am-icon am-icon-icon-remember am-icon-md icon-remember"-->
<!--                    >-->
<!--                      <use xlink:href="#icon-remember"></use>-->
<!--                    </svg-->
<!--                    >&lt;!&ndash; react-text: 5348 &ndash;&gt;-->
<!--                    &lt;!&ndash; /react-text &ndash;&gt;</i-->
<!--                  ><span class="sm-label">Remember</span>-->
<!--                </div>-->
<!--                <a class="forget_password" href="/m/forget"-->
<!--                >&lt;!&ndash; react-text: 5351 &ndash;&gt;Forgot password&lt;!&ndash; /react-text &ndash;&gt;-->
<!--                  &lt;!&ndash; react-text: 5352 &ndash;&gt;?&lt;!&ndash; /react-text &ndash;&gt;</a-->
<!--                >-->
                <span class="forget_password" @click="$store.commit('setShowForgotModal', true)" >{{ $t("a6.modal.forgotPassword.title") }}</span>
                <ForgotModal v-if="$store.state.showForgotModal"/>
              </div>
              <button type="button" @click="showActionCaptcha('captchaLogonHandler')" class="submit-btn login-btn">{{ $t("login") }}</button>
<!--              <div class="submit-btn register-btn">{{ $t("hd_sign_button") }}</div>-->
<!--              <div class="disclaimer-check">
                <span
                >{{ $t('local_terms_content') }}
                  <span
                      class="policy-link"
                      @click="$store.commit('setHelpIndex', 0);$router.push('/m/help')"
                  >{{ $t('local_terms_link') }}</span
                  ></span
                >
              </div>-->
<!--              <div class="social-media-login">-->
<!--                <div class="tips"><span>Or log in with</span></div>-->
<!--                <div class="item-group">-->
<!--                  <span class="item facebook"-->
<!--                  ><svg class="am-icon am-icon-icon-facebook am-icon-md">-->
<!--                      <use xlink:href="#icon-facebook"></use></svg></span-->
<!--                  ><span class="item google"-->
<!--                ><svg class="am-icon am-icon-icon-google am-icon-md">-->
<!--                      <use xlink:href="#icon-google"></use></svg-->
<!--                ></span>-->
<!--                </div>-->
<!--              </div>-->
            </div>
          </div>
        </form>
      </div>
    </div>
</template>

<style scoped lang="scss">
.form_wrap .outter_form .input_icon+input::placeholder {
  color: #FFF;
  font-weight: 600;
  font-size: .23rem;
}
</style>
