<script>
import { lang } from "@/mixins/lang";
import {ROUTE_PLATFORM_NOTICEDETAILS} from "@/api";

export default {
  name: "notice",
  mixins: [lang],
  methods: {
    setNoticeIndex(index) {
      if (this.$store.state.noticeIndex === index) {
        this.$store.commit("setNoticeIndex", -1);
      } else {
        this.$store.commit("setNoticeIndex", index);
      }
    },
  },
};
</script>

<template>
  <div class="notice-wrap" style="background-color: #0e131b;">
    <div id="page_bg" class="common"></div>
    <van-sticky>
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-withdrawReport am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
          </div>
          <div class="am-navbar-title">{{ $t('in_increase_announcement') }}</div>
          <div class="am-navbar-right">
            <div class="to-record">
            </div>
          </div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
    </van-sticky>
    <div
      class="shell-notice-container"
      :class="{ on: $store.state.noticeIndex === index }"
      v-for="(item, index) in $store.state.notices"
      :key="index"
      @click="setNoticeIndex(index)"
    >
      <div class="mc-notice-header">
        <img
          src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEoAAABOCAMAAACwn3BVAAAAn1BMVEVHcEz/1ZzsiRX/05b/kgT2igz8kgfkbAD1q0vtmkT/lATYZwT/1Jv/05jQYgT/1p7/1qDFWwP4igb/qjT/rTX/rzz/zoz/0pf/1Z70gQD/lQD7iQD/jwD/x3z/wnL/yoPtdgD/mwH/uV3/2af/3bD/vWX/rTv/qSz/v2v/sUb/tlX/pBX/s07/ogX/4Lf/68//477/piL/5sXYYgDKWwC7LGPHAAAAFnRSTlMAjlPJ8mqV/g0r0LjncI85TdqwvuCTqMGBTwAABJBJREFUWMO9mOt2okoQheMVMBo9yQyNUbygiEZUIL7/s526dDdNooBy1tlr1kx+TL5Vl13V3by8/I8atzqLj792c5Ddcl1/8RV1e41RLU+4rhtG82lTluNttsJdhul8atlNg8oyAQkeP2erhmF1vCxx/fCAqLdmqOEm2ULZgxRQr82M4IFcd+nvg8Ou2366Wk5rCM0TAlGLfRh8xX/+tp/yUwc4S5Dv+wskAeqwi/70HwzNHrmknBQy6Xhadx+COUOBeSmWDApI6Wn9OZ9366c5RpIoxhUS6gikGahd2+GeEMKIi+oeAgqCms+mqHp2tYceS0eGXSQ7pKdPiao3RS0GbTYIu4FC1mr1XoNlM2gL2ngqLHeJCcpSIWlVZyJbTDonyXm7Ecpbi33wxVWfc1RWjTEaMim7ZAmhdAdjheKwLKeKNOGgksvlkmnUAoNiL6gMLatdMz8DJS1aQE0B9Vq9oAiV/ULFZKtPVSzLsqpQglBnRkEHseS8FnYaNSPUd0WxxkJoVEIoX6MiE7WyviuK1SqiiqX6gXqrjcrOG4UKb6L+qai6gcqrLlHHIupaYVCBrM05yZRDfVrGQaxQuRuu5WMoGLUF1Bmr7soG3kSVttDJUcm56IUbqNIWThSKpvkOSnn0+lYXRTvmPmplXV8rvCBR5zOj/Fuo2cMoc5hvoMqNpVHbxqhOjtr+lyivClXq0fqoKaKcOqhzDVS5R4elqPQBlC3umSG4iXqrQHGp8BT0GqDGhRE0B4dX39FErUpR+QjSumKUXH1wTEDdCaWG8LtkckaClyjsmEumNsO9LVPuUbnZt7SPk0ILfxVrVW6sjsjP5ktinvMqwwKq7CjkSxofXZfC7eP38UWnaq/k5igvRHg2827X9+29PnTyFt6/N4y8/Jq2hVufeYGE10kYKJZEAcwuu3oIYdxpOSiMypc5Akzd/VD9+1YQrshlXrehXvQQCL7AFocIgNSCm49XZ0SZKBn3YyMy5O2BCEhgktc+eoVXlD2hF80NGZD9ngkxRLXb4Rih1hjboJ2XO89JFCjM0Sh6MkEkgDmt+QYo1TcaJwr6GRy5Ag1BpodCnYzS58+Lsae1UZJPiSX9kUEaJAwJ3cWsFclRFiAMeJN9jltB+dPoHpZ5R8nJBcEs9Cq5dZyHxDa/ZHJXqaFRVdIgJP1AfeOWmOj26wTZ5hqk+maCJClHXeEkG922gHaRsmUBhKQ8KIWaFJu/VL1n7cmRevEVQTnJul4hQdu0j68BIWoPP6utEBlF0hyeaSwVXWs6eRRMCHgqlMjd6dEstnYU+wAbSFtwvOAQAkbE+Ks0GVGapjQaRlqKZLqTaHz4jGI1Vij160VAUTNDxWXT/hgMBl3YGPMuCf83/MOo9Sk6HFKeE8xlgHp/74Pwr16v1++1f+14m6R/6kpUGsfRmt7fQHruc1GPUevjIU41ynkK9TJg1nG3ox081dV9XG2VYcTfF+o8mO+pT51cn04qv+e/0dmDtbQFH8ivDb4cOl3pL0K9NvoGCXGpcZm+N/yaafe6bPBB+6WxbKcHpnaqQ/oXzGh4E2oPa2gAAAAASUVORK5CYII="
          alt=""
        />
        <div class="notice-body">
          <div class="notice-body__title">
            <span class="notice-body__title--text">{{ changeLang(item.messages).title }}</span>
            <!--            <div class="notice-body__title&#45;&#45;time">-->
            <!--              {{ item.startDate | dateFormat }}-->
            <!--            </div>-->
          </div>
          <div class="notice-body__content">
            <span class="ellipsis">{{ changeLang(item.messages).contents | removeHtmlTag }} </span>
          </div>
        </div>
        <svg class="am-icon am-icon-side-arrow-down am-icon-md accordion-arrow">
          <use xlink:href="#side-arrow-down"></use>
        </svg>
      </div>
      <div
        class="mc-notice-content"
        v-show="$store.state.noticeIndex === index"
      >
        <div class="wysiwyg" v-html="changeLang(item.messages).contents"></div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
