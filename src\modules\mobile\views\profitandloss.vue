<script>
import {mdate} from "@/mixins/mdate";
import moment from "moment";
import {profit} from "@/mixins/profit";

export default {
  name: "profitandloss",
  computed: {
    moment() {
      return moment
    }
  },
  mixins: [mdate, profit],
  data() {
    return {
      tabIndex: 0
    }
  },
  methods: {
    typeChange(picker, value, index) {
      this.categoryIndex = index;
    },
  }
};
</script>

<template>
  <div class="mc-profitLoss-record-root">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-profitandloss am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('in_personal_loss') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="mc-filter-container" style="position: relative">
      <div class="am-flexbox am-flexbox-align-middle mc-trans-filter mc-filter">
        <div class="am-flexbox am-flexbox-align-middle mc-trans-filter">
          <div class="flex-shrink1 filter-time-btn" @click="show = true">
            <a
                role="button"
                class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
                aria-disabled="false"
                style="flex: 0 0 50%"
            >
              <svg
                  class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs"
                  aria-hidden="true"
              >
                <use xlink:href="#calendar_c4db3b67"></use>
              </svg>
              <span>{{ date }}</span></a
            >
          </div>
          <a
              @click="showPicker = true"
              role="button"
              class="am-button am-button-ghost am-button-small am-button-inline"
              aria-disabled="false"> <span>{{ categories[categoryIndex].categoryName }}</span> </a>
          <div translate="button_search" class="button button-submit tabPane-span" :class="{ processing: processing }" @click="search(false)">
            {{ $t("button_search") }}
          </div>
           <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" get-container=".mc-profitLoss-record-root" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
              :max-date="maxDate" :allowSameDay="true"/>
          <van-action-sheet get-container=".mc-profitLoss-record-root" v-model="showPicker" :title="$t('game_type')">
            <van-picker :columns="categories.map(item => item.categoryName)" @change="typeChange"/>
          </van-action-sheet>
        </div>
      </div>
    </div>
    <div class="pal-records-list-root">
      <template v-if="records.length">
        <van-list
            v-model="loading"
            :finished="finished"
            @load="search(true)"
            :immediate-check="false"

        >
          <div style="height: .2rem"></div>
          <div v-for="(item, index) in records" :key="index" class="records-root">
            <div>
              <div class="records-item-header">
                <div
                    class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle"
                    style="height: 100%"
                >
                  <div class="header-block">
<!--                    <span class="ph-icon personal-icon">-->
<!--                      <svg class="am-icon am-icon-rng_385ad810 am-icon-md">-->
<!--                        <use xlink:href="#rng_385ad810"></use>-->
<!--                      </svg>-->
<!--                    </span>-->
                    <span class="ph-text" style="font-weight: 600"><span></span>{{ $t('label_date') }}: </span>
                  </div>
                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-center am-flexbox-align-bottom game-amount-content">
                    <span class=""></span><span
                      class="amount-total"
                      style="font-weight: 700; color: rgb(51, 51, 51)">{{ item.createTime | dateFormat }}</span>
                  </div>
                </div>
              </div>
              <div class="records-item-content">
                <div
                    style="
                  padding: 0.2rem 0.1rem;
                  border-bottom: 1px solid rgb(222, 222, 222);
                "
                >
                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle">
                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top game-amount-content">
                      <span class="time">{{ $t('label_deposit') }}</span><span class="game-amount-title">{{ item.deposit | currency }}</span>
                    </div>
                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top game-amount-content">
                      <span class="time">{{ $t('label_withdrawals') }}</span><span class="game-amount-title">{{ item.withdraw | currency }}</span>
                    </div>
                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top game-amount-content">
                      <span class="time">{{ $t('label_bet') }}</span><span class="game-amount-title">{{ item.bet | currency }}</span>
                    </div>
                  </div>
                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle">
                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top game-amount-content">
                      <span class="time">{{ $t('label_income') }}</span><span class="game-amount-title">{{ item.income | currency }}</span>
                    </div>
                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top game-amount-content" style="text-align: center">
                      <span class="time">{{ $t('label_profit_loss') }}</span><span class="game-amount-title">{{ item.profitAndLoss | currency }}</span>
                    </div>
                    <div class="am-flexbox am-flexbox-dir-column am-flexbox-justify-center am-flexbox-align-top game-amount-content">
                      <span class="time">{{ $t('label_rebate') }}</span><span class="game-amount-title">{{ item.rebate | currency }}</span>
                    </div>

                  </div>
                  <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle"></div>
                </div>
              </div>
            </div>
          </div>
          <template #loading>
            <div class="scroll-loading">
              <svg
                  class="loading-icon"
                  x="0px"
                  y="0px"
                  width="40px"
                  height="40px"
                  viewBox="0 0 40 40"
              >
                <path
                    opacity="0.2"
                    d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                ></path>
                <path
                    d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                >
                  <animateTransform
                      attributeType="xml"
                      attributeName="transform"
                      type="rotate"
                      from="0 20 20"
                      to="360 20 20"
                      dur="0.5s"
                      repeatCount="indefinite"
                  ></animateTransform>
                </path>
              </svg>
            </div>
          </template>
        </van-list>

      </template>
      <div class="nodata-container" v-else>
        <svg
            class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon"
        >
          <use xlink:href="#nodata_f4c19c2d"></use>
        </svg>
        <p class="">{{ $t('no_data') }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.game-amount-content {
  width: 33%;
  text-align: center;
}
.game-amount-content .time {
  width: 100%;
}
.game-amount-content .game-amount-title {
  text-align: center;
}
.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}
</style>
