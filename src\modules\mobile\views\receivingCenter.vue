<script>
export default {
  name: "receivingCenter",
};
</script>

<template>
  <div>
    <div class="mc-header-wrap">
      <div
        id="mc-header"
        class="mc-navbar-blue mc-receivingCenter am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
            ><span class="return_icon"
              ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">Reivindicar</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div
      class="mall-home-top"
      style="background-image: url('mobile/mc/mall-bg.5d092411.jpg')"
    >
      <div class="mall-header">
        <div class="profile-icon">
          <img
              :src="`img/profile/icon_${$store.state.account.icon}.png`"
            alt=""
          /><!-- react-empty: 10001 -->
        </div>
      </div>
      <div class="mall-number-info">
        <p class="mall-user-name"><span>antder</span></p>
        <p class="mall-user-amount number-mc">
          <!-- react-text: 9992 -->R$<!-- /react-text --><!-- react-text: 9993 -->
          <!-- /react-text --><!-- react-text: 9994 -->0.00<!-- /react-text -->
        </p>
      </div>
    </div>
    <div class="not_activity">
      <h2>
        <!-- react-text: 9997 -->Nenhuma promoção disponível ainda<!-- /react-text -->
        <!-- react-text: 9998 -->
        <!-- /react-text -->
      </h2>
    </div>
  </div>
</template>

<style scoped></style>
