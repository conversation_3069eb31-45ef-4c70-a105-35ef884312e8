<script>
import {debounce} from "@/utils/common";
import {ROUTE_CAPTCHA, ROUTE_REGISTER_USERNAME} from "@/api";
import {MD5} from "crypto-js";
import {logon} from "@/mixins/logon";
import {actionCaptcha} from "@/mixins/actionCaptcha";

export default {
  name: "register",
  mixins: [logon, actionCaptcha],
  data(){
    return {
      showPassword: !1,
      showConfirmPassword: !1,
      user:{
        username: '',
        password: '',
        captcha: '',
        id: 0,
        referral: '',
      },
      agree: true,
      captcha:{}
    }
  },
  mounted() {
    this.user.referral = this.$store.state.referral
    this.initActionCaptcha(this.captchaRegisterHandler, "captchaRegisterHandler")
    // this.getCaptcha()
  },
  methods: {
    changeCaptcha() {
      this.getCaptcha()
    },
    getCaptcha() {
      debounce(() => {
        this.$protoApi(ROUTE_CAPTCHA).then((res) => {
          this.captcha = res
        }).catch(() => { });
      }, 500)()
    },
    submitRegister: function () {
      var e = this;
      // if (!this.termsUserPass) return this.termsCheck = !0;
      // this.termsCheck = !1;
      this.$validator.validateAll().then((function (t) {
        t && e.registerMethod()
      }))
    },
    registerMethod: function () {
      this.event_registerClick()
      debounce(() => {
        this.$protoApi(ROUTE_REGISTER_USERNAME, {
          channel: this.$store.state.channel,
          device: this.$store.state.device,
          username: this.user.username,
          passwd: MD5(this.user.password).toString(),
          id: this.captcha.id,
          captcha: this.user.captcha,
          referral: this.user.referral,
          host: location.hostname.split(".").slice(-2).join(".").toLowerCase(),
        }).then((res) => {
          this.$store.commit("setLogon", res)
          this.event_register()
          this.platformInit()
          this.$router.replace("/")
        }).catch(() => {
          this.user.captcha = ''
          this.getCaptcha()
        })
      }, 500)()

    }
  }
};
</script>

<template>
  <div class="register_wrap" style="height: 100vh; background: rgba(1,1,1,1);">
    <div id="page_bg" class="login-bg" style="top: 0; height: 100vh"></div>
    <div style="display: flex;justify-content: center;align-items: center;padding: 1rem 0 0.2rem;">
      <img src="/img/logo.png" alt="" style="width: 4.96rem;
height: 3.04rem">
    </div>
    <div class="form-menu">
      <div class="menu-links">
        <router-link
          class="menu-link"
          to="/m/login"
          :class="{ on: $route.path === '/m/login' }"
          >{{ $t("login") }}</router-link
        >
        <router-link
          class="menu-link"
          to="/m/register"
          :class="{ on: $route.path === '/m/register' }"
          >{{ $t("register") }}</router-link
        >
      </div>
    </div>
    <div class="form_wrap" style="position: relative">
      <div style="position: absolute; bottom: -1rem; left: 44%" @click="$router.push('/m')">
        <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
      </div>
      <form class="form_inputs form_register" autocomplete="off" @submit.prevent="submitRegister">
        <div class="outter_form login_form">
          <div class="register-form-bg">
            <div class="no-method-select"></div>
            <!-- react-text: 6106 --><!-- /react-text --><!-- react-text: 6107 --><!-- /react-text -->
            <div class="form-group">
              <div class="fixed_list">
                <i class="input_icon icon_username"
                  ><svg class="am-icon am-icon-form-icon-name am-icon-md">
                    <use xlink:href="#form-icon-name"></use></svg></i
                ><input
                  type="text"
                  autocomplete="off"
                  class="form-control username undefined"
                  name="username"
                  :placeholder="$t('username')"
                  v-model.trim="user.username"
                  v-validate="{ regex: /^[a-zA-Z0-9_\u00C0-\u00D6\u00D8-\u00f6\u00f8-\u02AF]{4,48}$/, required: true}"
                /><span class="right-icon remove-btn" @click="user.username = ''"
                  >
                <svg class="am-icon am-icon-remove am-icon-md">
                      <use xlink:href="#icon-close"></use>
                  </svg>
              </span>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                      errors.first("username") ? $t('regex.username') : ""
                  }}</strong>
                </div>
              </div>
            </div>
            <div class="form-group">
              <div class="fixed_list">
                <i class="input_icon icon_password" @click="showPassword = !showPassword"
                  ><svg class="am-icon am-icon-form-icon-password am-icon-md">
                    <use xlink:href="#form-icon-password"></use></svg></i
                ><input
                  :type="[showPassword ? 'text' : 'password']"
                  autocomplete="off"
                  class="form-control undefined"
                  name="password"
                  :placeholder="$t('password')"
                  v-model.trim="user.password"
                  @input="checkPasswordStrength(user.password)"
                  v-validate="{
                    required: true}"
                  ref="password"
                /><!-- react-text: 6125 --><!-- /react-text -->
                <span class="right-icon" @click="showPassword = !showPassword">
                    <svg class="am-icon am-icon-md" :class="!showPassword ? 'am-icon-eye-open' : 'am-icon-eye-closed'">
                      <use xlink:href="#eye-open" v-if="showPassword"></use>
                      <use xlink:href="#eye-closed" v-else></use>
                    </svg>
                  </span>
                <div class="password-strength-indicator" v-show="user.password">
                  <div class="strength-level" :style="{backgroundColor: strength >= item ? levels[strength] : levels[0]}" v-for="item in 4" :key="item"></div>
                </div>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                    errors.first("password")
                  }}</strong>
                </div>
              </div>
            </div>
            <div class="form-group">
              <div class="fixed_list">
                <i class="input_icon icon_password"
                  ><svg class="am-icon am-icon-form-icon-password am-icon-md">
                    <use xlink:href="#form-icon-password"></use></svg></i
                ><input
                  :type="[showConfirmPassword ? 'text' : 'password']"
                  class="form-control undefined"
                  autocomplete="off"
                  name="checkPass"
                  :placeholder="$t('label_pwd_confirm')"
                  v-validate="'required|confirmed:password'"
                /><!-- react-text: 6134 --><!-- /react-text -->
                <span class="right-icon" @click="showConfirmPassword = !showConfirmPassword">
                    <svg class="am-icon am-icon-md" :class="!showConfirmPassword ? 'am-icon-eye-open' : 'am-icon-eye-closed'">
                      <use xlink:href="#eye-open" v-if="showConfirmPassword"></use>
                      <use xlink:href="#eye-closed" v-else></use>
                    </svg>
                  </span>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                    errors.first("checkPass")
                  }}</strong>
                </div>
              </div>
            </div>
            <!-- react-text: 6137 --><!-- /react-text --><!-- react-text: 6138 --><!-- /react-text --><!-- react-text: 6139 --><!-- /react-text --><!-- react-text: 6140 --><!-- /react-text --><!-- react-text: 6141 --><!-- /react-text --><!-- react-text: 6142 --><!-- /react-text --><!-- react-text: 6143 --><!-- /react-text --><!-- react-text: 6144 --><!-- /react-text -->
            <div class="form-group" v-if="false">
              <div class="fixed_list">
                <i class="input_icon"
                  ><svg class="am-icon am-icon-form-icon-invitation am-icon-md">
                    <use xlink:href="#form-icon-invitation"></use></svg></i
                ><input
                  type="text"
                  autocomplete="off"
                  class="form-control"
                  name="captcha"
                  :placeholder="$t('in_increase_captcha')"
                  v-model.trim="user.captcha"
                  v-validate="{ required: true, alpha_num:true, min: 4 }"
                />
                <span class="right-icon remove-btn" style="width: auto"  @click="changeCaptcha"
                >
                  <img :src="captcha.image" />
                </span>
                <div class="panel panel-default">
                  <strong class="undefined">{{
                    errors.first("captcha")
                  }}</strong>
                </div>
              </div>
            </div>
            <div class="form-group" v-show="false">
              <div class="fixed_list">
                <i class="input_icon"
                ><svg class="am-icon am-icon-form-icon-invitation am-icon-md">
                  <use xlink:href="#form-icon-invitation"></use></svg></i
                ><input
                  type="text"
                  autocomplete="off"
                  class="form-control"
                  name="referral"
                  :placeholder="$t('in_increase_affiliateCode')"
                  v-model.trim="user.referral"
                  v-validate="{ alpha_num:true }"
              />
                <div class="panel panel-default">
                  <strong class="undefined">{{
                      errors.first("referral")
                    }}</strong>
                </div>
              </div>
            </div>
            <!-- react-text: 6145 --><!-- /react-text -->
<!--            <div class="disclaimer-check">
              <div class="sm-checkbox-item" :class="{'sm-checkbox-item-select': agree}" @click="agree = !agree">
                <i class="sm-checkbox-item-bg"
                  ><svg
                    class="am-icon am-icon-icon-remember am-icon-md icon-remember"
                  >
                    <use xlink:href="#icon-remember"></use></svg
                  >&lt;!&ndash; react-text: 6151 &ndash;&gt;
                  &lt;!&ndash; /react-text &ndash;&gt;</i
                >
                <input type="checkbox" name="agree_terms" v-validate="'required'" v-model="agree" hidden="" />
                <span class="sm-label"></span>
              </div>
              <span
                >{{ $t('local_terms_content') }}
                <span class="policy-link" @click="$store.commit('setHelpIndex', 0);$router.push('/m/help')"
                  >{{ $t('local_terms_link') }}</span
                ></span
              >
              <div class="panel panel-default">
                <strong class="undefined">
                    {{ errors.first("agree_terms") }}
                </strong>
              </div>
            </div>-->
            <button type="button" @click="showActionCaptcha('captchaRegisterHandler')" class="submit-btn">{{ $t("register") }}</button>
            <!--              <div class="social-media-login">-->
            <!--                <div class="tips"><span>Or log in with</span></div>-->
            <!--                <div class="item-group">-->
            <!--                  <span class="item facebook"-->
            <!--                    ><svg class="am-icon am-icon-icon-facebook am-icon-md">-->
            <!--                      <use xlink:href="#icon-facebook"></use></svg></span-->
            <!--                  ><span class="item google"-->
            <!--                    ><svg class="am-icon am-icon-icon-google am-icon-md">-->
            <!--                      <use xlink:href="#icon-google"></use></svg-->
            <!--                  ></span>-->
            <!--                </div>-->
            <!--              </div>-->
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<style scoped>
.password-strength-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top : .2rem;
}

.strength-level {
  height: .1rem;  flex: 1; margin-left: .1rem;border-radius: .1rem
}

.form_wrap .outter_form .input_icon+input::placeholder {
  color: #FFF;
  font-weight: 600;
  font-size: .23rem;
}
</style>