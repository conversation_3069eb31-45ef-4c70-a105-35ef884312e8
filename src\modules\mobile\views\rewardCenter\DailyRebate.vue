<script>
import {dailyrebate} from "@/mixins/activity/dailyrebate";

export default {
  mixins: [dailyrebate]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title" style="overflow: hidden">
          <span class="van-ellipsis">{{ $t(`ACTIVITY_TYPE_14`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('mc/v.1.0.1/manifest/images/sign-rules.6b536871.png'); height: 4.2rem"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-content">
          <div class="am-tabs am-tabs-top sigin-tab">
            <div
                class="am-tabs-content am-tabs-content-animated"
            >
              <div
                  role="tabpanel"
                  aria-hidden="false"
                  class="am-tabs-tabpane am-tabs-tabpane-active"
              >
                <div class="sigin-operating">
                  <div class="sigin-container">
                    <div class="sigin-amount">
                      <span class="sigin-title-item"></span>
                    </div>
                    <div class="sigin-title">
                      <div class="sigin-tab-title"></div>
                      <div class="sigin-today-title">
                        <div></div>
                        <svg
                            class="am-icon am-icon-mobile_question_58bfb030 am-icon-md qs-svg"
                        >
                          <use xlink:href="#mobile_question_58bfb030"></use>
                        </svg>
                      </div>
                    </div>
                    <div class="sigin-btn" @click="submit()">
                      <span class="sigin-today-sub"  :class="{'sb-disable': valid() }">{{ btnReceive }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div style="width: 100%; overflow: auto">
        <table id="table" class="table table-borderless" style="margin-bottom: .1rem;">
          <thead>
          <tr class="table-light" style="line-height: .3rem;font-size: .28rem;">
            <th style="font-weight: 600!important;">{{ $t('label_bet_amount') }}</th>
            <th v-if="Details.show[0]" style="font-weight: 600!important;">{{ $t('casino') }}</th>
            <th v-if="Details.show[1]" style="font-weight: 600!important;">{{ $t('con_rng') }}</th>
            <th v-if="Details.show[2]" style="font-weight: 600!important;">{{ $t('Cards') }}</th>
            <th v-if="Details.show[3]" style="font-weight: 600!important;">{{ $t('con_live') }}</th>
            <th v-if="Details.show[4]" style="font-weight: 600!important;">{{ $t('con_sports') }}</th>
            <th v-if="Details.show[5]" style="font-weight: 600!important;">{{ $t('con_fish') }}</th>
            <th v-if="Details.show[6]" style="font-weight: 600!important;">{{ $t('con_lottery') }}</th>
            <th v-if="Details.show[7]" style="font-weight: 600!important;">{{ $t('compete_game') }}</th>
          </tr>
          </thead>
          <tbody>
          <tr v-for="item in Details.details" style=" font-size: .26rem">
            <td>{{ item.minBetamount | currency }}</td>
            <td v-if="Details.show[0]">{{ item.casinoRebate | toRate }}</td>
            <td v-if="Details.show[1]">{{ item.slotRebate | toRate }}</td>
            <td v-if="Details.show[2]">{{ item.cardsRebate | toRate }}</td>
            <td v-if="Details.show[3]">{{ item.liveRebate | toRate }}</td>
            <td v-if="Details.show[4]">{{ item.sportsRebate | toRate }}</td>
            <td v-if="Details.show[5]">{{ item.fishRebate | toRate }}</td>
            <td v-if="Details.show[6]">{{ item.lotteryRebate | toRate }}</td>
            <td v-if="Details.show[7]">{{ item.esportsRebate | toRate }}</td>
          </tr>
          </tbody>
          <tfoot>
          <tr style="font-size: .26rem">
            <td>{{ $t('Yesterday Bet') }}</td>
            <td v-if="Details.show[0]">{{ Details.yestodayCasinoBetamount | currency }}</td>
            <td v-if="Details.show[1]">{{ Details.yestodaySlotBetamount | currency }}</td>
            <td v-if="Details.show[2]">{{ Details.yestodayCardsBetamount | currency }}</td>
            <td v-if="Details.show[3]">{{ Details.yestodayLiveBetamount | currency }}</td>
            <td v-if="Details.show[4]">{{ Details.yestodaySportsBetamount | currency }}</td>
            <td v-if="Details.show[5]">{{ Details.yestodayFishBetamount | currency }}</td>
            <td v-if="Details.show[6]">{{ Details.yestodayLotteryBetamount | currency }}</td>
            <td v-if="Details.show[7]">{{ Details.yestodayEsportsBetamount | currency }}</td>
          </tr>
          </tfoot>
        </table>
        </div>
<!--        <div class="sigin-c-footer" style="overflow: hidden">-->
<!--          <div style="width: 100%;overflow: auto">-->
<!--          </div>-->
<!--        </div>-->
        <div class="sigin-c-remarks" style="margin-top: 0.04rem;" v-if="!$store.state.activitySwitchDetails[$store.state.rewardCenterIndex].rewardDist">
          <b class="sigin-rule">{{ $t('activity_tip') }}</b><br />
          <p>
             <span class="ql-size-large">
               {{ $t('activity_tip_detail') }}
             </span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b><br />
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('ACTIVITY_RULE_14') }}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div class="winner-board" style="padding: unset;margin-top: .13rem;">
          <div class="winner-content" style="padding: unset;background-color: white;height: unset; min-height: 6rem">
            <span class="winner-title" style="background-color: unset;color: red">{{ $t('List of records') }}</span>
<!--            <div class="winner-head" style="font-weight: 600; margin-bottom: unset;">-->
<!--              <div style="text-align: center; width: 50%">-->
<!--                <p>{{ $t('label_received_time') }}</p>-->
<!--              </div>-->
<!--              <div style="text-align: center; width: 50%">-->
<!--                <p>{{ $t('sign_in_reward') }}</p>-->
<!--              </div>-->
<!--            </div>-->
            <div class="winner-wrap">
              <div class="winner-list" style="max-height: 6rem;overflow: auto;">
                <table v-if="Query.records.length" id="table" class="table table-borderless" style="margin-bottom: .1rem;">
                  <thead>
                  <tr class="table-light" style="line-height: .3rem;font-size: .23rem;">
                    <th style="font-weight: 600!important;">{{ $t('label_received_time') }}</th>
                    <th style="font-weight: 600!important;">{{ $t('label_total') }}</th>
                    <th v-if="Details.show[0]" style="font-weight: 600!important;">{{ $t('casino') }}</th>
                    <th v-if="Details.show[1]" style="font-weight: 600!important;">{{ $t('con_rng') }}</th>
                    <th v-if="Details.show[2]" style="font-weight: 600!important;">{{ $t('Cards') }}</th>
                    <th v-if="Details.show[3]" style="font-weight: 600!important;">{{ $t('con_live') }}</th>
                    <th v-if="Details.show[4]" style="font-weight: 600!important;">{{ $t('con_sports') }}</th>
                    <th v-if="Details.show[5]" style="font-weight: 600!important;">{{ $t('con_fish') }}</th>
                    <th v-if="Details.show[6]" style="font-weight: 600!important;">{{ $t('con_lottery') }}</th>
                    <th v-if="Details.show[7]" style="font-weight: 600!important;">{{ $t('compete_game') }}</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(item, index) in Query.records" :key="index" style=" font-size: .26rem">
                    <td style="min-width: 3rem">{{ item.createTime | datetimeFormat }}</td>
                    <td>{{ item.totalReward | currency }}</td>
                    <td v-if="Details.show[0]">{{ item.casinoReward | currency }}</td>
                    <td v-if="Details.show[1]">{{ item.slotReward | currency }}</td>
                    <td v-if="Details.show[2]">{{ item.cardsReward | currency }}</td>
                    <td v-if="Details.show[3]">{{ item.liveReward | currency }}</td>
                    <td v-if="Details.show[4]">{{ item.sportsReward | currency }}</td>
                    <td v-if="Details.show[5]">{{ item.fishReward | currency }}</td>
                    <td v-if="Details.show[6]">{{ item.lotteryReward | currency }}</td>
                    <td v-if="Details.show[7]">{{ item.esportsReward | currency }}</td>
                  </tr>
                  </tbody>
                </table>
                <van-empty v-else/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image : linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}
.v-base .am-list-body table, .v-base tbody, .v-base td, .v-base tfoot, .v-base th, .v-base thead, .v-base time, .v-base tr, .v-base tt {
  border: none!important;
}
.table>:not(caption)>*>*{
  padding: 0.2rem 0.1rem 0.2rem 0.2rem;
}

th, td {
  min-width: 2rem;
}
</style>