<script>
import {luckwheel} from "@/mixins/activity/luckwheel";

export default {
  mixins: [luckwheel],
  data() {
    return {
      width: "7rem",
      height: "7rem",
      blocks: [
        {
          padding: ".76rem",
          imgs: [
            {
              src: "img/activity/wheel/round_loop3_1.png",
              height: '7rem',
              // rotate: true,
              top: ".1rem"
            },
            {
              src: "img/activity/wheel/round_loop3.png",
              width: '1.68rem',
              top: '.39rem',

            },
          ],
        },
      ],
    }
  },
  computed: {
    buttons() {
      return [
        {
          radius: "45%",
          pointer: true,
          fonts: [
            {
              text: "SPIN",
              top: "-.66rem",
              fontColor: "white",
              fontSize: ".8rem",
              fontWeight: 800
            },
            {
              text: this.btnCheck ? "1 Remaining" : "0 Remaining",
              top: ".3rem",
              fontColor: "yellow",
              fontSize: ".26rem",
            },
          ],
          imgs: [
            {
              src: "img/activity/wheel/btn_cj.png",
              top: "-1.7rem",
              height: '3rem',
            },
          ],
        },
      ]
    },
    prizes() {
      let list = [];
      for (let i = 0; i < this.Details.confs.length; i++) {
        list.push({
          fonts: [
            {
              text: !i ? 'Thanks' : this.$options.filters["currency"](this.Details.confs[i].reward),
              top: ".83rem",
              fontColor: "white",
              fontSize: ".22rem",
            },
          ],
          imgs: [
            {
              src: `img/activity/wheel/Rewards_${i}.png`,
              height: ".7rem",
            },
          ],
        });
      }
      return list;
    },
  },
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title" style="overflow: hidden">
          <span class="van-ellipsis">{{ $t(`ACTIVITY_TYPE_3`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('img/activity/wheel/bg.png')"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-bonus">{{ Details.minChargeamount | currency }}</span><br/>
              <div class="sc-score-desc">{{ $t('Min Deposit') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.userYestodayChargeamount | currency }}</span><br/>
              <div class="sc-score-desc">{{ $t('Yesterday Deposit') }}</div>
            </div>
          </div>
        </div>
        <div class="sigin-c-content">
          <div class="am-tabs am-tabs-top sigin-tab">
            <div
                class="am-tabs-content am-tabs-content-animated"
            >
              <div
                  role="tabpanel"
                  aria-hidden="false"
                  class="am-tabs-tabpane am-tabs-tabpane-active"
              >
                <div class="sigin-operating">
                  <div class="sigin-container">
                    <div class="sigin-amount">
                      <span class="sigin-title-item"></span>
                    </div>
                    <div class="sigin-title">
                      <div class="sigin-tab-title"></div>
                      <div class="sigin-today-title">
                        <div>{{ $t('Begin Time') }}: {{ Details.beginTime | timeFormat }}</div>
                      </div>
                    </div>
                    <div class="sigin-title">
                      <div class="sigin-tab-title"></div>
                      <div class="sigin-today-title">
                        <div>{{ $t('End Time') }}: {{ Details.endTime | timeFormat }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="sigin-c-footer">
          <LuckyWheel
              ref="myLucky"
              :width="width"
              :height="height"
              :prizes="prizes"
              :blocks="blocks"
              :buttons="buttons"
              @start="valid"
              @end="endCallback"
              :default-config="{
       offsetDegree: 360/12/2
      }"
          />
        </div>
        <div class="sigin-c-remarks" v-if="!$store.state.activitySwitchDetails[$store.state.rewardCenterIndex].rewardDist">
          <b class="sigin-rule">{{ $t('activity_tip') }}</b><br />
          <p>
             <span class="ql-size-large">
               {{ $t('activity_tip_detail') }}
             </span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b><br />
          <div>
            <div class="wysiwyg">
              <p><span class="ql-size-large">{{ $t('ACTIVITY_RULE_3',{0: $options.filters['currency'](Details.minChargeamount), 1: $options.filters['currency'](Details.maxReward)}) }}</span></p>
            </div>
          </div>
        </div>
        <div class="winner-board" style="padding: unset;margin-top: .13rem;">
          <div class="winner-content" style="padding: unset;background-color: white;height: unset; min-height: 6rem">
            <span class="winner-title" style="background-color: unset;color: red">{{ $t('List of records') }}</span>
            <div class="winner-head" style="font-weight: 600; margin-bottom: unset;">
              <div style="text-align: center; width: 50%">
                <p>{{ $t('label_received_time') }}</p>
              </div>
              <div style="text-align: center; width: 50%">
                <p>{{ $t('sign_in_reward') }}</p>
              </div>
            </div>
            <div class="winner-wrap">
              <div class="winner-list" style="max-height: 6rem;overflow: auto;">
                <template v-if="Query.ownnerRecords.length">
                  <div class="winner-item" style="height: unset" v-for="(item, index) in Query.ownnerRecords" :key="index">
                    <div class="swiper-inner" style="color: unset;justify-content: space-between; font-weight: 400; padding: .04rem 0">
                      <div style="text-align: center; width: 50%">
                        <span>{{ item.createTime | datetimeFormat }}</span>
                      </div>
                      <div style="text-align: center; width: 50%">
                        <span>{{ item.reward | currency }}</span>
                      </div>
                    </div>
                  </div>
                </template>
                <van-empty v-else/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image : linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}
</style>