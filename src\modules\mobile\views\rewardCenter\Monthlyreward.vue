<script>
import {monthlyreward} from "@/mixins/activity/monthlyreward";

export default {
  mixins: [monthlyreward]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title" style="overflow: hidden">
          <span class="van-ellipsis">{{ $t(`ACTIVITY_TYPE_12`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('img/activity/12-1.png')"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-bonus">{{ Details.userTotalBetAmount | currency }}</span><br/>
              <div class="sc-score-desc">{{ $t('Total Bet') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.minChargeamount | currency }}</span><br/>
              <div class="sc-score-desc">{{ $t('Min Deposit') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.userTotalChargeAmount | currency }}</span><br/>
              <div class="sc-score-desc">{{ $t('total_deposit') }}</div>
            </div>
          </div>
        </div>
        <div class="sigin-c-content">
          <div class="am-tabs am-tabs-top sigin-tab">
            <div
                class="am-tabs-content am-tabs-content-animated"
            >
              <div
                  role="tabpanel"
                  aria-hidden="false"
                  class="am-tabs-tabpane am-tabs-tabpane-active"
              >
                <div class="sigin-operating">
                  <div class="sigin-container">
                    <div class="sigin-amount">
                      <span class="sigin-title-item"></span>
                    </div>
                    <div class="sigin-title">
                      <div class="sigin-tab-title"></div>
                      <div class="sigin-today-title">
                        <div>{{ $t('Max Reward') }}: {{ Details.maxReward | currency }}</div>
                        <svg
                            class="am-icon am-icon-mobile_question_58bfb030 am-icon-md qs-svg"
                        >
                          <use xlink:href="#mobile_question_58bfb030"></use>
                        </svg>
                      </div>
                    </div>
                    <div class="sigin-btn" @click="submit()">
                      <span class="sigin-today-sub"  :class="{'sb-disable': valid() }">{{ btnReceive }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="sigin-c-remarks" v-if="!$store.state.activitySwitchDetails[$store.state.rewardCenterIndex].rewardDist">
          <b class="sigin-rule">{{ $t('activity_tip') }}</b><br />
          <p>
             <span class="ql-size-large">
               {{ $t('activity_tip_detail') }}
             </span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b><br />
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('ACTIVITY_RULE_12',{0: Details.rewardReceiptDay,1: $options.filters['currency'](Details.maxReward), 2: Details.rewardReceiptDay, 3: $options.filters['currency'](Details.minChargeamount)}) }}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div class="winner-board" style="padding: unset;margin-top: .13rem;">
          <div class="winner-content" style="padding: unset;background-color: white;height: unset; min-height: 6rem">
            <span class="winner-title" style="background-color: unset;color: red">{{ $t('List of records') }}</span>
            <div class="winner-head" style="font-weight: 600; margin-bottom: unset;">
              <div style="text-align: center; width: 50%">
                <p>{{ $t('label_received_time') }}</p>
              </div>
              <div style="text-align: center; width: 50%">
                <p>{{ $t('sign_in_reward') }}</p>
              </div>
            </div>
            <div class="winner-wrap">
              <div class="winner-list" style="max-height: 6rem;overflow: auto;">
                <template v-if="Query.records.length">
                  <div class="winner-item" style="height: unset" v-for="(item, index) in Query.records" :key="index">
                    <div class="swiper-inner" style="color: unset;justify-content: space-between; font-weight: 400; padding: .04rem 0">
                      <div style="text-align: center; width: 50%">
                        <span>{{ item.createTime | datetimeFormat }}</span>
                      </div>
                      <div style="text-align: center; width: 50%">
                        <span>{{ item.reward | currency }}</span>
                      </div>
                    </div>
                  </div>
                </template>
                <van-empty v-else/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image : linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}
</style>