<script>
import {vipwelfare} from "@/mixins/activity/vipwelfare";

export default {
  mixins: [vipwelfare]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title" style="overflow: hidden">
          <span class="van-ellipsis">{{ $t(`ACTIVITY_TYPE_6`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('img/activity/5/1.png')"
      >
        <div class="mall-header" style="border-radius: unset; border: unset;margin: 1rem 0 0 .5rem;">
          <div class="profile-icon">
            <img
                src="img/activity/5/2.png"
                alt=""
            /><!-- react-empty: 8304 -->
          </div>
        </div>
        <div style="
        padding-top: 1.3rem;
font-size: .6rem;
font-weight: 900;
font-style: italic;
color: #BD8863;
background: linear-gradient(0deg, #FFFAEA 0%, #C29269 100%);
-webkit-background-clip: text;
-webkit-text-fill-color: transparent;">LV.{{ $store.state.account.vip }}</div>
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.userTotalBetamount | currency}}</span><br />
              <div class="sc-score-desc">{{ $t('Total Bet') }}</div>
            </div>
          </div>
        </div>
        <div class="sigin-c-footer">
          <ul>
            <li v-for="(item, index) in Details.details" :key="index">
              <div class="no-claimed" style="display: flex; justify-content: space-between; align-items: center">
                <span class="title" style="font-size: .3rem;width: 2rem">vip {{ item.vip }}
                  <svg class="am-icon am-icon-mobile_question_58bfb030 am-icon-md qs-svg">
                    <use xlink:href="#mobile_question_58bfb030"></use>
                  </svg>
                </span>
                <div class="reward-content" style="text-align: center;">
                  <p>reward: {{ $store.state.configs.currency_symbol }} {{ item.reward | currency }}</p>
                  <p>bet ≥ {{ item.totalBetAmount | currency }}</p>
                </div>
                <div class="sigin-btn" @click="submit(item)">
                  <span class="sigin-today-sub" style="padding: 0 .2rem" :class="{'sb-disable': valid(item) }">
                     {{ btnReceive(item.vip ) }}
                  </span>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="sigin-c-remarks" v-if="!$store.state.activitySwitchDetails[$store.state.rewardCenterIndex].rewardDist">
          <b class="sigin-rule">{{ $t('activity_tip') }}</b><br />
          <p>
             <span class="ql-size-large">
               {{ $t('activity_tip_detail') }}
             </span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b><br />
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('ACTIVITY_RULE_6',{0: $options.filters['currency'](Details.minChargeamount)}) }}
                </span>
              </p>
            </div>
          </div>
        </div>
        <div class="winner-board" style="padding: unset;margin-top: .13rem;">
          <div class="winner-content" style="padding: unset;background-color: white;height: unset; min-height: 6rem">
            <span class="winner-title" style="background-color: unset;color: red">{{ $t('List of records') }}</span>
            <div class="winner-head" style="font-weight: 600; margin-bottom: unset;">
              <div style="text-align: center; width: 40%">
                <p>{{ $t('label_received_time') }}</p>
              </div>
              <div style="text-align: center; width: 30%">
                <p>Vip</p>
              </div>
              <div style="text-align: center; width: 30%">
                <p>{{ $t('sign_in_reward') }}</p>
              </div>
            </div>
            <div class="winner-wrap">
              <div class="winner-list" style="max-height: 6rem;overflow: auto;">
                <template v-if="Query.records.length">
                  <div class="winner-item" style="height: unset" v-for="(item, index) in Query.records" :key="index">
                    <div class="swiper-inner" style="color: unset;justify-content: space-between; font-weight: 400; padding: .04rem 0">
                      <div style="text-align: center; width: 40%">
                        <span>{{ item.createTime | datetimeFormat }}</span>
                      </div>
                      <div style="text-align: center; width: 30%">
                        <span>{{ item.vip }}</span>
                      </div>
                      <div style="text-align: center; width: 30%">
                        <span>{{ item.reward | currency }}</span>
                      </div>
                    </div>
                  </div>
                </template>
                <van-empty v-else/>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image : linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}
.sigin-c-footer li > div {
  height: 1.8rem;
  border-radius: .2rem;
}
.sigin-c-footer li {
  width: 100%;
}
.sigin-btn .already-singin, .sigin-btn .sigin-today-sub {
  min-height: .5rem;
}
</style>