<script>
import {play} from "@/mixins/play";

export default {
  name: "rewardCenter",
  mixins: [play],
  mounted() {
    this.event_enterEventCenter()
  },
  methods: {
    go(activityType, i) {
      this.$store.commit('setRewardCenterIndexByType', activityType)
      switch (activityType) {
        case 2:
          this.$router.push("/m/rewardCenter/signIn")
          break;
        case 3:
          this.$router.push("/m/rewardCenter/Luckwheel")
          break;
        case 4:
          this.$router.push("/m/rewardCenter/Dailylogonredpacket")
          break;
        case 5:
          this.$router.push("/m/rewardCenter/Redpacketrain")
          break;
        case 6:
          this.$router.push("/m/rewardCenter/Vipwelfare")
          break;
        case 7:
          this.$router.push("/m/rewardCenter/Firstchargereward")
          break;
        case 8:
          this.$router.push("/m/rewardCenter/Dailybetreward")
          break;
        case 9:
          this.$router.push("/m/rewardCenter/Weeklybetreward")
          break;
        case 10:
          this.$router.push("/m/rewardCenter/Dailywinnerreward")
          break;
        case 11:
          this.$router.push("/m/rewardCenter/Dailyrelieffund")
          break;
        case 12:
          this.$router.push("/m/rewardCenter/Monthlyreward")
          break;
        case 13:
          this.$router.push("/m/rewardCenter/SaturdayCarnival")
          break;
        case 14:
          this.$router.push("/m/rewardCenter/DailyRebate")
          break;
        case 15:
          this.$router.push("/m/rewardCenter/EarnCashByPromote")
          break;
      }
    }
  }
};
</script>

<template>
  <div class="reward-center-container">
    <div class="reward-center" style="background-image: unset">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            style="background: #1b2033 !important; position: fixed !important;"
            class="mc-navbar-blue mc-rewardCenter am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
            <span class="am-navbar-left-content"
            ><span class="return_icon"
            ><svg class="am-icon am-icon-left am-icon-lg">
                  <use xlink:href="#left"></use></svg></span
            ></span>
          </div>
          <div class="am-navbar-title">{{ $t('in_reward_center') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <img src="img/activity/bg/mb.jpg" style="width: 100%; height: 100%" alt="">
      <div class="task-list-warp" style="
      background-color: #fff;
      margin: 0 .2rem;
      padding: .3rem 0.3rem;
      border-radius: .2rem;
     ">
        <div style=" border-bottom: 1px solid #f5f5f9;padding: .22rem 0;
        width: 100%;display: flex; justify-content: space-between;align-items:center; color: #595959; font-size: .3rem"
            @click="go(item.activityType, i)"
            v-for="(item, i) in $store.state.activitySwitchDetails" :key="i">
          <img :src="`img/activity/mb/${item.activityType}.png`" alt="" style="width: .6rem; margin-right: .2rem;">
          <div style="flex-grow: 1;">
            {{ $t(`ACTIVITY_TYPE_${item.activityType}`) }}
          </div>
          <svg style="height: .3rem" data-v-3f3e1ad8="" color="#A6A6A6" class="am-icon am-icon-rightarrows_e029c01f am-icon-md"><use data-v-3f3e1ad8="" xlink:href="#rightarrows_e029c01f"></use></svg>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
