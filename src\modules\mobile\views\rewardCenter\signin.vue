
<script>
import {dailycheckin} from "@/mixins/activity/dailycheckin";

export default {
  mixins: [dailycheckin]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
        id="mc-header"
        class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
            ><span class="return_icon"
              ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('button_sign') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;" >
      <div
        class="mall-home-top signin-activity-card"
        style="background-image: url('img/activity/14-1.jpg')"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-days">{{ loggerDailyId }}</span><br />
              <div class="sc-score-desc">{{ $t('checkin_days') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-bonus">{{ userTodayBetamount | currency }}</span><br />
              <div class="sc-score-desc">{{ $t('Today Bet') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ userTodayChargeamount | currency}}</span><br />
              <div class="sc-score-desc">{{ $t('Today Deposit') }}</div>
            </div>
          </div>
        </div>
        <div class="sigin-c-content">
          <div class="am-tabs am-tabs-top sigin-tab">
            <div role="tablist" class="am-tabs-bar" tabindex="0">
              <div
                class="am-tabs-ink-bar am-tabs-ink-bar-animated"
                style="
                  display: block;
                  transform: translate3d(0px, 0px, 0px);
                  width: 0px;
                "
              ></div>
              <div
                role="tab"
                aria-disabled="false"
                aria-selected="true"
                class="am-tabs-tab-active am-tabs-tab"
              >
                <span class="tab-title">Check-in {{ $t('period_D') }}</span>
              </div>
            </div>
            <div
              class="am-tabs-content am-tabs-content-animated"
            >
              <div
                role="tabpanel"
                aria-hidden="false"
                class="am-tabs-tabpane am-tabs-tabpane-active"
              >
                <div class="sigin-operating">
                  <div class="sigin-container">
                    <div class="sigin-amount">
                      <span class="sigin-title-item"></span>
                    </div>
                    <div class="sigin-title">
                      <div class="sigin-tab-title">Check-in {{ $t('period_D') }}</div>
                      <div class="sigin-today-title">
                        <div>{{ $t('signin_today') }}</div>
                        <svg
                          class="am-icon am-icon-mobile_question_58bfb030 am-icon-md qs-svg"
                        >
                          <use xlink:href="#mobile_question_58bfb030"></use>
                        </svg>
                      </div>
                    </div>
                    <div class="sigin-btn" @click="canReceiveDailyId && dailycheckin()">
                      <span class="sigin-today-sub" :class="{finished: !canReceiveDailyId }">{{ !canReceiveDailyId ? $t('has_contract') : $t('sign_in') }}</span>
                    </div>
                  </div>
                  <div class="sigin-today-desc">
                    <div class="item-wrap">
                      <div class="item-title">
                        {{ $t('recharge_threshold') }}：
                      </div>
                      <div class="item-desc">
                        {{ $store.state.configs.currency_symbol }}
                        {{ minChargeamount | currency}}
                      </div>
                    </div>
                    <div class="item-wrap">
                      <div class="item-title">
                        {{ $t('betting_conditions') }}：
                      </div>
                      <div class="item-desc">
                        {{ $store.state.configs.currency_symbol }}
                        {{ minBetamount | currency }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="sigin-c-footer">
          <ul>
            <li v-for="(item, index) in detail" :key="index">
              <div class="no-claimed">
                <span class="title" :class="{today: canReceiveDailyId && loggerDailyId+1 === item.dailyId}"
                  >{{ $t('label_task_day', {0: item.dailyId}) }}<svg
                    class="am-icon am-icon-mobile_question_58bfb030 am-icon-md qs-svg"
                  >
                    <use xlink:href="#mobile_question_58bfb030"></use></svg
                ></span>
                <div class="item-a si-item"></div>
                <div class="reward-content">
                  <div>
                    {{ $t('bouns') }}: {{ $store.state.configs.currency_symbol }} {{ item.reward | currency }}
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="sigin-c-remarks" v-if="!$store.state.activitySwitchDetails[$store.state.rewardCenterIndex].rewardDist">
          <b class="sigin-rule">{{ $t('activity_tip') }}</b><br />
          <p>
             <span class="ql-size-large">
               {{ $t('activity_tip_detail') }}
             </span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b><br />
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('ACTIVITY_RULE_2',{0: $options.filters['currency'](minChargeamount), 1: $options.filters['currency'](minBetamount)}) }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.finished {
  background-image: linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}
</style>