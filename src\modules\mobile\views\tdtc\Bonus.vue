<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {TDTC_ROURE} from "@/api/tdtc";
import {DATA_NAME, FieldRenderType} from "@/utils/common";

export default {
  components: {RecordBoard},
  data() {
    return {
      hasMore: true,
      column: [
        {
          label: this.$t("BONUS.LIST_TIP1"),
          prop: "bonus_type",
        },
        {
          label: this.$t("BONUS.LIST_TIP2"),
          prop: "total_score",
          render: FieldRenderType.formatGold
        },
        {
          label: this.$t("BONUS.LIST_TIP3"),
          prop: "locked_score",
          render: FieldRenderType.formatGold
        },
        {
          label: this.$t("BONUS.LIST_TIP4"),
          prop: "released_score",
          render: FieldRenderType.formatGold
        },
        {
          label: this.$t("BONUS.LIST_TIP5"),
          prop: "flag",
        },
        {
          label: this.$t("BONUS.LIST_TIP6"),
          prop: "issued_date",
        },
        {
          label: this.$t("BONUS.LIST_TIP7"),
          prop: "expiry_date",
        },
      ],
      form: {
        "type": 0,
        "page": 1,
      },
      datas: []
    }
  },
  mounted() {
    this.search()
  },
  methods: {
    search(paginate = false) {
      if (!paginate) {
        this.form.page = 1;
        this.finished = false;
        this.datas = [];
      }
      this.processing = true;


      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_BONUS_INFO, {
        'page': this.form.page,
      })
          .then((res) => {
            // QueryAgentNextReportResponse
            iconsole(res)
            if (res['bonus_info']) {
              for (let i = 0; i < res['bonus_info'].length; i++) {
                let data = res['bonus_info'][i];
                let label;

                data['bonus_type'] = DATA_NAME[data['bonus_type']];
                data['total_score'] = data['total_score'] ?? 0
                data['released_score'] = data['released_score'] ?? 0
                data['locked_score'] = data['total_score'] - data['released_score'];
                if ((data["total_score"] ? data["total_score"] : 0) === (data["released_score"] ? data["released_score"] : 0)) {
                 //已释放完
                  label = this.$t("BONUS.LIST_TIP10")
                 } else {
                 //过期 未过期
                  label = (data["flag"] && data["flag"] === 1) ? this.$t("BONUS.LIST_TIP8") : this.$t("BONUS.LIST_TIP9")
                 }
                 data['flag'] = label
              }
            }
            if (this.$store.state.webType === 1) {
              if (res['bonus_info']) this.datas = res['bonus_info'];
            } else {
              if (res['bonus_info']){
                this.datas = this.datas.concat(res['bonus_info']);
                this.form.page++;
              } else {
                this.hasMore = false
              }

              this.loading = false;
            }
          })
          .catch(() => {
            this.hasMore = false
          })
          .finally(() => {
            this.processing = false;
          });
    },
  }
}
</script>

<template>
  <div class="withdraw-history" style="height: 100vh;background: #F9F9F9;">
    <div class="vip-header">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue am-navbar am-navbar-light" style="position: fixed !important;width: 100%; z-index: 1; top: 0;"
        >
          <div class="am-navbar-left" role="button">
          <span class="am-navbar-left-content" @click="$router.back()">
            <span class="return_icon">
              <svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg>
            </span>
          </span>
          </div>
          <div class="am-navbar-title" style="font-size: .4rem !important;font-weight: 600;">Bonus</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
    </div>
    <div  style="color: red; text-align: center;font-size: .23rem; margin: .2rem 0 0;">
      {{ $t('BONUS.TO_BONUS_TIP') }}
    </div>
    <RecordBoard :data="datas" :column="column" :has-more="hasMore && datas.length" @loadingMore="search(true)"/>

  </div>
</template>

<style scoped>
::v-deep .record-board {height: calc(100vh - 2rem);overflow: scroll !important
}
</style>