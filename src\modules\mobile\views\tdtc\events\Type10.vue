<script>
import {type10} from "@/mixins/tdtc/events/type10";
import RecordBoard from '@/modules/mobile/components/RecordBoard.vue'

export default {
  components: {RecordBoard},
  mixins: [type10]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.10`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('img/activity/bg/4.png')"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header">
          <div style="font-size: .26rem;display: flex;justify-content: center;margin-top: .2rem;">
            {{ $t('REDBAG.TIPS_3') }}<van-count-down :time="res.sys_now_time*1000" style="font-size: .3rem;color:red;margin-left: .2rem;"/>
          </div>
          <div style="font-size: .26rem;display: flex;justify-content: center;margin-top: .2rem;">
            {{ $t('REDBAG.TIPS_4') }}
            <div style="display: flex;flex-direction: column">
              <span v-for="item in res.time_info" style="margin-left: .2rem;"> {{ item.begin_time | timeFormat }} - {{ item.end_time | timeFormat }}</span>
            </div>
          </div>
        </div>
        <div class="sigin-c-content">
        </div>

        <div style="margin: 0 .2rem">
          <van-button @click="submit" type="warning" block round >{{ $t('events_page.10.TEXT_BTN_GET') }}</van-button>
        </div>
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks" >
          <b class="sigin-rule">{{ $t('label_proportion_tip1') }}</b>
          <p>
             <span class="ql-size-large">
               {{ $t('events_page.10.TEXT_TIP1') }}
               <br />
               {{ $t('events_page.10.TEXT_TIP2') }}
               <br />
               {{ $t('events_page.10.TEXT_TIP6') }}
             </span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_1') }}
                  <br />
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_2') }}
                  <br />
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_3') }}
                  <br />
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_4') }}
                  <br />
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_5') }}
                  <br />
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_6') }}
                  <br />
                  {{ $t('events_page.10.REDBAG_HELP.TEXT_HELP_7') }}
                </span>
              </p>
            </div>
          </div>
        </div>
        <record-board :column="column" :data="records"/>
      </div>
    </div>
  </section>
</template>
<style scoped>
</style>