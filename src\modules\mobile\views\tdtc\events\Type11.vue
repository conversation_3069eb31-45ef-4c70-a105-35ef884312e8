<script>
import {type11} from "@/mixins/tdtc/events/type11";
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";

export default {
  components: {RecordBoard},
  mixins: [type11],
  data() {
    return {
      data:[],
      width: "7.2rem",
      height: "7.2rem",
    }
  },
  computed: {
    blocks() {
      return [
        {
          padding: ".82rem",
          imgs: [
            {
              src: `img/activity/wheel/round_loop${this.wheelIndex+1}_1.png`,
              height: '6.6rem',
              rotate: true,
              top: '0.3rem',

            },
            {
              src: `img/activity/wheel/round_loop${this.wheelIndex+1}.png`,
              width: this.wheelIndex ? '7rem' : '6.7rem',
              top: this.wheelIndex ? '0.32rem' : '0.25rem',
            },
          ],
        },
      ]
    },
    buttons() {
      return [
        {
          radius: "45%",
          pointer: true,
          imgs: [
            {
              src: `img/activity/wheel/btn_cj${this.wheelIndex+1}.png`,
              top: "-.98rem",
              height: '1.9rem',
            },
          ],
        },
      ]
    },
    prizes() {
      let list = [];
      for (let i = 0; i < this.conf.length; i++) {
        list.push({
          fonts: [
            {
              text: !i ? 'Thanks' : this.$options.filters["formatGold"](this.conf[i]['award_money']),
              top: ".83rem",
              fontColor: "white",
              fontSize: ".22rem",
            },
          ],
          imgs: [
            {
              src: `img/activity/wheel/Rewards_${i}.png`,
              height: ".8rem",
            },
          ],
        });
      }
      return list;
    },
  },
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.11`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('img/activity/wheel/bg.png')"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-bonus">{{ res.now_score  }}</span><br/>
              <div class="sc-score-desc">{{ $t('events_page.11.ACCUMULATE_POINT') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ res.today_bet | formatGold }}</span><br/>
              <div class="sc-score-desc">{{ $t('events_page.11.AMOUNT_BET') }}</div>
            </div>
          </div>
        </div>
        <div class="sigin-c-content"></div>
        <van-tabs type="card" background="#F9F9F9" v-model="wheelIndex" animated line-width="20%" line-height="2px" color="#FFB627" title-active-color="#312E2A" title-inactive-color="#A9A9A9" swipe-threshold="1">
          <van-tab :title="$t('events_page.11.BTN_TYPE1_H_POINTS')">
          </van-tab>
          <van-tab :title="$t('events_page.11.BTN_TYPE2_H_POINTS')">
          </van-tab>
          <van-tab :title="$t('events_page.11.BTN_TYPE3_H_POINTS')">
          </van-tab>
        </van-tabs>
        <LuckyWheel style="margin: 0 auto;"
            ref="myLucky"
            :width="width"
            :height="height"
            :prizes="prizes"
            :blocks="blocks"
            :buttons="buttons"
            @start="luckywheelstart"
            @end="endCallback"
            :default-config="{
       offsetDegree: 350/12/2
      }"
        />
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p><span class="ql-size-large" v-html="$t('ACTIVITYHELP.LAB_3')"></span></p>
            </div>
          </div>
        </div>


        <van-tabs style="padding-top: .2rem;" animated line-width="20%" line-height="2px" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="1">
          <van-tab :title="$t('NEWHALL.TAG_RANK')">
            <record-board :column="column3" :data="records.info3"/>
          </van-tab>
          <van-tab :title="$t('events_page.11.BTN_WINHISTORY')">
            <record-board :column="column" :data="records.info1"/>
          </van-tab>
          <van-tab :title="$t('events_page.11.BTN_SPINRESULTS')">
            <record-board :column="column" :data="records.info2"/>
          </van-tab>
        </van-tabs>
      </div>
    </div>
  </section>
</template>
<style scoped>
::v-deep .van-tab__pane {
  height: unset !important;
  overflow: hidden !important;
}
</style>