<!--<script>
import {type201} from "@/mixins/tdtc/events/type201";

export default {
  mixins: [type201]
}
</script>-->
<script>
export default {
  data() {
    return {
      col1: [
        50000000,
        100000000,
        200000000,
        500000000,
        1000000000,
        5000000000,
        7000000000,
        15000000000,
        50000000000
      ],
      col2: [
        2800000,
        5800000,
        11800000,
        22800000,
        55800000,
        77800000,
        118800000,
        168800000,
        688800000,
      ]
    }
  },
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.201`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="ad-wrap">
      <div class="ad-bg">
        <img src="/img/tdtc/events/12.png" style="height: 2rem; margin-top: 1.3rem;margin-left: 3rem;" alt="">
      </div>
      <!--    <div class="ad-title">{{ $t('ad.tab.12') }}</div>-->
      <div class="ad-rule">
        <div>{{ $t('ad.panel.12.text.0') }}</div>
        <div style="width: 2.63rem;">{{ $t('ad.panel.12.text.1') }}</div>
      </div>
      <div>
        <div style="font-weight: bold;
font-size: 0.38rem;
color: #FFF600;">{{ $t('ad.panel.12.text.2') }}</div>
        <div style="font-size: 0.28rem;
color: #FFFFFF;">{{ $t('ad.panel.12.text.3') }}</div>
      </div>
      <div class="grade">
        <div class="grade-head">{{ $t('ad.panel.12.text.4') }}</div>
        <div class="grade-sub-head">
          <div style="width: 2.45rem;">{{ $t('ad.panel.12.th.0') }}</div>
          <div style="width: 2.08rem;">{{ $t('ad.panel.12.th.1') }}</div>
          <div style="width: 1.66rem;">{{ $t('ad.panel.12.th.2') }}</div>
        </div>
        <div class="grade-content">
          <div style="width: 2.45rem;
background: #FFFDF4;color: #CB542B;">
            <div v-for="item in col1">{{item|currency}}</div>
          </div>
          <div style="width: 2.08rem;
background: #FFF3E8;">
            <div v-for="item in col2">{{item|currency}}</div>
          </div>
          <div style="width: 1.66rem;
background: #FFEAE8;">

            <div style="font-weight: 700">X1</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped lang="scss">

.ad-wrap {
  background: linear-gradient(180deg, rgba(105, 0, 235, 1), #F9F9F9);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: .5rem .4rem;

  font-size: 0.26rem;
  color: #FFFFFF;
  text-align: center;


  .grade {
    scale: 1.1;
    margin-top: .2rem;
    width: 6.19rem;
    height: 4.95rem;
    background: #FFF9ED;
    border-radius: 0.12rem;
    overflow: hidden;


    &-head {
      width: 6.19rem;
      height: 0.61rem;
      background: #FC4984;
      line-height: .61rem;
    }

    &-sub-head {
      width: 6.19rem;
      height: 0.5rem;
      background: #FC9949;
      display: flex;
      flex-direction: row;
      align-items: center;

    }

    &-content {
      display: flex;
      flex-direction: row;
      color: #FC4984;
      font-size: 0.23rem;
      height: 3.87rem;
      > div {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
      }
    }
  }

  .ad-title {
    font-weight: bold;
    font-size: 0.32rem;
    color: #FFF948;
  }

  .ad-rule {
    align-self: start;
    z-index: 1;
    height: 1.8rem;
    width: 4rem;
    text-align: start;
    font-weight: bold;
    font-size: 0.26rem;
    color: #3BFF4D;
  }

  .ad-bg {
    position: absolute;
    top: 0;
  }

  .ad-btn {
    width: 6.19rem;
    height: 0.58rem;
    line-height: 0.58rem;
    background: linear-gradient(90deg, rgba(235, 127, 62, 1), rgba(249, 182, 6, 1));
    border-radius: 0.12rem;
    margin-top: .2rem;
  }
}

</style>