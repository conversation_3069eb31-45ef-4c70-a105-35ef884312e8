<script>
import {type26} from "@/mixins/tdtc/events/type26";

export default {
  mixins: [type26]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.26`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('/img/tdtc/events/type26.png')"
      >
      </div>
      <div class="sigin-content" style="position: absolute;height: 100%;">
        <div class="sigin-c-footer">
          <ul>
            <li v-for="(item, index) in res.conf" :key="index">
              <div class="no-claimed" style="display: flex; justify-content: space-between; align-items: center">
                <span class="title">{{ $t('ACTIVITY_PANEL8.ACTIVITY8_TASK') }}
                  <svg class="am-icon am-icon-mobile_question_58bfb030 am-icon-md qs-svg">
                    <use xlink:href="#mobile_question_58bfb030"></use>
                  </svg>
                </span>
                <div class="reward-content" style="text-align: center;">
                  <p>{{ item['now_bet'] | currency }} / {{ item['need_bet'] | formatGold }}</p>
                  <p>{{ $t('reward') }}: {{ item['award_score'] | formatGold }}</p>
                </div>
                <div class="sigin-btn" @click="submit(item['award_index'])">
                  <span class="sigin-today-sub" style="padding: 0 .2rem" :class="{'sb-disable': item['flag'] !== 2 }">
                     {{ item['flag'] == 1 ? $t('ACTIVITY_PANEL1.BTN_3') : $t('ACTIVITY_PANEL1.BTN_2') }}
                  </span>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div style="font-size: .26rem;display: flex;justify-content: center">
          {{ $t('ACTIVITY_PANEL8.ACTIVITY8_TIME') }}<van-count-down :time="t" style="font-size: .3rem;color:red;margin-left: .2rem;"/>
        </div>
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large" v-html="$t('ACTIVITY_PANEL8HELP.INFOS')">
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.sigin-c-footer li {
  width: 100%;
}

.sigin-c-footer li > div {
  height: 0.95rem;
  background: #FFFFFF;
  border-radius: 0.1rem;
  border: 0.02px solid #CECDC9;
}
.sigin-c-footer .reward-content {
  flex: 1;
}
</style>