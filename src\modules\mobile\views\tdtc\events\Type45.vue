<script>
import {type45} from "@/mixins/tdtc/events/type45";
import currency from "currency.js";

export default {
  methods: {currency},
  mixins: [type45]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.45`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('/img/activity/10-1.png')"
      >
      </div>
      <div class="sigin-content" style="position: absolute">
        <div class="sigin-c-header">
          <div class="am-flexbox am-flexbox-align-middle">
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ res['now_recharge'] | formatGold }}</span><br />
              <div class="sc-score-desc">{{$t('a6.cashback.table.current')}}</div>
            </div>
<!--            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.maxReward | currency}}</span><br />
              <div class="sc-score-desc">{{ $t('Max Reward') }}</div>
            </div>
            <div class="am-flexbox-item">
              <span class="sc-score sc-integral">{{ Details.userFirstChargeamount | currency}}</span><br />
              <div class="sc-score-desc">{{ $t('First Deposit') }}</div>
            </div>-->
          </div>
        </div>
        <div class="sigin-content">
        </div>
        <div style="margin: -.5rem .2rem 0;">
          <van-button @click="!res['flag'] && submit()" type="warning" block round >{{ res['flag'] ? $t('claimed') : $t('claim') }}</van-button>
        </div>
        <div class="sigin-c-footer">
          <ul style="display: flex;justify-content: space-between;flex-wrap: wrap">
            <li v-for="(item, index) in res['conf']" :key="index">
              <div class="no-claimed">
                <span class="title" style="align-self: center;text-align: center">{{ item['award_score'] | formatGoldWithK }}
                </span>
                <div class="item-a si-item"></div>
                <div class="reward-content" style="text-align: center;">
                  {{ $t('recharge') }} ≥ {{ item['need_recharge'] | formatGold }}
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('events_page.45.tip.0', [this.$options.filters['formatGold'](this.maxReward)]) }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.no-claimed {
  display: flex;flex-direction:column;justify-content: space-around;
  padding: .2rem 0;
}
.sigin-c-footer .title {
  margin-bottom : unset;
  padding-bottom: unset;
  margin-top: unset;
}
</style>