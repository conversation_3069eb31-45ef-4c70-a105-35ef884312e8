<script>
import {type46} from "@/mixins/tdtc/events/type46";
import CountTo from "vue-count-to";
export default {
  components: {CountTo},
  mixins: [type46],
  data() {
    return {
      blocksImgs : [
        {
          src: "img/activity/15/bg_6.png",
          height: '4.36rem',
          top: '.25rem',
          rotate: true,
        },
        {
          src: "img/activity/15/bg_5.png",
          height: '4.87rem',
        },
      ],
      blocksImgsSelected : [
        {
          src: "img/activity/15/bg_6.png",
          height: '4.36rem',
          top: '.25rem',
          rotate: true,
        },
        {
          src: "img/activity/15/bg_5.png",
          height: '4.87rem',
        },
        {
          src: "img/activity/15/bg_8.png",
          height: '2.1rem',
          top: '.2rem',
        },
        {
          src: "img/activity/15/bg_9.png",
          height: '1.95rem',
          top: '.4rem',
        },
      ],
      width: "4.86rem",
      height: "4.87rem",
      blocks: [
        {
          padding: ".76rem",
          imgs: [],
        },
      ],
    }
  },
  computed: {
    buttons() {
      return [
        {
          radius: "45%",
          pointer: true,
          fonts: [
            {
              text: this.btnText,
              top: this.upIng && this.rewardValue ? "-.26rem" : "-.4rem",
              fontColor: "white",
              fontSize: this.upIng && this.rewardValue ? ".4rem" : ".6rem",
              fontWeight: 800
            },
          ],
          imgs: [
            {
              src: "img/activity/15/bg_7.png",
              top: "-1.21rem",
              height: '2.1rem',
            },
          ],
        },
      ]
    },
    prizes() {
      let list = [];
      let img = ""
      if (typeof this.Details.wheelConf != 'object') return
      for (let i = 0; i < this.Details.wheelConf.length; i++) {
        switch (this.Details.wheelConf[i]['reward_type']) {
          default:
            img = "img/activity/15/thanks.png"
            break;
          case 1:
            img = "img/activity/15/cash.png"
            break;
          case 2:
            break;
          case 3:
            img = "img/activity/15/sacar.png"
            break;
          case 4:
            img = "img/activity/15/gold.png"
            break;

        }
        if (this.Details.wheelConf[i]['reward_type'] === 2) {
          list.push({
            fonts: [
              {
                text: this.Details.wheelConf[i].value,
                fontColor: "white",
                fontSize: ".36rem",
                fontWeight: 700
              },
            ],
          });
        } else {
          if (this.showNum && this.Details.wheelConf[i]['wheel_id'] === this.StartWheel.rewardWheelId && this.Details.wheelConf[i]['reward_type']) {
            list.push({
              fonts: [
                {
                  text: "+" + this.$options.filters['currency'](this.StartWheel.rewardValue),
                  fontColor: "white",
                  fontSize: ".23rem",
                  fontWeight: 400
                },
              ],
            });
          } else {
            list.push({
              imgs: [
                {
                  src: img,
                  height: ".6rem",
                },
              ],
            });
          }
        }

      }
      return list;
    },
  },
  mounted() {
    this.query()
  },
  methods: {
  }
}
</script>
<template>
  <section id="container">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.46`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div style="width:100%;display:flex; justify-content: space-around; align-items: center; padding: 0.4rem 0.8rem .2rem">
        <img src="img/activity/15/cash_1.png" style="width: 0.79rem; height: 0.7rem" alt=""> <span style="
height: 0.76rem;
font-weight: bold;
font-size: 0.4rem;
color: #FFB627;
line-height: 0.77rem;
">VND
        <count-to :start-val='preCashAmount' :end-val="Details.currentCashAmount / 100" :duration='1000' :decimals='2' :autoplay=true /></span>
        <div style="
        width: 1.83rem;
height: 0.49rem;
background: #FF8200;
border-radius: 0.25rem;
        display: flex; align-items: center;justify-content: space-around; padding: 0 0.15rem" @click="showWithdraw = true">
          <img src="img/activity/15/btn_coin.png" style="width: 0.4rem; height: 0.38rem" alt=""> <span style="
font-weight: bold;
font-size: 0.26rem;
color: #FEFEFF;
line-height: 0.22rem;margin-left: .1rem;

         ">{{ $t('withdrawal') }}</span>
        </div>
      </div>
      <div style="text-align: center;">
        <van-progress style="width: 6.5rem;margin: 0.2rem auto;" :percentage="percentage" stroke-width="10" text-color="white" color="#FFBA00" track-color="#F22626" :show-pivot="true"/>
        <div v-html="$t('activity_15_text_2', {0: $options.filters['formatGold'](cha)})" style="
font-size: 0.25rem;
color: #1B1414;
line-height: 0.21rem;
"></div>
      </div>
      <div style="height: 8.33rem; width: 7.5rem;position: relative; margin-top: 0.6rem;">
        <img src="img/activity/15/bg_1.png" style="height: 5.88rem; width: 7.39rem; position: absolute; top: 1.01rem; left: 0.11rem" alt="">
        <img src="img/activity/15/sun.png" style="height: 8.33rem; width: 7.5rem; position: absolute; top: 0;" alt="">
        <img src="img/activity/15/bg_3.png" style="height: 4.23rem; width: 4.17rem; position: absolute; top: -0.59rem; left: 1.73rem" alt="">
        <div style="position: absolute; top: 1.7rem; left: 1.21rem">
          <LuckyWheel
              ref="myLucky"
              :width="width"
              :height="height"
              :prizes="prizes"
              :blocks="blocks"
              :buttons="buttons"
              @start="valid"
              @end="endCallback(blocksImgs, blocksImgsSelected)"
              :default-config="{
       offsetDegree: 360/8/2
      }"
          />
          <div style="position: absolute; left: -.8rem;bottom: -1.8rem;">
            <div style="width: 100%; text-align: center;margin: .1rem 0;">
          <span style="
font-family: Arial;
font-weight: 400;
font-size: 0.28rem;
color: #1B1414;
line-height: 0.25rem;
">{{ $t('activity_15_text_expires') }}</span>
              <span style="font-family: Arial;
        font-family: Arial;
font-weight: 400;
font-size: 0.28rem;
color: red;
line-height: 0.25rem;
margin-left: .1rem;">{{ countDownElement}}</span>
            </div>
            <div @click="showShare = true" style="display: flex; color: white; align-items: center;justify-content: center;
margin: 0 auto;
      width: 6.79rem;
height: 0.68rem;
background: #FFB627;
box-shadow: 0 0 0 0 rgba(166,42,42,0.1);
border-radius: 0.1rem;
margin-bottom: .2rem;
">
              <img src="img/activity/15/share.png" style="height: 0.37rem; width: 0.35rem" alt="">
              <div style="cursor: pointer;
margin-left: .1rem;
font-weight: 600;
font-size: 0.28rem;
color: #FFFFFF;
line-height: 0.36rem;
">{{ $t('activity_15_text_share' ) }}</div>
            </div>
          </div>
        </div>
      </div>
<!--      <van-tabs animated color="translate" style="margin-top: .3rem;">-->
<!--        <van-tab title="Relatorio"  style="height: 5rem; overflow: scroll">-->
<!--          <div v-for="item in 20" :key="item" :title="item" style="display: flex; justify-content: space-around; align-items: center; padding: .1rem 0;font-family: Arial;-->
<!--font-size: .28rem;-->
<!--color: #7181A6;">-->
<!--            <div>5066**********</div>-->
<!--            <div>Acabou de sac</div>-->
<!--            <div style="color: #FFB627;">-->
<!--              + 100 <span style="background-color: #FFB627;color: white; border-radius: 50%; padding: .1rem">R$</span>-->
<!--            </div>-->
<!--          </div>-->
<!--        </van-tab>-->
<!--        <van-tab title="Minha Referencia" style="height: 190px; overflow: scroll">-->

<!--        </van-tab>-->
<!--      </van-tabs>-->

            <div class="sigin-content">
              <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
                <p>
                  <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
                  <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
                  <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
                </p>
                <p v-if="currentActivity.withdrawRate">
                  <span class="ql-size-large"> {{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
                </p>
              </div>
              <div class="sigin-c-remarks">
                <b class="sigin-rule" style="font-size: .31rem">{{ $t('invite_rules') }}</b>
                <div style="font-size: .26rem;">
                  <div>{{ $t('ad.panel.14.tip.0', [$options.filters['formatGold'](Details.withdrawalAmount)]) }}</div>
                  <div>{{ $t('ad.panel.14.tip.1', [$options.filters['formatGold'](Details.AgentRechargeAmount),$options.filters['formatGold'](Details.AgentBetScore)]) }}</div>
                  <div>{{ $t('ad.panel.14.tip.2', [Details.everyRoundActivityValidtime, Details.freegivingLotteryCount, Details.freegivingTimeInterval]) }}</div>
                  <div>{{ $t('ad.panel.14.tip.3', [Details.withdrawalValidtime]) }}</div>
                  <div>{{ $t('ad.panel.14.tip.4') }}</div>
                  <div>{{ $t('ad.panel.14.tip.5', [Details.goldcoinToCashOfGoldcoin, $options.filters['currency'](Details.goldcoinToCashOfCash)]) }}</div>
                </div>
                    <ul style="font-size: .3rem;">
<!--                      v-html="$t('ACTIVITY_RULE_15', {0: Details.withdrawalAmount / 100, 1: Details.everyRoundActivityValidtime, 2: Details.withdrawalValidtime})"-->
                    </ul>
              </div>
              <div class="sigin-c-footer" id="wheellist">
                <div style="font-size: .3rem; height: .6rem; background: transparent;color: red; text-align: center; font-weight: 600;">{{ $t('List of records') }}</div>
                <ul v-if="Query.ownnerRecords">
                  <li v-for="(item, index) in Query.ownnerRecords" :key="index">
                    <div class="no-claimed" style="display: flex; justify-content: space-between; align-items: center; padding: 0 .2rem; height: 1.8rem!important;">
                <div class="title" style="font-size: .26rem; display: flex;align-items: center">
<!--                  <div>{{ $t('rounds') }} {{ item.round }}</div>-->

                    <img src="img/activity/15/cash_1.png" style="width: 0.5rem; height: 0.6rem;margin-right: .1rem;" alt="">
                  {{ item['withdrawal_amount'] | formatGold }}
                </div>
                      <div class="reward-content" style="color:#6e6e6e;font-size: .2rem">
                        <p>{{ $t('rounds') }}: {{ item['round'] }} </p>
                        <p>{{ $t('created_time') }}: {{ item['create_time']*1000 | dateFormat }} </p>
                        <p>{{ $t('expires_time') }}: {{ item['expired_time']*1000 | dateFormat }} </p>
                      </div>
                      <div class="sigin-btn" >
                  <span v-if="item['confirm_time']" class="sigin-today-sub sb-disable" style="padding: 0 .2rem">
                    {{$t('claimed') }}
                  </span>

                        <span v-else @click="item['expired_time']*1000 > Date.now() && withdrawal(item.round)" :class="{'sb-disable': item['expired_time']*1000 < Date.now()}" class="sigin-today-sub" style="padding: 0 .2rem">
                    {{$t('claim') }}
                  </span>
                      </div>
                    </div>
                  </li>
                </ul>
                <van-empty v-else/>
              </div>
            </div>
    </div>
    <van-popup get-container="#app" round v-model="showWithdraw" :close-on-click-overlay="false" style="width: 6.82rem;
  height: 8.13rem;
  background: #F1F2F7;overflow: visible">
      <div style="position: fixed; bottom: -1rem; left: 44%" @click="showWithdraw = false">
        <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
      </div>
      <div style="margin-left: 0.39rem;margin-top: .3rem;">
        <span style="font-size: 0.28rem;color: #312E2A;line-height: 0.25rem;">{{ $t('activity_15_title_2' )}}</span>
      </div>
      <div style="display: flex; flex-direction: column;height: 83%;justify-content: space-around; align-items: center;font-size: 0.23rem;color: #312E2A;line-height: 0.25rem;">

        <div style="font-size: 0.24rem;color: #FF8200;line-height: 0.2rem;">{{ $t('activity_15_text_3' ) }}</div>
        <div style="font-weight: bold;font-size: 0.8rem;color: #FFB910;line-height: 0.97rem;">
          <count-to :start-val='preCashAmount' :end-val="Details.currentCashAmount / 100" :duration='1000' :decimals='2' :autoplay=true /> VND</div>

        <div>{{ $t('activity_15_text_4' ) }}</div>
        <div>{{ $t('activity_15_text_5' ) }}</div>


        <div style="width: 6.01rem;
height: 2.54rem;
background: #FFF;
border-radius: 0.08rem;overflow: scroll; text-align: center; padding: 0 .1rem">
          <van-steps direction="vertical" :active="stepActive" active-icon="img/activity/15/checked.png" finish-icon="img/activity/15/checked.png">
            <van-step>
              {{ $t('events_page.46.ACTIVITY_PROMOTE_TIP_1') }}
            </van-step>
            <van-step>
              {{ $t('events_page.46.ACTIVITY_PROMOTE_TIP_2',{money: $options.filters['currency'](cha)}) }}
            </van-step>
            <van-step>
              {{ $t('events_page.46.ACTIVITY_PROMOTE_TIP_3',{money: Details.withdrawalAmount / 100}) }}
            </van-step>
          </van-steps>
        </div>

        <div v-if="!Details.state" @click="showShare = true" style="width: 6.17rem;
height: 0.68rem;
background: #FFB627;line-height: 0.68rem;
border-radius: 0.08rem;text-align: center">
        <span style="font-weight: bold;
font-size: 0.28rem;
color: #FFFFFF;">{{ $t('activity_15_text_share' ) }}</span>
        </div>
        <a href="#wheellist" v-else @click="showWithdraw = false;" style="width: 6.17rem;
height: 0.68rem;
background: #FFB627;line-height: 0.68rem;
border-radius: 0.08rem;text-align: center">
        <span style="font-weight: bold;
font-size: 0.28rem;
color: #FFFFFF;">{{ $t('button_receive' ) }}</span>
        </a>
      </div>
    </van-popup>
    <van-popup get-container="#app" v-model="showShare" position="bottom" round :style="{ height: '3.61rem' }" style="background: #F9F9F9;">
      <div style="font-size: 0.23rem;
color: #1B1414;
padding: 0.17rem 0.19rem 0.19rem 0.18rem; height: 100%;display: flex;flex-direction: column;align-items: start;justify-content: space-between">

        <div style="color: #1B1414;">
<!--          1.-->
          {{ $t('activity_15_text_share' ) }}</div>

        <div style="height: 1.66rem; display: flex;justify-content: space-between;width: 100%;">
<!--          <div class="gird_item">-->
<!--            <img src="img/activity/15/mais.png" alt="" style="width: 0.97rem; height: 0.97rem;">-->
<!--            <span>Mais</span>-->
<!--          </div>-->
          <div class="gird_item" @click="share('sms')">
            <img src="img/activity/15/sms.png" alt="" style="width: 0.97rem; height: 0.97rem;">
            <span>SMS</span>
          </div>
          <div class="gird_item" @click="share('facebook')">
            <img src="img/activity/15/facebook.png" alt="" style="width: 0.97rem; height: 0.97rem;">
            <span>Facebook</span>
          </div>
          <div class="gird_item" @click="share('whatsapp')">
            <img src="img/activity/15/whatapp.png" alt="" style="width: 0.97rem; height: 0.97rem;">
            <span>WhatsApp</span>
          </div>
          <div class="gird_item" @click="share('telegram')">
            <img src="img/activity/15/telegram.png" alt="" style="width: 0.97rem; height: 0.97rem;">
            <span>Telegram</span>
          </div>
          <div class="gird_item" @click="share('twitter')">
            <img src="img/activity/15/twitter.png" alt="" style="width: 0.97rem; height: 0.97rem;">
            <span>Twitter</span>
          </div>
          <div class="gird_item" @click="share('email')">
            <img src="img/activity/15/email.png" alt="" style="width: 0.97rem; height: 0.97rem;">
            <span>Email</span>
          </div>
        </div>

        <div style="width: 6.84rem;
height: 0.6rem;
background: #EDEDED;
border-radius: 0.1rem;display: flex;justify-content: space-between;align-items: center;padding: 0 0.23rem">
          <span style="color: #1B1414;">{{ showUrl }}</span>
          <span class="copy-icon copy copy-btn" :data-clipboard-text="showUrl" style="
color: #FF3600;">{{ $t('label_copy') }}</span>
        </div>
      </div>

    </van-popup>
  </section>
</template>
<style scoped>
.finished {
  background-image : linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}

::v-deep .van-progress__pivot {
  font-size: 12px !important;
}
::v-deep .van-tab__text {
  font-size: 0.32rem !important;
  font-weight: 500;
  color: #000000 !important;
}
::v-deep .van-tabs__line {
  width: 1rem;
}
::v-deep .van-tabs--line .van-tabs__wrap {
  height: .8rem;
}

::v-deep .van-tab__text--ellipsis {
  overflow: visible;
}


::v-deep #cha {
  margin: 0 0.1rem !important;
  font-size: 0.37rem !important;
  color: #FD3C3C !important;
  line-height: 0.31rem !important;
}


.finished {
  background-image : linear-gradient(0deg, #56cf30 0, #38a888 100%) !important;
}

.sigin-c-footer li {
  width: 100%;
}
.sigin-btn .already-singin, .sigin-btn .sigin-today-sub {
  min-height: .5rem;
}



::v-deep .van-steps {
  background-color : transparent;
}
::v-deep .van-step--finish {
  color: #FFB627;
}

::v-deep .van-step {
  font-size: 0.18rem;
}

.van-step--vertical {
  line-height: 0.84rem;
  padding: 0;
}

::v-deep .van-step__circle {
  height: 0.14rem;
  width: 0.14rem;
}
::v-deep .van-step--vertical .van-step__circle-container{
  font-size: 0.23rem;
  top: 0.46rem;
}
::v-deep .van-step--vertical .van-step__line {
  top: 0.43rem;
  left: -16px;
}

.gird_item {
  display: flex;flex-direction: column;justify-content: space-between;align-items: center
}
</style>