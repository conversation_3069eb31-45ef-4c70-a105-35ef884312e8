<script>
import {type5} from "@/mixins/tdtc/events/type5";

export default {
  mixins: [type5]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.5`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('img/activity/11-1.png')"
      >
      </div>
      <div class="sigin-content" style="position: absolute">
        <div style="margin: .3rem .2rem 0;">
          <van-button @click="submit" type="warning" block round >{{ $t('events_page.5.TEXT_BTN_GET') }}</van-button>
        </div>

        <div class="sigin-c-footer">
          <ul style="display: flex;justify-content: space-between;flex-wrap: wrap">
            <li v-for="(item, index) in conf" :key="index">
              <div class="no-claimed">
                <span class="title" style="align-self: center;text-align: center">{{ item.award_money | formatGoldWithK }}
                </span>
                <div class="item-a si-item"></div>
                <div class="reward-content" style="text-align: center;">
                  {{$t('a6.selfoperatedGames.sports.lose')}} ≥ {{ item.loss_money | formatGold }}
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
        <div class="sigin-c-remarks">
          <b class="sigin-rule">{{ $t('invite_rules') }}</b>
          <div>
            <div class="wysiwyg">
              <p>
                <span class="ql-size-large">
                  {{ $t('events_page.5.TEXT_LS') }}
                  <br />
                  {{ $t('events_page.5.TEXT_LS_COPY') }}
                  <br />
                  {{ $t('events_page.5.TEXT_HINT') }}
                  <br />
                  {{ $t('events_page.5.TEXT_HINT2') }}
                </span>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<style scoped>
.no-claimed {
  display: flex;flex-direction:column;justify-content: space-around;
  padding: .2rem 0;
}
.sigin-c-footer .title {
  margin-bottom : unset;
  padding-bottom: unset;
  margin-top: unset;
}
</style>