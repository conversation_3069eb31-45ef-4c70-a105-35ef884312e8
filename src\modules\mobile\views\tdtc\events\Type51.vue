<script>
import {type51} from "@/mixins/tdtc/events/type51";
import RecordBoard from '@/modules/mobile/components/RecordBoard.vue'
import RedPacketGame from '@/components/RedPacketGame.vue'

export default {
  components: {
    RedPacketGame,
    RecordBoard},
  mixins: [type51]
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.type.51`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div style="font-size: .28rem;display: flex;justify-content: center;margin-top: .2rem; flex-direction: column;position: absolute; top:2rem; right:.7rem; color: #fff9f9">
      <div>{{ $t('REDBAG.TIPS_4') }}</div>
      <div style="display: flex;flex-direction: column">
        <span v-for="item in res.config.time_configs" style="margin-left: .2rem;"> {{ item.start_time }} - {{ item.end_time }}</span>
      </div>
    </div>
    <div class="sigin-container" style="margin-bottom: 0.5rem;">
      <div
          class="mall-home-top signin-activity-card"
          style="background-image: url('/img/activity/bg/51.png');background-position-y: -.2rem;height: 4.6rem"
      >
      </div>
      <div class="sigin-content">
        <div class="sigin-c-header" >
          <div style="font-size: .23rem;display: flex;justify-content: left;">
            Đếm ngược thời gian nhận lì xì đỉnh cao:
            <van-count-down v-if="res.config.redpacket_time_countdown" :time="res.config.redpacket_time_countdown*1000" class="top-item" />
            <span v-else style="margin-top: -.01rem;" class="top-item">00:00:00</span>
          </div>

          <div style="font-size: .23rem">
            Số lượng lì xì có thể rút được: <span class="top-item">{{ res.extract_amount | formatGold }}</span> <span @click="res.extract_amount && get45()" :style="{color: res.extract_amount ? 'red': '#ccc'}" style="margin-left: .2rem;text-decoration: underline;">Rút</span><br>
            <p></p>
            Số lần nhận lì xì hiện tại của bạn: <span class="top-item">{{ res.lottery_count }}</span> <br>
            <p style="font-size: .2rem;color: #ccc" v-html="$t('ACTIVITY_SUPERRED_TIPS', {inviteCount: res.lty_count_by_invite,rgCount: res.lty_count_by_recharge})"></p>
            <p style="margin-top: .1rem;">Làm thế nào để tăng số lần:
              <span @click="$router.push('/m/voucherCenter')" class="top-item" style="text-decoration: underline;">Nạp tiền</span>
              <span @click="share = !share" class="top-item" style="text-decoration: underline;margin-left: .4rem;">mời</span></p>
            <div style="text-decoration: underline;display: flex;justify-content: center"></div>
          </div>
        </div>
        <div class="sigin-c-content">
        </div>

        <div style="margin: 0 .2rem; display: flex">
          <van-button @click="liXi = true" type="warning" block round :disabled="!res.lottery_count || res.config.redpacket_time_countdown">Nhận lì xì</van-button>
        </div>
        <van-popup lock-scroll :close-on-click-overlay="true" v-if="liXi" v-model="liXi">
          <RedPacketGame @close="liXi = false; query43()"/>
          <div style="position: fixed; bottom: -1.2rem; left: 44%" @click="liXi = false; query43()">
            <img src="/img/tdtc/events/close.png" alt="" style="width: .64rem">
          </div>
        </van-popup>

        <div class="invite-friends-container" v-if="share">
          <div class="invite-friends-link">
            <div class="link-top-title">Link giới thiệu cá nhân của bạn</div>
            <VueQr
                :logoMargin="2"
                :text="showUrl"
                :size="160"
                :margin="5"
            />
            <div class="link-subtitle">
              {{ $t('invite_link') }}
              <svg style="fill: #8c8a8a !important;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="showUrl">
                <use xlink:href="#icon-copy"></use>
              </svg>
            </div>

            <div class="invite-get-link">
              <div class="link-url">{{ showUrl }}</div>
            </div>
          </div>
        </div>

        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>
<!--        <div class="sigin-c-remarks" >-->
<!--          <b class="sigin-rule">{{ $t('label_proportion_tip1') }}</b>-->
<!--          <p>-->
<!--             <span class="ql-size-large">-->
<!--               {{ $t('events_page.10.TEXT_TIP1') }}-->
<!--               <br />-->
<!--               {{ $t('events_page.10.TEXT_TIP2') }}-->
<!--               <br />-->
<!--               {{ $t('events_page.10.TEXT_TIP6') }}-->
<!--             </span>-->
<!--          </p>-->
<!--        </div>-->
        <div class="sigin-c-remarks">
          <b class="sigin-rule">Hướng dẫn cho người mới bắt đầu</b>
          <div>
            <div class="wysiwyg">
              <p>{{ $t('ACTIVITY_TUTORIAL_RULE_1') }}</p>
              <p>{{ $t('ACTIVITY_TUTORIAL_RULE_2', {betAmount: this.$options.filters['formatGold'](res.config.dl_b)}) }}</p>
              <p>{{ $t('ACTIVITY_TUTORIAL_RULE_3', {rgAmount: this.$options.filters['formatGold'](res.config.dl_gv_rg_amount)}) }}</p>
              <p>{{ $t('ACTIVITY_TUTORIAL_RULE_4') }}</p>
              <p>{{ $t('ACTIVITY_TUTORIAL_RULE_5', {invitePcnt: res.config.dl_gv_invite_pcnt,inviteMax: res.config.dl_gv_invite_max}) }}</p>
              <p>{{ $t('ACTIVITY_TUTORIAL_RULE_6', {rgAmount: this.$options.filters['formatGold'](res.config.dl_gv_rg_amount),dl_gv_rg_count: res.config.dl_gv_invite_max,rgMax: res.config.dl_gv_rg_max}) }}</p>
            </div>
          </div>
        </div>
        <record-board :column="column" :data="res.ranking_records" :useTable="true"/>
      </div>
    </div>
  </section>
</template>
<style scoped>
::v-deep .van-popup{
  background-color: unset;
  overflow: visible;
}
::v-deep .record-board table th {
  width: unset;
}
::v-deep .record-board table tr {
  line-height : unset;
}

.top-item {
  font-size: .26rem;
  color:red;
  margin-left: .2rem;
}
</style>