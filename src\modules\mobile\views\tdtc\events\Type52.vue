<script>
import { type52 } from "@/mixins/tdtc/events/type52";

export default {
  mixins: [type52],
  methods: {
    formatter(day) {
      const month = day.date.getMonth() + 1;
      const date = day.date.getDate();
      this.res.receive_records.forEach((item)=>{

        if (month === item.month && date === item.day) {
          if (item.flag) {
            day.className = 'checkin_status_' + item.flag
          } else {
            day.className = 'checkin_status_0'
          }
        }
      })
      return day;
    },
  },

}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-signIn am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use>
              </svg></span></span>
        </div>
        <div class="am-navbar-title">{{ $t(`events_page.type.52`) }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div
        style="margin-bottom: 0.5rem;background-image: url('img/activity/52/bg.png');background-position-y: -.5rem; background-size: contain;background-repeat: no-repeat;width: 100%;min-height: 10.6rem;">
      <div class="light_box">
        <img src="img/activity/52/1.png" style="width: 7.33rem; height: 7.66rem" alt="">
      </div>
      <div>

      </div>
      <div
          style="padding-top:.3rem;margin-top: .4rem;height: calc(100vh - 6rem);background: linear-gradient(180deg, #FFE9A7, #FFFFFF);z-index: 1; position: relative; overflow: scroll"
          class="sigin-content">

        <van-calendar style="background-color: unset; height: unset"
            :show-title="false"
            :poppable="false"
            :min-date="minDate"
            :max-date="maxDate"
            :formatter="formatter">
          <template #footer>
            <div style="display: flex;justify-content: center">
              <div style="font-size: .26rem;color: #C25E00">Đếm ngược sự kiện: <span style="color: #EC0000;">{{intervalDownElement}}</span></div>
            </div>
          </template>
        </van-calendar>
        <div class="panel">
          <div class="panel-title">
            <span>Tổng trợ cấp có thể nhận: {{ res.total_amount | formatGold }}</span>
            <div class="panel-item-right">
              <span @click="res.total_amount && get48()" :style="{color: res.total_amount ? '#FF4A0B':'#ccc'}" style="text-decoration: underline; font-weight: bold">Nhận tất cả</span>
              <img src="/img/activity/52/arrow.png" alt="">
            </div>
          </div>
          <div class="panel-item">
            <img src="/img/activity/wheel/Rewards_5.png" alt="">
            <span>Trợ cấp hôm nay: {{ res.today_amount | formatGold }}</span>
          </div>
          <div class="panel-bottom" v-if="res.today_receive_duration">Đếm ngược thời gian nhận thưởng: <van-count-down format="DDd HH:mm:ss" :time="res.today_receive_duration*1000" style="font-size: .23rem;color:red;margin-left: .2rem;"/></div>
        </div>
        <div class="panel" style="filter: grayscale(0.2);">
          <div class="panel-title">
            <span>Tổng trợ cấp đã hết hạn: {{ res.total_expired_amount | formatGold }}</span>
            <div class="panel-item-right">
              <span></span>
              <img src="/img/activity/52/arrow.png" alt="">
            </div>
          </div>
          <div class="panel-item">
            <img src="/img/activity/wheel/Rewards_5.png" alt="">
            <span>Trợ cấp hết hạn hôm nay: {{ res.today_expired_amount | formatGold }}</span>
          </div>
          <div class="panel-bottom" v-if="res.today_expired_duration">Đếm ngược thời gian hết hạn: <van-count-down format="DDd HH:mm:ss" :time="res.today_expired_duration*1000" style="font-size: .23rem;color:red;margin-left: .2rem;"/></div>
        </div>
        <div style="width: 7.5rem;
height: 0.43rem;
background: #F7BC88;display: flex;justify-content: center;color: #7C290B;">Quy tắc & Hướng dẫn</div>
        <div style="font-size: 0.23rem;
color: #CE7628;
padding: .2rem;">
          <p>Thời gian sự kiện:<br/>
            từ ngày {{res.config.active_start_time}} đến {{res.config.active_end_time}}.</p>
          <br/>
          <p>{{ $t('ad.panel.18.tip.0', {0:res.config.tax_to_award_pro}) }}</p>
          <p>{{ $t('ad.panel.18.tip.1', {0:res.config.receive_time_duration / 24, 1:res.config.receive_time - res.config.receive_time_duration}) }}</p>
          <p>{{ $t('ad.panel.18.tip.2', {0:res.config.receive_time_duration / 24}) }}</p>
          <p>{{ $t('ad.panel.18.tip.3') }}</p>
          <p>{{ $t('ad.panel.18.tip.4') }}</p>
          <p>{{ $t('ad.panel.18.tip.5') }}</p>
        </div>

        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>

      </div>
    </div>
  </section>
</template>
<style scoped>
::v-deep .van-calendar__selected-day {
  background-color : unset;
  color: #FFB627;
}
::v-deep .van-calendar__weekday {
  font-size: .23rem;
  background: #0DBC5A;
  color: #FFFFFF;
}
::v-deep .van-calendar__day {
  height: .8rem;
}
.light_box {
  position: relative;
  height: 4.5rem;
}

.panel {
  width: 7.36rem;
  height: 2.16rem;
  background: #F7E6D7;
  border-radius: 0.1rem;
  margin: 0.2rem auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .panel-title {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.23rem;
    color: #EB803D;
    margin-bottom : .1rem;
    padding: 0 .2rem;

    .panel-item-right {
      display         : flex;
      justify-content : center;
      align-items     : center;

      img {
        width  : .38rem;
        height : .38rem;
        margin-left : .1rem;
      }
    }

  }
  .panel-bottom {
    font-size: .2rem;
    margin-top: .1rem;
    color: #a76950;
    display: flex;
    justify-content: left;
    align-items: center;
  }
  .panel-item {
    width: 6.92rem;
    height: 0.86rem;
    background: linear-gradient(90deg, #EB7F3E, #F8B10B);
    box-shadow: 0 0 0 0 rgba(0,0,0,0.19);
    border-radius: 0.1rem;
    display: flex;
    align-items: center;
    color: #FFFFFF;
    font-size: 0.3rem;


    img {
      width: 0.75rem;
      height: 0.62rem;
      margin: 0 .2rem;
    }
  }
}
</style>