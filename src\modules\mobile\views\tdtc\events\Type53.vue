<script>
import { type53 } from "@/mixins/tdtc/events/type53";

export default {
  mixins: [type53],
}
</script>
<template>
  <section>
    <div class="mc-header-wrap">
      <div id="mc-header" class="mc-navbar-blue mc-signIn am-navbar am-navbar-light">
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"><span class="return_icon"><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use>
              </svg></span></span>
        </div>
        <div class="am-navbar-title" style="font-size: .32rem !important;">{{ $t(`events_page.type.53`) }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div
      style="margin-bottom: 0.5rem;background-image: url('img/activity/19/bg.png');background-size: contain;background-repeat: no-repeat;width: 100%;min-height: 10.6rem;">
      <div class="light_box">
        <div v-for="index in 7" :key="index">
          <span>Ngày thứ {{ index }}</span>
          <img v-if="index <= res.checkin_day_id" src="img/activity/19/on.png" alt="" class="swing">
          <img v-else src="img/activity/19/off.png" alt="">
        </div>
        <van-count-down v-if="res.checkin_duration"  :time="res.checkin_duration*1000" class="top-item"  style="position:absolute;bottom: 0.1rem;right: 1.5rem;font-size: .26rem; color: rgb(236, 0, 0)"/>
      </div>
      <div>
        
      </div>
      <div class="light_action">
        <div @click="!res.bind_phone && $router.push('/m/myAccount/phone')">{{$t(res.bind_phone ? "ad.panel.19.SIGNIN_BINDPHONE_2" : "ad.panel.19.SIGNIN_BINDPHONE_1")}}</div>
        <div :class="{'hasCheck': res.today_is_checkin || res.checkin_duration}" @click="!res.today_is_checkin && !res.checkin_duration && get50()">{{ $t('ad.panel.19.SIGNIN_STATUS_1') }}</div>
      </div>
      <div
        style="padding-top:.2rem;margin-top: .4rem;height: calc(100vh - 7.9rem);background: linear-gradient(180deg, #EEE5FF, #FFFFFF);overflow: scroll"
        class="sigin-content">
        <div style="display: flex;justify-content: center; margin: .1rem 0">
          <div style="font-size: .26rem;color: #C25E00">Đếm ngược sự kiện: <span style="color: #EC0000;">{{intervalDownElement}}</span></div>
        </div>
        <div style="width: 7.5rem;
height: 0.43rem;
background: #F7BC88;display: flex;justify-content: center;color: #7C290B;">Quy tắc & Hướng dẫn</div>
        <div style="font-size: 0.23rem;
color: #7D1000;
padding: .2rem;">
          <p>Thời gian sự kiện:<br/>
            từ ngày {{res.active_start_time}} đến {{res.active_end_time}}.</p>
          <br/>
          <p>{{ $t('ad.panel.19.tip.0', {0:res.checkin_max_day_id}) }}</p>
          <p>{{ $t('ad.panel.19.tip.1', {0:res.last_check_in_time, 1:res.checkin_max_day_id}) }}</p>
          <p>{{ $t('ad.panel.19.tip.2') }}</p>
        </div>
        
        <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
          <p>
            <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
            <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
            <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
          </p>
          <p v-if="currentActivity.withdrawRate">
            <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
          </p>
        </div>

      </div>
    </div>
  </section>
</template>
<style scoped>
.swing {
  animation: swing 2s ease-in-out infinite alternate;
  transform-origin: top center; /* 设置旋转中心点 */
}
@keyframes swing {
  0% {
    transform: rotate(-15deg);
  }
  100% {
    transform: rotate(15deg);
  }
}
.hasCheck {
  filter: grayscale(1)
}

.light_box {
  position: relative;
  height: 5.8rem;

  div {
    font-size: 0.16rem;
    color: #FFFFFF;
    display: flex;
    flex-direction: column;
    position: absolute;

    span {
      width: 0.93rem;
      height: 0.3rem;
      background: #B46656;
      border-radius: 0.06rem;
      margin-bottom: .2rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    img {
      width: 0.83rem;
      height: 1.35rem;
      box-shadow: 0rem 0rem 0rem 0rem rgba(0, 0, 0, 0.15);
    }
  }

  div:nth-child(1) {
    top: 1.45rem;
    left: 0.1rem;
  }

  div:nth-child(2) {
    top: 2.36rem;
    left: 1.8rem;
  }

  div:nth-child(3) {
    top: 1.5rem;
    left: 2.8rem;
  }

  div:nth-child(4) {
    top: 1.26rem;
    right: 1.8rem;
  }

  div:nth-child(5) {

    top: 0.53rem;
    right: 0.1rem;
  }

  div:nth-child(6) {
    top: 3.3rem;
    right: 0.4rem;
  }

  div:nth-child(7) {
    top: 3rem;
    left: 4rem;
  }
}

.light_action {
  display: flex;
  justify-content: space-around;
  align-items: center;
  color: #FFFFFF;
  padding: 0 .3rem;

  div {
    width: 3.09rem;
    height: 0.64rem;
    border-radius: 0.1rem;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  div:nth-child(1) {
    background: linear-gradient(90deg, #EC833A, #F9B606);
  }

  div:nth-child(2) {
    background: linear-gradient(90deg, #D31176, #F471DD);
  }
}
</style>