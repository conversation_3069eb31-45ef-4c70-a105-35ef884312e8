<script>
import {type6} from "@/mixins/tdtc/events/type6";

export default {
  mixins: [type6],
}
</script>
<template>
  <div style="background: #F9F9F9;">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-signIn am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">
          <span class="van-ellipsis">{{ $t(`events_page.6.LAYER_TITLE`) }}</span>
        </div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-tabs v-draggable v-model="index" animated line-width="20%" line-height="2px"  color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="3">
      <van-tab :title="$t('events_page.6.BUTTON_1')"></van-tab>
      <van-tab :title="$t('events_page.6.BUTTON_2')"></van-tab>
      <van-tab :title="$t('events_page.6.BUTTON_3')"></van-tab>
      <van-tab :title="$t('events_page.6.BUTTON_4')"></van-tab>
      <van-tab :title="$t('events_page.6.BUTTON_5')"></van-tab>
    </van-tabs>
    <div style="padding: .2rem; width: 100%">
      <div style="display:flex;align-items: center;  font-weight: 600;
          margin: .1rem 0;
font-size: 0.26rem;
color: #495064;
line-height: 0.35rem;">
        <img src="/img/tdtc/tasks/coin.png" width="18" alt="">
        <span style="margin-left: .1rem;">{{ $t('events_page.6.TIP_1') }} </span> &nbsp;
        <span style="color: #FF8200;"> {{ res['task_score'] ? res['task_score'] : 0 }}</span>
      </div>
      <swiper :options="swiperOptions">
        <swiper-slide
            v-for="item in res['money_config']"
            :key="item['award_index']"
        >
          <div @click="getLivenessAward(item['award_index'])">
            <div :class="{'received': item['flag']}" style="width: 1.71rem;
height: 2.1rem;background: linear-gradient(180deg,rgba(234, 239, 244, 1), rgba(220, 228, 235, 1));
border-radius: 0.08rem;display: flex;flex-direction: column;align-items: center; justify-content: center">
              <div style="font-size: 0.26rem;color: #495064;">{{ item['award_money'] | formatGold }}</div>
              <div :style="{backgroundImage: 'url(/img/tdtc/tasks/cash_'+ item['award_index'] +'.png)'}" style="width:100%;background-size: .93rem;background-repeat: no-repeat;background-position:center;height: .93rem;">
              </div>
              <div style="font-weight: 600;display: flex;align-items: center;justify-content: center;
font-size: 0.26rem;
color: #495064;">
                <img src="/img/tdtc/tasks/coin.png" width="15" alt="">
                <span style="margin-left: .05rem;">{{ item['need_score']}}</span>
              </div>
            </div>
            <div style="height: .42rem;line-height: .42rem;background: #FFB627;border-radius: .12rem;margin: .15rem .05rem .1rem;font-size: 0.23rem;text-align: center;
color: #FFFFFF;">
              {{ item['flag'] ? $t('events_page.6.BTN_FINISH') : $t('events_page.6.BTN_GET') }}
            </div>
          </div>
        </swiper-slide>
      </swiper>
      <div class="sigin-c-remarks" v-if="currentActivity.awardType || currentActivity.withdrawRate">
        <p style="font-size: .22rem">
          <span v-if="currentActivity.awardType === 1" class="ql-size-large">{{ $t('TIP_AWARD_TO1') }}</span>
          <span v-if="currentActivity.awardType === 2" class="ql-size-large">{{ $t('TIP_AWARD_TO2') }}</span>
          <span v-if="currentActivity.awardType === 3" class="ql-size-large">{{ $t('TIP_AWARD_TO3') }}</span>
        </p>
        <p style="font-size: .22rem" v-if="currentActivity.withdrawRate">
          <span class="ql-size-large">{{ $t('TIP_AWARD_RATE', {money: currentActivity.withdrawRate}) }}</span>
        </p>
      </div>
      <ul v-if="res['info']">
        <li v-for="item in res['info'].filter(row=>row['task_type1'] === index+1)" style="margin: 0.05rem 0 .1rem;height: 1.7rem;
background: #FFFFFF;
border-radius: 0.08rem; display: flex;justify-content: space-between;align-items: center;padding: 0 .1rem">
          <div style="display: flex;justify-content: center;align-items: center">
            <img src="/img/tdtc/tasks/coin.png" width="38" alt="">
            <span style="margin-left: .1rem;font-weight: 600;
font-size: 0.32rem;
color: #495064;">x{{ item['award_score'] }}</span>
          </div>
          <div style="flex: 1;padding-left: .2rem;padding-right: .1rem;overflow: hidden">
            <div class="text-ellipsis" style="font-weight: 600;color: #FFB627;font-size: .23rem;">{{ $t(`events_page.6.TASK_NAME_PAGE_${item['task_type1']}_${item['task_type2']}`) }}</div>
            <div class="text-ellipsis" style="margin-top: .1rem;color: #7F8392;font-size: .2rem">{{ item['task_type1'] === 1 ? $t(`events_page.6.TASK_MISSION_PAGE_${item['task_type1']}_${item['task_type2']}`, {}) :
                (item['task_type1'] === 2 || item['task_type1'] === 5 ? $t(`events_page.6.TASK_MISSION_PAGE_${item['task_type1']}_${item['task_type2']}`, {"money":$options.filters['formatGold'](item['target_num'])}) :
                        $t(`events_page.6.TASK_MISSION_PAGE_${item['task_type1']}_${item['task_type2']}`, {"num":item['target_num']})
                )
              }}</div>
          </div>
          <div style="text-align: center;display: flex;flex-direction: column;align-items: end" @click="(item['progress'] && item['progress'] >= item['target_num']) && getTaskItemAward(item)">
            <div style="">
              <span>
                <i style="font-weight: bold;font-size: 0.38rem;color: #FF8200;" v-if="index === 1 || index === 4">{{ item['progress'] | formatGold }}</i>
                <i style="font-weight: bold;font-size: 0.38rem;color: #FF8200;" v-else>{{ item['progress'] ? item['progress'] : 0 }}</i>
                <i style="font-size: 0.2rem; color: #68416C;" v-if="index === 1 || index === 4"> / {{ item['target_num'] | formatGold }}</i>
                <i style="font-size: 0.2rem; color: #68416C;" v-else> / {{ item['target_num'] }}</i>
              </span>
            </div>

            <div style="width: 1.62rem;
height: 0.48rem;
background: #FFB627;
border-radius: 0.12rem; font-size: 0.22rem;
color: #FFFFFF;
line-height: 0.48rem;" :class="{'sb-disable': !item['progress'] || item['progress'] < item['target_num'] || item['flag']}">{{ item['flag'] ? $t('events_page.6.BTN_FINISH') : $t('events_page.6.BTN_GET') }}</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<style scoped lang="scss">

.swiper-slide {
  width: unset !important
}
::v-deep .van-tab__pane {
  height: unset !important
}

.text-ellipsis {
//  width: 100%;
//  overflow: hidden;
//  white-space: nowrap;
//  text-overflow: ellipsis;
}

.received {
  background: linear-gradient(180deg, rgba(51, 159, 238, 1), rgba(176, 131, 207, 1)) !important;
  div {
    color: #FFFFFF !important;
  }
}
</style>