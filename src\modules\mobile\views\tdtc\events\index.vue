<script>
import {play} from "@/mixins/play";

export default {
  name:   "rewardCenter",
  mixins: [play],
  data() {
    return {
      icons: {
        5:   "#icon-a-zu33",
        7:   "#icon-a-zu333",
        8:   "#icon-a-zu3314",
        9:   "#icon-a-zu335",
        10:  "#icon-a-zu332",
        11:  "#icon-a-zu336",
        18:  "#icon-a-zu331",
        26:  "#icon-a-zu3313",
        200: "#icon-a-zu338",
        201: "#icon-a-zu3312",
        45:  "#icon-a-zu3311",
        46:  "#icon-a-zu3310",
        202: "#icon-a-zu339",
        51: "#icon-red",
        52: "#icon-a-relieffund",
        53: "#icon-a-signin",
      },
      events: [
        46, // :12,        //推广现金 Kiếm tiền bằng cách quảng cáo
        // 49, // 推广豪礼
        51, // 至尊红包雨
        52, // 补助金
        53, // 签到现金奖金
        18, // :0,         //每日首冲 Nạp tiền lần đầu mỗi ngày
        45, // :9,         //累计充值 Phần thưởng tích lũy nạp tiền
        201, // :10,       //见面礼 Quà gặp mặt
        200, // :11,       //分享奖励 Thưởng chia sẻ
        8, // :8,          //新人礼包 Gói thưởng hội viên mới
        26, // :7,         //流水奖励 Điểm cược mỗi ngày
        10, // :6,         //红包雨 Mưa lì xì thiên sứ giáng trần
        7, // :1,          //成长基金 Tích điểm trưởng thành
        9, // :2,          //签到奖励 Lĩnh thưởng hàng ngày
        11, // :3,         //转盘 Thưởng Online trực tuyến
        // 11, // :4,         //在线奖励 Vòng quay may mắn
        5, // :5,          //救援金 Cứu trợ hàng ngày
      ]
    }
  },
  mounted() {
    this.event_enterEventCenter()
  },
  computed: {
    activityTypes() {
      const types = this.$store.state.activitySwitchDetails.map(item => item.activityType);
      return this.events.filter(type=>types.includes(type))
    }
  },
  methods: {
    go(activityType) {
      this.$router.push("/m/events/" + activityType)
    }
  }
};
</script>

<template>
  <div class="reward-center-container">
    <div class="reward-center" style="background-image: unset">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            style="background: #1b2033 !important; position: fixed !important;"
            class="mc-navbar-blue mc-rewardCenter am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
            <span class="am-navbar-left-content"
            ><span class="return_icon"
            ><svg class="am-icon am-icon-left am-icon-lg">
                  <use xlink:href="#left"></use></svg></span
            ></span>
          </div>
          <div class="am-navbar-title">{{ $t('events') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <img src="/img/tdtc/events/banner.png" style="width: 100%; height: 100%;padding: .2rem" alt="">
      <div class="task-list-warp" style="margin: 0 .2rem;">
        <div style="background-image: url(/img/tdtc/events/bg.png);background-repeat:no-repeat;background-size:contain;padding: .15rem 0.3rem .2rem .6rem;background-position: -.01rem 0;
        width: 100%;display: flex; justify-content: space-between;align-items:center; font-weight: 600;
font-size: 0.26rem;
color: #8B523A;margin-bottom: .1rem;"
            @click="go(item)"
            v-for="(item, i) in activityTypes" :key="i">
          <svg class="icon svg-icon" aria-hidden="true" style="width: .6rem;height: .6rem; margin-right: .72rem;fill:#FB8E00">
            <use v-bind:xlink:href="icons[item]"></use>
          </svg>
          <div style="flex-grow: 1;">
          {{ $t(`events_page.type.${item}`) }}
          </div>
          <svg style="height: .3rem" data-v-3f3e1ad8="" color="#A6A6A6" class="am-icon am-icon-rightarrows_e029c01f am-icon-md">
            <use data-v-3f3e1ad8="" xlink:href="#rightarrows_e029c01f"></use>
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
