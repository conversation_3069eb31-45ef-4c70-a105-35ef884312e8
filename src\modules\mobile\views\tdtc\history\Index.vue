<template>
  <div class="invite-friends-root" ref="inviteRoot" style="background-color: white;">
    <div class="mc-header-wrap" ref="search">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-inviteFriends am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('ONLINEGAMEDETAIL.LAYER_TITLE') }}</div>
        <div class="am-navbar-right"></div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <van-tabs v-model="index" animated line-width="20%" line-height="2px" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="3">
      <van-tab :title="$t('ONLINEGAMEDETAIL.LAYER_TITLE1')">
        <Tab0 v-cloak/>
      </van-tab>
      <van-tab :title="$t('ONLINEGAMEDETAIL.LAYER_TITLE2')">
        <Tab1 v-cloak/>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script>
import Tab0 from "@/modules/mobile/views/tdtc/history/Tab0.vue";
import Tab1 from "@/modules/mobile/views/tdtc/history/Tab1.vue";

export default {
  components: {Tab0, Tab1},
  data() {
    return {
      index: 0,
    }
  },
}
</script>

<style scoped lang="scss">
::v-deep .van-tabs__nav {
  background-color : #F9F9F9 !important;
}
</style>
