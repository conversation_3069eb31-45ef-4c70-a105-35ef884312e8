<script>
import Tab0 from "@/modules/mobile/views/tdtc/rank/Tab0.vue";
import Tab1 from "@/modules/mobile/views/tdtc/rank/Tab1.vue";
import Tab2 from "@/modules/mobile/views/tdtc/rank/Tab2.vue";

export default {
  components: {Tab2, Tab1, Tab0},
  data() {
    return {
      index: 0,
    }
  },
};
</script>

<template>
  <div :class="`td-rank td-rank${index}`">
    <div class="comp-bg"></div>
    <div style="position: fixed !important;width: 100%; z-index: 1; top: 0;max-width: var(--theme-max-width)">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-home am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button">
          <span class="am-navbar-left-content" @click="$router.back()">
            <span class="return_icon">
              <svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg>
            </span>
          </span>
          </div>
          <div class="am-navbar-title" style="font-size: .4rem !important;font-weight: 600;">{{ $t('RANK.LAYER_TITLE') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <van-tabs duration="0.6" v-model="index" animated line-width="20%" line-height="2px" swipe-threshold="1" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#818181">
        <van-tab :title="$t('RANK.TIP_1')">
          <Tab0 v-cloak/>
        </van-tab>
        <van-tab :title="$t('RANK.TIP_2')">
          <Tab1 v-cloak />
        </van-tab>
        <van-tab :title="$t('RANK.TIP_3')">
          <Tab2 v-cloak />
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>
<style lang="scss">

.td-rank {

  .van-tab {
    color: #636E7C;
    font-size: .26rem;
    font-weight: 600;
  }
  .plat-1 {
    background: #FF8200;
  }
  .plat-2, .plat-3 {
    background: #FCB541;
    color: #FFFFFF;
    padding-top: .1rem;
  }

  .comp-bg {
    width: 7.5rem;
    height: 6.02rem;
    //background: linear-gradient(0deg, rgba(73, 87, 109, 1), rgba(46, 46, 46, 1)) !important;
    background: #222222 !important;
  }

  .van-tabs__nav {
    background-color: unset;
  }

  .van-tab__pane {
    height: 100vh;
    background: unset;
  }
  .tab-pre3 {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-end;
    font-weight: 400;
    font-size: 0.2rem;
    color: #FFFFFF;
    margin-top: .1rem;

    .ribbon {
      background-size: cover;height: .3rem;width: 1.2rem;margin-top: -.1rem;
      padding-top: .05rem;
      font-weight: bold;
      font-size: 0.2rem;
      color: #FFFFFF;
    }

    div {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center
    }

    .plat-1 {
      width: 3.05rem;
      height: 1rem;
      font-weight: bold;
      border-top-left-radius: 0.18rem;
      border-top-right-radius: 0.18rem;
      font-size: 0.88rem;
      color: #F2F6FF;
    }
    .plat-2 {
      width: 1.8rem;
      height: .68rem;
      border-top-left-radius: 0.18rem;
      font-weight: bold;
      font-size: 0.68rem;
    }
    .plat-3 {
      width: 1.8rem;
      height: 0.68rem;
      border-top-right-radius: 0.18rem;
      font-weight: bold;
      font-size: 0.68rem;
    }
    .li-data {
    //  font-weight: 600;
    //  font-size: 0.23rem;
      color: #FF8200;
      display: flex;
      flex-direction: row !important;
      justify-content: center;
      align-items: center;
      > svg {
        margin-right: .05rem;
        width: .21rem;
        height: .21rem;
      }
    }
    img:first-child {
      margin-top: .08rem;
      margin-bottom: -.08rem;
      z-index: 1;
    }
  }


  ul {
    position: absolute;
    width: 100%;
    height: calc(100vh - 5.7rem);
    background: #F9F9F9;
    border-top-left-radius: .18rem;
    border-top-right-radius: .18rem;
    padding: .17rem .14rem 2rem .14rem;
    overflow: scroll;
    li {
      width: 7.22rem;
      height: 0.96rem;
      background: linear-gradient(180deg, rgba(255, 251, 244, 1),rgba(255, 255, 253, 1));
      border-radius: 0.1rem;
      border: 0.02px solid #CECDC9;
      margin-bottom: .14rem;

      padding: 0 .36rem 0 .5rem;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .li-rank {
        width: 1rem;
        font-weight: bold;
        font-size: 0.38rem;
        color: #312E2A;
      }
      .li-avatar {
        width: 0.7rem;
        height: 0.7rem;
        margin-right: .2rem;
        background: #EFF6FF;
        border-radius: 50%;
        border: 0.02px solid #FFB070;
      }
      .li-title {
        font-weight: bold;
        font-size: 0.26rem;
        color: #FCB541;
      }
      .li-data {
        font-weight: 600;
        font-size: 0.23rem;
        color: #FF8200;
        display: flex;
        flex-direction: row !important;
        justify-content: center;
        align-items: flex-start;
        > svg {
          margin-right: .1rem;
        }
      }

    }
  }
  .li-mine {
    display: none;
    width: 7.5rem;
    height: 1.01rem;
    background: #343434;
    box-shadow: 0rem 0rem 0rem 0rem rgba(255,255,255,0.1);

    padding: 0 .5rem;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;

    .li-rank {
      width: 1rem;
      font-weight: bold;
      font-size: 0.38rem;
      color: white;
    }
    .li-avatar {
      width: 0.7rem;
      height: 0.7rem;
      margin-right: .2rem;
      background: #EFF6FF;
      border-radius: 50%;
      border: 0.02px solid #FFB070;
    }
    .li-title {
      font-weight: bold;
      font-size: 0.26rem;
      color: #FCB541;
    }
    .li-data {
      font-weight: 600;
      font-size: 0.23rem;
      color: #FF8200;
      display: flex;
      flex-direction: row !important;
      justify-content: center;
      align-items: flex-start;
      > svg {
        margin-right: .1rem;
      }
    }

  }
}
.td-rank0  .li-mine0,
.td-rank1  .li-mine1,
.td-rank2  .li-mine2 {
  display: flex !important;
}
</style>
