<script>
import {rank} from "@/mixins/tdtc/rank";

export default {
  mixins: [rank],
  data() {
    return {
      show: true,
    }
  },
  mounted() {
    this.queryTab2()
  },
}
</script>

<template>
  <section v-if="tab2.rankinglistinfo.length > 3">
    <div class="tab-pre3">
      <div>
        <img src="/img/tdtc/rank/2.png" width="30" alt="">
        <img style="width: 0.98rem;
height: 0.98rem;
background: #EFF6FF;
border-radius: 50%;
border: 0.03px solid #8AC5D2;" :src="`img/profile/icon_${tab2.rankinglistinfo[1]['face_id']}.png`">
        <div class="ribbon" style="background-image: url('/img/tdtc/rank/0.png');">
          VIP{{ tab2.rankinglistinfo[1]['viplevel'] }}
        </div>
        <div>{{ tab2.rankinglistinfo[1]['nickname'] }}</div>
        <div class="li-data"><svg class="am-icon am-icon-xxs"><use xlink:href="#icon-level"></use></svg>{{ tab2.rankinglistinfo[1]['rank_data'] }}</div>
        <div class="plat-2">
          2
        </div>
      </div>

      <div>
        <img src="/img/tdtc/rank/1.png" width="30" alt="">
        <img style="width: 0.98rem;
height: 0.98rem;
background: #EFF6FF;
border-radius: 50%;
border: 0.03px solid #FFE41D;" :src="`img/profile/icon_${tab2.rankinglistinfo[0]['face_id']}.png`">
        <div class="ribbon" style="background-image: url('/img/tdtc/rank/0.png');">
          VIP{{ tab2.rankinglistinfo[0]['viplevel'] }}
        </div>
        <div>{{ tab2.rankinglistinfo[0]['nickname'] }}</div>
        <div class="li-data"><svg class="am-icon am-icon-xxs"><use xlink:href="#icon-level"></use></svg>{{ tab2.rankinglistinfo[0]['rank_data'] }}</div>
        <div class="plat-1">
          1
        </div>
      </div>

      <div>
        <img src="/img/tdtc/rank/3.png" width="30" alt="">
        <img style="width: 0.98rem;
height: 0.98rem;
background: #EFF6FF;
border-radius: 50%;
border: 0.03px solid #DDA26A;" :src="`img/profile/icon_${tab2.rankinglistinfo[2]['face_id']}.png`">
        <div class="ribbon" style="background-image: url('/img/tdtc/rank/0.png');">
          VIP{{ tab2.rankinglistinfo[2]['viplevel'] }}
        </div>
        <div>{{ tab2.rankinglistinfo[2]['nickname'] }}</div>
        <div class="li-data"><svg class="am-icon am-icon-xxs"><use xlink:href="#icon-level"></use></svg>{{ tab2.rankinglistinfo[2]['rank_data'] }}</div>
        <div class="plat-3">
          3
        </div>
      </div>

    </div>
    <ul>
      <li v-for="(item, index) in tab2.rankinglistinfo.slice(3)">
        <div class="li-rank">{{ index + 4}}</div>
        <div style="flex:1;display: flex;">
          <img class="li-avatar" :src="`img/profile/icon_${item['face_id']}.png`">
          <div>
            <div  class="li-title">VIP{{ item['viplevel'] }}</div>
            <div style="font-weight: 600;
font-size: 0.23rem;
color: #7F8392;">{{ item['nickname'] }}</div>
          </div>
        </div>
        <div class="li-data"><svg class="am-icon am-icon-xxs"><use xlink:href="#icon-level"></use></svg>{{ item['rank_data'] }}</div>
      </li>
    </ul>
    <van-popup transition="fade" duration="0.6" get-container=".td-rank" v-model="show" position="bottom" :overlay="false">
      <div class="li-mine li-mine2">
        <div class="li-rank">{{ inRank2 !== -1 ? inRank2+1: '99+' }}</div>
        <div style="flex:1;display: flex;">
          <img class="li-avatar" :src="`img/profile/icon_1.png`">
          <div>
            <div class="li-title">VIP{{ $store.state.account.vip }}</div>
            <div style="font-weight: 600;font-size: 0.23rem;color: #7F8392;">{{ $store.state.account.nickname }}</div>
          </div>
        </div>
        <div class="li-data">
          <svg class="am-icon am-icon-xxs"><use xlink:href="#icon-level"></use></svg>
          {{ tab2.rank_data }}
        </div>
      </div>
    </van-popup>
  </section>
</template>

<style scoped >

</style>