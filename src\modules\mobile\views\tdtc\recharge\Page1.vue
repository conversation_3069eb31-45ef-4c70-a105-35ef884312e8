<script>
import {getRandomInt} from '@/utils/common'
import {dot} from '@/mixins/dot'

export default {
  props: {
    datas: {
      require: true,
    }
  },
  mixins: [dot],
  mounted() {
    this.post_infos[3] = getRandomInt(100000, 999999);
  },
  data() {
    return {
      loading: false,
      selectBankName: false,
      bankNameIndex:  -1,
      bankArr:        [
        this.$t('RECHARGEINFO1.TEXT_BANKLIST2'),
        this.$t('RECHARGEINFO1.TEXT_BANKLIST3'),
      ],
      tabIndex:       0,
      withdrawAmount: null,
      swiperIndex:    0,
      swiperOption:   {
        slidesPerView: 'auto',
        spaceBetween:  6,
        navigation:    {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      post_infos:     {
        0: "",
        1: "",
        2: "",
        3: "",
      },
    }
  },
  computed: {
    bankAccount() {
      if (!this.datas.bankTransfer[this.swiperIndex]) return ''
      return this.datas.bankTransfer[this.swiperIndex]['bankAccount'] ?? ''
    },
    bankUser() {
      if (!this.datas.bankTransfer[this.swiperIndex]) return ''
      return this.datas.bankTransfer[this.swiperIndex]['bankUser'] ?? ''
    }
  },
  methods: {
    onSelectBankName(value, index) {
      this.bankNameIndex = index;
      this.post_infos[0] = this.bankArr[index];
      this.selectBankName = false;
    },
    setDataRechargeInfo() {
      if (!(this.post_infos[1] === "")) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));//"提交充值金额错误!"
        return;
      }

      if (parseInt(this.post_infos[1]) !== parseFloat(this.post_infos[1])) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));
        return;
      }

      if (this.post_infos[2] === "") {
        window.$toast.fail(this.$t('rechargeDeposAcountFail'));
        return;
      }

       if (this.post_infos[3] === "") {
         window.$toast.fail(this.$t('rechargeDeposTipsFail'));//"未输入备注"
        return;
       }
      let minMoney = this.datas.bankTransfer[this.swiperIndex]['min'] / 100;
      let maxMoney = this.datas.bankTransfer[this.swiperIndex]['max'] / 100;
      if (parseInt(this.post_infos[1]) < minMoney ||
          parseInt(this.post_infos[1]) > maxMoney) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail1', [minMoney, maxMoney]));//"提交充值金额错误!"
        return;
      }

      let depositMoney = parseInt(this.post_infos[1]) * 100;
      let payer = this.post_infos[2]

      let rechargeData = {};
      rechargeData["rechCfgId"] = this.datas.bankTransfer[this.swiperIndex]['id'];
      rechargeData["depositMoney"] = depositMoney;
      rechargeData["payerName"] = payer + " (" + "方式:" + this.post_infos[0] + " 备注:" + this.post_infos[3] + ")";
      rechargeData["userId"] = this.$store.state.account.userId; //测试固定账号 1167
      // getRecharge
      this.loading = true;
      this.event_rechargeClick()
      this.$tdtcApi.getRecharge({
        method: 'post',
        url:    '/front/userrech/add',
        params: {
          signature: this.$store.state.token.token
        },
        data:   rechargeData
      })
          .then((result) => {
            if (typeof result === 'string' && result.indexOf("<html>") !== 0) {
              //跳转
            } else if (result["code"] !== 0) {
              //平台提示
              window.$toast.fail(result["msg"]);
            } else if (result["code"] === 0) {
              //成功
              $toast.success({
                icon:    "passed",
                message: this.$t('rechargeSuccess'),
              });
              if (this.post_infos) { this.post_infos[1] = "" }
              if (this.post_infos) { this.post_infos[2] = "" }
              if (this.post_infos) { this.post_infos[3] = getRandomInt(100000, 999999) }
            } else {
              //系统提示，比如充值代理忙，请稍后再试或切换其他充值代理
              window.$toast.fail(result);
            }
          }).finally(()=>{this.loading = false;})
    }
  }
}
</script>

<template>
  <div>
    <div style="position:relative;" v-if="datas.bankTransfer.length">
      <swiper :options="swiperOption">
        <swiper-slide v-for="(item,index) in datas.bankTransfer" :key="index" :class="{'swiper-active': index === swiperIndex}">
          <div @click="swiperIndex = index">{{ item['bankName'] }}</div>
        </swiper-slide>
      </swiper>
      <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>
      <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>
    </div>
    <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;" v-if="datas.bankTransfer[swiperIndex] && datas.bankTransfer[swiperIndex]['min'] && datas.bankTransfer[swiperIndex]['max']">
      {{ $t('RECHARGEINFO1.TEXT_SUM4') }} {{datas.bankTransfer[swiperIndex]['min'] | currency}} ~ {{datas.bankTransfer[swiperIndex]['max'] | currency}}
    </div>
    <van-cell-group style="margin-top: .2rem;">
      <van-field readonly input-align="right" :value="bankAccount" clearable label-width="3rem" :label="$t('RECHARGEINFO1.TEXT_SUM2')">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="bankAccount">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
      <van-field readonly input-align="right" :value="bankUser" clearable label-width="3rem" :label="$t('RECHARGEINFO1.TEXT_SUM3')">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="bankUser">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>

      <van-field readonly @click="selectBankName = true" input-align="right" :value="post_infos[0]" label-width="3rem" :label="$t('RECHARGEINFO1.TEXT_SUM1_1')" :placeholder="$t('RECHARGEINFO1.TEXT_BANKLIST1')"/>
      <van-action-sheet get-container=".td-recharge" v-model="selectBankName" :title="$t('RECHARGEINFO1.TEXT_BANKLIST1')">
        <van-picker show-toolbar :columns="bankArr" @confirm="onSelectBankName" @cancel="selectBankName = false"/>
      </van-action-sheet>
      <van-field input-align="right" v-model.number="post_infos[1]" type="number" clearable label-width="3rem" :label="$t('RECHARGEINFO1.TEXT_SUM2_1')"/>
      <van-field input-align="right" v-model.trim="post_infos[2]" clearable label-width="3rem" :label="$t('RECHARGEINFO1.TEXT_SUM3_1')"/>
      <van-field readonly input-align="right" type="number" :value="post_infos[3]" label-width="3.5rem" :label="$t('RECHARGEINFO1.TEXT_SUM4_1')">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="post_infos[3]">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button @click="setDataRechargeInfo" size="middle" type="warning" block>{{ $t('RECHARGEINFO3.TEXT_BTN_OK') }}</van-button>
    </div>
    <div class="withdraw-tip">
      {{ $t('RECHARGEINFO1.TEXT_EG') }}
    </div>
  </div>
</template>

<style scoped>

</style>