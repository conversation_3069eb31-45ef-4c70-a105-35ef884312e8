<script>
import {getRandomInt} from '@/utils/common'
import {dot} from '@/mixins/dot'

export default {
  props: {
    datas: {
      require: true,
    }
  },
  mixins: [dot],
  data() {
    return {
      loading: false,
      post_infos:     {
        0: "",
        1: "",
        2: "",
        3: "",
      },
    }
  },
  mounted() {
    this.post_infos[2] = getRandomInt(100000, 999999);
  },
  computed: {
    bankAccount() {
      if (!this.datas.alipay[0]) return ''
      return this.datas.alipay[0]['bankAccount'] ?? ''
    },
    bankUser() {
      if (!this.datas.alipay[0]) return ''
      return this.datas.alipay[0]['bankUser'] ?? ''
    }
  },
  methods: {
    setDataRechargeInfo() {
      if (!(this.post_infos[0])) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));//"提交充值金额错误!"
        return;
      }

      if (parseInt(this.post_infos[0]) !== parseFloat(this.post_infos[0])) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));
        return;
      }

      if (this.post_infos[1] === "") {
        window.$toast.fail(this.$t('rechargeDeposAcountFail'));
        return;
      }

      if (this.post_infos[2] === "") {
        window.$toast.fail(this.$t('rechargeDeposTipsFail'));//"未输入备注"
        return;
      }

      let minMoney = this.datas.alipay[0]['min'] / 100;
      let maxMoney = this.datas.alipay[0]['max'] / 100;
      if (parseInt(this.post_infos[0]) < minMoney ||
          parseInt(this.post_infos[0]) > maxMoney) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail1', [minMoney, maxMoney]));//"提交充值金额错误!"
        return;
      }

      let depositMoney = parseInt(this.post_infos[0]) * 100;
      let payer = this.post_infos[1]

      let rechargeData = {};
      rechargeData["rechCfgId"] = this.datas.alipay[0]['id'];
      rechargeData["depositMoney"] = depositMoney;
      rechargeData["payerName"] = payer;
      rechargeData["payer"] =  "备注:" + this.post_infos[2];
      rechargeData["userId"] = this.$store.state.account.userId; //测试固定账号 1167

      this.loading = true;
      this.event_rechargeClick()
      this.$tdtcApi.getRecharge({
        method: 'post',
        url:    '/front/userrech/add',
        params: {
          signature: this.$store.state.token.token
        },
        data:  rechargeData
      })
          .then((result) => {
            if (typeof result === 'string' && result.indexOf("<html>") !== 0) {
              //跳转
            } else if (result["code"] !== 0) {
              //平台提示
              window.$toast.fail(result["msg"]);
            } else if (result["code"] === 0) {
              //成功
              $toast.success({
                icon:    "passed",
                message: this.$t('rechargeSuccess'),
              });
              if (this.post_infos) { this.post_infos[0] = "" }
              if (this.post_infos) { this.post_infos[1] = "" }
              if (this.post_infos) { this.post_infos[2] = getRandomInt(100000, 999999) }
            } else {
              //系统提示，比如充值代理忙，请稍后再试或切换其他充值代理
              window.$toast.fail(result);
            }
          }).finally(()=>{this.loading = false;})
    }
  }

}
</script>

<template>
  <div>
    <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;" v-if="datas.alipay[0] && datas.alipay[0]['min'] && datas.alipay[0]['max']">
      {{ $t('RECHARGEINFO2.TEXT_SUM4') }} {{datas.alipay[0]['min'] | currency}} ~ {{datas.alipay[0]['max'] | currency}}
    </div>
    <van-cell-group>
      <van-field readonly input-align="right" :value="bankAccount" label-width="3rem" :label="$t('RECHARGEINFO2.TEXT_SUM2')">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="bankAccount">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
      <van-field readonly input-align="right" :value="bankUser" label-width="3rem" :label="$t('RECHARGEINFO2.TEXT_SUM3')">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="bankUser">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>

      <van-field input-align="right" v-model.number="post_infos[0]" type="number" clearable label-width="3rem" :label="$t('RECHARGEINFO2.TEXT_SUM2_1')" />
      <van-field input-align="right" v-model.number="post_infos[1]" clearable label-width="3rem" :label="$t('RECHARGEINFO2.TEXT_SUM3_1')" />
      <van-field readonly input-align="right" :value="post_infos[2]" label-width="3.5rem" :label="$t('RECHARGEINFO2.TEXT_SUM4_1')">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="post_infos[2]">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button @click="setDataRechargeInfo" size="middle" style="font-size: .36rem" type="warning" block>{{ $t('RECHARGEINFO3.TEXT_BTN_OK') }}</van-button>
    </div>
    <div class="withdraw-tip">
      {{ $t('RECHARGEINFO2.TEXT_EG') }}
    </div>
  </div>
</template>

<style scoped>

</style>