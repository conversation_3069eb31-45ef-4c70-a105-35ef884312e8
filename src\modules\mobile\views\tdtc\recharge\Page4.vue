<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";

export default {
  components: {RecordBoard},
  data() {
    return {
      loading: false,
      column: [
        {
          label: this.$t("RECHARGEINFO4.TEXT_ORDERLD"),
          prop: "orderNo",
          copy: true,
          style: { width: '3rem'}
        },
        {
          label: this.$t("RECHARGEINFO4.TEXT_TIME"),
          prop: "addTime",
          render: FieldRenderType.datetime
        },
        {
          label: this.$t("RECHARGEINFO4.TEXT_MONEY"),
          prop: "rechMoney",
          render: FieldRenderType.currency

        },
        {
          label: this.$t("RECHARGEINFO4.TEXT_ONLINETYPE"),
          prop: "onlineType",
          // render: "customTemplate",
          // customTemplate: this.toOnlineType,
        },
        {
          label: this.$t("RECHARGEINFO4.TEXT_STATUS"),
          prop: "status",
          render: "customTemplate",
          customTemplate: this.toStatus,
        },
      ],
      data: []
    }
  },
  mounted() {
    this.query()
  },
  methods: {
    toOnlineType (value) {
      return this.$t("rechargeStatu_" + value)
    },
    toStatus (value) {
      return this.$t("rechargeStatu_" + value)
    },
    query() {
      this.$tdtcApi.getRecharge({
        method:'get',
        url:'/front/userrech/list',
        params: {
          userId: this.$store.state.account.userId,
          signature: this.$store.state.token.token
        }
      })
          .then((res) => {
            if (res['success']) {
              this.data = res["t"]['data'];
            }
          })
          .catch(() => {
          });
    },
  }
}
</script>

<template>
  <div class="withdraw-history" style="height: 100vh;background: #F9F9F9;">
    <div class="vip-header">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue am-navbar am-navbar-light" style="position: fixed !important;width: 100%; z-index: 1; top: 0;"
        >
          <div class="am-navbar-left" role="button">
          <span class="am-navbar-left-content" @click="$router.back()">
            <span class="return_icon">
              <svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg>
            </span>
          </span>
          </div>
          <div class="am-navbar-title" style="font-size: .4rem !important;font-weight: 600;">{{ $t('RECHARGEINFO4.TEXT_SUM1') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
    </div>
    <RecordBoard :data="data" :column="column" />

  </div>
</template>

<style scoped>

</style>