<script>
import {TDTC_ROURE} from "@/api/tdtc";
import {dot} from '@/mixins/dot'

export default {
  data() {
    return {
      loading: false,
      giftCode: ""
    }
  },
  mixins: [dot],
  methods: {
    BtnOKClickCallBack(){
      if (this.giftCode.length < 6) {
        window.$toast.fail(this.$t("GIFTCODE_ERR_4"));
        return
      }
      this.loading = true;
      this.event_rechargeClick()
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.GET_EXCHANGE_CARD, {
             'cardnum': this.giftCode,
           })
               .then((msg) => {
                 // GetDailyRescueResponse
                 if (msg["code"] === 501) {
                   window.$toast.fail(this.$t('501'));
                 } else {
                   if (!msg['code']) {
                     $toast.success({
                       icon: "passed",
                       message: this.$t('GIFTCODE_SUCCESS', [this.$options.filters['currency'](msg['awardMoney'])] ),
                     });
                   }
                   else {
                     window.$toast.fail(this.$t('GIFTCODE_ERR_' + msg['code']));
                   }
                 }
               })
               .catch(() => {})
          .finally(()=> {
            this.giftCode = "";
            this.loading = false;
          })
    }
  }
}
</script>

<template>
  <div>
    <van-cell-group style="margin-top: .2rem;">
      <van-field input-align="right" v-model.trim="giftCode" clearable label-width="3rem" :label="$t('RECHARGEINFO5.TEXT_EDITBOX_MONEY')" :placeholder="$t('RECHARGEINFO5.TEXT_EDITBOX_MONEY')"/>
    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button @click="BtnOKClickCallBack" size="middle" style="font-size: .36rem" type="warning" block>
        {{ $t('RECHARGEINFO5.BTN_1') }}
      </van-button>
    </div>
  </div>
</template>

<style scoped>

</style>