<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {FieldRenderType} from "@/utils/common";
import {dot} from '@/mixins/dot'

export default {
  components: {RecordBoard},
  props:      {
    datas: {
      require: true,
    }
  },
  mixins: [dot],
  data() {
    return {
      loading: false,
      pars:              [
        1000000,
        2000000,
        3000000,
        5000000,
        10000000,
        20000000,
        30000000,
        50000000,
        100000000
      ],
      selectPar:         false,
      parIndex:          -1,
      swiperIndex:       0,
      swiperOption:      {
        slidesPerView: 'auto',
        spaceBetween:  6,
        navigation:    {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      column:            [
        {
          label:  this.$t("RECHARGEINFO6.TIPS_1"),
          prop:   "key1",
          render: FieldRenderType.currency
        },
        {
          label:  this.$t("RECHARGEINFO6.TIPS_2"),
          prop:   "key2",
          render: FieldRenderType.currency
        },
      ],
      edit_value_select: "",
      edit_value_id:     "",
      edit_value_psw:    "",
    }
  },
  computed: {
    parsCol() {
      return this.pars.map(item=>this.$options.filters['currency'](item))
    },
    parsData() {
      let list = [];
      this.pars.forEach(item => {
        list.push({
          key1: item,
          key2: this.banks.length ? item * this.para[this.banks[this.swiperIndex]['bankName']] : item,
        })
      })
      return list;
    },
    banks() {
      return this.datas.guaguaka['banks'] ?? []
    },
    para() {
      let list = []
      if (!this.datas.guaguaka["para1"]) return list;
      let temp_para = this.datas.guaguaka["para1"].split(",")
      for (let i = 0; i < temp_para.length; i++) {
        let temp_info = temp_para[i].split(":");
        list[temp_info[0]] = temp_info[1]
      }
      return list;
    }
  },
  methods:  {
    onSelectPar(value, index) {
      this.parIndex = index;
      this.selectPar = false
    },
    onRecharge() {
      if (-1 === this.parIndex) {
        window.$toast.fail(this.$t('RECHAGE_GUA_TIP_INFO_ERROR'));
        return
      }
      if ("" === this.edit_value_id) {
        window.$toast.fail(this.$t('RECHAGE_GUA_TIP_INFO_ERROR'));
        return
      }
      if ("" === this.edit_value_psw) {
        window.$toast.fail(this.$t('RECHAGE_GUA_TIP_INFO_ERROR'));
        return
      }

      let rechargeData = {};
      rechargeData["rechCfgId"] = this.datas.guaguaka['id'];
      rechargeData["depositMoney"] = this.pars[this.parIndex];
      rechargeData["bankCode"] = this.banks[this.swiperIndex]['value'];
      rechargeData["payer"] = this.edit_value_id;
      rechargeData["payerInfo"] = this.edit_value_psw;
      rechargeData["userId"] = this.$store.state.account.userId;

      this.loading = true;
      this.event_rechargeClick()
      this.$tdtcApi.getRecharge({
        method: 'post',
        url:    '/front/userrech/onlinepay',
        params: {
          signature: this.$store.state.token.token
        },
        data:   rechargeData
      })
          .then((result) => {
            if (result["success"]) {
              let msg = null;
              try {
                msg = JSON.parse(result["msg"]);
              } catch (e) {
              }
              if (null == msg) {
                msg = result["msg"];
              } else {
                msg = msg["msg"];
              }

              $toast.success({
                icon:    "passed",
                message: msg,
              });

              this.edit_value_select = ""
              this.edit_value_id = ""
              this.edit_value_psw = ""
            } else {
              window.$toast.fail(result["msg"]);
            }
          }).finally(()=>{this.loading = false;})
    }
  }

}
</script>

<template>
  <div>

    <div style="position:relative;" v-if="banks.length">
      <swiper :options="swiperOption">
        <swiper-slide v-for="(item,index) in banks" :key="index" :class="{'swiper-active': index === swiperIndex}">
          <img :src="`/img/tdtc/recharge/guagua/${item.ico}.png`" alt="" @click.stop="swiperIndex = index">
        </swiper-slide>
      </swiper>
      <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>
      <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>
    </div>
    <RecordBoard :data="parsData" :column="column"/>
    <van-cell-group>
      <van-field readonly input-align="right" @click="selectPar=true" :value="parsCol[parIndex]" label-width="3rem" :label="$t('RECHARGEINFO6.TEXT_TIP_1')" :placeholder="$t('RECHARGEINFO6.TEXT_TIP_1')"/>
      <van-action-sheet get-container=".td-recharge" v-model="selectPar" :title="$t('RECHARGEINFO6.TEXT_TIP_1')">
        <van-picker :columns="parsCol" @confirm="onSelectPar" @cancel="selectPar=false" show-toolbar/>
      </van-action-sheet>

      <van-field input-align="right" type="number" v-model.trim="edit_value_id" clearable label-width="3rem" :label="$t('RECHARGEINFO6.TEXT_TIP_2')" :placeholder="$t('RECHARGEINFO6.TEXT_TIP_2')"/>
      <van-field input-align="right" type="number" v-model.trim="edit_value_psw" clearable label-width="3rem" :label="$t('RECHARGEINFO6.TEXT_TIP_3')" :placeholder="$t('RECHARGEINFO6.TEXT_TIP_3')"/>
    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button @click="onRecharge" size="middle" style="font-size: .36rem" type="warning" block>{{ $t('RECHARGEINFO6.BTN_1') }}</van-button>
    </div>
    <div class="withdraw-tip">
      {{ $t('RECHARGEINFO6.TIPS_3') }}
    </div>


  </div>
</template>

<style scoped lang="scss">
img {
  height: 100%;
  width: 100%;
  flex: 1
}

/*::v-deep .record-board table th {
  background: #FFB627;
}*/
.td-recharge .swiper-container {
  .swiper-slide {
    height: 1rem;
    width: 2rem;
    padding: 0 !important;
  }
}

::v-deep .record-board .record-board-wrap {
  height: 3.5rem;
}
</style>