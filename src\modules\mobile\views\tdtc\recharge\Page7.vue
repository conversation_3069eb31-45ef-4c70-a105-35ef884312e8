<script>
import DatetimePicker from '@/components/DatetimePicker.vue'
import {dot} from '@/mixins/dot'
import {TDTC_ROURE} from '@/api/tdtc'
import VueQr from 'vue-qr'

export default {
  components: {DatetimePicker, VueQr},
  props: {
    datas: {
      require: true,
    }
  },
  mixins: [dot],
  mounted() {
    this.query108();
  },
  data() {
    return {
      res: {
         order_no: "", // 订单号 string
         payee_name: "", // TRC20 string
         payee: "", //      地址 string
         currency: "", //   USDT string
         exchange_rate: 0, // 兑换比例  1USDT=多少游戏币 double
         rebate_fee: 0, //返点比例  0.01表示1%  double
         act_rech_money: 0, //  实际需要充值的usdt double
         game_money: 0, //   可以获得多少游戏币  分 int64
         rebate_money: 0, //  可以获得多少游戏币返利  分 int64
         status: 0, //   0表示没有未完成的订单 显示配置界面  1的话表示还有未完成订单 要显示充值界面 int32
         min_money: 0, //  最小充值的usdt int64
         max_money: 0, // int64
      },
      pay_info_post_amount:  "",
    }
  },
  computed: {
    amountChange() {
      let str = this.pay_info_post_amount;
      let currency = this.res.currency;
      let exchangeRate = this.res.exchange_rate;
      let rebateFee = this.res.rebate_fee;
      if (str === "") {
        str = 1;
      }
      let num_vnd = parseInt(str) * exchangeRate

      return str + currency + " ≈ " + (num_vnd + num_vnd * rebateFee) + "VND"
    }
  },
  methods: {

    query108() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_CRYPTO_PAY)
          .then((res) => {
            Object.assign(this.res, res)
          })
          .catch(() => {
          })
    },

    setDataRechargeInfo() {
      //bi 充值
      if (this.pay_info_post_amount === "") {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));//"提交充值金额错误!"
        return;
      }
      let depositMoney = parseInt(this.pay_info_post_amount);
      if (depositMoney < this.res.min_money ||
          depositMoney > this.res.max_money) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail1', [this.res.min_money, this.res.max_money]));//"提交充值金额错误!"
        return;
      }
      this.event_rechargeClick()

      this.$tdtcApi.getQueryInfo(TDTC_ROURE.CRYPTO_PAY_ADD, {rech_money: depositMoney})
          .then((res) => {
            if (res["code"] === 200) {
              Object.assign(this.res, res)
            } else if (res["code"] === 1) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_NOTALLOW"))
            } else if (res["code"] === 2) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_CONFIG_ERROR"))
            } else if (res["code"] === 3) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_CLOSED"))
            } else if (res["code"] === 4) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_MONEY_ERROR", { min: this.res.min_money, max: this.res.max_money }))
            } else if (res["code"] === 5) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_TOO_MANY"))
            } else if (res["code"] === 6) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_EXIST"))
            } else if (res["code"] === 7) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_EXCEED_LIMIT", { money: res["game_money"] }))
            } else if (res["code"] === 8) {
              window.$toast.fail(this.$t("ERROR_CRYPTO_RECHARGE_EXCEED_ONEDAY_LIMIT", { money: res["game_money"] }))
            }
          })
          .catch(() => {
          })
    }
  }
}
</script>

<template>
  <div v-if="!res.status">
    <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;">

        1 {{res.currency}} ≈ {{res.exchange_rate}} VND ({{ $t('RECHARGEINFO7.TEXT_SUM5') }} {{res.rebate_fee*100}}%)

      <br />

        {{ $t('RECHARGEINFO7.TEXT_SUM4') }} {{res.min_money}} {{res.currency}} ~ {{res.max_money}} {{res.currency}}

    </div>
    <van-cell-group style="margin-top: .2rem;">
      <van-field readonly input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM1')" :value="res.payee_name">
      </van-field>

      <van-field readonly input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM3')" :value="res.currency"/>

      <van-field v-model.number="pay_info_post_amount" type="number" input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM6')"/>

      <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;">
        {{ amountChange }}
      </div>

    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button @click="setDataRechargeInfo" size="middle" type="warning" block>{{ $t('RECHARGEINFO7.TEXT_BTN_OK') }}</van-button>
    </div>
  </div>
  <div v-else>
    <div style="display: flex;justify-content: center;margin-top: .2rem;">
      <VueQr
          :logoMargin="2"
          :text="res.payee"
          :size="160"
          :margin="5"
      />
    </div>
    <van-cell-group style="margin-top: .2rem;">
      <van-field readonly input-align="right" style="font-size: .23rem" clearable label-width="1.3rem" :label="$t('RECHARGEINFO7.TEXT_SUM2')" :value="res.payee">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="res.payee">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
      <van-field readonly input-align="right" clearable label-width="3rem" label="Mã đơn" :value="res.order_no" />
      <van-field readonly input-align="right" clearable label-width="3rem" :label="res.currency" :value="res.act_rech_money">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="res.act_rech_money">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
      <van-field readonly input-align="right" clearable label-width="3rem" label="Tiền thực nhận" :value="res.game_money / 100">
        <template #button>
          VND
        </template>
      </van-field>
      <van-field readonly input-align="right" clearable label-width="3rem" label="Tiền chiết khấu" :value="res.rebate_money / 100">
        <template #button>
          VND
        </template>
      </van-field>

      <div class="withdraw-tip" style="text-align: left">
        <p>{{ $t('RECHAGE_USDT_TIP1', {type: res.currency}) }}</p>
        <p>{{ $t('RECHAGE_USDT_TIP2', {money: res.act_rech_money, type: res.currency}) }}</p>
      </div>
    </van-cell-group>
  </div>
</template>

<style scoped>

</style>