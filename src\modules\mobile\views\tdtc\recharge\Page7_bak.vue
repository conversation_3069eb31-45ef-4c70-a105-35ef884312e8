<script>
import moment from 'moment'
import DatetimePicker from '@/components/DatetimePicker.vue'
import {dot} from '@/mixins/dot'

export default {
  components: {DatetimePicker},
  props: {
    datas: {
      require: true,
    }
  },
  mixins: [dot],
  mounted() {
  },
  data() {
    return {
      loading: false,
      show: false,
      minDate: new Date(2024, 0, 1),
      maxDate: new Date(),
      format: "YYYY-MM-DD HH:mm:ss",

      swiperIndex:    0,
      swiperOption:   {
        slidesPerView: 'auto',
        spaceBetween:  6,
        navigation:    {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      pay_info_user_name:  "",
      pay_info_post_amount:  "",
      pay_info_post_order:  "",
      pay_info_post_time: Date.now(),
    }
  },
  computed: {
    postTime() {
      return moment(this.pay_info_post_time).format(this.format);
    },
    amountChange() {
      let str = this.pay_info_post_amount;
      let currency = this.datas.cryptoPay[this.swiperIndex]['currency'];
      let exchangeRate = this.datas.cryptoPay[this.swiperIndex]['exchangeRate'];
      let rebateFee = this.datas.cryptoPay[this.swiperIndex]['rebateFee'];
      if (str === "") {
        str = 1;
      }
      let num_vnd = parseInt(str) * exchangeRate

      return str + currency + "=" + (num_vnd - num_vnd * rebateFee) + "VND"
    }
  },
  methods: {
    setDataRechargeInfo() {
      //bi 充值
      if (this.pay_info_post_amount === "") {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));//"提交充值金额错误!"
        return;
      }

      if (this.pay_info_post_order === "") {
        window.$toast.fail(this.$t('rechargeDeposAcountFail'));//"提交名称错误"
        return;
      }

      if (this.pay_info_user_name === "") {
        window.$toast.fail(this.$t('rechargeDeposWalletFail'));//"提交钱包地址错误"
        return;
      }

      // if (Utils.Conver.withoutSpace(this.pay_info_post_order.string) == "") {
      //     Toast.show(i18n.t("rechargeDeposOrderFail"))//"提交订单错误"
      //     return;
      // }

      // if (this.pay_info_post_order.string.length < 6 || this.pay_info_post_order.string.length > 255) {
      //     Toast.show(i18n.t("rechargeDeposOrderFail"))//"提交订单错误"
      //     return;
      // }

      // if (Utils.Conver.withoutSpace(this.pay_info_post_time.string) == "" || this.payTimestamp > new Date().getTime() / 1000) {
      //     Toast.show(i18n.t("rechargeDeposTimeFail"))//"提交时间错误"
      //     return;
      // }


      let minMoney = this.datas.cryptoPay[this.swiperIndex]['min'];
      let maxMoney = this.datas.cryptoPay[this.swiperIndex]['max'];
      let depositMoney = parseInt(this.pay_info_post_amount);
      if (depositMoney < minMoney ||
          depositMoney > maxMoney) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail1', [minMoney, maxMoney]));//"提交充值金额错误!"
        return;
      }


      let userId = this.$store.state.account.userId;//测试固定账号 1167

      let rechargeData = {};
      rechargeData["rechCfgId"] = this.datas.cryptoPay[this.swiperIndex]['id'];   //配置ID
      rechargeData["userId"] = userId;                //用户ID
      rechargeData["depositMoney"] = depositMoney; //充值金额
      rechargeData["txHashCode"] = this.pay_info_post_order; //单号 6-256位
      rechargeData["depositDate"] = this.postTime;//充值时间
      rechargeData["payerName"] = this.pay_info_user_name;//充值人名

      // getRecharge
      this.loading = true;
      this.event_rechargeClick()
      this.$tdtcApi.getRecharge({
        method: 'post',
        url:    '/front/userrech/cryptopayadd',
        params: {
          signature: this.$store.state.token.token
        },
        data:   rechargeData
      })
          .then((result) => {
            if (typeof result === 'string' && result.indexOf("<html>") !== 0) {
              //跳转
            } else if (result["code"] !== 0) {
              //平台提示
              window.$toast.fail(result["msg"]);
            } else if (result["code"] === 0) {
              //成功
              $toast.success({
                icon:    "passed",
                message: this.$t('rechargeSuccess'),
              });
              this.pay_info_user_name = ""
              this.pay_info_post_amount = ""
              this.pay_info_post_order = ""
            } else {
              //系统提示，比如充值代理忙，请稍后再试或切换其他充值代理
              window.$toast.fail(result);
            }
          }).finally(()=>{this.loading = false;})
    }
  }
}
</script>

<template>
  <div v-if="datas.cryptoPay.length">
    <div style="position:relative;">
      <swiper :options="swiperOption">
        <swiper-slide v-for="(item,index) in datas.cryptoPay" :key="index" :class="{'swiper-active': index === swiperIndex}">
          <div @click="swiperIndex = index">{{ item['bankName'] }}</div>
        </swiper-slide>
      </swiper>
      <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>
      <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>
    </div>
    <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;" v-if="datas.cryptoPay[swiperIndex] && datas.cryptoPay[swiperIndex]['min'] && datas.cryptoPay[swiperIndex]['max']">

        1 {{datas.cryptoPay[swiperIndex]['currency']}} = {{datas.cryptoPay[swiperIndex]['exchangeRate']}} VND ({{ $t('RECHARGEINFO7.TEXT_SUM5') }} {{datas.cryptoPay[swiperIndex]['rebateFee']*100}}%)

      <br />

        {{ $t('RECHARGEINFO7.TEXT_SUM4') }} {{datas.cryptoPay[swiperIndex]['min']}} {{datas.cryptoPay[swiperIndex]['currency']}} ~ {{datas.cryptoPay[swiperIndex]['max']}} {{datas.cryptoPay[swiperIndex]['currency']}}

    </div>
    <van-cell-group style="margin-top: .2rem;">
      <van-field readonly input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM1')" :value="datas.cryptoPay[swiperIndex]['bankUser']">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="datas.cryptoPay[swiperIndex]['bankUser']">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
      <van-field readonly input-align="right" style="font-size: .23rem" clearable label-width="1.3rem" :label="$t('RECHARGEINFO7.TEXT_SUM2')" :value="datas.cryptoPay[swiperIndex]['payee']">
        <template #button>
          <svg style="margin-left: .07rem;width: .3rem;height: .3rem;margin-top: -.1rem;" class="am-icon am-icon-icon-copy_8fbca030 am-icon-md copy-btn" :data-clipboard-text="datas.cryptoPay[swiperIndex]['payee']">
            <use xlink:href="#icon-copy"></use>
          </svg>
        </template>
      </van-field>
      <van-field readonly input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM3')" :value="datas.cryptoPay[swiperIndex]['currency']"/>

      <van-field v-model.number="pay_info_post_amount" type="number" input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM6')"/>

      <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;">
        {{ amountChange }}
      </div>
      <van-field v-model.trim="pay_info_user_name" input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM7')"/>
      <van-field v-model.trim="pay_info_post_order" input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM8')"/>
      <van-field readonly input-align="right" clearable label-width="3rem" :label="$t('RECHARGEINFO7.TEXT_SUM9')" :value="postTime" @click="show=true"/>
      <van-action-sheet get-container=".td-recharge" v-model="show" :title="$t('RECHARGEINFO7.TEXT_SUM9')">
        <datetime-picker
            :datetimePickerProps="{ 'visible-item-count': 6, 'min-date': minDate, 'max-date': maxDate }"
            :pickerProps="{ 'visible-item-count': 6 }"
            v-model="pay_info_post_time"
            @cancel="show = false"
            @input="show = false"
        />
      </van-action-sheet>

    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button @click="setDataRechargeInfo" size="middle" type="warning" block>{{ $t('RECHARGEINFO7.TEXT_BTN_OK') }}</van-button>
    </div>
  </div>
</template>

<style scoped>

</style>