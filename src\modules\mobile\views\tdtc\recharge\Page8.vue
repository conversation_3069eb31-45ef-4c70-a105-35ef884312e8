<script>

import {dot} from '@/mixins/dot'

export default {
  props: {
    datas: {
      require: true,
    },
  },
  mixins: [dot],
  data() {
    return {
      loading: false,
      tabIndex: 0,
      swiperIndex: 0,
      swiperOption: {
        slidesPerView: 'auto',
        spaceBetween: 6,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
      },
      editbox_money: null,
      bankIndex: 0,
    }
  },
  computed: {
    filterData() {
      return this.datas.onlinePayment.filter(row => row.onlineType === 101 )
    },
    banks() {
      if (!this.filterData.length) return [];
      return this.filterData[this.swiperIndex]['banks'] ?? [
       /* {
          "value": "Viettel",
          "ico": "abc",
          "bankName": "Viettel"
        },
        {
          "value": "Mobifone",
          "ico": "abc",
          "bankName": "Mobifone"
        },
        {
          "value": "Vinaphone",
          "ico": "abc",
          "bankName": "Vinaphone"
        },
        {
          "value": "Vietnamobile",
          "ico": "abc",
          "bankName": "Vietnamobile"
        }*/
      ]
    }
  },
  methods: {
    setDataRechargeInfo() {
      let depositMoney = parseInt(this.editbox_money) * 100;
     if ((!depositMoney || depositMoney <= 0) || depositMoney < this.filterData[this.swiperIndex]['min'] || depositMoney > this.filterData[this.swiperIndex]['max']) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));
        return;
      }
      if (parseInt(this.editbox_money) !== parseFloat(this.editbox_money)) {
        window.$toast.fail(this.$t('rechargeDeposMoneyFail'));
        return;
      }
      let data = {};
      data["rechCfgId"] = this.filterData[this.swiperIndex]['id'];
      if (this.banks.length) {
        data["bankCode"] = this.banks[this.bankIndex]['value']
      }
      data["depositMoney"] = depositMoney;
      data["userId"] = this.$store.state.account.userId;//测试固定账号 1167
      this.loading = true;
      this.event_rechargeClick()
      this.$tdtcApi.getRecharge({
        method: 'post',
        url: '/front/userrech/onlinepay',
        params: {
          signature: this.$store.state.token.token
        },
        data: data
      })
          .then((responseText) => {
            if (typeof responseText === 'string' && responseText.indexOf("<html>") !== -1) {
              //跳转
              let url = (responseText).match(/window.location.href='([^\']+)'/)[1];
              this.$router.push({
                path: "/m/seamless/go",
                query: {
                  go: url
                }
              }).catch(()=>{})
              /*const userAgent = navigator.userAgent;
              if (/Android/i.test(userAgent) && 'jsBridge' in window) {
                url = "browser:" + url
                window.open(url, "_self")
              } else {
                setTimeout(()=>{
                  let w = window.open(url)
                  if (!w) {
                    window.$Dialog.confirm({
                      title: this.$t("in_popup_prompt"),
                      message: this.$t("in_sure_deposit"),
                      confirmButtonText: this.$t("ok"),
                      cancelButtonText: this.$t("button_cancel"),
                    })
                        .then(() => {
                          window.open(url)
                        })
                        .catch(() => {});
                  }
                })
              }*/
            } else {
              try {
                let data = responseText
                if (typeof responseText === 'string') {
                  data = JSON.parse(responseText+"")
                }
                if (data["msg"]) {
                  window.$toast.fail(data["msg"]);
                } else {
                  window.$toast.fail(this.$t('rechargeStatu_3'));
                }
              } catch (error) {
                window.$toast.fail(responseText);
              }
            }


          }).finally(()=>{this.loading = false;})
    }
  }
}
</script>

<template>
  <div>
    <div style="position:relative;" v-if="filterData.length">
      <swiper :options="swiperOption">
        <swiper-slide v-for="(item,index) in filterData" :key="index" :class="{'swiper-active': index === swiperIndex}">
          <div @click="swiperIndex = index">{{ item['bankName'] }}</div>
        </swiper-slide>
      </swiper>
      <van-icon name="arrow" class="swiper-button-next" color="#6a6a6a"/>
      <van-icon name="arrow-left" class="swiper-button-prev" color="#6a6a6a"/>
    </div>
    <div  style="color: red; text-align: center;font-size: .26rem; margin: .2rem 0;" v-if="filterData[swiperIndex] && filterData[swiperIndex]['min'] && filterData[swiperIndex]['max']">
      {{ $t('RECHARGEINFO3.TEXT_MONEY') }} {{filterData[swiperIndex]['min'] | formatGold}} ~ {{filterData[swiperIndex]['max'] | formatGold}}
    </div>
    <van-grid clickable icon-size=".68rem" gutter=".1rem" :column-num="4">
      <van-grid-item v-for="(item,index) in banks" :key="index" :icon="`/img/tdtc/recharge/banks/${item.ico}.png`" :class="{'bank-active': bankIndex === index}" @click="bankIndex = index"/>
    </van-grid>
    <van-cell-group style="margin-top: .2rem;">
      <van-field input-align="right" v-model.number="editbox_money" type="number" clearable label-width="3rem" :label="$t('RECHARGEINFO3.TEXT_SUM')" :placeholder="$t('RECHARGEINFO3.TEXT_EDITBOX_MONEY')" />
    </van-cell-group>
    <div style="padding: .2rem .46rem; ">
      <van-button :loading="loading" @click="setDataRechargeInfo" size="middle" type="warning" block>{{ $t('RECHARGEINFO3.TEXT_BTN_OK') }}</van-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.recharge-banks {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  padding-left: .18rem;
  margin-top: .2rem;
  > img {
    width: 1.2rem;
    padding: .1rem;
  }
}
::v-deep .bank-active {
  .van-grid-item__content {
    border: 1px solid #FF8200 !important;
    //transform: scale(1);
    filter:unset !important;
    opacity:1 !important;
  }
}
::v-deep .van-grid-item__content {
  background-color: unset;
  padding: .1rem 0;
  border: 1px solid #b0b0b0;
  border-radius: 0.08rem;
  overflow: hidden;
  filter:grayscale(1);
  opacity:0.8;
  //transform: scale(.88);
}
</style>