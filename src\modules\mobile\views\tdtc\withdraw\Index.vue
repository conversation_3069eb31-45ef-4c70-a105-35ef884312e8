<script>
import {withdraw} from "@/mixins/tdtc/withdraw/withdraw";

export default {
  mixins: [withdraw],
};
</script>

<template>
  <div class="withdraw-container-v2">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-withdraw am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('EXCHANGE.LAYER_TITLE') }}</div>
        <div class="am-navbar-right">
          <svg @click="$router.push('/m/withdrawReport')"
              class="am-icon am-icon-withdraw_record_c986adfb am-icon-md am-navbar-title"
          >
            <use xlink:href="#withdraw_record_c986adfb"></use>
          </svg>
        </div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <div class="am-tabs am-tabs-top show-one">
      <div
          class="am-tabs-content am-tabs-content-animated"
      >
        <div
            role="tabpanel"
            aria-hidden="false"
            class="am-tabs-tabpane am-tabs-tabpane-active"
        >
          <div>
            <div class="withdraw-main"  style="padding-top: .15rem;overflow-y: auto">

              <div>
                <van-tabs @change="withdrawAmount = null" v-model="tabIndex" animated line-width="20%" line-height="2px" color="#FF8200" title-active-color="#FF8200" title-inactive-color="#312E2A" swipe-threshold="1">
                  <van-tab :title="$t('1017')">
                    <van-cell-group>
                      <van-action-sheet get-container=".withdraw-container-v2" v-model="selectBankName" :title="$t('BINDBANK.TIP_INPUT_1')">
                        <van-picker show-toolbar :columns="bankArr" @confirm="onSelectBankName" @cancel="selectBankName = false"/>
                      </van-action-sheet>
                      <van-field readonly input-align="right" v-if="!GetExchangeBindInfoResponse.bank_desposit"
                          v-model="bankArr[bankNameIndex]"
                           @click="!readonly.tab0 && (selectBankName = true)" label-width="2.2rem" :label="$t('BINDBANK.TIP_1')" :placeholder="$t('BINDBANK.TIP_INPUT_1')" />
                      <van-field v-else input-align="right" readonly v-model="GetExchangeBindInfoResponse.bank_desposit" clearable label-width="2.2rem" :label="$t('BINDBANK.TIP_1')" :placeholder="$t('BINDBANK.TIP_INPUT_1')" />

                      <van-field input-align="right" :readonly="readonly.tab0" v-model="GetExchangeBindInfoResponse.bank_name" clearable label-width="2.2rem" :label="$t('BINDBANK.TIP_2')" :placeholder="$t('BINDBANK.TIP_INPUT_2')" />
                      <van-field input-align="right" :readonly="readonly.tab0" v-model="GetExchangeBindInfoResponse.bank_account" clearable label-width="2.2rem" :label="$t('BINDBANK.TIP_3')" :placeholder="$t('BINDBANK.TIP_INPUT_3')" />
                      <div style="padding: .2rem .46rem; " v-if="!readonly.tab0">
                        <van-button @click="do1023" type="warning" block round :disabled="!(bankNameIndex !== -1 && GetExchangeBindInfoResponse.bank_account && GetExchangeBindInfoResponse.bank_name)">{{ $t('BINDBANK.LAYER_TITLE') }}</van-button>
                      </div>
                      <div class="withdraw-tip">{{ $t('BINDBANK.TIP') }}</div>
                    </van-cell-group>
                  </van-tab>
                  <van-tab :title="$t('1016')" v-if="$store.state.configs.crypto_switch">
                    <van-cell-group>
                      <van-field input-align="right" readonly label-width="2.2rem" :label="$t('BIND_TAB1.TIP_1')" value="TRC20" />
                      <van-field input-align="right" :readonly="readonly.tab1" v-model.trim="GetExchangeBindInfoResponse.alipay_account" clearable label-width="2.2rem" :label="$t('BIND_TAB1.TIP_2')" :placeholder="$t('BIND_TAB1.TIP_INPUT_2')" />
                      <div style="padding: .2rem .46rem; " v-if="!readonly.tab1">
                        <van-button @click="do1023" type="warning" block round :disabled="!GetExchangeBindInfoResponse.alipay_account">{{ $t('BIND_TAB1.LAYER_TITLE') }}</van-button>
                      </div>
                      <div class="withdraw-tip">{{ $t('BIND_TAB1.TIP') }}</div>
                    </van-cell-group>

                  </van-tab>
                  <van-tab :title="$t('1016_1')" v-if="$store.state.configs.wallet_switch">
                    <van-cell-group>
                      <van-field input-align="right" readonly
                          v-model="walletNames[walletIndex]"
                          @click="!readonly.tab2 && (selectWallet = true)" label-width="2.2rem" :label="$t('BIND_TAB2.TIP_1')" :placeholder="$t('BIND_TAB2.TIP_1')" />
                      <van-action-sheet get-container=".withdraw-container-v2" v-model="selectWallet" :title="$t('BIND_TAB2.TIP_1')">
                        <van-picker show-toolbar :columns="walletNames" @confirm="onSelectWalletName" @cancel="selectWallet=false"/>
                      </van-action-sheet>

                      <van-field input-align="right" :readonly="readonly.tab2" v-model.trim="GetExchangeBindInfoResponse.wallet_account" clickable label-width="2.2rem" :label="$t('BIND_TAB2.TIP_2')" :placeholder="$t('BIND_TAB2.TIP_INPUT_2')" />
                      <div style="padding: .2rem .46rem; " v-if="!readonly.tab2">
                        <van-button @click="do1023" type="warning" block round :disabled="!(GetExchangeBindInfoResponse.wallet_account && walletIndex >= 0)">{{ $t('BIND_TAB2.LAYER_TITLE') }}</van-button>
                      </div>
                      <div class="withdraw-tip">{{ $t('BIND_TAB2.TIP') }}</div>
                    </van-cell-group>
                  </van-tab>
                </van-tabs>
              </div>
              <div>
                <div class="withdraw-bkinfo">
                  <div>
                    <span>{{ $t('EXCHANGE.LAB_1') }}:</span>
                    <span v-if="!refresh">{{ UserScoreInfoResponse.info.score + UserScoreInfoResponse.info.insurescore | formatGoldWithK }}</span>
                    <span v-else>****</span>
                  </div>
                  <div>
                    <span>{{ $t('EXCHANGE.LAB_2') }}: </span>
                    <span v-if="!refresh">{{ UserScoreInfoResponse.info.insurescore | formatGoldWithK }}</span>
                    <span v-else>****</span>
                  </div>
                  <div>
                    <span>{{ $t('EXCHANGE.LAB_3') }}:</span>
                    <span v-if="!refresh">{{ UserScoreInfoResponse.info.score | formatGoldWithK }}</span>
                    <span v-else>****</span>
                  </div>
                  <div>
                    <span>{{ $t('EXCHANGE.LAB_4') }}:</span>
                    <span v-if="!refresh">{{ UserScoreInfoResponse.bonus_score | formatGoldWithK }}</span>
                    <span v-else>****</span>
                  </div>
                  <div>
                    <span>Điều kiện rút tiền:</span>
                    <span>{{ QueryWithDrawCondition ? "Đã hoàn thành" : "Chưa hoàn thành"}}</span>
                  </div>
                </div>
                <div v-if="!QueryWithDrawCondition" class="withdraw-bkinfo" style="padding-top: 0rem;">
                  <div>{{ $t('withdrawal_tip') }}</div>
                  <ol style="list-style-type: decimal; padding-left: .5rem;">
                    <li v-if="!QueryWithDrawConditionResp.phone">{{ $t('EXCHANGE_ERROR_CODE_14') }}</li>
                    <li v-if="!QueryWithDrawConditionResp.telegram">{{ $t('EXCHANGE_ERROR_CODE_18') }}</li>
                    <li v-if="!QueryWithDrawConditionResp.zalo">{{ $t('EXCHANGE_ERROR_CODE_19') }}</li>
                    <li v-if="!QueryWithDrawConditionResp.email">{{ $t('EXCHANGE_ERROR_CODE_20') }}</li>
                    <li v-if="!QueryWithDrawConditionResp.birthday">{{ $t('EXCHANGE_ERROR_CODE_21') }}</li>
                    <li v-if="QueryWithDrawConditionResp.withdraw_bet">{{ $t('EXCHANGE_ERROR_CODE_24', {money: $options.filters["formatGold"](QueryWithDrawConditionResp.withdraw_bet)}) }}</li>
                    <li v-if="!QueryWithDrawConditionResp.signal">{{ $t('EXCHANGE_ERROR_CODE_25') }}</li>
                    <li v-if="!QueryWithDrawConditionResp.idcard">{{ $t('EXCHANGE_ERROR_CODE_26') }}</li>
                  </ol>
                </div>
                <div class="wallet_return">
                  <div @click.prevent.stop="query39">
                    <svg class="am-icon am-icon-refresh_37023e62 am-icon-md" :class="{rotateFull: refresh}">
                      <use xlink:href="#refresh_37023e62"></use>
                    </svg>
                    <span>{{ $t('return_balance') }}</span>
                  </div>
                </div>
                <div v-if="tabIndex === 1" style="color: red; text-align: center;font-size: .3rem">{{ ApplyExchangeRespone.val / 100 }} VND = 1 USDT</div>
                <van-cell-group>
<!--                  <van-field clearable label-width="3rem" :label="$t('EXCHANGE.WITHDRAW_LAB_1')" />-->
                  <van-field @input="v=>{if(v) sliderValue = parseInt(v)}" input-align="right" @blur="onBlur" v-model="withdrawAmount" type="number" clearable label-width="3rem" :label="$t('EXCHANGE.WITHDRAW_LAB_2')" :placeholder="$t('EXCHANGE.WITHDRAW_TIPS_1')" />

                  <div v-if="tabIndex !== 1" style="padding: 0.5rem .8rem">

                    <van-slider active-color="#FFB627" v-model="sliderValue" @input="onInputSlider" :max="UserScoreInfoResponse.info.insurescore/ 100" />
                  </div>
                  <van-field v-if="GetExchangeBindInfoResponse.set_passwd" input-align="right" v-model="withdrawPassword" clearable label-width="3rem" label="Nhập mật khẩu"
                      :right-icon="!showPassword?'closed-eye':'eye'"
                      :type="!showPassword?'password':'text'"
                      @click-right-icon="showPassword=!showPassword"/>
                </van-cell-group>
                <div style="padding: .2rem .46rem; ">
                  <van-button @click="withdrawAmount>0 && do1024()" type="warning" block :disabled="!(withdrawAmount > 0)">{{ $t('EXCHANGE.WITHDRAW_BTN_2') }}</van-button>
                </div>
<!--                <div style="display: flex;justify-content: center;margin-bottom: .3rem;" v-if="!GetExchangeBindInfoResponse.set_passwd">
                  <a style="color: rgb(255, 130, 0); text-decoration: underline" @click="$router.push('/m/securityCenter/FundsPasswordBox')">
                    {{ $t('profile_description') }}
                  </a>
                </div>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">

::v-deep .van-cell {
  font-weight: 600;
  font-size: 0.26rem;
  color: #312E2A;
}



.withdraw-main{
  height: 92vh;
}
::v-deep .van-tab__pane {
  height: unset;
}
::v-deep .van-cell {
  align-items: center;
}

.withdraw-bkinfo {
  > div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: .1rem 0;
    & span:first-child {
      font-weight: 600;
      font-size: 0.26rem;
      color: #312E2A;
    }
    & span:last-child {
      font-weight: 600;
      font-size: 0.26rem;
      color: #F3518C;
    }

  }



}
.withdraw-tip {
  width: 7.2rem;
  background: #F0DEDC;
  border-radius: 0.08rem;
  border: 0.02px solid #E6D3D7;margin: 0 auto;text-align: center;padding: .1rem;font-size: 0.23rem;
  color: #AF3841;
}
</style>
