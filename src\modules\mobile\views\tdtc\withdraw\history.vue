<script>
import RecordBoard from "@/modules/mobile/components/RecordBoard.vue";
import {TDTC_ROURE} from "@/api/tdtc";
import {FieldRenderType} from "@/utils/common";

export default {
  name: "history",
  components: {RecordBoard},
  data() {
    return {
      column: [
        {
          label: this.$t("EXCHANGELOG.TITLE_1"),
          prop: "orderno",
          style: { width: '3rem'}
        },
        {
          label: this.$t("EXCHANGELOG.TITLE_2"),
          prop: "apply_time",
        },
        {
          label: this.$t("EXCHANGELOG.TITLE_3"),
          prop: "withdraw_type",
          render: "customTemplate",
          customTemplate: this.toWithdrawType,
        },
        {
          label: this.$t("EXCHANGELOG.TITLE_4"),
          prop: "money",
          render: FieldRenderType.currency

        },
        {
          label: this.$t("EXCHANGELOG.TITLE_6"),
          prop: "flag",
          render: "customTemplate",
          customTemplate: this.toWithdrawStatus,
        },
      ],
      data: []
    }
  },
  mounted() {
    this.query()
  },
  methods: {
    toWithdrawType (value) {
      switch (Number(value)) {
        case 1:
          return this.$t('1016')
        case 2:
          return this.$t('1017')
        case 3:
          return this.$t('1016_1')
      }
    },
    toWithdrawStatus (value) {
      return this.$t('EXCHANGELOG.EXCHANGE_RECORD_STATUS_'+value)
    },
    query() {
      this.$tdtcApi.getQueryInfo(TDTC_ROURE.QUERY_EXCHANGE_RECORD)
       .then((res) => {
         // ExchangeRecordResponse
         this.data = res['record_list'] ?? []
       })
       .catch(() => {})
    }
  }
}
</script>

<template>
  <div class="withdraw-history" style="height: 100vh;background: #F9F9F9;">
    <div class="vip-header">
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue am-navbar am-navbar-light" style="position: fixed !important;width: 100%; z-index: 1; top: 0;"
        >
          <div class="am-navbar-left" role="button">
          <span class="am-navbar-left-content" @click="$router.back()">
            <span class="return_icon">
              <svg class="am-icon am-icon-left am-icon-lg"><use xlink:href="#left"></use></svg>
            </span>
          </span>
          </div>
          <div class="am-navbar-title" style="font-size: .4rem !important;font-weight: 600;">{{ $t('EXCHANGELOG.LAYER_TITLE') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
    </div>

    <RecordBoard :data="data" :column="column" />

  </div>
</template>

<style scoped>

</style>