<template>
  <div>

  </div>
</template>

<script>

export default {

  data() {
    return {

    };
  },

  mounted() {

  },
  watch: {
    screenHW: {
      immediate: true,
      handler(newval, old) {
        this.rotate()
      }

    }
  },
  methods: {
    // 判断横竖屏
    rotate() {
      if (this.screenHW == 180 || this.screenHW == 0) {
        // iconsole('竖屏')
        this.isScreen = true
      } else if (this.screenHW == 90 || this.screenHW == -90) {
        // iconsole('横屏')
        this.isScreen = false
      }
    },

  }
}
</script>
