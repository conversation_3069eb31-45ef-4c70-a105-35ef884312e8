<script>
import {mdate} from "@/mixins/mdate";
import {transactionRecord} from "@/mixins/transactionRecord";

export default {
  name: "record",
  mixins: [mdate, transactionRecord],
  data() {
    return {
      showPicker: false
    }
  },
  methods: {
    typeChange(picker, value, index) {
      this.form.action = index
    },
  }
};
</script>

<template>
  <div class="mc-transaction-record-root mtrr-container" style="background-color: #fff; height: 100vh">
    <van-sticky>
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-record am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
          </div>
          <div class="am-navbar-title">{{ $t('in_account_record') }}</div>
          <div class="am-navbar-right"></div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <div class="mc-filter-container name-filter">
        <div class="am-flexbox am-flexbox-align-middle mc-trans-filter mc-filter" style="padding-left: 0; padding-right: 0;">
          <div class="am-flexbox am-flexbox-align-middle mc-trans-filter">
            <!-- react-text: 7871 --><!-- /react-text -->
            <!-- react-text: 7872 --><!-- /react-text --><a
              @click="showPicker = true"
              role="button"
              class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
              aria-disabled="false"
              style="flex: 0 0 40%; padding: 0px 0.2rem"
          >
            <svg class="am-icon am-icon-down am-icon-xxs" aria-hidden="true">
              <use xlink:href="#down"></use>
            </svg>
            <span>{{ actions[form.action] }}</span></a
          >

            <div class="flex-shrink1 filter-time-btn" @click="show = true">
              <a
                  role="button"
                  class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
                  aria-disabled="false"
                  style="flex: 0 0 50%"
              >
                <svg
                    class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs"
                    aria-hidden="true"
                >
                  <use xlink:href="#calendar_c4db3b67"></use>
                </svg>
                <span>{{ date }}</span></a
              >
            </div>
            <div translate="button_search" class="button button-submit tabPane-span" :class="{ processing: processing }" @click="search(false)">
              {{ $t("button_search") }}
            </div>
            <!--          <a-->
            <!--              role="button"-->
            <!--              class="am-button am-button-primary am-button-small am-button-inline am-button-icon"-->
            <!--              aria-disabled="false"-->

            <!--              :class="{ processing: processing }" @click="search(false)"-->
            <!--          >-->
            <!--            <svg-->
            <!--                class="am-icon am-icon-check-circle-o am-icon-xxs"-->
            <!--                aria-hidden="true"-->
            <!--            >-->
            <!--              <use xlink:href="#check-circle-o"></use>-->
            <!--            </svg>-->
            <!--            <span>{{ $t('button_search') }}</span></a-->
            <!--          >-->
             <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" get-container=".mc-transaction-record-root" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
                :max-date="maxDate" :allowSameDay="true"/>
            <van-action-sheet get-container=".mc-transaction-record-root" v-model="showPicker" :title="$t('select_transfer_type')">
              <van-picker :columns="actions" @change="typeChange"/>
            </van-action-sheet>
          </div>
        </div>
      </div>
    </van-sticky>
    <div class="mc-trans-record-container">
      <div class="records-list-root">
        <div
            class="list-wrapper" style="overflow: unset"
        >
          <ul
              class="scroll-inner-container"

          >
            <!--                        <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transferência para fora</span></div><span class="tr-item-time">10-30 13:55:04</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12056 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12057 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12058 &ndash;&gt;220231030495841442&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12060 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;<p class="9046379879">&lt;!&ndash; react-text: 12062 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12063 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12064 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">0.00</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-red-out">&lt;!&ndash; react-text: 12072 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12073 &ndash;&gt;-11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->
            <!--                        <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transferência para fora</span></div><span class="tr-item-time">10-30 13:55:04</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12056 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12057 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12058 &ndash;&gt;220231030495841442&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12060 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;<p class="9046379879">&lt;!&ndash; react-text: 12062 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12063 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12064 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">0.00</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-red-out">&lt;!&ndash; react-text: 12072 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12073 &ndash;&gt;-11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->
            <!--                        <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transferência para fora</span></div><span class="tr-item-time">10-30 13:55:04</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12056 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12057 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12058 &ndash;&gt;220231030495841442&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12060 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;<p class="9046379879">&lt;!&ndash; react-text: 12062 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12063 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12064 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">0.00</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-red-out">&lt;!&ndash; react-text: 12072 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12073 &ndash;&gt;-11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->
            <!--                        <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transferência para fora</span></div><span class="tr-item-time">10-30 13:55:04</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12056 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12057 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12058 &ndash;&gt;220231030495841442&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12060 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;<p class="9046379879">&lt;!&ndash; react-text: 12062 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12063 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12064 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">0.00</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-red-out">&lt;!&ndash; react-text: 12072 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12073 &ndash;&gt;-11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->
            <!--                        <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transferência para fora</span></div><span class="tr-item-time">10-30 13:55:04</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12056 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12057 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12058 &ndash;&gt;220231030495841442&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12060 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;<p class="9046379879">&lt;!&ndash; react-text: 12062 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12063 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12064 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">0.00</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-red-out">&lt;!&ndash; react-text: 12072 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12073 &ndash;&gt;-11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->
            <!--                        <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transferência para fora</span></div><span class="tr-item-time">10-30 13:55:04</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12056 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12057 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12058 &ndash;&gt;220231030495841442&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12060 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;<p class="9046379879">&lt;!&ndash; react-text: 12062 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12063 &ndash;&gt;➞PP&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12064 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">0.00</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-red-out">&lt;!&ndash; react-text: 12072 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12073 &ndash;&gt;-11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->
            <!--            <li><div class="rr-container"><div class="tr-item-header"><div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;"><div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap; width: 66%;"><span class="record-icon-ticket"></span><span class="tr-item-title">Transfer In</span></div><span class="tr-item-time">10-30 13:48:48</span></div></div><div class="tr-item-content"><div class="tr-item-detail"><div class="tr-item-content-item"><div class="tic-code">&lt;!&ndash; react-text: 12086 &ndash;&gt;Nº do pedido&lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12087 &ndash;&gt;: &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12088 &ndash;&gt;220231030495823123&lt;!&ndash; /react-text &ndash;&gt;</div><div class=" tic-remark false">&lt;!&ndash; react-text: 12090 &ndash;&gt;PP➞&lt;!&ndash; /react-text &ndash;&gt;<p class="9046278867">&lt;!&ndash; react-text: 12092 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12093 &ndash;&gt;PP➞&lt;!&ndash; /react-text &ndash;&gt;</p></div></div>&lt;!&ndash; react-text: 12094 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;</div><div class="tr-item-content-item tic-footer"><div><span class="tic-footer-title number-mc">Equilíbrio</span><span class="tic-footer-amount number-mc">11.98</span></div><div><span class="tic-footer-title number-mc">Valor da transação</span><span class="tic-footer-amount number-mc color-green-in ">&lt;!&ndash; react-text: 12102 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12103 &ndash;&gt;11.98&lt;!&ndash; /react-text &ndash;&gt;</span></div></div></div></div></li>-->


            <van-list
                v-if="records.length"
                v-model="loading"
                :finished="finished"
                @load="search(true)"
                :immediate-check="false"

            >
              <li v-for="(item, index) in records" :key="index">
                <div class="rr-container">
                  <div class="tr-item-header">
                    <div class="am-flexbox am-flexbox-dir-row am-flexbox-justify-between am-flexbox-align-middle" style="height: 100%;">
                      <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        <span class="record-icon-ticket"></span>
                        <span class="tr-item-title">{{ $t(actions[item.action]) }}</span>
                      </div>
                      <span class="tr-item-time">{{ item.transferTime | datetimeFormat }}</span></div>
                  </div>
                  <div class="tr-item-content">
                    <div class="tr-item-detail">
                      <div class="tr-item-content-item">
                        <div class="tic-code">{{ $t('order_number') }}: {{ item.sequenceId }}</div>
                        <div class=" tic-remark false">{{
                            item.action === 1
                                ? "➞" + getPlatformName(item.platformId)
                                : getPlatformName(item.platformId) + "➞"
                          }}
                          <!--                          <p class="9046408349">&lt;!&ndash; react-text: 12032 &ndash;&gt; &lt;!&ndash; /react-text &ndash;&gt;&lt;!&ndash; react-text: 12033 &ndash;&gt;PP➞111&lt;!&ndash; /react-text &ndash;&gt;</p>-->
                        </div>
                      </div><!-- react-text: 12034 --> <!-- /react-text --></div>
                    <div class="tr-item-content-item tic-footer">
                      <div>
                        <span class="tic-footer-title number-mc">{{ $t('balance') }}</span><span class="tic-footer-amount number-mc">{{ item.scoreAfter | currency }}</span>
                      </div>
                      <div>
                        <span class="tic-footer-title number-mc">{{ $t('transaction_amount') }}</span><span class="tic-footer-amount number-mc " :class="item.action === 1 ? 'color-green-in' : 'color-red-out'">
                        {{
                          item.action === 1 ? item.score : -item.score | currency
                        }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <template #loading>
                <div class="scroll-loading">
                  <svg
                      class="loading-icon"
                      x="0px"
                      y="0px"
                      width="40px"
                      height="40px"
                      viewBox="0 0 40 40"
                  >
                    <path
                        opacity="0.2"
                        d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                    ></path>
                    <path
                        d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                    >
                      <animateTransform
                          attributeType="xml"
                          attributeName="transform"
                          type="rotate"
                          from="0 20 20"
                          to="360 20 20"
                          dur="0.5s"
                          repeatCount="indefinite"
                      ></animateTransform>
                    </path>
                  </svg>
                </div>
              </template>
            </van-list>
            <div class="nodata-container" v-else>
              <svg class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon">
                <use xlink:href="#nodata_f4c19c2d"></use>
              </svg>
              <p class="">{{ $t('no_data') }}</p>
            </div>
          </ul>

          <!--          <div class="pullup-wrapper"></div>-->
        </div>
      </div>

    </div>
  </div>
</template>


<style scoped>

.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}
</style>
