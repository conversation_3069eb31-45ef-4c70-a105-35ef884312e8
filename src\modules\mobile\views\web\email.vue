<template>
  <section class="webMail" style="height: 100%; background: rgb(255, 255, 255)">
    <header class="mc-header-wrap">
      <div class="mc-navbar-blue mc-profitandloss am-navbar am-navbar-light">
        <div class="am-navbar-left" @click="$router.back()">
          <svg class="am-icon am-icon-left am-icon-lg">
            <use xlink:href="#left"></use>
          </svg>
        </div>
        <div class="am-navbar-title">Mail</div>
        <div class="am-navbar-right"></div>
      </div>
    </header>
    <van-tabs v-model="active" animated line-width="50%" line-height="2px" color="#108ee9">
      <van-tab title="Inbox">
        <div
            style="display: flex; align-items: center; justify-content: center"
        >
          <div class="noDataIcon">
            <!-- react-empty: 6531 -->
            <p>No messages</p>
          </div>
        </div>
      </van-tab>
      <van-tab title="Outbox">
        <div
            style="display: flex; align-items: center; justify-content: center"
        >
          <div class="noDataIcon">
            <!-- react-empty: 6531 -->
            <p>No messages</p>
          </div>
        </div>
      </van-tab>
    </van-tabs>
  </section>
</template>
<script>
export default {
  data() {
    return {
      active: 0
    }
  }
}
</script>