<script>
import {withdraw} from "@/mixins/withdraw";

export default {
  name: "index",
  mixins: [withdraw],
  data() {
    return {
      swiperOptions: {
        loop: false,
        speed: 1000,
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
        observer: true,
        observeParents: !0,
      },
    }
  }
};
</script>

<template>
  <div class="withdraw-container-v2">
    <div class="mc-header-wrap">
      <div
          id="mc-header"
          class="mc-navbar-blue mc-withdraw am-navbar am-navbar-light"
      >
        <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
        </div>
        <div class="am-navbar-title">{{ $t('label_withdrawals') }}</div>
        <div class="am-navbar-right">
          <svg @click="$router.push('/m/withdrawReport')"
              class="am-icon am-icon-withdraw_record_c986adfb am-icon-md am-navbar-title"
          >
            <use xlink:href="#withdraw_record_c986adfb"></use>
          </svg>
        </div>
      </div>
      <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
    </div>
    <!-- react-text: 18406 --><!-- /react-text -->
    <div class="am-tabs am-tabs-top show-one">
      <div role="tablist" class="am-tabs-bar" tabindex="0">
        <div
            class="am-tabs-ink-bar am-tabs-ink-bar-animated"
            style="
            display: block;
            transform: translate3d(0px, 0px, 0px);
            width: 180px;
          "
        ></div>
        <div
            v-for="(item, index) in withdrawalTypes"
            :key="index"
            :class="{'am-tabs-tab-active': index === withdrawalTypeIndex}"
            @click="switchWithdrawalType(index)"
            class="am-tabs-tab"
        >
          <span>{{ $t('label_withdraw_plat' + item.platformType) }}</span>
        </div>
      </div>
      <div
          class="am-tabs-content am-tabs-content-animated"
          style="transform: translateX(0%) translateZ(0px)"
      >
        <div
            role="tabpanel"
            aria-hidden="false"
            class="am-tabs-tabpane am-tabs-tabpane-active"
        >
          <div>
            <div class="withdraw-main">
              <div class="wm-space"></div>
              <div>
                <div class="withdraw-bank withdraw-bank-container vc pix">
                  <div class="cards-title">{{ $t('label_pix_wallet_alreadyBound') }} {{ cardStr }}
                  </div>
                  <div class="am-wingblank am-wingblank-lg">
                    <swiper
                        key="ewallet"
                        ref="ewallet"
                        class="swiper-wrap"
                        :options="swiperOptions"
                    >

                      <swiper-slide
                          v-for="(item, index) in walletList(withdrawalTypeIndex)"
                          :key="index"
                          class="vc-cards-item PT withdraw-bankcard number-mc c0" style="width: 305px; margin-right: 40px;"
                      >
                        <div class="withdraw-bankcard-icon"><span>{{ item.platform.platformName }}</span></div>
                        <div class="withdraw-bankcard-title">{{ $t('label_cardNum') }}</div>
                        <div class="withdraw-bankcard-num">{{ item.wallet.cardAddress | cardReplace }}</div>
                        <span class="create-time">{{ item.wallet.createTime | dateFormat }}</span>
                        <!--                        <div class="withdraw-bankcard-cont wbc-date">-->
                        <!--                          <div><span class="card-name">1*****</span></div>-->
                        <!--                          <div><span>Data de vinculação</span><span>{{ item.wallet.createTime | dateFormat }}</span></div>-->
                        <!--                        </div>-->
                      </swiper-slide>
                      <div
                          class="swiper-pagination swiper-pagination-bullets"
                          slot="pagination"
                          v-show="walletList(withdrawalTypeIndex).length"
                      ></div>
                    </swiper>
                    <div class="withdraw-nbank" v-if="!walletList(withdrawalTypeIndex).length">
                      <span>{{ $t('null') }}</span>
                    </div>
                  </div>
                </div>
                <div class="withdraw-bkdbtn" v-if="noItem(withdrawalTypeIndex)">
                  <div class="withdraw-bkadd" @click="btnAdd(withdrawalTypeIndex)">
                    <svg class="am-icon am-icon-bankadd_7c674007 am-icon-md">
                      <use xlink:href="#bankadd_7c674007"></use>
                    </svg>
                  </div>
                </div>
              </div>
              <div>
                <div class="withdraw-bkinfo">
                  <div style="color: red" v-if="totalTurnover">
                    {{ $t('info_withdraw_turnover') }}: {{ totalTurnover | currency }}
                  </div>
                  <div>
                    {{ $t('main_wallet') }}: {{ $store.state.configs.currency_symbol }} {{ balance }}
                  </div>
                  <div>
                    {{ $t('withdrawal_available_amount') }}: {{ $store.state.configs.currency_symbol }} {{ balance }}
                  </div>
                  <div v-if="withdraw.amount && walletList(withdrawalTypeIndex).length && walletList(withdrawalTypeIndex)[dotIndex - 1].platform.platformCommission">
                    {{ $t('platform_commission') }}: {{ commission }}
                  </div>
                </div>
                <div class="withdraw-bkinfo" style="padding-top: 0" v-if="walletList(withdrawalTypeIndex)[dotIndex - 1] && walletList(withdrawalTypeIndex)[dotIndex - 1].platform && walletList(withdrawalTypeIndex)[dotIndex - 1].platform.platformRate !== 1">
                  <div>
                    {{ $t('label_converted_crypto_amount') }}: {{ walletList(withdrawalTypeIndex)[dotIndex - 1].platform.currencySymbol }} {{ convertedAmount }}
                  </div>
                  <div>
                    {{ $t('label_reference_rate') }}: 1 {{ walletList(withdrawalTypeIndex)[dotIndex - 1].platform.currencySymbol }} = {{ $store.state.configs.currency_symbol }} {{ walletList(withdrawalTypeIndex)[dotIndex - 1].platform.platformRate | currency(false, true) }}
                  </div>
                </div>
                <div class="withdraw-bkinfo" style="padding-top: 0">
                  <div class="wallet_return">
                    <div @click.prevent.stop="transferoutall">
                      <svg class="am-icon am-icon-refresh_37023e62 am-icon-md" :class="{rotateFull: refresh}">
                        <use xlink:href="#icon-refresh"></use>
                      </svg>
                      <span>{{ $t('return_balance') }}</span>
                    </div>
                  </div>
                </div>
                <form class="">
                  <div class="w-input-content">
                    <span class="placeholder">{{ $t('exchange_balance') }}</span>
                    <input type="number" inputmode="decimal" autocomplete="new-password" :placeholder="withdrawLimitStr"
                        maxlength="8"
                        v-model="withdraw.amount">
                  </div>
                  <div class="red-input-error"></div>
                  <div class="w-input-content-i18n CN w-input-content">
                    <span class="placeholder">{{ $t('label_pwd_pay') }}</span>
                    <input type="password" autocomplete="new-password" v-model.trim="withdraw.pwd">
                  </div>
                  <br>
                  <div class="w-input-submit" style="margin-top: 10px;">
                    <a @click="withdrawal" role="button" class="am-button" :class="(withdraw.amount && withdraw.pwd && !totalTurnover)? 'w-input-submit-true' :'w-input-submit-false'">
                      <span>{{ $t('button_submit') }}</span> </a>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <div
            role="tabpanel"
            aria-hidden="true"
            class="am-tabs-tabpane am-tabs-tabpane-inactive"
        ></div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.placeholder {
  background-color : unset;
  opacity          : 1;
}
</style>
