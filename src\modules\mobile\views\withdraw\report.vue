<script>
import {mdate} from "@/mixins/mdate";
import {withdrawReport} from "@/mixins/withdrawReport";

export default {
  name: "report",
  mixins: [mdate, withdrawReport],
  data() {
    return {
      showChooseType: false,
    }
  },
  methods: {
    typeChange(picker, value, index) {
      this.form.status = index
    },
  }
};
</script>

<template>
  <div class="withdraw-report" style="background-color: #fff; height: 100vh">
    <van-sticky>
      <div class="mc-header-wrap">
        <div
            id="mc-header"
            class="mc-navbar-blue mc-withdrawReport am-navbar am-navbar-light"
        >
          <div class="am-navbar-left" role="button" @click="$router.back()">
          <span class="am-navbar-left-content"
          ><span class="return_icon"
          ><svg class="am-icon am-icon-left am-icon-lg">
                <use xlink:href="#left"></use></svg></span
          ></span>
          </div>
          <div class="am-navbar-title">{{ $t('withdraw_report') }}</div>
          <div class="am-navbar-right">
            <div class="to-record">
              <router-link to="/m/vouReport">
                <svg
                    class="am-icon am-icon-deposit_record_b99e1f01 am-icon-md am-navbar-title"
                >
                  <use xlink:href="#deposit_record_b99e1f01"></use>
                </svg>
              </router-link>
            </div>
          </div>
        </div>
        <div class="am-whitespace am-whitespace-sm mc-whitespace"></div>
      </div>
      <div class="am-tabs am-tabs-top">
        <div class="am-tabs-bar" role="tablist">
          <div class="am-tabs-tab active" data-bs-toggle="tab" data-bs-target="#tab-0" @click="setDate(0)">
            <span class="am-badge">{{ $t('today') }}</span>
          </div>
          <div class="am-tabs-tab" data-bs-toggle="tab" data-bs-target="#tab-1" @click="setDate(1)">
          <span class="am-badge">{{ $t('yesterday') }}
          </span>
          </div>
          <div class="am-tabs-tab" data-bs-toggle="tab" data-bs-target="#tab-2" @click="setDate(7)">
          <span class="am-badge">{{ $t('week') }}
          </span>
          </div>
        </div>
        <div
            class="am-tabs-content am-tabs-content-animated">
          <div
              role="tabpanel"
              aria-hidden="false"
              class="am-tabs-tabpane"
              style="overflow: unset"
          >
            <!--          am-tabs-tabpane-active-->
            <div
                class="am-flexbox am-flexbox-align-middle mc-trans-filter mc-filter"
            >
              <div class="am-flexbox am-flexbox-align-middle mc-trans-filter">
                <span class="tabPane-span filter-tabPane-btn" @click="showChooseType = true">{{ form.status === 0 ? $t('label_type') : statusActions[form.status] }}</span>
                <div class="flex-shrink1 filter-time-btn" @click="show = true">
                  <a
                      role="button"
                      class="am-button am-button-ghost am-button-small am-button-inline am-button-icon"
                      aria-disabled="false"
                      style="flex: 0 0 50%"
                  >
                    <svg
                        class="am-icon am-icon-calendar_c4db3b67 am-icon-xxs"
                        aria-hidden="true"
                    >
                      <use xlink:href="#calendar_c4db3b67"></use>
                    </svg>
                    <span>{{ date }}</span></a
                  >
                </div>
                <div translate="button_search" class="button button-submit tabPane-span" :class="{ processing: processing }" @click="search(false)">
                  {{ $t("button_search") }}
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </van-sticky>
    <van-action-sheet get-container=".pop_root" v-model="showChooseType" :title="$t('label_type')">
      <van-picker :columns="statusActions" @change="typeChange"/>
    </van-action-sheet>
     <van-calendar :confirm-disabled-text="$t('confirm-text')" :confirm-text="$t('confirm-text')" get-container=".withdraw-report" :max-range="30" v-model="show" type="range" @confirm="onConfirmDate" color="#FFB627" :min-date="minDate" :defaultDate="defaultDate"
        :max-date="maxDate" :allowSameDay="true"/>
    <div class="mc-trans-record-container  voucher">
      <div
          class="deposit-record-root">
        <template v-if="records.length">
          <van-list
              v-model="loading"
              :finished="finished"
              @load="search(true)"
              :immediate-check="false"
          >

            <div class="records-root other vou" v-for="(item, index) in records" :key="index" style="margin-bottom: .15rem">
              <div class="am-card">
                <div class="am-card-body">
                  <div class="card-header">
                    <div><span class="card-header-span">{{ $t('label_withdraw_plat' + item.platformType) }}</span>
                    </div>
                    <div class="time" style="white-space: nowrap;">{{ item.createTime | datetimeFormat }}</div>
                  </div>
                  <div class="card-body">
                    <p class="clearfix deposit-id">{{ $t('transaction_id') }}：{{ item.sequenceId }}
                      <svg class="am-icon am-icon-copy_2a930dd0 am-icon-md">
                        <use xlink:href="#copy_2a930dd0"></use>
                      </svg>
                    </p>
                    <!--                              <p class="clearfix">{{ $t('load_message') }}：&lt;!&ndash; /react-text &ndash;&gt;</p>-->
                    <!--                          <p class="clearfix">{{ $t('arrival_time') }}：{{ item.comfirmTime | datetimeFormat }}</p>-->
                    <p class="clearfix">{{ $t("label_withdrawals_money") }}：{{ item.amount | currency }}</p>
                    <!--                              <p class="clearfix">{{ $t('activity') }}：</p>-->
                    <p class="clearfix">{{ $t('remarks') }}：{{ item.remark }}</p>
                  </div>
                  <div class="card-footer">
                    <ul>
                      <!--                            <li><p>{{ $t('application_amount') }}</p>-->
                      <!--                              <p class="requestedAmount">{{ item.chargeAmount | currency }}</p></li>-->
                      <!--                            <li>-->
                      <!--                              <p></p>-->
                      <!--                              <p></p></li>-->{{ $t('label_status') }}:&nbsp;
                      <i class="col2" style="display: inline">{{ statusActions2[item.status] }}</i>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <template #loading>
              <div class="scroll-loading">
                <svg
                    class="loading-icon"
                    x="0px"
                    y="0px"
                    width="40px"
                    height="40px"
                    viewBox="0 0 40 40"
                >
                  <path
                      opacity="0.2"
                      d="M20.201,5.169c-8.254,0-14.946,6.692-14.946,14.946c0,8.255,6.692,14.946,14.946,14.946 s14.946-6.691,14.946-14.946C35.146,11.861,28.455,5.169,20.201,5.169z M20.201,31.749c-6.425,0-11.634-5.208-11.634-11.634 c0-6.425,5.209-11.634,11.634-11.634c6.425,0,11.633,5.209,11.633,11.634C31.834,26.541,26.626,31.749,20.201,31.749z"
                  ></path>
                  <path
                      d="M26.013,10.047l1.654-2.866c-2.198-1.272-4.743-2.012-7.466-2.012h0v3.312h0 C22.32,8.481,24.301,9.057,26.013,10.047z"
                  >
                    <animateTransform
                        attributeType="xml"
                        attributeName="transform"
                        type="rotate"
                        from="0 20 20"
                        to="360 20 20"
                        dur="0.5s"
                        repeatCount="indefinite"
                    ></animateTransform>
                  </path>
                </svg>
              </div>
            </template>
          </van-list>
        </template>
        <div class="nodata-container" v-else>
          <svg class="am-icon am-icon-nodata_f4c19c2d am-icon-md nodata-icon">
            <use xlink:href="#nodata_f4c19c2d"></use>
          </svg>
          <p class="">{{ $t('no_data') }}</p>
        </div>
      </div>
    </div>
    <div class="records-footer hide">
      <span
      ><!-- react-text: 18388 -->Total<!-- /react-text --><!-- react-text: 18389 -->:
        <!-- /react-text --></span
      ><span>-</span>
    </div>
  </div>
</template>

<style scoped>
.am-tabs-bar .active {
  color         : #108ee9;
  box-shadow    : none;
  border        : 0;
  border-bottom : .04rem solid #108ee9;
}

.button {
  position         : relative;
  display          : inline-block;
  height           : .6rem;
  line-height      : .6rem;
  padding          : 0 10px;
  color            : #fff;
  vertical-align   : middle;
  text-align       : center;
  border-radius: 0.08rem;
  cursor           : pointer;
  width            : 1.6rem;
  transition       : .2s ease-in-out;
  box-shadow       : none;
}

.button.processing {
  opacity        : .5;
  pointer-events : none;
  transition     : none;
  /* position: relative; */
  color          : transparent !important;
}

.processing:after {
  position           : absolute !important;
  display            : block;
  height             : .4rem;
  width              : .4rem;
  top                : 50%;
  left               : 50%;
  margin-left        : -.2rem;
  margin-top         : -.2rem;
  border             : 2px solid #fff;
  border-radius      : 50%;
  border-right-color : transparent;
  border-top-color   : transparent;
  -webkit-animation  : rotate-full .5s infinite linear;
  animation          : rotate-full .5s infinite linear;
  content            : "";
}
</style>
