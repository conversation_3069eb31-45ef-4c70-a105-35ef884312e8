<template>
    <img :src="flagIcon" alt="lang" class="flag-icon" />
</template>

<script>
export default {

    props: {
        lang: { type: String, required: !0 },
        countryCode: { type: String, required: !1 },
        type: { type: String, required: !0, enum: ["1", "2"] },
    },
    data() {
        return {
            ip: {
                CN: { code: "CN" },
                EN: { code: "US" },
                JA: { code: "JP" },
                TH: { code: "TH" },
                VI: { code: "VN" },
                MY: { code: "MM" },
                ES: { code: "MX" },
                HI: { code: "IN" },
                ID: { code: "ID" },
                KM: { code: "KH" },
                KO: { code: "KR" },
                KR: { code: "KR" },
                MS: { code: "MY" },
                PT: { code: "BR" },
                TA: { code: "IN" },
                TW: { code: "HK" },
                UR: { code: "PK" },
                TY: { code: "TLG" },
                GB: { code: "GB" },
                HK: { code: "HK" },
                "PT-BR": { code: "BR" },
            }
        }
    },
    computed: {
        flagIcon: function () {
            var t = "1" === this.type ? "RECT" : "CIRCLE";
            if (this.countryCode)
                return ""
                    .concat(
                        "img/country/"
                    )
                    .concat(this.countryCode, ".svg");
            var e = this.ip[this.lang];
            return e
                ? ""
                    .concat(
                        "img/country/"
                    )
                    .concat(e.code, ".svg")
                : "";
        },
    },
}
</script>

<style></style>