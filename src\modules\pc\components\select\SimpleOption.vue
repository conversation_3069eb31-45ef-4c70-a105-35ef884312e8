<template>
    <div class="select-option" @click="selectOption">
        <slot />
    </div>
</template>

<script>
export default {
    name: "simpleOption",
    props: {
        option: { type: [String, Number, Object], required: !0 },
        className: { type: String, default: "" },
    },
    methods: {
        selectOption: function () {
            this.$emit("option-selected", this.option);
        },
    },
}
</script>

<style></style>