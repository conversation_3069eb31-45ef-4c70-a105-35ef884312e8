<template>
    <div class="simple-select">
        <div ref="selectInput" class="select-input" @click="toggleOptions">
            <div class="selected-option">
                <slot name="selected" />
            </div>
            <i class="arrow-icon" :class="{ up: showOptions, down: !showOptions }">
                <slot name="arrow" />
            </i>
        </div>
        <transition :name="dropAnimationName">
            <div class="select-options" v-show="showOptions">
                <slot />
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    name: "simpleSelect",
    props: { dropAnimationName: { type: String, default: "zoom-in-top" } },
    data: function () {
        return { showOptions: !1 };
    },
    methods: {
        toggleOptions: function () {
            (this.showOptions = !this.showOptions),
                this.showOptions
                    ? document.addEventListener("click", this.closeOptions)
                    : document.removeEventListener("click", this.closeOptions);
        },
        closeOptions: function (e) {
            this.$refs.selectInput.contains(e.target) ||
                ((this.showOptions = !1),
                    document.removeEventListener("click", this.closeOptions));
        },
    },
}
</script>

<style></style>