import Vue from "vue";
import VueRouter from "vue-router";

// import { routes as routerPc } from "@/modules/pc/router";
import { routes as routerM } from "@/modules/mobile/router";
import store from "@/store";

// 导入 myFunction 用于主动调用
import { myFunction } from "@/utils/filter";

const routerPush = VueRouter.prototype.push;
VueRouter.prototype.push = function (location) {
  return routerPush.call(this, location).catch((err) => {});
};
Vue.use(VueRouter); // i["b"].use(bt["a"]);

let routes = [];
// let Adaptive =
//   /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i;
// if (navigator.userAgent.match(Adaptive)) {
  routes = routerM;
// } else {
//   routes = routerPc;
// }

const router = new VueRouter({
  mode: "hash",
  base: process.env.BASE_URL,
  routes,
  scrollBehavior (to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { x: 0, y: 0 }
    }
  }
});

// 添加全局变量，控制 myFunction 是否执行
window.shouldRunMyFunction = false;

router.beforeEach((to, from, next) => {
  // 根据路由路径设置是否执行 myFunction
  const path = to.path;
  const shouldExecute = (path === '/' || path === '/m/member/home');
  window.shouldRunMyFunction = shouldExecute;
  
  // if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
    // 切换路由隐藏侧边栏
    if (store.state.pc.mcShow) {

      store.commit("setPcMcShow");
    }
    if (store.state.mobile.header.showDrop) {
      store.commit("setMobileHeadShowDrop");
    }
    if (store.state.showMobileSide) {
      store.commit("setShowMobileSide");
    }
    if (store.state.showAd) {
        store.commit("setShowAd", false);
    }
 /*   if (!to.matched.length) {
      next("/m");
      return;
    }*/
    if (
        !store.getters.isLogin &&
        ![
          "/",
          "/m",
          "/m/",
          "/m/login",
          "/m/register",
          "/m/activity",
          "/m/activityDetail",
          "/m/notice",
          "/m/help",
          "/m/inviteOverview",
          "/m/seamless/go",
        ].includes(to.path)
    ) {
      next("/m/login");
    } else {
      next();
    }
  /*} else {
    if (
        (!store.getters.isLogin &&
            [
              "/seamless",
            ].includes(to.path)) || !to.matched.length
    ) {
      next("/");
    } else {
      next();
    }
  }*/
});

router.afterEach((to, from) => {
  // 在路由完成后，如果是指定页面，则主动调用 myFunction
  if (window.shouldRunMyFunction) {
    // 确保 DOM 已加载完毕
    setTimeout(() => {
      myFunction();
    }, 500);
  }
});

export default router;
