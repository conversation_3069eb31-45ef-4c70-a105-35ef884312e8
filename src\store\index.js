import Vue from "vue";
import Vuex from "vuex";
import persistedState from "vuex-persistedstate";
import i18n from "@/lang";
import {getRandomInt} from '@/utils/common'

Vue.use(Vuex); //i["b"].use(To["a"]);

export default new Vuex.Store({
  state: {
    popups: {
      active_types:         [],
      tabshow_active_types: [],
      params:               {},
      configs: {
        49: {
          reward_amount: 0,
          start_time: "",
          end_time: "",
        },
        53: {
          reward_amount: 0,
          start_time: "",
          end_time: "",
        }
      }
    },
    bottomModalStatus: 0,
    phonePreShow: "\+84",
    phonePreApi: "0084",
    menu: {
      scrollPosition: 0,
      filter: {
        categoryId: 0,
        platformId: 0,
      },
    },
    thirdGame: {
      firmType: "",
      firmCode: "",
    },
    showAd: false,
    showForgotModal: false,
    showBannersNotice: false,
    preHost: "",
    smsExpiresTime: 0,
    activity15Step: 0,
    dotKeys: {
      afKey: '',
      pixelId: '',
      kwai: '',
      firebase: '',
      rb: {
        link_id: "",
        uuid: "",
        channel_id: "",
      }
    },
    languages: {
      en: 1,
      pt: 15,
      vn: 7,
    },
    language: i18n.locale,
    channel: 1,
    device: 1,
    webType: 1,// 1=pc,2=h5
    playing: false,
    referral: "",
    balance: 0,
    account: {
      userId: 0,
      flag: 0,
      icon: 1,
      lastLogonIp: "",
      lastLogonTime: 0,
      nickname: "",
      vip: 0,
      role: 0, // 角色:0-普通,1-管理员 （分类，平台，游戏，充值）
      registermobile: "",
    },
    token: {
      token: "",
      expireTime: 0,
    },
    showPcSide: true,
    showMobileSide: false,
    openBind: false,
    pc: {
      rcShow: false,
      referralShow: false,
      mcShow: false,
      mcPage: "securityCenter",
      TransactionHistoryIndex: 0,
    },
    mobile: {
      header: {
        showDrop: false,
      },
      modal: {
        downloadBar: 0,
      },
      chooseGame: {
        show: false,
        freeCheck: false,
        normalCheck: false,
        platformId: 0,
        gameId: 0,
        gameName: '',
        firmType: '',
        firmCode: '',
      },
    },
    platform: {
      platformGames: [],
      gamePlatforms: [],
      currentPlatformIndex: 0,
      hotGames: {
        games: [],
        platforms: [],
      },
      favoriteGames: [],
      casinoGames: [],
      casinoPlatforms: [],
      fishGames: [],
      fishPlatforms: [],
      cardsGames: [],
      cardsPlatforms: [],
      liveGames: [],
      livePlatforms: [],
      sportsGames: [],
      sportsPlatforms: [],
      esportsGames: [],
      esportsPlatforms: [],
      currentCategory: "-3",
      categories: [],
      platforms: [],
      games: [],
    },
    noticeIndex: 0,
    helpIndex: 0,
    rewardCenterIndex: 0,
    currentPromotionId: 0,
    activitySwitchDetails: [],
    banners: [],
    notices: [],
    helper: [],
    configs: {
      currency_symbol: "",
      withdrawal_interval: "",
      customer_facebook: "",
      customer_telegram: "",
      customer_web: "",
      download_url_ios: "",
      download_url_android: "",
      shoplab: 0,
      crypto_switch: 0,
      wallet_switch: 0,
      bindphone: 0,
      bindphone_web: 0,
      web_game_website: "",

      sms_type: 0,
      sms_telegram: "",
      sms_phone: "",
      sms_content: "",
      tg_flag: 0,
    },
  },

  getters: {
    isLogin: (state) => {
      return (
        state.account.flag > 1 &&
        (state.token.expireTime
          ? state.token.expireTime * 1000 > new Date().getTime()
          : true)
      );
    },
    tempAccount(state) {
      return state.account.userId ? state.account.userId : getRandomInt(10000, 99999).toString()
    },
    inJsBridge() {
      iconsole("<<< inj", 'jsBridge' in window)
      const ua = navigator.userAgent;
      return 'jsBridge' in window || (/iPhone|iPad|iPod/.test(ua) && /AppleWebKit/.test(ua) && !/Safari/.test(ua));
    },
    bannersNotice: (state) => {
      let list = []
      for (const banner of  state.banners) {
        if (banner.popup) {
          for (const item of  banner.messages) {
            if (item.language === state.languages[state.language]) {
              item.imageUrl && list.push(item)
            }
          }
        }
      }
      return list
    },
  },
  mutations: {
    setPopups: function (e, t) {
      e.popups.active_types = t.active_types ?? [];
      e.popups.tabshow_active_types = t.tabshow_active_types ?? [];
      e.popups.params = t.params ?? {};
      if (t.params[49]) {
        e.popups.configs[49].reward_amount = t.params[49]['int_params'][0]
        e.popups.configs[49].start_time = t.params[49]['str_params'][0]
        e.popups.configs[49].end_time = t.params[49]['str_params'][1]
      }
      if (t.params[53]) {
        e.popups.configs[53].reward_amount = t.params[53]['int_params'][0]
        e.popups.configs[53].start_time = t.params[53]['str_params'][0]
        e.popups.configs[53].end_time = t.params[53]['str_params'][1]
      }
    },
    setNickname: function (e, t) {
      e.account.nickname = t;
    },
    setThirdGame: function (e, t) {
      e.thirdGame.firmCode = t.firmCode;
      e.thirdGame.firmType = t.firmType;
    },
    setShowAd: function (e, t) {
      e.showAd = t;
    },
    setBottomModalStatus: function (e, t) {
      e.bottomModalStatus = t;
    },
    setShowForgotModal: function (e, t) {
      e.showForgotModal = t;
    },
    setMenuScrollPosition: function (e, t) {
      e.menu.scrollPosition = t;
    },
    setMenuFilterPlatformId: function (e, t) {
      e.menu.filter.platformId = t;
    },
    setShowBannersNotice: function (e, t) {
      if (t && !this.getters.bannersNotice.length) return
      e.showBannersNotice = t;
    },
    setSmsExpiresTime: function (e, t) {
      e.smsExpiresTime = t;
    },
    setPreHost: function (e, t) {
      e.preHost = t;
    },
    setOpenBind: function (e, t) {
      e.openBind = t;
    },
    setRewardCenterIndexByType: function (e, type) {
      e.activitySwitchDetails.forEach((v, i) => {
        if (v.activityType === type) {
          e.rewardCenterIndex = i;
        }
      })

    },
    setLanguage: function (e, t) {
      e.language = t;
    },
    setAFKey: function (e, t) {
      t && (e.dotKeys.afKey = t);
    },
    setFBKey: function (e, t) {
      t && (e.dotKeys.pixelId = t);
    },
    setKwaiKey: function (e, t) {
      t && (e.dotKeys.kwai = t);
    },
    setRbKey: function (e, t) {
      t && (e.dotKeys.rb = t);
    },
    setFirebaseKey: function (e, t) {
      t && (e.dotKeys.firebase = t);
    },
    initConfig: function (e) {
      e.showForgotModal = false;
      e.menu.filter.platformId = 0;
      e.menu.scrollPosition = 0;
      e.openBind = false;
      e.activity15Step = 0;
      e.showPcSide = true;
      e.showMobileSide = false;
      e.platform.currentCategory = "-3";
      e.playing = false;
      e.showBannersNotice = false
      e.showAd = false

      e.pc.mcShow = 0;
      e.pc.rcShow = 0;
      e.pc.referralShow = 0;

      e.mobile.header.showDrop = 0;
      e.mobile.chooseGame = {
        show: false,
        freeCheck: false,
        normalCheck: false,
        platformId: 0,
        gameId: 0,
        firmType: '',
        firmCode: '',
      };
    },
    setPlaying: function (e, t) {
      e.playing = t;
      e.showPcSide = !t;
    },
    setReferral: function (e, t) {
      e.referral = t;
    },
    setLogon: function (e, t) {
      e.account = t.account;
      if(t.token) e.token = t.token;
      // if (location.hash === '#/') e.showAd = true;
    },
    setLogout: function (e) {
      e.account = {};
      e.token = {};
    },
    setToken: function (e, t) {
      e.token = t.token;
    },
    setIcon: function (e, t) {
      e.account.icon = t;
    },
    setShowPcSide: function (e, t) {
      e.showPcSide = "reverse" === t ? !e.showPcSide : t;
    },
    setShowMobileSide: function (e, t = true) {
      if (!t) {
        e.showMobileSide = false;
      } else {
        e.showMobileSide = !e.showMobileSide;
      }
    },
    setMobileModalDownloadBar: function (e, t) {
      e.mobile.modal.downloadBar = t;
      if (e.showMobileSide) {
        e.showMobileSide = false;
      }
    },
    setActivity15Step: function (e, t) {
      e.activity15Step = t
    },
    setPlatform(e, t) {
      e.activitySwitchDetails = t.activitySwitchDetails;
      /*let activity15Step = 0;
      t.activitySwitchDetails.forEach(function(element) {
        if (element.activityType === 15 && ["#/","#/m"].includes(location.hash)) {
            activity15Step = 1
        }
      });
      setTimeout(()=> {
        if (activity15Step) {
          e.activity15Step = 1
        } else {
          if (["#/","#/m"].includes(location.hash)) e.showBannersNotice = true
        }
      }, e.webType === 1 ? 500 : 2000)*/
      e.banners = t.banners;
      switch (e.account.role) {
        case 1: // 管理员
          e.platform.categories = t.categories;
          e.platform.gamePlatforms = [...t.platforms];
          e.platform.games = t.games
          break
        default: // 普通玩家
          e.platform.categories = t.categories.filter(row => row.status !== 2);
          e.platform.gamePlatforms = t.platforms.filter(row => row.status !== 2);
          e.platform.games = t.games.filter(row => {
            for (const category of e.platform.categories) {
              if (category.categoryId === row.categoryId) {
                return  category.status !== 2
              }
            }
            return false
          });
      }
      for (const config of t.configs) {
        switch (config.key) {
          case "web_currency_symbol":
            e.configs.currency_symbol = config.contentString;
            break;
          case "web_customer_facebook":
            e.configs.customer_facebook = config.contentString;
            break;
          case "web_customer_telegram":
            e.configs.customer_telegram = config.contentString;
            break;
          case "web_customer_web":
          case "qrcode":
            e.configs.customer_web = config.contentString;
            break;
          case "web_download_url_ios":
            e.configs.download_url_ios = config.contentString;
            break;
          case "web_download_url_android":
            e.configs.download_url_android = config.contentString;
            break;
          case "shoplab":
            e.configs.shoplab = config.contentNumber;
            break;
          case "crypto_switch":
            e.configs.crypto_switch = config.contentNumber;
            break;
          case "wallet_switch":
            e.configs.wallet_switch = config.contentNumber;
            break;
          case "bindphone":
            e.configs.bindphone = config.contentNumber;
            break;
          case "bindphone_web":
            e.configs.bindphone_web = config.contentNumber;
            break;

          case "sms_type":
            e.configs.sms_type = config.contentNumber;
            break;
          case "sms_telegram":
            e.configs.sms_telegram = config.contentString;
            break;
          case "sms_phone":
            e.configs.sms_phone = config.contentString;
            break;
          case "sms_content":
            e.configs.sms_content = config.contentString;
            break;
          case "tg_flag":
            e.configs.tg_flag = config.contentNumber;
            break;
          case "web_game_website":
            e.configs.web_game_website = config.contentString;
            break;

        }
      }
      e.platform.favoriteGames = t.favoriteGames;
      e.helper = t.helps;
      e.platform.hotGames = t.hotGames;
      e.notices = t.notices;
      e.platform.platforms = [{ platformId: 0, platformName: i18n.t("All"), status: 0 }, ...e.platform.gamePlatforms];
    },
    updatePlatforms: function (e, t) {
      let platforms = e.platform.platforms;
      platforms.forEach((i, k) => {
        t.forEach(j => {
          if (i.platformId === j.platformId) {
            e.platform.platforms[k] = j
          }
        })
      })
    },
    setCurrentCategory: function (e, t) {
      e.platform.currentCategory = t;
      e.menu.filter.platformId = 0;
    },
    toggleFav: function (e, t) {
      e.platform.favoriteGames = t.games;
    },
    setFavoriteGames: function (e, t) {
      e.platform.favoriteGames = t.games;
    },
    setPlatformGames: function (e, t) {
      switch (e.account.role) {
        case 1: // 管理员
          e.platform.platformGames = t.games;
          break
        default: // 普通玩家
          e.platform.platformGames = t.games.filter(row => {
            for (const category of e.platform.categories) {
              if (category.categoryId === row.categoryId) {
                return  category.status !== 2
              }
            }
            return false
          });
      }
    },
    setPlatformPlatform: function (e, t) {
      e.platform.currentPlatformIndex = t;
    },
    setNoticeIndex: function (e, t) {
      e.noticeIndex = t;
    },
    setHelpIndex: function (e, t) {
      e.helpIndex = t;
    },
    setCurrentPromotionId: function (e, t) {
      e.currentPromotionId = t;
    },
    setPcMcShow: function (e) {
      e.pc.mcShow = !e.pc.mcShow;
    },
    setPcReferralShow: function (e) {
      e.pc.referralShow = !e.pc.referralShow;
    },
    setPcRcShow: function (e) {
      e.pc.rcShow = !e.pc.rcShow;
    },
    setPcMcPage: function (e, t) {
      if (t !== 'securityCenter') {
        e.openBind = false
      }
      e.pc.mcPage = "";
      setTimeout(()=>{
        e.pc.mcPage = t;
      },100)
      e.pc.mcShow = true;
    },
    setTransactionHistoryIndex: function (e, t) {
      e.pc.TransactionHistoryIndex = t
    },
    setMobileHeadShowDrop: function (e) {
      e.mobile.header.showDrop = !e.mobile.header.showDrop;
    },
    setDevice: function (e, t) {
      e.webType = t;

      const userAgent = navigator.userAgent; // 获取User Agent信息
      // const platform = navigator.platform; // 获取操作系统平台信息
      // const userAgentData = navigator.userAgentData;
      // const { platform } = userAgentData;
      // 判断操作系统类型
      // DEVICE_WINDOWS = 1
      // DEVICE_ANDROID = 3
      // DEVICE_IOS     = 5
      // DEVICE_IPAD    = 7
      // DEVICE_MAC     = 9
      // DEVICE_LINUX   = 11
      //
      // DEVICE_WINDOWS_BROSWER = 2
      // DEVICE_ANDROID_BROSWER = 4
      // DEVICE_IOS_BROSWER     = 6
      // DEVICE_IPAD_BROSWER    = 8
      // DEVICE_MAC_BROSWER     = 10
      // DEVICE_LINUX_BROSWER   = 12
      if (/(Android)/i.test(userAgent)) {
        e.device = 4
      } else if (/(iPhone|iPod)/i.test(userAgent)) {
        e.device = 6
      } else if (/(iPad)/i.test(userAgent)) {
        e.device = 8
      } else if (/(Mac|Macintosh)/i.test(userAgent)) {
        e.device = 10
      } else if (/(Linux)/i.test(userAgent)) {
        e.device = 12
      } else if (/(Windows NT 10.0|Windows NT 6.2|Windows NT 6.1|Windows NT 6.0|Windows NT 5.1|Windows NT 5.0)/i.test(userAgent)) {
        e.device = 2
      }
    },
    setBalance: function (e, t) {
      e.balance = t.balance;
      e.account.vip = t.vip;
    },
    setMobileChooseGame: function (e, t) {
      e.mobile.chooseGame = t;
    },
    resetMobileChooseGame: function (e) {
      e.mobile.chooseGame = {
        show: false,
        freeCheck: false,
        normalCheck: false,
        platformId: 0,
        gameId: 0,
        firmType: '',
        firmCode: '',
      };
    },
    updateNotice: function (e, t) {
      for (const notice of e.notices) {
        if (notice.id === t.id) {
          notice.messages = t.messages;
        }
      }
    },
    updateHelp: function (e, t) {
      for (const item of e.helper) {
        if (item.category === t.category) {
          item.details = t.details;
        }
      }
    },
  },
  plugins: [
    persistedState({
      // 默认使用localStorage来固化数据，也可使用sessionStorage，配置一样
      // storage: window.sessionStorage,
    }),
  ],
});
