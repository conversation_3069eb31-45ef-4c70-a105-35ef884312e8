export default {
  loader: function (e, t) {
    var a = document.getElementById("br_loader"),
      n = a;
    if (!a) {
      var o = document.createDocumentFragment(),
        i = document.createElement("div");
      i.id = "br_loader";
      var s = document.getElementById("app");
      n = i;
      var r =
        "<div class='br_loader_main'><div class='br_loader_bg'></div><div class='br_loader_content'><div class='br_spinner'><div class='spinner_inner'><div class='spinner_line'></div><div class='spinner_line'></div><div class='spinner_line'></div><div class='spinner_circle'>&#9679;</div></div></div></div></div>";
      (n.innerHTML = r),
        o.appendChild(i),
        t
          ? ((i.className = "br_loader_child"), t.appendChild(o))
          : ((i.className = "br_loader_root"),
            document.body.insertBefore(o, s));
    }
    this.close(n, e);
  },
  init: function (e, t) {
    var a,
      n = this,
      o = e || {},
      i = document.getElementById("pp_model_dialog"),
      s = this.sortObj(o);
    if (i) {
      var r = this.getByClass(i, "pp_model_dmain"),
        c = this.getByClass(i, "pp_model_dbody");
      r.removeChild(c);
      var l = document.createDocumentFragment();
      (a = i),
        l.appendChild(
          this.createDom(s, o, a, function (e) {
            t && t(e);
          })
        ),
        i.classList.add("active"),
        r.appendChild(l);
    } else {
      var d = document.createDocumentFragment(),
        u = 0,
        m = [];
      while (u < 2)
        (m[u] = document.createElement("div")),
          u > 0 && m[u - 1].appendChild(m[u]),
          u++;
      (m[0].id = "pp_model_dialog"),
        (m[0].className = o.class
          ? "pp_model_dialog active ".concat(o.class)
          : "pp_model_dialog active"),
        (m[1].className = "pp_model_dmain"),
        (a = m[0]),
        m[1].appendChild(
          this.createDom(s, o, a, function (e) {
            t && t(e);
          })
        );
      var p = document.createElement("div");
      (p.className = "pp_model_dcover"),
        (p.id = "pp_model_dcover"),
        m[1].appendChild(p),
        d.appendChild(m[0]),
        document.getElementsByTagName("body")[0].appendChild(d);
    }
    var g = document.getElementById("pp_model_dcover");
    (g.onclick = null),
      (g.onclick = function () {
        ("boolean" !== typeof o.cover || o.cover) &&
          (n.close(a), t && t("cover"));
      }),
      o.timeOut && this.close(a, o.timeOut);
  },
  sortObj: function (e) {
    var t = {};
    return (
      e.icon && (t.icon = e.icon),
      e.title && (t.title = e.title),
      e.html && (t.html = e.html),
      e.btn && (t.btn = e.btn),
      t
    );
  },
  getByClass: function (e, t) {
    for (var a = e.getElementsByTagName("*"), n = [], o = 0; o < a.length; o++)
      a[o].className === t && n.push(a[o]);
    return n[0];
  },
  createDom: function (e, t, a, n) {
    var o = this,
      i = document.createElement("div");
    (i.className = "pp_model_dbody"), (i.style = t.style || "");
    var s = {};
    for (var r in e)
      if ("btn" === r) {
        var c = e.btn || {},
          l = document.createElement("div");
        l.className = "pp_model_db".concat(r);
        var d = function (e) {
          var t = document.createElement("div");
          (t.className = e),
            t.addEventListener("click", function (t) {
              o.close(a), n(e);
            }),
            (t.innerHTML = "<span>".concat(c[e], "</span>")),
            l.appendChild(t);
        };
        for (var u in c) d(u);
        i.appendChild(l);
      } else if ("icon" === r) {
        var m = document.createElement("div");
        (m.className = "pp_model_db".concat(r, " ").concat(e[r])),
          (m.innerHTML = "<span class=br_dialog_d".concat(e[r], "></span>")),
          i.appendChild(m);
      } else
        "class" !== r &&
          ((s[r] = document.createElement("div")),
          (s[r].className = "pp_model_db".concat(r)),
          (s[r].innerHTML = e[r]),
          i.appendChild(s[r]));
    return i;
  },
  close: function (e, t) {
    "number" === typeof t
      ? (e.classList.add("active"),
        setTimeout(function () {
          e.classList.remove("active");
        }, t))
      : t
      ? e.classList.add("active")
      : e.classList.remove("active");
  },
};
