function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, '\\$&');
    let regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, ' '));
}
window['iconsole'] = getParameterByName('deb') === "1" || process.env.VUE_APP_ENV === "development"  ? console.log : () => {}

import moment from "moment";
import Vue from "vue";
import store from "@/store";
import currency from "currency.js";

import { protoApi } from "@/api";
Vue.prototype.$protoApi = protoApi;
import  tdtcApi from "@/api/tdtc";
Vue.prototype.$tdtcApi = tdtcApi;

import { Toast, Dialog } from 'vant';
import { Locale } from 'vant';
// 引入英文语言包
// import enUS from 'vant/es/locale/lang/en-US';
import viVN from 'vant/es/locale/lang/vi-VN';

// Locale.use('en-US', enUS);
Locale.use('vi-VN', viVN);

// import 'vant/es/toast/style';
// import 'vant/es/dialog/style';
import 'vant/lib/index.css';

Toast.setDefaultOptions({
    duration: 3000,
    overlay: true,
    // forbidClick: true,
    closeOnClick: true,
    closeOnClickOverlay: true,
});
Vue.use(Toast);
Vue.use(Dialog);
window.$toast = Toast;
window.$Dialog = Dialog;

import VueLuckyCanvas from '@lucky-canvas/vue'
Vue.use(VueLuckyCanvas)

import VeeValidate from "vee-validate";
import vn from "vee-validate/dist/locale/vi"
import en from "vee-validate/dist/locale/en"
const config = {
    locale: 'vn',
    dictionary: {
        en,
        vn
    },
    // errorBagName: 'errors',
    fieldsBagName: 'fieldBags',
    // delay: 1000,
    // locale: 'en',
    // strict: true,
    // enableAutoClasses: true,
    events: 'blur',
    // inject: true
}
//使用插件
Vue.use(VeeValidate,config)

Vue.filter("dateFormat", (str, part = "YYYY-MM-DD") => {
    if (!str) {
        return '-'
    }
    return moment(str).format(part);
});
let datetimeFormat = (str, part = "YYYY-MM-DD HH:mm:ss") => {
    if (!str) {
        return '-'
    }
    return moment(str).format(part);
}
Vue.filter("datetimeFormat", datetimeFormat);
Vue.filter("timeFormat", (str, part = "HH:mm:ss") => {
    if (!str) {
        return '-'
    }
    return moment(Number(moment(new Date()).startOf("day").format('x')) + str * 1000).format(part);
});

Vue.filter("removeHtmlTag", function (str) {
    if (!str) return "";
    return str.replace(/(<([^>]+)>)/gi, "");
});

Vue.filter("cardReplace", function (str) {
    return '******' + str.substr(-4);
});

Vue.filter("hideStr", function (str) {
    if (str === undefined) return '-'
    const length = str.length;

    if (length >= 5) {
        return str.slice(0, 2) + '***' + str.slice(5);
    } else if (length === 4) {
        return str[0] + '**' + str[3];
    } else {
        return '*' + str.substr(1);
    }
});

Vue.filter("toRate", function (str) {
    return (str*100).toFixed(2)+'%';
});


Vue.prototype.currency = function (value, fromCents = true, calc = false) {
    if (!value) {
        value = 0
    }
    let precision = 2
    if (calc && value < 1 && value !== 0) {
        if (value * 100 > 1) {
            precision = 2
        } else if(value * 10000 > 1) {
            precision = 6
        } else if(value * 1000000 > 1) {
            precision = 6
        } else {
            precision = 8
        }
    }

    let num = currency(value, { separator: ",", decimal: ".", symbol: "", fromCents: fromCents, precision: precision  }).format()
    let numArr = num.toString().split('.')
    return numArr[1] === '00' ? numArr[0] : num;


   /* switch (store.state.language) {
        case 'pt':
            return currency(value, { separator: ".", decimal: "," , symbol: "", fromCents: fromCents, precision: precision }).format() //, symbol: "R$"
        default: // en
            return currency(value, { separator: ",", decimal: ".", symbol: "", fromCents: fromCents, precision: precision  }).format()
    }*/
};

Vue.prototype.formatGold = function (data, decimal = 2) {
    if (data === undefined || data === null || data === 0) {
        return 0 + "K"
    }

    data /= 100
    //整数不留小数
    // let d = Math.floor(data * 1000) % 1000;
    // if (d == 0) {
    decimal = 0
    // }

    let temp = Math.abs(data)

    if (temp < 1000) {
        return data.toFixed(0)
    } else if (temp < 1000000) {
        data = Number((data / 1000).toFixed(3))

        let d = Math.floor(data * 1000) % 1000;
        if (d === 0) {
            decimal = 0
        } else if (d % 100 === 0) {
            decimal = 1
        } else if (d % 10 === 0) {
            decimal = 2
        } else {
            decimal = 2
        }

        return data.toFixed(decimal) + "K"
    } else { // if (temp < 1000000000)
        data = Number((data / 1000000).toFixed(3))

        let d = Math.floor(data * 1000) % 1000;
        if (d === 0) {
            decimal = 0
        } else if (d % 100 === 0) {
            decimal = 1
        } else if (d % 10 === 0) {
            decimal = 2
        } else {
            decimal = 2
        }

        return data.toFixed(decimal) + "M"
    }
    // else {
    //     data = Number((data / 1000000000).toFixed(3))

    //     let d = Math.floor(data * 1000) % 1000;
    //     if (d == 0) {
    //         decimal = 0
    //     } else if (d % 100 == 0) {
    //         decimal = 1
    //     } else if (d % 10 == 0) {
    //         decimal = 2
    //     } else {
    //         decimal = 2
    //     }

    //     return data.toFixed(decimal) + "B"
    // }

    // if (data < 1){
    data = data * 1000
    return data.toFixed(0) + "đ"

    // }else if (data >= 10000) {

    data = data / 1000
    return data.toFixed(decimal) + "M"

    // }else if (data >= 10000000) {

    data = data / 1000
    return data.toFixed(decimal) + "B"

    // }
    // else {

    return data.toFixed(decimal) + "K"

    // }
};
Vue.prototype.formatGold2 = function (data, decimal = 2) {
    if (data === undefined || data === null || data === 0) {
        return 0
    }

    data /= 100

    let temp = Math.abs(data)

    if (temp < 1000) {
        return data.toFixed(decimal)
    } else if (temp < 1000000) {
        data = Number((data / 1000).toFixed(decimal))
        return data.toFixed(decimal) + "K"
    } else { // if (temp < 1000000000)
        data = Number((data / 1000000).toFixed(decimal))
        return data.toFixed(decimal) + "M"
    }
};
Vue.prototype.formatGoldWithK = function (data, decimal = 2) {

    if (data === undefined || data === null || data === 0) {
        return 0 + "K"
    }
    data /= 100

    //整数不留小数
    // let d = Math.floor(data * 1000) % 1000;
    // if (d == 0) {
    decimal = 0
    // }

    let temp = Math.abs(data)

    if (temp < 1000) {
        return data.toFixed(0)
    }
    data = Number((data / 1000).toFixed(3))

    let d = Math.floor(data * 1000) % 1000;
    if (d === 0) {
        decimal = 0
    } else if (d % 100 === 0) {
        decimal = 1
    } else if (d % 10 === 0) {
        decimal = 2
    } else {
        decimal = 2
    }

    return data.toFixed(decimal) + "K"
}

Vue.filter("currency", Vue.prototype.currency);
Vue.filter("formatGold", Vue.prototype.formatGold);
Vue.filter("formatGold2", Vue.prototype.formatGold2);
Vue.filter("formatGoldWithK", Vue.prototype.formatGoldWithK);


import VueLazyload from 'vue-lazyload'
Vue.use(VueLazyload, {
    // loading: "img/img-loading.97ee6a57.gif",
    error: "img/img-loading.97ee6a57.gif",
    adapter: {
        threshold: 500,
        listenEvents: ['scroll'],
        preLoad: 1.3,
    },
});


import {ROUTE_RECORDER_QUERY_QUERYADEVENT} from "@/api";
import {dot} from "@/mixins/dot";
import i18n from "@/lang";
let timerId;
function myFunction() {
    if (store.getters.isLogin) {
        protoApi(ROUTE_RECORDER_QUERY_QUERYADEVENT, {
            channel: store.state.channel,
            device: store.state.device,
            token: store.state.token.token,
        })
            .then((res) => {
                for (const event of res.events) {
                    let param = {
                        amount: (event.amount/100).toFixed(2),
                        value: (event.amount/100).toFixed(2),
                        currency: "VND",
                        createTime: datetimeFormat(event.createTime * 1000),
                        userId: event.userId,
                    }
                    switch (event.advertiseEvent) {
                        case 1:
                            dot.methods.event_recharge(param)
                            break;
                        case 2:
                            dot.methods.event_firstrecharge(param)
                            break;
                        case 3:
                            dot.methods.event_withdrawOrderSuccess(param)
                            break
                    }
                }
            })
            .catch(() => {});
    }
}

// 导出 myFunction 函数以供其他模块使用
export { myFunction };

let e = Date.now();
window.document.body.addEventListener("click", (()=>{
        const t = Date.now();
        // if (t - e > 1000*10) {
        if (t - e > 36e5) {
            if (store.state.webType === 1) {
                window.cover.init(
                    {
                        title: i18n.t("in_popup_prompt"),
                        html: i18n.t("a6.error.guide2Reload"),
                        icon: "info",
                        btn: {
                            cancel: i18n.t("button_cancel"),
                            confirm: i18n.t("ok"),
                        },
                    },
                    (e) => {
                        if ("confirm" === e) {
                            location.reload()
                        }
                    }
                );
            } else {
                window.$Dialog.confirm({
                    title: i18n.t("in_popup_prompt"),
                    message: i18n.t("a6.error.guide2Reload"),
                    confirmButtonText: i18n.t("ok"),
                    cancelButtonText: i18n.t("button_cancel"),
                })
                    .then(() => {
                        location.reload()
                    })
                    .catch(() => {
                        // on cancel
                    });
            }
        }
        e = t
    }
), {
    capture: !0
})
window.onload = function() {
    timerId = setInterval(myFunction, 30000);
}
window.onunload = function() {
    clearInterval(timerId);
}

store.commit("setAFKey", getParameterByName('afKey'));
store.commit("setFBKey", getParameterByName('pixelId'));
store.commit("setKwaiKey", getParameterByName('kwaiPixelBaseCode'));

// Import the functions you need from the SDKs you need
let firebase = getParameterByName('firebase');
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

if (firebase && firebaseConfigs[firebase]) {
    store.commit("setFirebaseKey", getParameterByName('firebase'));
    // Your web app's Firebase configuration
    // For Firebase JS SDK v7.20.0 and later, measurementId is optional
    const firebaseConfig = firebaseConfigs[firebase];

    // Initialize Firebase
    const app = initializeApp(firebaseConfig);
    window.analytics = getAnalytics(app);
}


let link_id = getParameterByName('link_id');
let uuid = getParameterByName('uuid');
let channel_id = getParameterByName('channel_id');

// localStorage.setItem("__rb_9269653842_link_id", "3333");
// localStorage.setItem("__rb_9269653842_uuid", "444");
// localStorage.setItem("__rb_9269653842_params", "?channel_id=9&rb_pixel_id=247306054405654t&promote_url_id=6355207413&invite_code=&rb_page=1&rb_time=1715414606699&link_id=0581445177082480&uuid=3375440135246523");
if (channel_id && link_id && uuid) {
    store.commit("setRbKey", {link_id, uuid, channel_id});
} else {
    for (let i = 0; i < localStorage.length; i++) {
        let key = localStorage.key(i);
        if (/^__rb_\d*_link_id$/.test(key)) {
            link_id = localStorage.getItem(key);
        }
        if (/^__rb_\d*_uuid$/.test(key)) {
            uuid = localStorage.getItem(key);
        }
        if (/^__rb_\d*_params$/.test(key)) {
            channel_id = new URLSearchParams(localStorage.getItem(key)).get("channel_id");
        }
    }
    if (link_id && uuid && channel_id) {
        store.commit("setRbKey", {link_id, uuid, channel_id});
    }
}

if (store.state.dotKeys.afKey) {
    !function(t,e,n,s,a,c,i,o,p){t.AppsFlyerSdkObject=a,t.AF=t.AF||function(){
        (t.AF.q=t.AF.q||[]).push([Date.now()].concat(Array.prototype.slice.call(arguments)))},
        t.AF.id=t.AF.id||i,t.AF.plugins={},o=e.createElement(n),p=e.getElementsByTagName(n)[0],o.async=1,
        o.src="https://websdk.appsflyer.com?"+(c.length>0?"st="+c.split(",").sort().join(",")+"&":"")+(i.length>0?"af_id="+i:""),
        p.parentNode.insertBefore(o,p)}(window,document,"script",0,"AF","pba",{pba: {webAppId: store.state.dotKeys.afKey}})
}

if (store.state.dotKeys.pixelId) {
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', store.state.dotKeys.pixelId);
    fbq('track', 'PageView');
}

if (store.state.dotKeys.kwai) {
    !function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.install=t():e.install=t()}(window,(function(){return function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=0)}([function(e,t,n){"use strict";var o=this&&this.__spreadArray||function(e,t,n){if(n||2===arguments.length)for(var o,r=0,i=t.length;r<i;r++)!o&&r in t||(o||(o=Array.prototype.slice.call(t,0,r)),o[r]=t[r]);return e.concat(o||Array.prototype.slice.call(t))};Object.defineProperty(t,"__esModule",{value:!0});var r=function(e,t,n){var o,i=e.createElement("script");i.type="text/javascript",i.async=!0,i.src=t,n&&(i.onerror=function(){r(e,n)});var a=e.getElementsByTagName("script")[0];null===(o=a.parentNode)||void 0===o||o.insertBefore(i,a)};!function(e,t,n){e.KwaiAnalyticsObject=n;var i=e[n]=e[n]||[];i.methods=["page","track","identify","instances","debug","on","off","once","ready","alias","group","enableCookie","disableCookie"];var a=function(e,t){e[t]=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=o([t],n,!0);e.push(i)}};i.methods.forEach((function(e){a(i,e)})),i.instance=function(e){var t,n=(null===(t=i._i)||void 0===t?void 0:t[e])||[];return i.methods.forEach((function(e){a(n,e)})),n},i.load=function(e,o){var a="https://s1.kwai.net/kos/s101/nlav11187/pixel/events.js";i._i=i._i||{},i._i[e]=[],i._i[e]._u=a,i._t=i._t||{},i._t[e]=+new Date,i._o=i._o||{},i._o[e]=o||{};var c="?sdkid=".concat(e,"&lib=").concat(n);r(t,a+c,"https://s16-11187.ap4r.com/kos/s101/nlav11187/pixel/events.js"+c)}}(window,document,"kwaiq")}])}));
    'kwaiq' in window && window.kwaiq.load(store.state.dotKeys.kwai);
    'kwaiq' in window && window.kwaiq.page();
}

import "@/assets/icon/events"
import "@/assets/icon/menu"
import "@/assets/icon/iconfont"


Vue.directive('draggable', {
    // 当被绑定的元素挂载到 DOM 上时调用
    bind(el, binding, vnode) {
        let isDragging = false;
        let startX = 0;
        let scrollLeft = 0;
        let getTabsNavDiv = el.childNodes.length === 1 ? el.firstChild : el.firstChild.firstChild;

        const handleMouseDown = (e) => {
            isDragging = !0;
            startX = e.pageX;
            scrollLeft = getTabsNavDiv?.scrollLeft

            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        };

        const handleMouseMove = (e) => {
            if (!isDragging) return;
            const t = (e.pageX - startX);
            // getTabsNavDiv.scrollLeft = scrollLeft - t;
            getTabsNavDiv.scrollTo(scrollLeft - t, 0)
            if (Math.abs(t) > 20) {
                getTabsNavDiv.style.pointerEvents = "none"
            }
        };

        const handleMouseUp = () => {
            isDragging = !1;
            getTabsNavDiv.style.pointerEvents = "auto"
        };

        getTabsNavDiv && getTabsNavDiv.addEventListener('mousedown', handleMouseDown);
        vnode.elm['vue-draggable-cleanup'] = () => {
            el.removeEventListener('mousedown', handleMouseDown);
        };
    },
    // 当绑定的元素被移除时调用
    unbind(el) {
        // 如果定义了清理函数，则调用它
        if (el['vue-draggable-cleanup']) {
            el['vue-draggable-cleanup']();
        }
    }
});