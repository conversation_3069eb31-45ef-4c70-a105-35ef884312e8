const { defineConfig } = require("@vue/cli-service");
const { VantResolver } = require("unplugin-vue-components/resolvers");
const ComponentsPlugin = require("unplugin-vue-components/webpack");
const MomentLocalesPlugin = require('moment-locales-webpack-plugin');
const webpack = require("webpack");
let date = new Date();
let y = date.getFullYear(),
    m = date.getMonth()+1,
    d = date.getDate(),
    h = date.getHours(),
    i = date.getMinutes()+2;
if (m < 10) { m = '0' + m; }
if (d < 10) { d = '0' + d; }
if (h < 10) { h = '0' + h; }
if (i < 10) { i = '0' + i; }
const version = "202507171740" //''+y+m+d+h+i;
module.exports = defineConfig({
  publicPath: process.env.NODE_ENV=== 'development' ? './' : '././',
  outputDir: "dist/tdtc-"+version,
  productionSourceMap: false,
  transpileDependencies: true,
  filenameHashing: false,
  // pages: {
  //   mobile: {
  //     template: "public/m/index.html",
  //     entry: "src/modules/mobile/main.js",
  //     filename: "m/index.html",
  //   },
  //   pc: {
  //     template: "public/index.html",
  //     entry: "src/modules/pc/main.js",
  //     filename: "index.html",
  //   },
  // },
  devServer: {
    historyApiFallback: {
      rewrites: [
        // shows views/landing.html as the landing page
        { from: /^\/$/, to: '/' },
        // shows views/subpage.html for all routes starting with /subpage
        // { from: /^\/m/, to: '/m/index.html' },
      ]
    }
  },
  chainWebpack: config => {
    config.output.filename('js/[name].' + version + '.js').end();
    config.output.chunkFilename('js/[name].' + version + '.js').end();
  },
  css: {
    extract: {
      filename: `css/[name].${version}.css`,
      chunkFilename: `css/[name].${version}.css`
    }
  },
  configureWebpack: {
    performance: {
      hints: 'warning',
      // 入口起点的最大体积
      maxEntrypointSize: 50000000,
      // 生成文件的最大体积
      maxAssetSize: 30000000,
      // 只给出 js 文件的性能提示
      assetFilter: function(assetFilename) {
        return assetFilename.endsWith('.js') || assetFilename.endsWith('.css');
      }
    },
    plugins: [
      // 支持 jquery
      new webpack.ProvidePlugin({
        $: "jquery",
        jQuery: "jquery",
        "windows.jQuery": "jquery",
      }),
      ComponentsPlugin({
        resolvers: [VantResolver()],
      }),
      new MomentLocalesPlugin({
        localesToKeep: ['en'],
      }),
    ],
  },
});
